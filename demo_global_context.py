#!/usr/bin/env python3
"""
Global Market Context Analysis Demonstration

This script shows how the system analyzes global market sessions
to predict NYSE behavior and detect manipulation patterns.
"""

import sys
import os
import logging
from datetime import datetime, time, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.global_context_analyzer import GlobalContextAnalyzer, MarketSession, MarketManipulation

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_global_session_data():
    """Create realistic global market session data for demonstration."""
    # Base time: Today at market open
    base_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    
    # Asian session data (previous day 2:00 PM - 6:00 AM EST)
    asian_candles = []
    asian_start = base_time - timedelta(days=1, hours=19, minutes=30)  # 2:00 PM previous day
    
    for i in range(16):  # 16 hours of Asian session
        candle_time = asian_start + timedelta(hours=i)
        base_price = 100.0 + (i % 4 - 2) * 0.5  # Oscillating pattern
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price,
            'high': base_price + 0.3,
            'low': base_price - 0.2,
            'close': base_price + 0.1,
            'volume': 300000 + (i * 5000),
            'ticker': 'AMD',
            'timeframe': '1h'
        })()
        asian_candles.append(candle)
    
    # European session data (2:00 AM - 11:00 AM EST)
    european_candles = []
    european_start = base_time - timedelta(hours=7, minutes=30)  # 2:00 AM
    
    for i in range(9):  # 9 hours of European session
        candle_time = european_start + timedelta(hours=i)
        base_price = 100.5 + (i % 3 - 1) * 0.3  # Slight uptrend
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price,
            'high': base_price + 0.2,
            'low': base_price - 0.1,
            'close': base_price + 0.05,
            'volume': 400000 + (i * 10000),
            'ticker': 'AMD',
            'timeframe': '1h'
        })()
        european_candles.append(candle)
    
    # Pre-market data (4:00 AM - 9:30 AM EST)
    pre_market_candles = []
    pre_market_start = base_time - timedelta(hours=5, minutes=30)  # 4:00 AM
    
    for i in range(5):  # 5.5 hours of pre-market
        candle_time = pre_market_start + timedelta(hours=i)
        
        # Simulate some manipulation in pre-market
        if i == 2:  # 6:00 AM - liquidity trap
            base_price = 101.0
            volume = 800000  # High volume
        else:
            base_price = 100.8 + i * 0.1
            volume = 200000 + (i * 50000)
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price,
            'high': base_price + 0.4 if i == 2 else base_price + 0.2,
            'low': base_price - 0.1,
            'close': base_price - 0.2 if i == 2 else base_price + 0.05,
            'volume': volume,
            'ticker': 'AMD',
            'timeframe': '1h'
        })()
        pre_market_candles.append(candle)
    
    # NYSE session data (9:30 AM - 4:00 PM EST)
    nyse_candles = []
    for i in range(6):  # 6.5 hours of NYSE session
        candle_time = base_time + timedelta(hours=i)
        base_price = 101.2 + (i % 2) * 0.2  # Slight uptrend
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price,
            'high': base_price + 0.3,
            'low': base_price - 0.2,
            'close': base_price + 0.1,
            'volume': 600000 + (i * 20000),
            'ticker': 'AMD',
            'timeframe': '1h'
        })()
        nyse_candles.append(candle)
    
    # After hours data (4:00 PM - 8:00 PM EST)
    after_hours_candles = []
    after_hours_start = base_time + timedelta(hours=6, minutes=30)  # 4:00 PM
    
    for i in range(4):  # 4 hours of after hours
        candle_time = after_hours_start + timedelta(hours=i)
        base_price = 101.8 + (i % 2) * 0.1
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price,
            'high': base_price + 0.2,
            'low': base_price - 0.1,
            'close': base_price + 0.05,
            'volume': 150000 + (i * 10000),
            'ticker': 'AMD',
            'timeframe': '1h'
        })()
        after_hours_candles.append(candle)
    
    return {
        "asian": asian_candles,
        "european": european_candles,
        "pre_market": pre_market_candles,
        "nyse": nyse_candles,
        "after_hours": after_hours_candles
    }

def demonstrate_global_analysis():
    """Demonstrate global market context analysis."""
    print("🌍 **Global Market Context Analysis**")
    print("=" * 50)
    
    # Create analyzer
    analyzer = GlobalContextAnalyzer()
    
    # Create sample data
    global_data = create_global_session_data()
    
    print("📊 **Session Data Created:**")
    for session, data in global_data.items():
        print(f"  • {session.title()}: {len(data)} candles")
        if data:
            print(f"    Time range: {data[0].timestamp.strftime('%H:%M')} - {data[-1].timestamp.strftime('%H:%M')}")
            print(f"    Price range: ${min(c.low for c in data):.2f} - ${max(c.high for c in data):.2f}")
    
    # Analyze global context
    print("\n🔍 **Analyzing Global Context...**")
    context = analyzer.analyze_global_context("AMD", global_data)
    
    if context:
        print(f"\n✅ **Analysis Complete for AMD**")
        print(f"  • Global Trend: {context.global_trend.upper()}")
        print(f"  • NYSE Probability: {context.nyse_probability:.1%}")
        print(f"  • Risk Level: {context.risk_level}")
        
        print(f"\n📈 **Session Analysis:**")
        print(f"  • Asian: {context.asian_session['trend']} (strength: {context.asian_session['strength']:.1%})")
        print(f"  • European: {context.european_session['trend']} (strength: {context.european_session['strength']:.1%})")
        print(f"  • Pre-Market: {context.pre_market['trend']} (strength: {context.pre_market['strength']:.1%})")
        
        if context.manipulation_detected:
            print(f"\n⚠️ **Manipulation Detected:**")
            for pattern in context.manipulation_detected:
                print(f"  • {pattern.value.replace('_', ' ').title()}")
        else:
            print(f"\n✅ No manipulation patterns detected")
    
    return analyzer, context

def demonstrate_manipulation_detection():
    """Demonstrate specific manipulation pattern detection."""
    print("\n🎯 **Manipulation Pattern Detection**")
    print("=" * 50)
    
    # Create analyzer
    analyzer = GlobalContextAnalyzer()
    
    # Create data with specific manipulation patterns
    print("🧪 **Creating Manipulation Test Data...**")
    
    # Liquidity trap data
    trap_data = []
    base_time = datetime.now().replace(hour=6, minute=0, second=0, microsecond=0)
    
    for i in range(5):
        candle_time = base_time + timedelta(minutes=i*15)
        
        if i == 2:  # Liquidity trap at 6:30 AM
            candle = type('MockCandle', (), {
                'timestamp': candle_time,
                'open': 100.0,
                'high': 100.8,  # Spike up
                'low': 99.8,
                'close': 99.9,  # Quick reversal
                'volume': 800000,
                'ticker': 'AMD',
                'timeframe': '15m'
            })()
        else:
            candle = type('MockCandle', (), {
                'timestamp': candle_time,
                'open': 100.0 + i * 0.1,
                'high': 100.0 + i * 0.1 + 0.2,
                'low': 100.0 + i * 0.1 - 0.1,
                'close': 100.0 + i * 0.1 + 0.05,
                'volume': 200000,
                'ticker': 'AMD',
                'timeframe': '15m'
            })()
        
        trap_data.append(candle)
    
    # Test manipulation detection
    test_data = {"pre_market": trap_data}
    patterns = analyzer._detect_manipulation_patterns("AMD", test_data)
    
    print(f"🔍 **Detected {len(patterns)} Manipulation Patterns:**")
    for pattern in patterns:
        print(f"\n  • **{pattern.pattern_type.value.replace('_', ' ').title()}**")
        print(f"    Confidence: {pattern.confidence:.1%}")
        print(f"    Time: {pattern.time_range[0].strftime('%H:%M')} - {pattern.time_range[1].strftime('%H:%M')}")
        
        for evidence in pattern.evidence:
            print(f"    Evidence: {evidence}")
    
    return analyzer, patterns

def demonstrate_nyse_prediction():
    """Demonstrate NYSE behavior prediction."""
    print("\n🔮 **NYSE Behavior Prediction**")
    print("=" * 50)
    
    # Create analyzer
    analyzer = GlobalContextAnalyzer()
    
    # Test different global scenarios
    scenarios = [
        {
            "name": "Bullish Global Trend",
            "asian": {"trend": "bullish", "strength": 0.05},
            "european": {"trend": "bullish", "strength": 0.03},
            "pre_market": {"trend": "bullish", "strength": 0.02}
        },
        {
            "name": "Bearish Global Trend",
            "asian": {"trend": "bearish", "strength": 0.04},
            "european": {"trend": "bearish", "strength": 0.06},
            "pre_market": {"trend": "bearish", "strength": 0.03}
        },
        {
            "name": "Mixed Signals",
            "asian": {"trend": "bullish", "strength": 0.02},
            "european": {"trend": "bearish", "strength": 0.04},
            "pre_market": {"trend": "neutral", "strength": 0.01}
        }
    ]
    
    print("📊 **Testing Different Global Scenarios:**")
    
    for scenario in scenarios:
        print(f"\n🎯 **{scenario['name']}:**")
        
        # Calculate NYSE probability
        probability = analyzer._calculate_nyse_probability(
            scenario["asian"], scenario["european"], scenario["pre_market"]
        )
        
        # Determine trend
        trend = analyzer._determine_global_trend(
            scenario["asian"], scenario["european"], scenario["pre_market"]
        )
        
        print(f"  • Global Trend: {trend.upper()}")
        print(f"  • NYSE Probability: {probability:.1%}")
        
        # Interpret probability
        if probability > 0.7:
            interpretation = "Strong bullish expectation"
        elif probability > 0.6:
            interpretation = "Moderate bullish expectation"
        elif probability < 0.3:
            interpretation = "Strong bearish expectation"
        elif probability < 0.4:
            interpretation = "Moderate bearish expectation"
        else:
            interpretation = "Neutral/uncertain expectation"
        
        print(f"  • Interpretation: {interpretation}")

def main():
    """Run the global context analysis demonstration."""
    print("🚀 **Global Market Context Analysis System**")
    print("=" * 60)
    print("This demonstration shows how the system:")
    print("• Analyzes global market sessions (Asian, European, Pre-market)")
    print("• Detects manipulation patterns (liquidity traps, stop hunting)")
    print("• Predicts NYSE behavior based on global context")
    print("• Identifies accumulation/distribution patterns")
    print("=" * 60)
    
    try:
        # Demonstrate global analysis
        analyzer, context = demonstrate_global_analysis()
        
        # Demonstrate manipulation detection
        analyzer2, patterns = demonstrate_manipulation_detection()
        
        # Demonstrate NYSE prediction
        demonstrate_nyse_prediction()
        
        print("\n🎉 **Global Context Analysis Complete!**")
        print("The system successfully analyzes global market context and predicts NYSE behavior.")
        
    except Exception as e:
        print(f"\n❌ **Demonstration Failed:** {e}")
        logger.error(f"Demonstration error: {e}", exc_info=True)

if __name__ == "__main__":
    main() 