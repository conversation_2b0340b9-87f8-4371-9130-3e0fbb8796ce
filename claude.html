<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Automation - Current System Status</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
            color: white;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .status-overview {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .status-overview h2 {
            color: #00ff88;
            margin-bottom: 15px;
        }
        
        .network-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .network-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .network-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 212, 255, 0.2);
        }
        
        .network-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .network-core .network-title { color: #00ff88; }
        .network-webhook .network-title { color: #4ecdc4; }
        .network-external .network-title { color: #45b7d1; }
        .network-optional .network-title { color: #ffaa00; }
        
        .service {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
            cursor: pointer;
            border-left: 4px solid transparent;
        }
        
        .service:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateX(5px);
        }
        
        .service.healthy { border-left-color: #00ff88; }
        .service.starting { border-left-color: #ffaa00; }
        .service.failing { border-left-color: #ff4757; }
        .service.placeholder { border-left-color: #ffaa00; }
        
        .service-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .service-name {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .service-port {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        .service-desc {
            font-size: 0.8em;
            opacity: 0.6;
            font-style: italic;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-healthy {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
        }
        
        .status-starting {
            background: rgba(255, 170, 0, 0.2);
            color: #ffaa00;
        }
        
        .status-failing {
            background: rgba(255, 71, 87, 0.2);
            color: #ff4757;
        }
        
        .status-placeholder {
            background: rgba(255, 170, 0, 0.2);
            color: #ffaa00;
        }
        
        .flow-diagram {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .flow-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            color: #00d4ff;
        }
        
        .flow-path {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .flow-node {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 0.9em;
            transition: all 0.2s ease;
        }
        
        .flow-node:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: scale(1.05);
        }
        
        .flow-arrow {
            font-size: 1.2em;
            color: #00d4ff;
            margin: 0 5px;
        }
        
        .port-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .port-table th,
        .port-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .port-table th {
            background: rgba(0, 212, 255, 0.2);
            font-weight: bold;
            color: #00d4ff;
        }
        
        .port-table tr:hover {
            background: rgba(255, 255, 255, 0.08);
        }
        
        .feature-panel {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 255, 136, 0.05));
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .feature-title {
            color: #00ff88;
            font-size: 1.5em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-box {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .info-title {
            color: #00d4ff;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            border-left: 4px solid #00d4ff;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            gap: 5px;
        }
        
        .tab {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 10px 10px 0 0;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .tab.active {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .command-list {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .command-item {
            background: rgba(0, 212, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #00d4ff;
        }
        
        .command-name {
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 5px;
        }
        
        .command-desc {
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .command-status {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .network-grid {
                grid-template-columns: 1fr;
            }
            
            .flow-path {
                flex-direction: column;
                align-items: stretch;
            }
            
            .flow-node {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 TradingView Automation - System Status</h1>
            <p>Current architecture, services, and features overview</p>
        </div>
        
        <div class="status-overview">
            <h2>🎉 System Status: OPERATIONAL</h2>
            <p><strong>Discord Bot:</strong> ✅ Online and responding | <strong>Core Services:</strong> ✅ Healthy | <strong>Smart Ask Command:</strong> ✅ Deployed</p>
            <p><em>Last Updated:</em> August 31, 2025 - Bot successfully restarted with new AI-powered ask command</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">System Overview</button>
            <button class="tab" onclick="showTab('commands')">Bot Commands</button>
            <button class="tab" onclick="showTab('flows')">Data Flows</button>
            <button class="tab" onclick="showTab('ports')">Port Mapping</button>
            <button class="tab" onclick="showTab('features')">New Features</button>
        </div>
        
        <div id="overview" class="tab-content active">
            <div class="network-grid">
                <div class="network-section network-core">
                    <div class="network-title">
                        🟢 Core Services
                        <small>(Healthy & Operational)</small>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('discord-bot')">
                        <div class="service-info">
                            <div class="service-name">Discord Bot</div>
                            <div class="service-port">External API</div>
                            <div class="service-desc">NHX.ai#2574 - Smart AI-powered commands</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('supabase')">
                        <div class="service-info">
                            <div class="service-name">Supabase Database</div>
                            <div class="service-port">Cloud API</div>
                            <div class="service-desc">Managed PostgreSQL with auth & realtime</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Connected</div>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('redis')">
                        <div class="service-info">
                            <div class="service-name">Redis</div>
                            <div class="service-port">Port: 6379</div>
                            <div class="service-desc">Cache and message queue</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                    <div class="service healthy" onclick="showServiceDetails('webhook-receiver')">
                        <div class="service-info">
                            <div class="service-name">Webhook Receiver</div>
                            <div class="service-port">Port: 8001</div>
                            <div class="service-desc">TradingView webhook processing</div>
                        </div>
                        <div class="status-badge status-healthy">✅ Healthy</div>
                    </div>
                </div>
                
                <div class="network-section network-optional">
                    <div class="network-title">
                        🟡 Optional Services
                        <small>(Placeholder/Development)</small>
                    </div>
                    <div class="service placeholder" onclick="showServiceDetails('processor')">
                        <div class="service-info">
                            <div class="service-name">Data Processor</div>
                            <div class="service-port">Internal</div>
                            <div class="service-desc">Placeholder service - not critical</div>
                        </div>
                        <div class="status-badge status-placeholder">🔄 Placeholder</div>
                    </div>
                    <div class="service placeholder" onclick="showServiceDetails('monitor')">
                        <div class="service-info">
                            <div class="service-name">System Monitor</div>
                            <div class="service-port">Internal</div>
                            <div class="service-desc">Placeholder service - not critical</div>
                        </div>
                        <div class="status-badge status-placeholder">🔄 Placeholder</div>
                    </div>
                </div>
                
                <div class="network-section network-external" style="grid-column: 1 / -1;">
                    <div class="network-title">
                        🌍 External Integrations
                        <small>(API Connections)</small>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                        <div class="service healthy" onclick="showServiceDetails('openrouter')">
                            <div class="service-info">
                                <div class="service-name">OpenRouter AI</div>
                                <div class="service-port">API: openrouter.ai</div>
                                <div class="service-desc">323 AI models available - Smart responses</div>
                            </div>
                            <div class="status-badge status-healthy">✅ Connected</div>
                        </div>
                        <div class="service healthy" onclick="showServiceDetails('discord-api')">
                            <div class="service-info">
                                <div class="service-name">Discord API</div>
                                <div class="service-port">API: discord.com</div>
                                <div class="service-desc">Bot communication - fully operational</div>
                            </div>
                            <div class="status-badge status-healthy">✅ Connected</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="commands" class="tab-content">
            <div class="feature-panel">
                <div class="feature-title">
                    🤖 Discord Bot Commands
                </div>
                
                <div class="command-list">
                    <div class="command-item">
                        <div class="command-name">/ask [query]</div>
                        <div class="command-desc">AI-powered intelligent query processing - understands context, tickers, and market intent</div>
                        <div class="command-status">✅ Status: Deployed and working | 🔄 Type: Smart AI Command</div>
                    </div>
                    
                    <div class="command-item">
                        <div class="command-name">/analyze [symbol]</div>
                        <div class="command-desc">Comprehensive stock analysis with technical indicators and market structure</div>
                        <div class="command-status">✅ Status: Operational | 🔄 Type: Market Analysis</div>
                    </div>
                    
                    <div class="command-item">
                        <div class="command-name">/status</div>
                        <div class="command-desc">Bot health check and performance metrics</div>
                        <div class="command-status">✅ Status: Operational | 🔄 Type: System Command</div>
                    </div>
                    
                    <div class="command-item">
                        <div class="command-name">!status</div>
                        <div class="command-desc">Legacy prefix command for bot status</div>
                        <div class="command-status">✅ Status: Operational | 🔄 Type: Legacy Command</div>
                    </div>
                </div>
                
                <div class="info-box">
                    <div class="info-title">🆕 New Smart Ask Command Features</div>
                    <ul style="margin-left: 20px; line-height: 1.6;">
                        <li><strong>Context-Aware Processing:</strong> Automatically detects tickers, market keywords, and query intent</li>
                        <li><strong>AI-Powered Responses:</strong> Uses OpenRouter AI for intelligent, contextual answers</li>
                        <li><strong>No More Rigid Routing:</strong> Eliminated old market vs. casual chat classification</li>
                        <li><strong>Smart Ticker Detection:</strong> Advanced regex with exclusion lists for common words</li>
                        <li><strong>Dynamic Response Generation:</strong> Market-specific, general market, or AI-powered responses</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div id="flows" class="tab-content">
            <div class="flow-diagram">
                <div class="flow-title">🤖 Discord Bot Command Flow</div>
                <div class="flow-path">
                    <div class="flow-node">User Command</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Discord Bot</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">AI Understanding</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Natural Response</div>
                </div>
                <div class="flow-path">
                    <div class="flow-node">Supabase</div>
                    <span class="flow-arrow">↔</span>
                    <div class="flow-node">Redis Cache</div>
                    <span class="flow-arrow">↔</span>
                    <div class="flow-node">OpenRouter AI</div>
                </div>
            </div>
            
            <div class="flow-diagram">
                <div class="flow-title">📡 Webhook Processing Flow</div>
                <div class="flow-path">
                    <div class="flow-node">TradingView</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Webhook Receiver</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Redis Queue</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Discord Bot</div>
                </div>
            </div>
            
            <div class="flow-diagram">
                <div class="flow-title">🔧 Data Access Flow</div>
                <div class="flow-path">
                    <div class="flow-node">Discord Bot</div>
                    <span class="flow-arrow">→</span>
                    <div class="flow-node">Supabase</div>
                    <span class="flow-arrow">↔</span>
                    <div class="flow-node">Redis Cache</div>
                </div>
            </div>
        </div>
        
        <div id="ports" class="tab-content">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🌐 Exposed Ports</h3>
            <table class="port-table">
                <thead>
                    <tr>
                        <th>Port</th>
                        <th>Service</th>
                        <th>Container</th>
                        <th>Access Level</th>
                        <th>Purpose</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>8001</td>
                        <td>Webhook</td>
                        <td>webhook-receiver</td>
                        <td>Public</td>
                        <td>TradingView webhook endpoint</td>
                        <td>✅ Healthy</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 style="color: #00ff88; margin: 30px 0 15px;">🔒 Internal Ports</h3>
            <table class="port-table">
                <thead>
                    <tr>
                        <th>Port</th>
                        <th>Service</th>
                        <th>Container</th>
                        <th>Network</th>
                        <th>Purpose</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>HTTPS</td>
                        <td>Supabase API</td>
                        <td>supabase</td>
                        <td>cloud</td>
                        <td>Managed PostgreSQL</td>
                        <td>✅ Connected</td>
                    </tr>
                    <tr>
                        <td>6379</td>
                        <td>Redis</td>
                        <td>redis</td>
                        <td>internal</td>
                        <td>Cache & queue</td>
                        <td>✅ Healthy</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="features" class="tab-content">
            <div class="feature-panel">
                <div class="feature-title">
                    🚀 Recent System Improvements
                </div>
                
                <div class="info-box">
                    <div class="info-title">🤖 Smart Ask Command Overhaul</div>
                    <p><strong>What Changed:</strong> Complete rewrite of the /ask command from rigid rule-based routing to intelligent AI-powered responses.</p>
                    
                    <div class="code-block">Old System: DualPurposeAskCommand
- Rigid market vs. casual routing
- Rule-based ticker detection
- Binary decision making
- Limited flexibility

New System: SmartAskCommand  
- Context-aware query analysis
- AI-powered response generation
- Dynamic routing based on content
- OpenRouter AI integration</div>
                    
                    <p><strong>Benefits:</strong> Users can now ask anything naturally, just like talking to a human assistant!</p>
                </div>
                
                <div class="info-box">
                    <div class="info-title">🔍 Enhanced Ticker Detection</div>
                    <ul style="margin-left: 20px; line-height: 1.6;">
                        <li><strong>Smart Regex:</strong> Improved patterns for stock symbols and crypto</li>
                        <li><strong>Exclusion Lists:</strong> Prevents common words from being identified as tickers</li>
                        <li><strong>Context Awareness:</strong> Understands when symbols are mentioned in conversation</li>
                        <li><strong>Dollar Prefix Support:</strong> Recognizes $AAPL, $TSLA format</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <div class="info-title">🧠 AI Integration</div>
                    <ul style="margin-left: 20px; line-height: 1.6;">
                        <li><strong>OpenRouter API:</strong> 323 AI models available for intelligent responses</li>
                        <li><strong>Context Analysis:</strong> Automatically detects market intent and tickers</li>
                        <li><strong>Dynamic Responses:</strong> Generates appropriate responses based on query type</li>
                        <li><strong>Fallback System:</strong> Graceful degradation when AI is unavailable</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <div class="info-title">📊 Current System Health</div>
                    <ul style="margin-left: 20px; line-height: 1.6;">
                        <li><strong>Discord Bot:</strong> ✅ Online and responding to commands</li>
                        <li><strong>Database:</strong> ✅ Supabase connection healthy</li>
                        <li><strong>Cache:</strong> ✅ Redis operational</li>
                        <li><strong>AI Service:</strong> ✅ OpenRouter API connected (323 models)</li>
                        <li><strong>Webhooks:</strong> ✅ TradingView integration working</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        function showServiceDetails(serviceName) {
            const serviceInfo = {
                'discord-bot': {
                    name: 'Discord Bot (NHX.ai#2574)',
                    network: 'External + Internal Networks',
                    port: 'Discord API Client',
                    status: 'Healthy ✅',
                    access: 'External Discord API + Internal database/cache',
                    purpose: 'AI-powered Discord bot with smart commands, market analysis, and intelligent responses'
                },
                'supabase': {
                    name: 'Supabase Database',
                    network: 'Cloud Service',
                    port: 'HTTPS API',
                    status: 'Connected ✅',
                    access: 'Cloud API with authentication',
                    purpose: 'Managed PostgreSQL database with built-in authentication, realtime subscriptions, and automatic backups'
                },
                'redis': {
                    name: 'Redis Cache & Queue',
                    network: 'Internal Network',
                    port: '6379',
                    status: 'Healthy ✅',
                    access: 'Internal only - no external access',
                    purpose: 'High-performance caching layer, message queue for webhook processing, and session storage'
                },
                'webhook-receiver': {
                    name: 'Webhook Receiver',
                    network: 'Bridge Network',
                    port: '8001',
                    status: 'Healthy ✅',
                    access: 'Public webhook endpoint for TradingView',
                    purpose: 'Receives and processes TradingView webhooks, queues them for bot processing'
                },
                'processor': {
                    name: 'Data Processor',
                    network: 'Internal Network',
                    port: 'Internal',
                    status: 'Placeholder 🔄',
                    access: 'Internal only',
                    purpose: 'Placeholder service for future data processing features - not critical for current operation'
                },
                'monitor': {
                    name: 'System Monitor',
                    network: 'Internal Network',
                    port: 'Internal',
                    status: 'Placeholder 🔄',
                    access: 'Internal only',
                    purpose: 'Placeholder service for future monitoring features - not critical for current operation'
                },
                'openrouter': {
                    name: 'OpenRouter AI API',
                    network: 'External Internet',
                    port: 'HTTPS (443)',
                    status: 'Connected ✅',
                    access: 'Public API with authentication',
                    purpose: 'AI service providing 323 models for intelligent responses, context analysis, and natural language processing'
                },
                'discord-api': {
                    name: 'Discord API',
                    network: 'External Internet',
                    port: 'HTTPS (443)',
                    status: 'Connected ✅',
                    access: 'Public API with bot token',
                    purpose: 'Discord platform integration for bot commands, message handling, and user interaction'
                }
            };
            
            const service = serviceInfo[serviceName];
            if (service) {
                alert(`🔍 Service Details\n\nName: ${service.name}\nNetwork: ${service.network}\nPort: ${service.port}\nStatus: ${service.status}\nAccess: ${service.access}\nPurpose: ${service.purpose}`);
            }
        }
        
        // Add some dynamic effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects for network sections
            const sections = document.querySelectorAll('.network-section');
            sections.forEach(section => {
                section.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                section.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Add click effects for services
            const services = document.querySelectorAll('.service');
            services.forEach(service => {
                service.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>