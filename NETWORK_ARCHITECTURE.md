# Network Architecture - What's Exposed vs Internal

## 🚨 **CURRENT REALITY - What's Actually Exposed:**

```
INTERNET
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    NGROK (Docker)                          │
│              Port 4040 (web interface)                     │
│              Port 443 (HTTPS tunnel)                       │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │           EXPOSED TO INTERNET                        │   │
│  │  https://[ngrok-url].ngrok-free.app                 │   │
│  │  ↓                                                  │   │
│  │  ALL REQUESTS → webhook-proxy:8001                  │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────────────────────┐
│                WEBHOOK-PROXY (Docker)                      │
│                    Port 8001                               │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │           EXPOSED TO INTERNET                        │   │
│  │  /webhook/tradingview                               │   │
│  │  ❌ /tickers/* (BLOCKED - 403 Forbidden)            │   │
│  │  ❌ /tickers/stats/* (BLOCKED - 403 Forbidden)      │   │
│  │  ❌ /health (BLOCKED - 403 Forbidden)                │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────────────────────┐
│              WEBHOOK-INGEST (Docker)                       │
│                    Port 8001 (internal)                    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┘   │
│  │           INTERNAL ONLY                              │   │
│  │  Processes webhook data                              │   │
│  │  Stores in database                                  │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 🔒 **WHAT'S INTERNAL (NOT EXPOSED):**

```
┌─────────────────────────────────────────────────────────────┐
│                    MAIN SYSTEM (INTERNAL)                  │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │           COMPLETELY INTERNAL                       │   │
│  │  nginx (ports 80/443) - INTERNAL ONLY               │   │
│  │  API service (port 8000) - INTERNAL ONLY            │   │
│  │  Database (postgres) - INTERNAL ONLY                 │   │
│  │  Redis cache - INTERNAL ONLY                         │   │
│  │  Trading bot logic - INTERNAL ONLY                   │   │
│  │  Ticker management endpoints - INTERNAL ONLY         │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 **WHAT'S EXPOSED TO INTERNET:**

1. **ngrok tunnel** - `https://[ngrok-url].ngrok-free.app`
2. **webhook-proxy** - **ONLY** `/webhook/tradingview` endpoint
3. **webhook-ingest** - Through webhook-proxy only

## ✅ **SECURITY STATUS:**

**Webhook Proxy Hardening COMPLETED:**
- ✅ **Restricted allowed routes** → only `/webhook/tradingview`
- ✅ **Return 403 Forbidden** on everything else
- ✅ **Blocked external access** to `/tickers/*` endpoints
- ✅ **Blocked external access** to `/tickers/stats/*` endpoints
- ✅ **Blocked external access** to `/health` endpoint

## 🚨 **REMAINING ISSUES:**

**ngrok Configuration:**
- ❌ **Still getting 400 Bad Request** from TradingView
- ❌ **ngrok firewall** blocking TradingView requests
- ❌ **Need to rotate ngrok URL** and test with new tunnel

---

# ✅ **WEBHOOK EXPOSURE FIX CHECKLIST**

## **Webhook Proxy Hardening**
- [x] Restrict allowed routes → only `/webhook/tradingview`
- [x] Return 403 Forbidden on everything else
- [ ] Validate request headers/signature if possible

## **Internal Service Protection**
- [x] Block `/tickers/*` and `/tickers/stats/*` from external traffic
- [x] Move ticker management endpoints to internal-only network
- [x] Confirm webhook-ingest isn't directly exposed (reachable only from proxy)

## **ngrok Configuration**
- [ ] Map public ngrok URL strictly to `/webhook/tradingview`
- [ ] Disable ngrok web inspector (`-inspect=false`) if not needed
- [ ] Rotate ngrok URL if exposure already happened

## **Defense in Depth**
- [ ] Add IP allowlist for TradingView webhook servers
- [ ] Add basic auth/token check for webhook requests
- [ ] Log all external hits and review regularly

---

## 🚀 **NEXT STEPS PRIORITY:**

1. **IMMEDIATE**: Get new ngrok tunnel URL and test TradingView webhooks
2. **HIGH PRIORITY**: Fix ngrok 400 Bad Request issue with TradingView
3. **MEDIUM PRIORITY**: Add IP allowlisting for TradingView webhook servers
4. **MEDIUM PRIORITY**: Add authentication for webhook requests
5. **LOW PRIORITY**: Disable ngrok inspector and optimize configuration

---

## 🎯 **CURRENT SECURE ARCHITECTURE:**

```
INTERNET → ngrok → ONLY /webhook/tradingview → webhook-proxy → webhook-ingest
```

**✅ SECURED: All other endpoints return 403 Forbidden**
**✅ SECURED: Ticker management is internal-only**
**✅ SECURED: Main system is completely internal**
**❌ ISSUE: ngrok still blocking TradingView requests with 400 errors** 