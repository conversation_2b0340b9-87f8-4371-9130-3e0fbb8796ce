#!/usr/bin/env python3
"""
Test script to verify the /ask command works in Docker environment.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

async def test_ask_command_docker():
    """Test the /ask command in Docker environment."""
    print("🐳 Testing /ask command in Docker environment...")
    
    try:
        # Import the pipeline function
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        print("✅ Successfully imported execute_ask_pipeline")
        
        test_queries = {
            "Technical Analysis": "What's the current trend for AAPL?",
            "Price Check": "Price of NVDA?",
            "General Question": "What is a moving average?",
            "Invalid Symbol": "Tell me about XYZFAKE"
        }
        
        all_passed = True
        for name, query in test_queries.items():
            print(f"\n🧪 Testing Scenario: {name} ('{query}')")
            # Execute the pipeline
            result = await execute_ask_pipeline(
                query=query,
                user_id="test_user_123",
                guild_id="test_guild_456",
                correlation_id=f"test_{name.replace(' ', '_')}"
            )
            
            print(f"✅ Pipeline executed successfully")
            print(f"📊 Result type: {type(result)}")
            
            # Check if we have a response
            if hasattr(result, 'processing_results'):
                response = result.processing_results.get('response')
                if response:
                    print(f"✅ Got response: {response[:100]}...")
                else:
                    print("⚠️ No response in processing_results")
                    print(f"Available keys: {list(result.processing_results.keys())}")
                    all_passed = False # A valid query should always produce a response
            
        
        if hasattr(result, 'status'):
            print(f"📈 Pipeline status: {result.status}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting /ask command Docker tests...\n")
    
    test1_passed = await test_ask_command_docker()
    
    if test1_passed:
        print("\n🎉 All Docker tests passed!")
        print("\n📋 Summary:")
        print("✅ Pipeline execution working")
        print("\n🔧 Your /ask command is now properly wired to:")
        print("  • AI Chat Engine (intelligent responses)")
        print("  • Tool Registry (market analysis tools)")
        print("  • Tool Executor (real data and analysis)")
        print("  • Pipeline System (robust execution)")
        print("\n🚀 Ready to test with real Discord bot!")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
