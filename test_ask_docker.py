#!/usr/bin/env python3
"""
Test script to verify the /ask command works in Docker environment.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

async def test_ask_command_docker():
    """Test the /ask command in Docker environment."""
    print("🐳 Testing /ask command in Docker environment...")
    
    try:
        # Import the pipeline function
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        print("✅ Successfully imported execute_ask_pipeline")
        
        # Test with a simple query
        test_query = "What's the current trend for AAPL?"
        print(f"🧪 Testing query: {test_query}")
        
        # Execute the pipeline
        result = await execute_ask_pipeline(
            query=test_query,
            user_id="test_user_123",
            guild_id="test_guild_456",
            correlation_id="test_correlation_123"
        )
        
        print(f"✅ Pipeline executed successfully")
        print(f"📊 Result type: {type(result)}")
        
        # Check if we have a response
        if hasattr(result, 'processing_results'):
            response = result.processing_results.get('response')
            if response:
                print(f"✅ Got response: {response[:100]}...")
            else:
                print("⚠️ No response in processing_results")
                print(f"Available keys: {list(result.processing_results.keys())}")
        
        if hasattr(result, 'status'):
            print(f"📈 Pipeline status: {result.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ask_command_integration():
    """Test the Discord command integration."""
    print("\n🤖 Testing Discord command integration...")
    
    try:
        # Import the command class
        from src.bot.commands.ask import SmartAskCommand
        print("✅ Successfully imported SmartAskCommand")
        
        # Create a mock bot
        class MockBot:
            pass
        
        # Create the command instance
        ask_command = SmartAskCommand(MockBot())
        print("✅ Successfully created SmartAskCommand instance")
        
        # Test the response extraction method
        class MockContext:
            def __init__(self):
                self.processing_results = {
                    'response': 'AAPL is showing bullish momentum with RSI at 65.4 and strong volume confirmation.',
                    'tools_used': ['real_time_price', 'technical_indicators'],
                    'intent': 'technical_analysis'
                }
                self.status = type('Status', (), {'value': 'completed'})()
        
        mock_context = MockContext()
        response = ask_command._extract_response_from_context(mock_context)
        
        print(f"✅ Response extraction works: {response[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting /ask command Docker tests...\n")
    
    test1_passed = await test_ask_command_docker()
    test2_passed = await test_ask_command_integration()
    
    if test1_passed and test2_passed:
        print("\n🎉 All Docker tests passed!")
        print("\n📋 Summary:")
        print("✅ Pipeline execution working")
        print("✅ Discord command integration working")
        print("✅ Response extraction working")
        print("\n🔧 Your /ask command is now properly wired to:")
        print("  • AI Chat Engine (intelligent responses)")
        print("  • Tool Registry (market analysis tools)")
        print("  • Tool Executor (real data and analysis)")
        print("  • Pipeline System (robust execution)")
        print("\n🚀 Ready to test with real Discord bot!")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
