#!/usr/bin/env python3
"""
Debug Specific Queries from Bot Logs
Test the exact queries that were failing in the Discord bot
"""

import re
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class MarketIntentDetector:
    """Test the market intent detection logic from ask.py."""
    
    def __init__(self):
        """Initialize the detector with the same logic as ask.py."""
        # Market detection patterns (copied from ask.py)
        self.ticker_pattern = re.compile(r'\b[A-Z]{1,5}\b')
        self.crypto_pattern = re.compile(r'\b(BTC|ETH|ADA|DOT|LINK|UNI|SOL|MATIC|AVAX|ATOM)\b', re.IGNORECASE)
        
        # Market-related keywords that trigger analysis mode
        self.market_keywords = {
            'trading': ['chart', 'price', 'bullish', 'bearish', 'stock', 'market', 'entry', 'exit', 'buy', 'sell', 'calls', 'puts', 'options'],
            'technical': ['trend', 'accumulation', 'distribution', 'liquidity', 'setup', 'candle', 'breakout', 'support', 'resistance', 'rsi', 'macd', 'moving average', 'volume', 'momentum'],
            'analysis': ['analysis', 'assessment', 'review', 'evaluation', 'study', 'examination'],
            'risk': ['risk', 'alert', 'stop loss', 'position size', 'volatility', 'correlation', 'exposure'],
            'market_structure': ['liquidity', 'order flow', 'market maker', 'institutional', 'retail', 'sector', 'rotation', 'sector rotation'],
            'general_market': ['trading', 'investing', 'portfolio', 'strategy', 'signal', 'indicator', 'pattern', 'setup', 'opportunity']
        }
        
        # Flatten keywords for easy checking
        self.all_market_keywords = []
        for category, keywords in self.market_keywords.items():
            self.all_market_keywords.extend(keywords)
    
    def detect_market_intent(self, query: str):
        """
        Detect if query is market-related and extract relevant information.
        
        Returns:
            Tuple of (is_market_query, ticker, context)
        """
        query_lower = query.lower()
        context = {}
        
        # Check for ticker symbols first (highest priority)
        ticker = None
        
        # Check for stock tickers (including with $ prefix)
        # First check for $TICKER format (case insensitive)
        dollar_ticker_match = re.search(r'\$([A-Za-z]{1,5})\b', query)
        if dollar_ticker_match:
            ticker = dollar_ticker_match.group(1).upper()
            context['ticker_type'] = 'stock'
            context['ticker'] = ticker
        else:
            # Check for standalone tickers (case insensitive)
            stock_match = self.ticker_pattern.search(query.upper())
            if stock_match:
                ticker = stock_match.group()
                # Filter out common non-ticker words and technical indicators
                common_words = [
                    'RSI', 'MACD', 'ETF', 'API', 'URL', 'HTTP', 'I', 'A', 'THE', 'AND', 'OR', 'FOR', 'WITH', 'IN', 'ON', 'AT', 'TO', 'OF',
                    'FIND', 'WHAT', 'WHATS', 'DO', 'YOU', 'CURRENTLY', 'HAVE', 'ACCESS', 'ANY', 'DATA', 'UP', 'WITH', 'HAPPENING', 'THE', 'MAREKT'
                ]
                if ticker not in common_words:
                    context['ticker_type'] = 'stock'
                    context['ticker'] = ticker
        
        # Check for crypto
        crypto_match = self.crypto_pattern.search(query)
        if crypto_match:
            ticker = crypto_match.group().upper()
            context['ticker_type'] = 'crypto'
            context['ticker'] = ticker
        
        # Check for market keywords
        market_keyword_count = 0
        detected_keywords = []
        
        for keyword in self.all_market_keywords:
            if keyword in query_lower:
                market_keyword_count += 1
                detected_keywords.append(keyword)
        
        context['detected_keywords'] = detected_keywords
        context['market_keyword_count'] = market_keyword_count
        
        # Decision logic:
        # 1. If ticker found → definitely market query
        # 2. If 2+ market keywords → likely market query
        # 3. If 1 market keyword + context → market query
        # 4. Otherwise → casual chat
        
        is_market_query = False
        
        if ticker:
            is_market_query = True
        elif market_keyword_count >= 2:
            is_market_query = True
        elif market_keyword_count == 1:
            # More lenient: any single market keyword triggers market mode
            is_market_query = True
        
        return is_market_query, ticker, context

def debug_query(query: str):
    """Debug a specific query."""
    detector = MarketIntentDetector()
    
    print(f"\n🔍 DEBUGGING QUERY: '{query}'")
    print("=" * 80)
    
    # Run detection
    is_market, ticker, context = detector.detect_market_intent(query)
    
    print(f"📊 RESULTS:")
    print(f"  Market Query: {'✅ YES' if is_market else '❌ NO'}")
    print(f"  Detected Ticker: {ticker or 'None'}")
    print(f"  Ticker Type: {context.get('ticker_type', 'None')}")
    print(f"  Keywords Found: {context.get('detected_keywords', [])}")
    print(f"  Keyword Count: {context.get('market_keyword_count', 0)}")
    
    # Analyze the decision
    print(f"\n🧠 DECISION ANALYSIS:")
    
    if ticker:
        print(f"  ✅ Ticker found: {ticker}")
        if ticker in ['RSI', 'MACD', 'ETF']:
            print(f"  ⚠️  WARNING: {ticker} is a technical indicator, not a stock ticker!")
            print(f"  🔧 SUGGESTION: Add {ticker} to the exclusion list")
    else:
        print(f"  ❌ No ticker found")
    
    keyword_count = context.get('market_keyword_count', 0)
    if keyword_count >= 2:
        print(f"  ✅ Multiple keywords ({keyword_count}) - triggers market mode")
    elif keyword_count == 1:
        print(f"  ⚠️  Single keyword ({keyword_count})")
        # Check context
        context_words = ['price', 'chart', 'analysis', 'trend']
        context_found = any(word in query.lower() for word in context_words)
        if context_found:
            print(f"  ✅ Context words found: {[w for w in context_words if w in query.lower()]}")
            print(f"  ✅ Triggers market mode due to context")
        else:
            print(f"  ❌ No context words found")
            print(f"  ❌ Does NOT trigger market mode")
    else:
        print(f"  ❌ No market keywords found")
    
    # Show expected vs actual
    expected_market = should_be_market_query(query)
    print(f"\n📊 EXPECTED vs ACTUAL:")
    print(f"  Expected: {'Market' if expected_market else 'Casual'}")
    print(f"  Actual: {'Market' if is_market else 'Casual'}")
    print(f"  Result: {'✅ PASS' if is_market == expected_market else '❌ FAIL'}")
    
    return is_market, ticker, context

def should_be_market_query(query: str) -> bool:
    """Determine if a query should be classified as market-related."""
    query_lower = query.lower()
    
    # Check for tickers (but exclude technical indicators)
    tickers = ['AAPL', 'TSLA', 'BTC', 'ETH', 'SPY', 'NVDA', 'QQQ', 'IWM', 'AMD', 'INTC']
    if any(ticker in query.upper() for ticker in tickers):
        return True
    
    # Check for market keywords
    market_indicators = [
        'price', 'chart', 'rsi', 'macd', 'support', 'resistance', 'trend',
        'volume', 'liquidity', 'order flow', 'market maker', 'institutional',
        'retail', 'buy', 'sell', 'calls', 'options', 'entry', 'exit',
        'breakout', 'accumulation', 'distribution', 'sector rotation'
    ]
    
    if any(indicator in query_lower for indicator in market_indicators):
        return True
    
    return False

def main():
    """Debug specific queries from the bot logs."""
    print("🚀 Debugging Specific Queries from Bot Logs")
    print("=" * 80)
    
    # Queries that were failing in the bot logs
    failing_queries = [
        "whats up with $aapl",
        "aapl",
        "find me a stock to buy calls on this week",
        "whats happening in the marekt",
        "do you currently have access to any market data?"
    ]
    
    for query in failing_queries:
        debug_query(query)
        print("\n" + "-" * 80)
    
    print("\n🔧 ISSUES IDENTIFIED:")
    print("1. Ticker detection is too aggressive - catching technical indicators")
    print("2. Single keywords without context aren't triggering market mode")
    print("3. Some market-related queries are being missed")
    
    print("\n💡 RECOMMENDATIONS:")
    print("1. Fix ticker exclusion list (RSI, MACD, ETF)")
    print("2. Lower keyword threshold from 2 to 1 for market queries")
    print("3. Improve context detection for single keywords")
    print("4. Add more market-related keywords")

if __name__ == "__main__":
    main() 