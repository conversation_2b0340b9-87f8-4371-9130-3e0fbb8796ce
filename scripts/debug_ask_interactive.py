#!/usr/bin/env python3
"""
Interactive Debugger for Ask Command
Test individual queries and see the complete pipeline flow
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from bot.commands.ask import DualPurposeAskCommand

class InteractiveAskDebugger:
    """Interactive debugger for testing ask command queries."""
    
    def __init__(self):
        """Initialize the interactive debugger."""
        self.ask_command = DualPurposeAskCommand(None)  # Mock bot for testing
        self.test_history = []
        
    def debug_query(self, query: str):
        """Debug a single query through the complete pipeline."""
        print(f"\n🔍 DEBUGGING QUERY: '{query}'")
        print("=" * 80)
        
        # Step 1: Query Analysis
        print("\n📝 STEP 1: Query Analysis")
        print("-" * 40)
        analysis = self._analyze_query(query)
        for key, value in analysis.items():
            if isinstance(value, list):
                print(f"  {key}: {', '.join(str(v) for v in value[:10])}")
            else:
                print(f"  {key}: {value}")
        
        # Step 2: Market Intent Detection
        print("\n🎯 STEP 2: Market Intent Detection")
        print("-" * 40)
        is_market, ticker, context = self.ask_command._detect_market_intent(query)
        
        print(f"  Final Decision: {'✅ MARKET QUERY' if is_market else '❌ CASUAL QUERY'}")
        print(f"  Detected Ticker: {ticker or 'None'}")
        print(f"  Ticker Type: {context.get('ticker_type', 'None')}")
        print(f"  Keywords Found: {len(context.get('detected_keywords', []))}")
        print(f"  Keyword Count: {context.get('market_keyword_count', 0)}")
        
        if context.get('detected_keywords'):
            print(f"  Detected Keywords: {', '.join(context['detected_keywords'])}")
        
        # Step 3: Decision Factors
        print("\n🧠 STEP 3: Decision Factors")
        print("-" * 40)
        
        decision_factors = []
        if ticker:
            decision_factors.append(f"✅ Ticker found: {ticker}")
        else:
            decision_factors.append("❌ No ticker found")
        
        keyword_count = context.get('market_keyword_count', 0)
        if keyword_count >= 2:
            decision_factors.append(f"✅ Multiple keywords ({keyword_count})")
        elif keyword_count == 1:
            decision_factors.append(f"⚠️  Single keyword ({keyword_count})")
            # Check context
            context_words = ['price', 'chart', 'analysis', 'trend']
            context_found = any(word in query.lower() for word in context_words)
            if context_found:
                decision_factors.append(f"✅ Context words found: {[w for w in context_words if w in query.lower()]}")
            else:
                decision_factors.append("❌ No context words found")
        else:
            decision_factors.append("❌ No market keywords found")
        
        for factor in decision_factors:
            print(f"  {factor}")
        
        # Step 4: Expected vs Actual
        print("\n📊 STEP 4: Expected vs Actual")
        print("-" * 40)
        
        expected_market = self._should_be_market_query(query)
        print(f"  Expected Result: {'Market' if expected_market else 'Casual'}")
        print(f"  Actual Result: {'Market' if is_market else 'Casual'}")
        print(f"  Test Result: {'✅ PASS' if is_market == expected_market else '❌ FAIL'}")
        
        # Step 5: Recommendations
        print("\n💡 STEP 5: Recommendations")
        print("-" * 40)
        
        if is_market != expected_market:
            if expected_market and not is_market:
                print("  ❌ False Negative: Should be market query but wasn't detected")
                print("  🔧 Suggestions:")
                if not ticker:
                    print("    - Check ticker detection regex patterns")
                if keyword_count == 0:
                    print("    - Review keyword list and matching logic")
                elif keyword_count == 1:
                    print("    - Consider lowering keyword threshold or improving context detection")
            else:
                print("  ❌ False Positive: Should be casual query but was detected as market")
                print("  🔧 Suggestions:")
                print("    - Review keyword sensitivity")
                print("    - Add more context validation")
                print("    - Consider implementing intent confidence scoring")
        else:
            print("  ✅ Query correctly classified - no changes needed")
        
        # Store in history
        self.test_history.append({
            'query': query,
            'expected': expected_market,
            'actual': is_market,
            'ticker': ticker,
            'keywords': context.get('detected_keywords', []),
            'passed': is_market == expected_market
        })
        
        print("\n" + "=" * 80)
    
    def _analyze_query(self, query: str) -> dict:
        """Analyze query structure."""
        return {
            'length': len(query),
            'word_count': len(query.split()),
            'contains_dollar': '$' in query,
            'contains_hashtag': '#' in query,
            'words': query.split(),
            'lowercase': query.lower()
        }
    
    def _should_be_market_query(self, query: str) -> bool:
        """Determine expected classification."""
        query_lower = query.lower()
        
        # Check for tickers
        tickers = ['AAPL', 'TSLA', 'BTC', 'ETH', 'SPY', 'NVDA', 'QQQ', 'IWM', 'AMD', 'INTC']
        if any(ticker in query.upper() for ticker in tickers):
            return True
        
        # Check for market keywords
        market_words = [
            'price', 'chart', 'rsi', 'macd', 'support', 'resistance', 'trend',
            'volume', 'liquidity', 'order flow', 'market maker', 'institutional',
            'retail', 'buy', 'sell', 'calls', 'options', 'entry', 'exit',
            'breakout', 'accumulation', 'distribution', 'sector rotation'
        ]
        
        if any(word in query_lower for word in market_words):
            return True
        
        return False
    
    def show_history(self):
        """Show test history."""
        if not self.test_history:
            print("\n📚 No tests run yet.")
            return
        
        print(f"\n📚 TEST HISTORY ({len(self.test_history)} queries)")
        print("=" * 80)
        
        passed = sum(1 for test in self.test_history if test['passed'])
        failed = len(self.test_history) - passed
        
        print(f"Total: {len(self.test_history)} | Passed: {passed} | Failed: {failed}")
        print(f"Success Rate: {(passed/len(self.test_history))*100:.1f}%")
        
        print("\nDetailed Results:")
        for i, test in enumerate(self.test_history, 1):
            status = "✅" if test['passed'] else "❌"
            print(f"\n{i}. {status} '{test['query']}'")
            print(f"   Expected: {'Market' if test['expected'] else 'Casual'}")
            print(f"   Got: {'Market' if test['actual'] else 'Casual'}")
            print(f"   Ticker: {test['ticker'] or 'None'}")
            print(f"   Keywords: {', '.join(test['keywords']) if test['keywords'] else 'None'}")
    
    def run_interactive(self):
        """Run interactive debugger."""
        print("🚀 Interactive Ask Command Debugger")
        print("=" * 80)
        print("Commands:")
        print("  /test <query>  - Test a specific query")
        print("  /history       - Show test history")
        print("  /examples      - Show example queries")
        print("  /quit          - Exit debugger")
        print("=" * 80)
        
        while True:
            try:
                command = input("\n🔍 Debug> ").strip()
                
                if command.lower() == '/quit':
                    print("👋 Goodbye!")
                    break
                elif command.lower() == '/history':
                    self.show_history()
                elif command.lower() == '/examples':
                    self.show_examples()
                elif command.startswith('/test '):
                    query = command[6:].strip()
                    if query:
                        self.debug_query(query)
                    else:
                        print("❌ Please provide a query to test")
                elif command:
                    # Treat as a query to test
                    self.debug_query(command)
                else:
                    print("❌ Please enter a command or query")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_examples(self):
        """Show example queries to test."""
        print("\n📝 EXAMPLE QUERIES TO TEST:")
        print("=" * 80)
        
        examples = [
            ("Market Queries", [
                "What's the price of AAPL?",
                "Show me the chart for TSLA",
                "How is BTC doing today?",
                "What's happening with $SPY?",
                "Give me analysis on NVDA"
            ]),
            ("Technical Analysis", [
                "What's the RSI for AAPL?",
                "Show me support and resistance for TSLA",
                "Is BTC in an uptrend?",
                "What's the MACD showing for SPY?"
            ]),
            ("Casual Queries", [
                "Hello, how are you?",
                "What's the weather like?",
                "Tell me a joke",
                "How do you make coffee?"
            ]),
            ("Edge Cases", [
                "AAPL RSI MACD volume trend",
                "TSLA support resistance breakout",
                "BTC ETH ADA market analysis"
            ])
        ]
        
        for category, queries in examples:
            print(f"\n📂 {category}:")
            for query in queries:
                print(f"  • {query}")

def main():
    """Main function."""
    debugger = InteractiveAskDebugger()
    
    if len(sys.argv) > 1:
        # Test specific query from command line
        query = " ".join(sys.argv[1:])
        debugger.debug_query(query)
    else:
        # Run interactive mode
        debugger.run_interactive()

if __name__ == "__main__":
    main() 