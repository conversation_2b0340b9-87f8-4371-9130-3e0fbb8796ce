#!/usr/bin/env python3
"""
Test Script for Ask Command Pipeline
Runs multiple test queries to identify issues in market intent detection
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from bot.commands.ask import DualPurposeAskCommand

class AskPipelineTester:
    """Test the ask command pipeline with various queries."""
    
    def __init__(self):
        """Initialize the tester."""
        self.ask_command = DualPurposeAskCommand(None)  # Mock bot for testing
        
        # Test queries organized by category
        self.test_queries = {
            "Basic Market Queries": [
                "What's the price of AAPL?",
                "Show me the chart for TSLA",
                "How is BTC doing today?",
                "What's happening with $SPY?",
                "Give me analysis on NVDA"
            ],
            "Technical Analysis": [
                "What's the RSI for AAPL?",
                "Show me support and resistance for TSLA",
                "Is BTC in an uptrend?",
                "What's the MACD showing for SPY?",
                "Volume analysis for NVDA"
            ],
            "Trading Decisions": [
                "Should I buy calls on AAPL this week?",
                "What's a good entry point for TSLA?",
                "Is BTC a good buy right now?",
                "Risk assessment for SPY options",
                "Position sizing for NVDA"
            ],
            "Market Structure": [
                "Liquidity analysis for AAPL",
                "Order flow for TSLA",
                "Market maker activity in BTC",
                "Institutional buying in SPY",
                "Retail sentiment for NVDA"
            ],
            "Casual Queries": [
                "Hello, how are you?",
                "What's the weather like?",
                "Tell me a joke",
                "What's your favorite color?",
                "How do you make coffee?"
            ],
            "Edge Cases": [
                "AAPL RSI MACD volume trend",
                "TSLA support resistance breakout",
                "BTC ETH ADA market analysis",
                "SPY QQQ IWM sector rotation",
                "NVDA AMD INTC semiconductor"
            ]
        }
    
    def test_market_intent_detection(self):
        """Test the market intent detection logic."""
        print("🔍 Testing Market Intent Detection Logic")
        print("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        failed_tests = []
        
        for category, queries in self.test_queries.items():
            print(f"\n📂 Category: {category}")
            print("-" * 40)
            
            for query in queries:
                total_tests += 1
                
                # Run market intent detection
                is_market, ticker, context = self.ask_command._detect_market_intent(query)
                
                # Determine expected result
                expected_market = self._should_be_market_query(query)
                
                # Check if result matches expectation
                test_passed = (is_market == expected_market)
                if test_passed:
                    passed_tests += 1
                    status = "✅ PASS"
                else:
                    status = "❌ FAIL"
                    failed_tests.append({
                        'query': query,
                        'expected': expected_market,
                        'got': is_market,
                        'ticker': ticker,
                        'context': context
                    })
                
                # Print result
                print(f"{status} | {query}")
                print(f"     Expected: {'Market' if expected_market else 'Casual'}")
                print(f"     Got: {'Market' if is_market else 'Casual'}")
                print(f"     Ticker: {ticker or 'None'}")
                print(f"     Keywords: {context.get('detected_keywords', [])}")
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests:
            print(f"\n❌ FAILED TESTS:")
            for i, failure in enumerate(failed_tests, 1):
                print(f"\n{i}. Query: {failure['query']}")
                print(f"   Expected: {'Market' if failure['expected'] else 'Casual'}")
                print(f"   Got: {'Market' if failure['got'] else 'Casual'}")
                print(f"   Ticker: {failure['ticker']}")
                print(f"   Keywords: {failure['context'].get('detected_keywords', [])}")
        
        return passed_tests, total_tests, failed_tests
    
    def _should_be_market_query(self, query: str) -> bool:
        """Determine if a query should be classified as market-related."""
        query_lower = query.lower()
        
        # Check for tickers
        if any(ticker in query.upper() for ticker in ['AAPL', 'TSLA', 'BTC', 'SPY', 'NVDA', 'ETH', 'ADA', 'QQQ', 'IWM', 'AMD', 'INTC']):
            return True
        
        # Check for market keywords
        market_indicators = [
            'price', 'chart', 'rsi', 'macd', 'support', 'resistance', 'trend',
            'volume', 'liquidity', 'order flow', 'market maker', 'institutional',
            'retail', 'buy', 'sell', 'calls', 'options', 'entry', 'exit',
            'breakout', 'accumulation', 'distribution', 'sector rotation'
        ]
        
        if any(indicator in query_lower for indicator in market_indicators):
            return True
        
        # Check for trading context
        trading_context = ['analysis', 'assessment', 'position', 'risk']
        if any(context in query_lower for context in trading_context):
            return True
        
        return False
    
    def analyze_patterns(self):
        """Analyze patterns in the detection logic."""
        print("\n🔍 PATTERN ANALYSIS")
        print("=" * 60)
        
        # Test ticker detection patterns
        print("\n📈 Ticker Detection Patterns:")
        ticker_tests = [
            "AAPL", "TSLA", "BTC", "ETH", "SPY", "QQQ", "RSI", "MACD", "ETF"
        ]
        
        for ticker in ticker_tests:
            is_market, detected_ticker, context = self.ask_command._detect_market_intent(ticker)
            print(f"  {ticker}: Market={is_market}, Detected={detected_ticker}")
        
        # Test keyword detection
        print("\n🔑 Keyword Detection Patterns:")
        keyword_tests = [
            "price analysis",
            "technical indicators",
            "market structure",
            "risk assessment",
            "hello world"
        ]
        
        for test in keyword_tests:
            is_market, ticker, context = self.ask_command._detect_market_intent(test)
            keywords = context.get('detected_keywords', [])
            print(f"  '{test}': Market={is_market}, Keywords={keywords}")
    
    def generate_recommendations(self, failed_tests):
        """Generate recommendations based on failed tests."""
        print("\n💡 RECOMMENDATIONS")
        print("=" * 60)
        
        if not failed_tests:
            print("✅ All tests passed! No recommendations needed.")
            return
        
        # Analyze failure patterns
        false_negatives = [f for f in failed_tests if f['expected'] and not f['got']]
        false_positives = [f for f in failed_tests if not f['expected'] and f['got']]
        
        if false_negatives:
            print(f"\n❌ False Negatives ({len(false_negatives)}): Market queries not detected")
            print("   These queries should trigger market mode but don't:")
            for failure in false_negatives[:5]:  # Show first 5
                print(f"   - '{failure['query']}'")
                print(f"     Missing: Check ticker detection or keyword logic")
        
        if false_positives:
            print(f"\n❌ False Positives ({len(false_positives)}): Casual queries detected as market")
            print("   These queries shouldn't trigger market mode but do:")
            for failure in false_positives[:5]:  # Show first 5
                print(f"   - '{failure['query']}'")
                print(f"     Issue: Overly aggressive keyword matching")
        
        # General recommendations
        print(f"\n🔧 General Recommendations:")
        print("   1. Review ticker detection regex patterns")
        print("   2. Adjust keyword sensitivity thresholds")
        print("   3. Implement context-aware detection")
        print("   4. Add more sophisticated NLP for intent classification")
        print("   5. Consider using ML models for better accuracy")

def main():
    """Run the ask pipeline tests."""
    print("🚀 Ask Command Pipeline Tester")
    print("=" * 60)
    
    tester = AskPipelineTester()
    
    # Run tests
    passed, total, failed = tester.test_market_intent_detection()
    
    # Analyze patterns
    tester.analyze_patterns()
    
    # Generate recommendations
    tester.generate_recommendations(failed)
    
    print(f"\n🎯 Test completed! Success rate: {(passed/total)*100:.1f}%")
    
    if failed:
        print(f"🔧 {len(failed)} issues found - review recommendations above")
        return 1
    else:
        print("✅ All tests passed!")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 