import asyncio
import json
import logging
import pandas as pd
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.technical_analysis.zones import supply_demand_detector

logging.basicConfig(level=logging.INFO)

async def main():
    agg = DataProviderAggregator()
    print('Fetching history for AAPL...')
    res = await agg.get_history('AAPL', period='3mo', interval='1d')

    data = res
    # Normalize provider shapes (simple handling similar to tests)
    if isinstance(data, dict):
        if 'data' in data and isinstance(data['data'], (list, tuple)):
            df = pd.DataFrame(data['data'])
        elif 'Time Series (Daily)' in data:
            raw = data['Time Series (Daily)']
            df = pd.DataFrame.from_dict(raw, orient='index').reset_index().rename(columns={'index': 'date'})
        elif 'results' in data:
            df = pd.DataFrame(data['results'])
        else:
            sample_vals = [v for v in data.values() if isinstance(v, (list, tuple))]
            if sample_vals:
                df = pd.DataFrame(data)
            else:
                df = pd.DataFrame(data.get('data', []))
    elif isinstance(data, list):
        df = pd.DataFrame(data)
    else:
        df = pd.DataFrame(data)

    if df.empty:
        print('No historical data returned for AAPL')
        return

    # Date handling
    if 'timestamp' in df.columns:
        df['date'] = pd.to_datetime(df['timestamp'])
    elif 'Date' in df.columns:
        df['date'] = pd.to_datetime(df['Date'])
    elif 'date' in df.columns:
        df['date'] = pd.to_datetime(df['date'])
    if 'date' in df.columns:
        df.set_index('date', inplace=True)

    # Column mapping
    column_mapping = {
        'open': ['open', 'Open', '1. open', 'o'],
        'high': ['high', 'High', '2. high', 'h'],
        'low': ['low', 'Low', '3. low', 'l'],
        'close': ['close', 'Close', '4. close', 'c', 'adjclose', 'Adj Close'],
        'volume': ['volume', 'Volume', '5. volume', 'v', 'adjvolume']
    }
    for std_name, alt_names in column_mapping.items():
        for alt in alt_names:
            if alt in df.columns:
                df[std_name] = pd.to_numeric(df[alt], errors='coerce')
                break

    df = df.apply(pd.to_numeric, errors='coerce').dropna()
    df = df[~df.index.duplicated(keep='first')]
    if not df.index.is_monotonic_increasing:
        df = df.sort_index()

    if len(df) < 20:
        print(f'Insufficient data points after normalization: {len(df)}')
        return

    zones = supply_demand_detector.detect_zones(df, symbol='AAPL')
    print(json.dumps(zones, default=str, indent=2))

asyncio.run(main())
