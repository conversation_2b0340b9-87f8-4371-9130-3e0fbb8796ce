#!/usr/bin/env python3
"""
Test the new Smart Ask Command
Tests the AI-powered approach that works like a real AI assistant
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from bot.commands.ask import SmartAskCommand

async def test_smart_ask():
    """Test the smart ask command."""
    print("🚀 Testing Smart Ask Command")
    print("=" * 80)
    
    # Create mock bot and command
    mock_bot = None
    ask_command = SmartAskCommand(mock_bot)
    
    # Test queries
    test_queries = [
        "whats up with $aapl",
        "aapl",
        "find me a stock to buy calls on this week",
        "whats happening in the market",
        "do you currently have access to any market data?",
        "hello, how are you?",
        "what's the weather like?",
        "tell me a joke",
        "how do you make coffee?",
        "what's the RSI for TSLA?",
        "show me support and resistance for BTC",
        "is SPY in an uptrend?"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 60)
        
        # Analyze context
        context = ask_command._analyze_query_context(query)
        
        print(f"Context Analysis:")
        print(f"  Has Ticker: {context['has_ticker']}")
        print(f"  Tickers: {context['tickers']}")
        print(f"  Market Related: {context['is_market_related']}")
        print(f"  Market Keywords: {context['market_keywords']}")
        print(f"  Query Type: {context['query_type']}")
        
        # Generate response
        try:
            response = await ask_command._generate_ai_response(query, None)
            print(f"\nResponse Preview: {response[:100]}...")
        except Exception as e:
            print(f"Error generating response: {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_smart_ask()) 