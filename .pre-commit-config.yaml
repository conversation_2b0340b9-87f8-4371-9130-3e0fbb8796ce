repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: debug-statements

-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
        language_version: python3.11

-   repo: https://github.com/PyCQA/flake8
    rev: 6.0.0
    hooks:
    -   id: flake8
        additional_dependencies: [
            'flake8-bugbear',
            'flake8-comprehensions',
            'flake8-docstrings',
        ]

-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
    -   id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports]

-   repo: https://github.com/asottile/pyupgrade
    rev: v3.10.1
    hooks:
    -   id: pyupgrade
        args: [--py311-plus]
