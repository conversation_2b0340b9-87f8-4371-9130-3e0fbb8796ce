version: '3.8'

services:
  ask-debugger:
    build:
      context: .
      dockerfile: Dockerfile.debug
    volumes:
      - ./src:/app/src:ro
      - ./scripts:/app/scripts:ro
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app/src
      - LOG_LEVEL=DEBUG
    command: python /app/scripts/test_ask_pipeline.py
    depends_on:
      - tradingview-db
      - tradingview-redis

  ask-debugger-interactive:
    build:
      context: .
      dockerfile: Dockerfile.debug
    volumes:
      - ./src:/app/src:ro
      - ./scripts:/app/scripts:ro
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app/src
      - LOG_LEVEL=DEBUG
    stdin_open: true
    tty: true
    depends_on:
      - tradingview-db
      - tradingview-redis
    command: /bin/bash

networks:
  default:
    name: tradingview-network
    external: true 