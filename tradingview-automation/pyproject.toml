[tool.poetry]
name = "tradingview-automation"
version = "0.1.0"
description = "AI-powered Discord bot for market analysis"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
cryptography = ">=41.0.0,<43.0.0"  # Updated for compatibility
python-multipart = "^0.0.6"
supabase = "^2.0.0"
structlog = "^23.2.0"
prometheus-client = "^0.19.0"
email-validator = "^2.1.0"

[tool.poetry.group.dev.dependencies]
pre-commit = "^3.5.0"
black = "^23.11.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.1"
httpx = "^0.25.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py311']

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true

[tool.flake8]
max-line-length = 100
extend-ignore = "E203,W503" 