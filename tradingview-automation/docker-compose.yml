version: '3.8'

services:
  api:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://:redis_password@redis:6379/0
    volumes:
      - ./src:/app/src
    depends_on:
      - redis

  redis:
    image: redis:alpine
    command: ["redis-server", "--appendonly", "yes", "--requirepass", "redis_password"]
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis_password", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    ports:
      - "6379:6379"