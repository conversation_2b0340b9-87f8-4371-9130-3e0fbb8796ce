# System Architecture

## 1. Overview

This document provides a high-level overview of the system's architecture and how the different components are interconnected. The system is designed to be a trading automation and market analysis platform. It consists of two main parts: a FastAPI application that provides a REST API for interacting with the system, and an AI-powered automation system that provides data-driven trading insights.

## 2. System Components

The system is composed of the following main components:

### 2.1. API

A FastAPI application that provides a REST API for interacting with the system. The API includes the following routers and endpoints:

*   **`analytics`:** Endpoints for retrieving analytics data.
*   **`feedback`:** Endpoints for submitting user feedback.
*   **`health`:** Endpoints for checking the health of the system.
*   **`market_data`:** Endpoints for retrieving market data, getting a market data analysis, and getting a list of supported symbols.
*   **`metrics`:** Endpoints for retrieving performance metrics.

### 2.2. AI Automation System

An AI-powered automation system that provides data-driven trading insights. The AI automation system uses a scheduler to run analysis jobs and sends notifications to Discord. The main components of the AI automation system are:

*   **`ReportScheduler`:** A scheduler that runs analysis jobs at regular intervals.
*   **`DiscordWebhookHandler`:** A handler for sending notifications to Discord.

### 2.3. Services

A collection of services that provide the core functionality of the system. The services include:

*   **`MarketDataService`:** Retrieves market data from the different data providers.
*   **`AlertService`:** Generates and sends alerts based on predefined thresholds.
*   **`TimeframeEngine`:** Manages different timeframes and aggregates 1-minute candle data into higher timeframes.
*   **`ORBDetector`:** Detects opening range breakouts.
*   **`AnalyticsService`:** Processes and analyzes response generation metrics and feedback.
*   **`CommandIntegrationService`:** Integrates with the bot's command system.
*   **`GlobalContextAnalyzer`:** Analyzes the global market context.

## 3. Data Flow

Data flows through the system as follows:

1.  The `MarketStream` service receives market data from TradingView webhooks.
2.  The `MarketStream` service validates and standardizes the data and then passes it to the `TimeframeEngine` for processing.
3.  The `TimeframeEngine` aggregates the 1-minute candle data into higher timeframes and stores it in a database.
4.  The `TimeframeEngine` also checks for heartbeat alerts and opening range breakouts and sends notifications to the `AlertService`.
5.  The `AlertService` sends alerts to the appropriate channels.
6.  The `MarketDataService` retrieves market data from the different data providers and provides it to the rest of the application.
7.  The API provides endpoints for retrieving market data, getting a market data analysis, and getting a list of supported symbols.
8.  The AI automation system uses the market data to generate data-driven trading insights and sends notifications to Discord.

## 4. Authentication and Authorization

Authentication and authorization are handled by the `JWTAuthMiddleware` middleware. The middleware authenticates users using JWTs and checks if they are authorized to access the requested endpoint. The system supports the following roles:

*   **`user`:** A regular user with access to the public API endpoints.
*   **`admin`:** An administrator with access to all API endpoints.

## 5. Configuration

The system is configured using a combination of environment variables and a YAML file. The `TradingBotConfig` class is responsible for loading the configuration from these sources. The configuration options include:

*   **`app`:** Core application settings, such as the application name, version, and environment.
*   **`api`:** API settings, such as the host, port, and CORS origins.
*   **`database`:** Database settings, such as the database URL and pool size.
*   **`redis`:** Redis settings, such as the Redis URL and password.
*   **`security`:** Security settings, such as the JWT secret and algorithm.
*   **`pipeline`:** Pipeline settings, such as the maximum number of concurrent pipelines and the pipeline timeout.
*   **`market_data`:** Market data settings, such as the default data provider and the cache TTL.
*   **`trading`:** Trading settings, such as the paper trading mode and the risk per trade.

## 6. Logging and Monitoring

Logging and monitoring are handled by the `logger.py` and `monitoring.py` modules. The `logger.py` module provides a structured logging system that outputs logs in JSON format. The `monitoring.py` module provides a system for monitoring the application's health and performance. The system includes the following features:

*   **Structured Logging:** The `StructuredFormatter` class outputs logs in JSON format, which makes it easy to parse and analyze the logs.
*   **Correlation Tracking:** The use of `contextvars` to manage the correlation ID is a good way to track requests as they flow through the system.
*   **Specialized Loggers:** The `AIInteractionLogger`, `PipelineLogger`, and `TradingBotLogger` classes are a good way to provide specialized logging for different parts of the application.
*   **Decorators and Context Managers:** The `log_function_call` decorator and `log_operation` context manager are useful tools for adding logging to the application with minimal code.
*   **System Monitor:** The `SystemMonitor` class uses the `psutil` library to collect system metrics like CPU, memory, and disk usage.
*   **Performance Tracker:** The `PerformanceTracker` class provides a decorator for tracking the performance of functions.
*   **Response Metrics Tracker:** The `ResponseMetricsTracker` class tracks metrics related to the AI's responses, such as the number of responses, the response types, and the error rates.