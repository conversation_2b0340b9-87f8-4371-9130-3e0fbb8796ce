services:
  discord-bot:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env.secure
    environment:
      - REDIS_HOST=redis
      - OPENROUTER_ENABLED=true
      - OPENROUTER_API_KEY="test_key"
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  redis_data:
