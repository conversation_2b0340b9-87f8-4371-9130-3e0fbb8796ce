"""
Discord Bot Client
Enhanced trading bot with modular pipeline system
"""

import discord
import asyncio
import logging
import time
import os
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from discord.ext import commands

class RateLimiter:
    """Simple rate limiter for user requests"""

    def __init__(self, max_requests: int = 50, time_window: int = 3600):
        self.max_requests = max_requests
        self.time_window = time_window  # in seconds
        self.user_requests = {}

    def can_make_request(self, user_id: str) -> bool:
        """Check if user can make a request"""
        now = time.time()

        if user_id not in self.user_requests:
            return True

        # Remove old requests outside the time window
        self.user_requests[user_id] = [
            t for t in self.user_requests[user_id]
            if now - t < self.time_window
        ]

        return len(self.user_requests[user_id]) < self.max_requests

    def record_user_query(self, user_id: str):
        """Record a user's query"""
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        self.user_requests[user_id].append(time.time())

    def get_remaining_time(self, user_id: str) -> float:
        """Get remaining time in hours until user can make another request"""
        if user_id not in self.user_requests or not self.user_requests[user_id]:
            return 0.0

        # Get the oldest request
        oldest_request = min(self.user_requests[user_id])
        time_elapsed = time.time() - oldest_request

        if time_elapsed >= self.time_window:
            return 0.0

        return (self.time_window - time_elapsed) / 3600.0  # Convert to hours

# Import pipeline components
from .pipeline.commands.ask.pipeline import execute_ask_pipeline
from .pipeline.commands.ask.stages.response_audit import response_auditor
from .pipeline.commands.ask.stages.ask_sections import AskPipelineSections
from .commands.analyze import setup_analyze_command
from src.core.logger import get_logger, generate_correlation_id, get_trading_logger
from src.core.formatting.response_templates import ResponseGenerator

# Import AI query analysis for quick price detection
from .pipeline.commands.ask.stages.query_analyzer import AIQueryAnalyzer, QueryIntent, ProcessingRoute
from .pipeline.commands.ask.stages.symbol_validator import SymbolValidator

# Configure logging for the bot
logger = get_logger(__name__)

class TradingBot:
    """Enhanced trading bot with modular pipeline system"""
    
    def __init__(self, token: str = None):
        """Initialize the trading bot with necessary intents"""
        # Define minimal required intents
        intents = discord.Intents.default()
        intents.messages = True  # For receiving message events
        intents.message_content = True  # For message content access
        intents.guilds = True  # For server information
        
        # Store token for later use
        self.token = token
        
        self.bot = commands.Bot(
            command_prefix='!',
            intents=intents,
            activity=discord.Activity(
                type=discord.ActivityType.listening,
                name="your trading commands"
            )
        )
        
        self.rate_limiter = RateLimiter(max_requests=50, time_window=3600)  # 50 requests per hour
        self.interaction_logger = get_trading_logger("trading_bot")
        
        # Initialize AI query analyzer for quick price detection
        self.symbol_validator = SymbolValidator()
        self.query_analyzer = AIQueryAnalyzer()
        self.query_analyzer.symbol_validator = self.symbol_validator
        
        # Setup event listeners
        @self.bot.event
        async def on_ready():
            logger.info(f'Logged in as {self.bot.user} (ID: {self.bot.user.id})')
            logger.info('------')
            
        @self.bot.event
        async def on_command_error(ctx, error):
            if isinstance(error, commands.CommandNotFound):
                return
            logger.error(f'Error in {ctx.command}: {error}')
        
        # Setup commands
        self.setup_commands()
        
        logger.info("TradingBot instance created")

        # Diagnostic: log registered slash commands after setup (useful to confirm /analyze was registered)
        try:
            registered_commands = [c.name for c in self.bot.tree.walk_commands()]
            logger.info(f"Registered slash commands during init: {registered_commands}")
        except Exception as e:
            logger.warning(f"Failed to list registered slash commands during init: {e}")
    
    async def run_in_degraded_mode(self):
        """Run the bot in degraded mode without Discord connection"""
        logger.info("🚨 Running in DEGRADED MODE - Discord connection unavailable")
        logger.info("✅ Pipeline services are still available for testing and development")
        logger.info("✅ AI analysis, market data, and technical indicators are functional")
        logger.info("✅ Use the API endpoints for testing instead of Discord commands")
        
        # Keep the process running for development/testing
        try:
            while True:
                await asyncio.sleep(60)  # Sleep for 1 minute
                logger.info("🔄 Degraded mode: Pipeline services are running...")
        except KeyboardInterrupt:
            logger.info("🛑 Degraded mode shutdown requested")
        except Exception as e:
            logger.error(f"❌ Error in degraded mode: {e}")
            raise
    
    def setup_commands(self):
        """Setup all bot commands with enhanced UX"""
        # Setup the analyze command (this registers /analyze)
        setup_analyze_command(self.bot)
        
        # Enhanced slash commands with rich embeds
        @self.bot.tree.command(name="ask", description="Ask the AI about trading and markets")
        async def ask_command(interaction: discord.Interaction, query: str):
            """Ask the AI about trading and markets"""
            await self.handle_ask_command(interaction, query)
        
        @self.bot.tree.command(name="zones", description="Get support and resistance zones for a stock")
        async def zones_command(interaction: discord.Interaction, symbol: str):
            """Get support and resistance zones analysis"""
            await self.handle_zones_command(interaction, symbol)
        
        @self.bot.tree.command(name="recommendations", description="Get AI-powered trading recommendations")
        async def recommendations_command(interaction: discord.Interaction, symbol: str):
            """Get AI-powered trading recommendations"""
            await self.handle_recommendations_command(interaction, symbol)
        
        @self.bot.tree.command(name="watchlist", description="Manage your stock watchlist")
        async def watchlist_command(interaction: discord.Interaction, action: str, symbol: str | None = None, notes: str | None = None):
            """Manage stock watchlist - add, remove, list, or analyze symbols"""
            await self.handle_watchlist_command(interaction, action, symbol, notes)
        
        @self.bot.tree.command(name="ping", description="Check bot latency")
        async def ping_command(interaction: discord.Interaction):
            """Check bot latency"""
            latency = round(self.bot.latency * 1000)
            await interaction.response.send_message(f"🏓 Pong! Latency: {latency}ms")
        
        @self.bot.tree.command(name="status", description="Show bot status and performance metrics")
        async def status_command(interaction: discord.Interaction):
            """Show bot status with performance metrics"""
            await self.handle_enhanced_status_command(interaction)
        
        @self.bot.tree.command(name="help", description="Get help with bot commands")
        async def help_command(interaction: discord.Interaction):
            """Get help with bot commands"""
            await self.handle_enhanced_help_command(interaction)
    
    async def handle_ask_command(self, interaction: discord.Interaction, query: str):
        """Handle the /ask command with comprehensive logging"""
        
        # Defer response
        await interaction.response.defer(thinking=True)
        
        # Sanitize query
        sanitized_query = self._sanitize_query(query.strip())
        
        # Check for simple price query pattern using AI classification
        symbol = await self._is_simple_price_query(sanitized_query)
        if symbol:
            try:
                # Try quick price lookup first
                from .pipeline.commands.ask.stages.quick_commands import QuickCommandHandler
                handler = QuickCommandHandler()
                price_data = await handler.handle_quick_price(symbol)
                
                if price_data.get('status') == 'quick_success':
                    response = (
                        f"📊 **{symbol}**: ${price_data['current_price']:.2f} "
                        f"({price_data['change']:+.2f}, {price_data['change_percent']:+.2f}%)"
                    )
                    await interaction.followup.send(response)
                    return
            except Exception as e:
                logger.warning(f"Quick price lookup failed, falling back to full pipeline: {e}")
        
        # Rate limiting check
        user_id = str(interaction.user.id)
        if not self.rate_limiter.can_make_request(user_id):
            remaining_time = self.rate_limiter.get_remaining_time(user_id)
            await interaction.followup.send(f"⏰ You've reached the rate limit ({self.rate_limiter.max_requests} requests per hour). Please wait {remaining_time:.1f} hour(s) before asking more questions.")
            return
        
        # Record the query for rate limiting
        self.rate_limiter.record_user_query(user_id)
        
        # Generate correlation ID for this request
        correlation_id = generate_correlation_id()
        logger.set_correlation_id(correlation_id)
                                    
        # CRITICAL SECURITY: Timeout protection
        try:
            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=sanitized_query,
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=False  # /ask is freeform
                ),
                timeout=45.0  # 45 second timeout to allow pipeline completion
            )
            
            # Check if pipeline completed successfully
            # Normalize result: pipeline may return a dict (new) or a PipelineContext-like object (legacy)
            if isinstance(result, dict):
                processing_results = {k: v for k, v in result.items() if k not in ['status', 'pipeline_id', 'correlation_id', 'execution_time']}
                status_completed = result.get('status') == 'completed'
                error_log = result.get('error_log', [])
            else:
                processing_results = getattr(result, 'processing_results', {}) or {}
                status_completed = False
                try:
                    status_attr = getattr(result, 'status', None)
                    status_completed = getattr(status_attr, 'value', None) == 'completed'
                except Exception:
                    status_completed = False
                error_log = getattr(result, 'error_log', []) if hasattr(result, 'error_log') else []

            if status_completed and not error_log:
                # Debug logging to see what's available
                logger.info(f"Pipeline completed. Available keys in processing_results: {list(processing_results.keys())}")

                # Check if we have a response (new refactored pipeline format)
                response = processing_results.get("response")
                if response:
                    logger.info(f"Found response, length: {len(response)}")

                    # Log the interaction transformation
                    raw_ai_output = processing_results.get("raw_ai_output", "No raw output available")
                    self.interaction_logger.log_interaction(
                        user_input=sanitized_query,
                        raw_ai_output=raw_ai_output,
                        final_response=response,
                        template_used="none",  # /ask is freeform
                        command_type="ask",
                        user_id=user_id,
                        correlation_id=correlation_id
                    )

                    # Wait for audit to complete before sending
                    logger.info("⏳ Auditing response before sending...")

                    # Audit response before sending with comprehensive context including market data
                    audit_context = {
                        "symbols": processing_results.get("symbols", []),
                        "intent": processing_results.get("intent", "unknown"),
                        "market_data": processing_results.get("data", {})
                    }
                    audit_results = response_auditor.audit_response(response, audit_context)

                    if not audit_results["passed"]:
                        logger.error(f"Response audit failed: {audit_results['issues']}")
                        await interaction.followup.send(f"❌ Response validation failed. Please try again. (correlation_id: {correlation_id})")
                        return

                    # Use validated response
                    response = audit_results["validated_response"]
                    logger.info(f"Response audit passed - Quality Score: {audit_results['quality_score']:.2f}")

                    # Final validation: ensure no intermediate language in response
                    if self._contains_intermediate_language(response):
                        logger.warning("Intermediate language detected in final response - prepending educational disclaimer and proceeding")
                        disclaimer = (
                            "**Educational Content Only**\n\n"
                            "I provide educational analysis and market insights, not personalized financial advice. "
                            "For personalized investment recommendations, please consult with a qualified financial advisor who can consider your specific financial situation, goals, and risk tolerance.\n\n"
                            "**What I can help with:**\n"
                            "• Market analysis and trends\n"
                            "• Technical indicator explanations\n"
                            "• Trading strategy education\n"
                            "• Risk management principles\n"
                            "• Market terminology and concepts"
                        )

                        # Prepend disclaimer but keep the AI response
                        response = f"{disclaimer}\n\n{response}"

                    # Ensure all processing is complete before sending
                    await asyncio.sleep(0.1)  # Small delay to ensure audit completion
                    logger.info("✅ All validation complete - sending final response")

                    # Handle long responses by splitting into multiple messages if needed
                    if len(response) > 2000:
                        # Split the response into chunks of 2000 characters or less
                        chunks = [response[i:i+2000] for i in range(0, len(response), 2000)]
                        logger.info(f"Long response detected. Splitting into {len(chunks)} chunks")

                        # Send the first chunk
                        await interaction.followup.send(chunks[0])

                        # Send remaining chunks
                        for chunk in chunks[1:]:
                            await interaction.channel.send(chunk)
                    else:
                        # Send the response normally
                        await interaction.followup.send(response)
                else:
                    # Try to get formatted_response as fallback (legacy format)
                    formatted_response = processing_results.get("formatted_response")
                    if formatted_response:
                        logger.info(f"Using formatted_response as fallback, length: {len(formatted_response)}")
                        await interaction.followup.send(formatted_response)
                    else:
                        # Try to get ai_response as fallback (legacy format)
                        ai_response = processing_results.get("ai_response")
                        if ai_response:
                            logger.info(f"Using ai_response as fallback, length: {len(ai_response)}")
                            await interaction.followup.send(ai_response)
                        else:
                            # CRITICAL SECURITY: Better error handling without exposing internal details
                            logger.warning(f"No response found. Available keys: {list(processing_results.keys())}")
                            await interaction.followup.send(f"❌ Analysis completed but no response was generated. Please try a different query. (correlation_id: {correlation_id})")
            else:
                # Get error details with proper logging
                error_msg = "Analysis failed"
                if error_log:
                    if isinstance(error_log, list) and error_log:
                        error_msg = f"Analysis failed: {error_log[-1].get('error_message', 'Unknown error')}"
                    else:
                        error_msg = f"Analysis failed: {str(error_log)}"
                    logger.error(f"Pipeline failed for user {interaction.user.id}: {error_msg}")

                await interaction.followup.send(f"❌ {error_msg} (correlation_id: {correlation_id})")
                
        except asyncio.TimeoutError:
            # CRITICAL SECURITY: Handle timeout gracefully
            logger.warning(f"Pipeline timeout for user {interaction.user.id}")
            await interaction.followup.send("⏰ Request timed out after 30 seconds. Please try a shorter or simpler query.")
            
        except Exception as e:
            # CRITICAL SECURITY: Comprehensive error logging with context
            logger.error(f"Pipeline execution failed for user {interaction.user.id}: {e}", exc_info=True)
            await interaction.followup.send("❌ An unexpected error occurred. Please try again later.")
    
    async def handle_watchlist_command(
        self, 
        interaction: discord.Interaction, 
        action: str, 
        symbol: str | None = None, 
        notes: str | None = None
    ) -> None:
        """Handle watchlist management commands"""
        await interaction.response.defer(thinking=True)
        
        try:
            user_id = str(interaction.user.id)
            action = action.lower().strip()
            
            # Import watchlist manager
            from src.core.watchlist.watchlist_manager import WatchlistManager
            watchlist_manager = WatchlistManager(user_id)
            
            if action == "add":
                if not symbol:
                    await interaction.followup.send("❌ Please provide a symbol to add (e.g., `/watchlist add AAPL`)")
                    return
                
                # Validate symbol using real ticker data
                symbol = symbol.upper().strip()
                if not await self._validate_symbol(symbol):
                    await interaction.followup.send(f"❌ '{symbol}' is not a valid stock symbol. Please check the spelling.")
                    return
                
                # Add to watchlist
                success = watchlist_manager.add_symbol(symbol, notes)
                if success:
                    embed = discord.Embed(
                        title="✅ Symbol Added to Watchlist",
                        description=f"**{symbol}** has been added to your watchlist",
                        color=discord.Color.green()
                    )
                    if notes:
                        embed.add_field(name="Notes", value=notes, inline=False)
                    embed.add_field(name="Total Symbols", value=len(watchlist_manager.watchlist), inline=True)
                    embed.add_field(name="Priority", value="🔴 High (New additions get priority)", inline=True)
                    await interaction.followup.send(embed=embed)
                else:
                    await interaction.followup.send(f"❌ **{symbol}** is already in your watchlist")
                
            elif action == "list":
                if not watchlist_manager.watchlist:
                    await interaction.followup.send("📋 Your watchlist is empty. Use `/watchlist add SYMBOL` to add stocks.")
                    return
                
                embed = discord.Embed(
                    title="📋 Your Watchlist",
                    description=f"You have **{len(watchlist_manager.watchlist)}** symbols in your watchlist",
                    color=discord.Color.gold()
                )
                
                # Group symbols by tags or show all
                for entry in watchlist_manager.watchlist:
                    field_name = f"📈 {entry.symbol}"
                    field_value = f"Added: {entry.added_at.strftime('%Y-%m-%d')}"
                    if entry.notes:
                        field_value += f"\nNotes: {entry.notes}"
                    if entry.tags:
                        field_value += f"\nTags: {', '.join(entry.tags)}"
                    
                    embed.add_field(name=field_name, value=field_value, inline=True)
                
                await interaction.followup.send(embed=embed)
                
            else:
                await interaction.followup.send(
                    "❌ Invalid action. Use:\n"
                    "• `/watchlist add SYMBOL [notes]` - Add a symbol\n"
                    "• `/watchlist list` - Show your watchlist"
                )
                
        except Exception as e:
            logger.error(f"Watchlist command error for user {interaction.user.id}: {e}", exc_info=True)
            await interaction.followup.send("❌ An error occurred while managing your watchlist. Please try again.")
    
    async def handle_enhanced_analyze_command(self, interaction: discord.Interaction, symbol: str):
        """Handle enhanced analyze command with rich embeds"""
        await interaction.response.defer(thinking=True)
        
        try:
            # Sanitize symbol
            symbol = symbol.upper().strip()
            
            # Generate correlation ID
            correlation_id = generate_correlation_id()
            logger.set_correlation_id(correlation_id)
            
            # Execute analysis pipeline
            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=f"analyze ${symbol} with comprehensive technical analysis",
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=True  # /analyze is strict
                ),
                timeout=45.0
            )
            
            if hasattr(result, 'processing_results'):
                results = result.processing_results
                
                # Create rich embed with actual technical analysis data
                embed = self._create_enhanced_analysis_embed(symbol, results, interaction.user)
                
                # Add performance metrics
                embed.add_field(
                    name="📊 Performance Metrics",
                    value=f"• Pipeline: ✅ Success\n• Execution: {getattr(result, 'execution_time', 'N/A')}\n• Quality: {results.get('data_quality', {}).get('status', 'N/A')}",
                    inline=False
                )
                
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Analysis failed for {symbol}")
                
        except Exception as e:
            logger.error(f"Enhanced analyze command failed: {e}", exc_info=True)
            await interaction.followup.send(f"❌ Analysis failed: {str(e)}")
    
    async def handle_zones_command(self, interaction: discord.Interaction, symbol: str):
        """Handle zones command for support/resistance analysis"""
        await interaction.response.defer(thinking=True)
        
        try:
            symbol = symbol.upper().strip()
            correlation_id = generate_correlation_id()
            
            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=f"show me the support and resistance zones for ${symbol}",
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=False
                ),
                timeout=45.0
            )
            
            if hasattr(result, 'processing_results'):
                results = result.processing_results
                embed = self._create_enhanced_zones_embed(symbol, results, interaction.user)
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Zones analysis failed for {symbol}")
                
        except Exception as e:
            logger.error(f"Zones command failed: {e}", exc_info=True)
            await interaction.followup.send(f"❌ Zones analysis failed: {str(e)}")
    
    async def handle_recommendations_command(self, interaction: discord.Interaction, symbol: str):
        """Handle recommendations command for AI-powered insights"""
        await interaction.response.defer(thinking=True)
        
        try:
            symbol = symbol.upper().strip()
            correlation_id = generate_correlation_id()
            
            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=f"give me trading recommendations for ${symbol} with risk management",
                    user_id=str(interaction.user.id),
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    strict_mode=False
                ),
                timeout=45.0
            )
            
            if hasattr(result, 'processing_results'):
                results = result.processing_results
                embed = self._create_enhanced_recommendations_embed(symbol, results, interaction.user)
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send(f"❌ Recommendations failed for {symbol}")
                
        except Exception as e:
            logger.error(f"Recommendations command failed: {e}", exc_info=True)
            await interaction.followup.send(f"❌ Recommendations failed: {str(e)}")
    
    def _create_analysis_embed(self, symbol: str, results: dict, user: discord.User) -> discord.Embed:
        """Create rich embed for analysis results"""
        embed = discord.Embed(
            title=f"📊 Technical Analysis: {symbol}",
            description="Comprehensive market analysis powered by AI",
            color=discord.Color.blue(),
            timestamp=discord.utils.utcnow()
        )
        
        # Add technical indicators
        if 'technical_analysis' in results:
            ta = results['technical_analysis']
            indicators = ta.get('indicators', {})
            
            if indicators:
                embed.add_field(
                    name="📈 Key Indicators",
                    value=f"• **Price**: ${indicators.get('current_price', 'N/A'):.2f}\n"
                          f"• **RSI**: {indicators.get('rsi', 'N/A'):.1f}\n"
                          f"• **MACD**: {indicators.get('macd', 'N/A'):.3f}\n"
                          f"• **Volume**: {indicators.get('volume', 'N/A'):,.0f}",
                    inline=True
                )
        
        # Add data quality
        if 'data_quality' in results:
            dq = results['data_quality']
            quality_color = discord.Color.green() if dq.get('quality_score', 0) >= 80 else discord.Color.orange()
            embed.add_field(
                name="🔍 Data Quality",
                value=f"• **Score**: {dq.get('quality_score', 'N/A')}/100\n"
                      f"• **Completeness**: {dq.get('completeness', 'N/A'):.1f}%\n"
                      f"• **Status**: {dq.get('status', 'N/A').title()}",
                inline=True
            )
        
        # Add user attribution
        embed.set_footer(text=f"Requested by {user.display_name}", icon_url=user.display_avatar.url)
        
        return embed
    
    def _create_zones_embed(self, symbol: str, results: dict, user: discord.User) -> discord.Embed:
        """Create rich embed for zones analysis"""
        embed = discord.Embed(
            title=f"🎯 Support & Resistance Zones: {symbol}",
            description="AI-detected supply and demand zones",
            color=discord.Color.gold(),
            timestamp=discord.utils.utcnow()
        )
        
        # Add zones information if available
        if 'technical_analysis' in results:
            ta = results['technical_analysis']
            zones = ta.get('zones', [])
            
            if zones:
                support_zones = [z for z in zones if z.get('type') == 'support']
                resistance_zones = [z for z in zones if z.get('type') == 'resistance']
                
                embed.add_field(
                    name="🟢 Support Zones",
                    value=f"Found **{len(support_zones)}** support levels" if support_zones else "No support zones detected",
                    inline=True
                )
                
                embed.add_field(
                    name="🔴 Resistance Zones", 
                    value=f"Found **{len(resistance_zones)}** resistance levels" if resistance_zones else "No resistance zones detected",
                    inline=True
                )
            else:
                embed.add_field(
                    name="⚠️ No Zones Detected",
                    value="Insufficient data for zone analysis",
                    inline=False
                )
        
        embed.set_footer(text=f"Requested by {user.display_name}", icon_url=user.display_avatar.url)
        return embed
    
    def _create_recommendations_embed(self, symbol: str, results: dict, user: discord.User) -> discord.Embed:
        """Create rich embed for trading recommendations"""
        embed = discord.Embed(
            title=f"🤖 AI Recommendations: {symbol}",
            description="AI-powered trading insights and risk management",
            color=discord.Color.purple(),
            timestamp=discord.utils.utcnow()
        )
        
        # Add recommendations if available
        if 'trading_signals' in results:
            signals = results['trading_signals']
            if signals:
                embed.add_field(
                    name="📊 Trading Signals",
                    value=f"Generated **{len(signals)}** signals",
                    inline=True
                )
            else:
                embed.add_field(
                    name="📊 Trading Signals",
                    value="No clear signals at this time",
                    inline=True
                )
        
        # Add risk management
        embed.add_field(
            name="⚠️ Risk Disclaimer",
            value="This is educational content only. Always do your own research and consider consulting a financial advisor.",
            inline=False
        )
        
        embed.set_footer(text=f"Requested by {user.display_name}", icon_url=user.display_avatar.url)
        return embed
    
    async def handle_enhanced_status_command(self, interaction: discord.Interaction):
        """Handle enhanced status command with performance metrics"""
        embed = discord.Embed(
            title="🤖 NHX.ai Bot Status",
            description="Professional trading automation bot",
            color=discord.Color.green(),
            timestamp=discord.utils.utcnow()
        )
        
        # System status
        embed.add_field(
            name="🟢 System Status",
            value="All systems operational",
            inline=True
        )
        
        # Performance metrics
        embed.add_field(
            name="⚡ Performance",
            value=f"Latency: {round(self.bot.latency * 1000)}ms\nConnected to {len(self.bot.guilds)} servers",
            inline=True
        )
        
        # Data quality status
        embed.add_field(
            name="📊 Data Quality",
            value="✅ 100% data validation\n✅ Market calendar integrated\n✅ Free-tier optimized",
            inline=False
        )
        
        embed.set_footer(text="Production-ready infrastructure", icon_url=self.bot.user.display_avatar.url)
        await interaction.response.send_message(embed=embed)
    
    async def handle_enhanced_help_command(self, interaction: discord.Interaction):
        """Handle enhanced help command with professional presentation"""
        embed = discord.Embed(
            title="🤖 NHX.ai Trading Bot Help",
            description="Professional AI-powered trading assistant with hedge-fund grade infrastructure",
            color=discord.Color.blue(),
            timestamp=discord.utils.utcnow()
        )
        
        # Core commands
        embed.add_field(
            name="📊 Analysis Commands",
            value="• `/analyze <symbol>` - Comprehensive technical analysis\n"
                  "• `/zones <symbol>` - Support & resistance zones\n"
                  "• `/recommendations <symbol>` - AI trading insights",
            inline=False
        )
        
        # Utility commands
        embed.add_field(
            name="🛠️ Utility Commands",
            value="• `/ask <query>` - Freeform AI assistance\n"
                  "• `/watchlist` - Manage your watchlist\n"
                  "• `/status` - Bot performance metrics",
            inline=False
        )
        
        # Features
        embed.add_field(
            name="🚀 Key Features",
            value="• **100% Data Quality** - No false gaps\n"
                  "• **Market Calendar** - Trading day aware\n"
                  "• **Free-Tier Optimized** - 5 calls/minute\n"
                  "• **AI-Powered** - Technical + sentiment analysis",
            inline=False
        )
        
        embed.set_footer(text="Built on production-ready infrastructure", icon_url=self.bot.user.display_avatar.url)
        await interaction.followup.send(embed=embed)
    
    def _sanitize_query(self, query: str) -> str:
        """Sanitize and normalize the user query"""
        # Basic sanitization - implement as needed
        return query.strip()
        
    async def _is_simple_price_query(self, query: str):
        """Check if the query is a simple price lookup using AI classification"""
        try:
            # Analyze the query with AI
            analysis = await self.query_analyzer.analyze_query(query, context=None)
            
            # Check if it's a simple price query: stock analysis intent with high confidence and quick response route
            if (analysis.intent == QueryIntent.STOCK_ANALYSIS and
                analysis.confidence > 0.7 and
                analysis.processing_route == ProcessingRoute.QUICK_RESPONSE and
                analysis.symbols):
                
                # Find the highest confidence symbol
                best_symbol = None
                best_confidence = 0
                for symbol in analysis.symbols:
                    if symbol.confidence > best_confidence:
                        best_confidence = symbol.confidence
                        best_symbol = symbol.text
                
                if best_confidence > 0.8:
                    return best_symbol
            return None
        except Exception as e:
            logger.warning(f"AI query analysis failed for quick price check: {e}")
            return None
    
    def _contains_intermediate_language(self, text: str) -> bool:
        """Check if text contains intermediate language that should be filtered out"""
        intermediate_phrases = [
            'as an AI', 'as a language model', 'I am an AI', 'I am a language model',
            'I don\'t have access', 'I do not have access', 'I cannot provide',
            'I don\'t have the ability', 'I do not have the ability',
            'my knowledge is limited', 'my knowledge cutoff', 'my training data',
            'I was trained', 'my training only includes', 'I don\'t have real-time',
            'I don\'t have live', 'I don\'t have current', 'I don\'t have up-to-date'
        ]
        
        text_lower = text.lower()
        return any(phrase in text_lower for phrase in intermediate_phrases)
    
    async def _validate_symbol(self, symbol: str) -> bool:
        """Validate if a symbol is valid"""
        # Simple validation for now
        if not symbol or len(symbol) < 1 or len(symbol) > 10:
            return False

        # Check if it's alphanumeric or contains dots (for crypto pairs like BTC.USD)
        return all(c.isalnum() or c == '.' for c in symbol)
    
    def add_listener(self, func, event: str):
        """Add event listener to bot"""
        self.bot.add_listener(func, event)
    
    async def on_ready(self):
        """Called when bot is ready"""
        logger.info(f"🤖 Bot is ready! Logged in as {self.bot.user}")
        logger.info(f"📊 Connected to {len(self.bot.guilds)} guilds")
    
    async def on_command_error(self, ctx, error):
        """Handle command errors"""
        if isinstance(error, commands.CommandNotFound):
            return
        
        logger.error(f"Command error: {error}")
        await ctx.send(f"❌ An error occurred: {str(error)}")
    
    async def start_bot(self, token: str):
        """Start the bot asynchronously"""
        # Debug logging for token
        logger.info(f"Starting bot with token: {token[:20]}... (length: {len(token)})")
        logger.info(f"Token type: {type(token)}")
        logger.info(f"Token is None: {token is None}")
        
        await self.bot.start(token)

    def run(self, token: str):
        """Run the bot with proper event loop handling"""
        try:
            if not self.bot.is_closed():
                return  # Already running
                
            # Check if we're already in an event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If loop is running, schedule the bot start
                    loop.create_task(self.start_bot(token))
                    return
            except RuntimeError:
                # No event loop, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run the bot in the event loop
            try:
                loop.run_until_complete(self.start_bot(token))
            finally:
                if not loop.is_closed():
                    loop.close()
            
        except Exception as e:
            logger.error(f"Error in bot run: {e}", exc_info=True)
            raise

class RateLimiter:
    """Simple rate limiter for user requests"""
    
    def __init__(self, max_requests: int = 50, time_window: int = 3600):
        self.max_requests = max_requests
        self.time_window = time_window
        self.user_requests = {}
    
    def can_make_request(self, user_id: str) -> bool:
        """Check if user can make a request"""
        now = time.time()
        
        if user_id not in self.user_requests:
            return True
        
        # Clean old requests
        self.user_requests[user_id] = [
            req_time for req_time in self.user_requests[user_id]
            if now - req_time < self.time_window
        ]
        
        return len(self.user_requests[user_id]) < self.max_requests
    
    def record_user_query(self, user_id: str):
        """Record a user query"""
        now = time.time()
        
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        
        self.user_requests[user_id].append(now)
    
    def get_remaining_time(self, user_id: str) -> float:
        """Get remaining time until user can make another request"""
        if user_id not in self.user_requests:
            return 0.0
        
        now = time.time()
        oldest_request = min(self.user_requests[user_id])
        return max(0.0, self.time_window - (now - oldest_request))

def create_bot() -> 'TradingBot':
    """Create and return a new bot instance"""
    # Get token from environment variable
    token = os.getenv('DISCORD_BOT_TOKEN')
    return TradingBot(token=token)


async def run_bot_async():
    """Run the bot asynchronously with proper error handling"""
    bot = create_bot()
    
    # Debug logging
    logger.info(f"Discord token loaded: {'SET' if bot.token else 'NOT SET'}")
    if bot.token:
        logger.info(f"Token length: {len(bot.token)} characters")
        logger.info(f"Token starts with: {bot.token[:10]}...")
    
    if not bot.token:
        logger.warning("DISCORD_BOT_TOKEN not provided - running in degraded mode")
        # Run bot in degraded mode without Discord
        await bot.run_in_degraded_mode()
        return
    
    try:
        logger.info("Attempting to start Discord bot...")
        await bot.start_bot(bot.token)
    except discord.errors.LoginFailure as e:
        logger.warning(f"Discord authentication failed: {e} - running in degraded mode")
        # Run bot in degraded mode when Discord auth fails
        await bot.run_in_degraded_mode()
    except Exception as e:
        logger.error(f"Bot failed to start: {e}", exc_info=True)
        raise
    finally:
        if hasattr(bot, 'bot') and not bot.bot.is_closed():
            await bot.bot.close()

def run_bot():
    """Run the bot with proper error handling"""
    try:
        asyncio.run(run_bot_async())
    except KeyboardInterrupt:
        logger.info("Bot shutdown requested by user")
    except Exception as e:
        logger.error(f"Bot failed to start: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    # Logging is already configured in setup_bot_logging()
    logger.info("Starting Discord bot...")
    run_bot()