{"created_at": "2025-08-27T17:07:39.154182", "operations": [{"operation_id": "0000_170739", "timestamp": "2025-08-27T17:07:39.146583", "file_path": "src/bot/pipeline/commands/ask/pipeline.py", "backup_path": "cleanup_backups/session_20250827_170739/files/src/bot/pipeline/commands/ask/0000_170739_pipeline.py", "operation_type": "import_cleanup", "description": "Backup before cleaning unused imports", "file_hash": "ea568ffee747dd14c88366e6898abace72b47f9de773ca9e6467e7d3581ba9cd"}, {"operation_id": "0001_170739", "timestamp": "2025-08-27T17:07:39.147772", "file_path": "src/bot/client.py", "backup_path": "cleanup_backups/session_20250827_170739/files/src/bot/0001_170739_client.py", "operation_type": "import_cleanup", "description": "Backup before cleaning unused imports", "file_hash": "e50b7212fb13cfe302ba240282a513330b5227502927830031eb65cccfcb0cbb"}, {"operation_id": "0002_170739", "timestamp": "2025-08-27T17:07:39.148488", "file_path": "src/shared/error_handling/fallback.py", "backup_path": "cleanup_backups/session_20250827_170739/files/src/shared/error_handling/0002_170739_fallback.py", "operation_type": "import_cleanup", "description": "Backup before cleaning unused imports", "file_hash": "57ce6c85b6a218ce73cbc601468eb8bd6c801e31297639cd203692d93fdde629"}, {"operation_id": "0003_170739", "timestamp": "2025-08-27T17:07:39.152391", "file_path": "src/core/config_manager.py", "backup_path": "cleanup_backups/session_20250827_170739/files/src/core/0003_170739_config_manager.py", "operation_type": "import_cleanup", "description": "Backup before cleaning unused imports", "file_hash": "8dbe34fb6944db13016451f2d9898cccd47b8d68a397f29f4e531c6fd13a729b"}, {"operation_id": "0004_170739", "timestamp": "2025-08-27T17:07:39.153328", "file_path": "src/api/main.py", "backup_path": "cleanup_backups/session_20250827_170739/files/src/api/0004_170739_main.py", "operation_type": "import_cleanup", "description": "Backup before cleaning unused imports", "file_hash": "71ba7a5efdbfbb0ce7600061df61353cab0a1b6b36e3228c7aa0b5e125fa00f1"}, {"operation_id": "0005_170739", "timestamp": "2025-08-27T17:07:39.154145", "file_path": "src/data/providers/manager.py", "backup_path": "cleanup_backups/session_20250827_170739/files/src/data/providers/0005_170739_manager.py", "operation_type": "import_cleanup", "description": "Backup before cleaning unused imports", "file_hash": "8c07fba183c87d8540e02d9390e196e2a60204750f5730313dec10d9ab0f379c"}], "total_files": 6, "total_size_bytes": 100431}