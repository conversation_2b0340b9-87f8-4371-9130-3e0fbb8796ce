"""
Database Configuration Module

Handles database-specific configuration with validation and defaults.
Uses the core config manager for base functionality.
"""

from typing import Dict, Any
from dataclasses import dataclass

from src.core.config_manager import BaseConfig, ConfigValidation


@dataclass
class DatabaseConfig(BaseConfig):
    """Database-specific configuration"""
    
    def _load_domain_config(self):
        """Load database configuration from environment or defaults"""
        # Database configuration
        self.use_supabase = self.core._get_bool("USE_SUPABASE", False)
        self.database_url = self.core._get_string("DATABASE_URL", "sqlite:///./local_dev.db")
        self.database_pool_size = self.core._get_int("DATABASE_POOL_SIZE", 5)
        self.database_max_overflow = self.core._get_int("DATABASE_MAX_OVERFLOW", 10)
        
        # Supabase-specific settings
        self.supabase_url = self.core._get_string("SUPABASE_URL", "")
        self.supabase_key = self.core._get_string("SUPABASE_KEY", "")
        self.supabase_bucket = self.core._get_string("SUPABASE_BUCKET", "storage")
    
    def _validate_domain_config(self):
        """Validate database configuration"""
        if self.use_supabase:
            if not self.supabase_url:
                self._validation.add_error("Supabase URL is required when USE_SUPABASE is True")
            if not self.supabase_key:
                self._validation.add_error("Supabase key is required when USE_SUPABASE is True")
        
        if self.database_pool_size <= 0:
            self._validation.add_error("Database pool size must be positive")
        
        if self.database_max_overflow < 0:
            self._validation.add_error("Database max overflow cannot be negative")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get database configuration summary"""
        return {
            "use_supabase": self.use_supabase,
            "database_url": self.database_url if not self.use_supabase else "REDACTED",
            "pool_size": self.database_pool_size,
            "max_overflow": self.database_max_overflow,
            "supabase_configured": bool(self.supabase_url and self.supabase_key)
        }


# Global database config instance
def get_database_config() -> DatabaseConfig:
    """Get database configuration instance"""
    from src.core.config_manager import get_config
    return DatabaseConfig(get_config())