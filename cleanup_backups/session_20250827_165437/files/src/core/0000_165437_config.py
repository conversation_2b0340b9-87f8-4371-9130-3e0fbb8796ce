"""
Configuration compatibility layer for src.core.config

This module provides backward compatibility for existing imports
while using the new config_manager system.
"""

import os
from .config_manager import get_config, config

# Create a settings object that mimics the expected interface
class Settings:
    """Compatibility settings object that provides the expected interface"""
    
    def __init__(self):
        self._config = get_config()
    
    @property
    def DEBUG(self) -> bool:
        """Get debug setting"""
        return self._config.get('app', 'debug', False)
    
    @property
    def MAX_CPU_THRESHOLD(self) -> float:
        """Get max CPU threshold for health checks"""
        return 90.0  # Default 90% CPU threshold
    
    @property
    def MAX_MEMORY_THRESHOLD(self) -> float:
        """Get max memory threshold for health checks"""
        return 90.0  # Default 90% memory threshold
    
    @property
    def MAX_DISK_THRESHOLD(self) -> float:
        """Get max disk threshold for health checks"""
        return 90.0  # Default 90% disk threshold
    
    @property
    def DATABASE_URL(self) -> str:
        """Get database URL"""
        return self._config.get('database', 'url', os.getenv('DATABASE_URL', '************************************************************/tradingview_data'))
    
    @property
    def USE_SUPABASE(self) -> bool:
        """Get Supabase usage setting"""
        return self._config.get('database', 'use_supabase', os.getenv('USE_SUPABASE', 'false').lower() == 'true')
    
    @property
    def REDIS_URL(self) -> str:
        """Get Redis URL"""
        return self._config.get('redis', 'url', os.getenv('REDIS_URL', 'redis://:${REDIS_PASSWORD}@redis:6379/0'))
    
    @property
    def ENVIRONMENT(self) -> str:
        """Get environment setting"""
        return self._config.get('app', 'environment', 'development')

# Create global settings instance
settings = Settings()

# Also provide the get_settings function for backward compatibility
def get_settings() -> Settings:
    """Get settings instance for backward compatibility"""
    return settings

# Export the main config for direct access
__all__ = ['settings', 'get_settings', 'config'] 