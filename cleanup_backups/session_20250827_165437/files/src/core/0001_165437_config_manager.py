"""
Unified Configuration Management System

Provides a single, comprehensive configuration management solution
with support for environment variables, YAML, domain-specific configurations,
and structured validation using dataclasses.
"""

import os
import logging
from typing import Dict, Any, List, Optional, Union, Type
import yaml
from pathlib import Path
from functools import lru_cache
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)

class ConfigurationError(Exception):
    """Raised when configuration loading or validation fails"""
    pass

class ValidationError(Exception):
    """Raised when configuration validation fails"""
    pass

class Environment(Enum):
    """Environment types for configuration"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class LogLevel(Enum):
    """Log levels for application logging"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class TechnicalAnalysisConfig:
    """Technical analysis parameters configuration"""
    sma_window: int = field(default=20)
    ema_span: int = field(default=12)
    rsi_period: int = field(default=14)
    macd_fast: int = field(default=12)
    macd_slow: int = field(default=26)
    macd_signal: int = field(default=9)
    bb_window: int = field(default=20)
    bb_std: float = field(default=2.0)
    atr_period: int = field(default=14)
    volume_window: int = field(default=20)
    vwap_window: int = field(default=20)
    
    # Supply/Demand zones parameters
    zone_lookback_period: int = field(default=100)
    zone_min_pivot_points: int = field(default=3)
    zone_tolerance_percentage: float = field(default=0.02)
    zone_strength_threshold: int = field(default=5)

@dataclass
class DataProviderConfig:
    """Data provider configuration parameters"""
    alpha_vantage: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'rate_limit': 5,
        'timeout': 10.0,
        'cache_ttl': 300,
        'retry_attempts': 3
    })
    
    yahoo_finance: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'rate_limit': 5,
        'timeout': 10.0,
        'retry_attempts': 3
    })
    
    polygon: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'rate_limit': 5,
        'timeout': 10.0,
        'retry_attempts': 3
    })
    
    finnhub: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'rate_limit': 30,
        'timeout': 10.0,
        'retry_attempts': 3
    })

@dataclass
class TradingStrategyConfig:
    """Trading strategy parameters configuration"""
    risk_per_trade: float = field(default=0.02)  # 2% risk per trade
    max_position_size: float = field(default=0.1)  # Max 10% of portfolio
    stop_loss_multiplier: float = field(default=2.0)
    take_profit_multiplier: float = field(default=3.0)
    max_open_positions: int = field(default=5)
    minimum_volume_threshold: float = field(default=100000.0)
    price_change_threshold: float = field(default=0.05)  # 5% minimum price change

@dataclass
class APIConfig:
    """API configuration parameters"""
    host: str = field(default="0.0.0.0")
    port: int = field(default=8000)
    cors_origins: List[str] = field(default_factory=lambda: [
        os.getenv('FRONTEND_URL', 'http://localhost:3000'),
        os.getenv('ALLOWED_ORIGIN', 'http://localhost:3000')
    ])
    rate_limit_requests: int = field(default=100)
    rate_limit_window: int = field(default=60)
    request_timeout: int = field(default=30)

@dataclass
class DatabaseConfig:
    """Database configuration parameters"""
    url: str = field(default=os.getenv('DATABASE_URL', '************************************************************/tradingview_data'))
    use_supabase: bool = field(default=os.getenv('USE_SUPABASE', 'false').lower() == 'true')
    pool_size: int = field(default=int(os.getenv('DB_POOL_SIZE', '5')))
    max_overflow: int = field(default=int(os.getenv('DB_MAX_OVERFLOW', '10')))
    echo: bool = field(default=os.getenv('DB_ECHO', 'false').lower() == 'true')

@dataclass
class RedisConfig:
    """Redis configuration parameters"""
    url: str = field(default=os.getenv('REDIS_URL', 'redis://:${REDIS_PASSWORD}@redis:6379/0'))
    enabled: bool = field(default=os.getenv('REDIS_ENABLED', 'true').lower() == 'true')
    pool_size: int = field(default=int(os.getenv('REDIS_POOL_SIZE', '10')))
    max_connections: int = field(default=int(os.getenv('REDIS_MAX_CONNECTIONS', '20')))
    password: Optional[str] = field(default=os.getenv('REDIS_PASSWORD', None))

@dataclass
class SecurityConfig:
    """Security configuration parameters"""
    jwt_secret: str = field(default="")
    jwt_algorithm: str = field(default="HS256")
    jwt_access_token_expire_minutes: int = field(default=15)
    jwt_refresh_token_expire_days: int = field(default=7)
    rate_limit_enabled: bool = field(default=True)
    cors_allow_credentials: bool = field(default=True)

class TradingBotConfig:
    """
    Centralized configuration management for the entire trading bot system.
    
    Supports:
    - Environment variable configuration
    - YAML file configuration
    - Domain-specific config retrieval
    - Comprehensive validation with dataclasses
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration with optional custom config path.
        
        Args:
            config_path: Optional path to a YAML configuration file
        """
        self._config: Dict[str, Any] = {}
        self._config_path = config_path
        
        # If no config path provided, try to find config.yaml in common locations
        if config_path is None:
            # Look for config.yaml in current directory, parent directory, or project root
            possible_paths = [
                'config.yaml',
                '../config.yaml',
                '../../config.yaml',
                'src/config.yaml'
            ]
            
            for path in possible_paths:
                if Path(path).exists():
                    config_path = path
                    break
        
        self._load_configuration(config_path)
        self._validate_configuration()
    
    def _load_configuration(self, config_path: Optional[str] = None):
        """
        Load configuration from multiple sources with priority:
        1. Environment Variables
        2. YAML Configuration File
        3. Default Values
        """
        # Default configuration
        self._config = {
            # Core Application Settings
            'app': {
                'name': 'TradingView Automation Bot',
                'version': '2.0.0',
                'environment': os.getenv('ENVIRONMENT', 'development'),
                'debug': os.getenv('DEBUG', 'false').lower() == 'true',
                'log_level': os.getenv('LOG_LEVEL', 'INFO')
            },
            
            # API Configuration
            'api': {
                'host': os.getenv('API_HOST', '0.0.0.0'),
                'port': int(os.getenv('API_PORT', '8000')),
                'cors_origins': os.getenv('CORS_ORIGINS', '*').split(','),
                'rate_limit_requests': int(os.getenv('RATE_LIMIT_REQUESTS', '100')),
                'rate_limit_window': int(os.getenv('RATE_LIMIT_WINDOW', '60')),
                'request_timeout': int(os.getenv('REQUEST_TIMEOUT', '30'))
            },
            
            # Database Configuration
            'database': {
                'url': os.getenv('DATABASE_URL', 'sqlite:///./local_dev.db'),
                'use_supabase': os.getenv('USE_SUPABASE', 'false').lower() == 'true',
                'pool_size': int(os.getenv('DATABASE_POOL_SIZE', '5')),
                'max_overflow': int(os.getenv('DATABASE_MAX_OVERFLOW', '10')),
                'echo': os.getenv('DATABASE_ECHO', 'false').lower() == 'true'
            },
            
            # Redis Configuration
            'redis': {
                'url': os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
                'enabled': os.getenv('REDIS_ENABLED', 'true').lower() == 'true',
                'pool_size': int(os.getenv('REDIS_POOL_SIZE', '10')),
                'max_connections': int(os.getenv('REDIS_MAX_CONNECTIONS', '20')),
                'password': os.getenv('REDIS_PASSWORD')
            },
            
            # Security Configuration
            'security': {
                'jwt_secret': os.getenv('JWT_SECRET', ''),
                'jwt_algorithm': os.getenv('JWT_ALGORITHM', 'HS256'),
                'jwt_access_token_expire_minutes': int(os.getenv('JWT_ACCESS_TOKEN_EXPIRE_MINUTES', '15')),
                'jwt_refresh_token_expire_days': int(os.getenv('JWT_REFRESH_TOKEN_EXPIRE_DAYS', '7')),
                'rate_limit_enabled': os.getenv('RATE_LIMIT_ENABLED', 'true').lower() == 'true',
                'cors_allow_credentials': os.getenv('CORS_ALLOW_CREDENTIALS', 'true').lower() == 'true'
            },
            
            # AI and Pipeline Configuration
            'pipeline': {
                'max_concurrent_pipelines': int(os.getenv('MAX_CONCURRENT_PIPELINES', '5')),
                'pipeline_timeout': int(os.getenv('PIPELINE_TIMEOUT', '300')),
                'enable_caching': os.getenv('ENABLE_CACHING', 'true').lower() == 'true',
                'cache_ttl': int(os.getenv('CACHE_TTL', '3600'))
            },
            
            # Market Data Configuration
            'market_data': {
                'default_provider': os.getenv('DEFAULT_MARKET_DATA_PROVIDER', 'alpha_vantage'),
                'cache_enabled': os.getenv('MARKET_DATA_CACHE_ENABLED', 'true').lower() == 'true',
                'cache_ttl': int(os.getenv('MARKET_DATA_CACHE_TTL', '300')),
                'rate_limit_enabled': os.getenv('MARKET_DATA_RATE_LIMIT_ENABLED', 'true').lower() == 'true'
            },
            
            # Trading Configuration
            'trading': {
                'paper_trading': os.getenv('PAPER_TRADING', 'true').lower() == 'true',
                'max_position_size': float(os.getenv('MAX_POSITION_SIZE', '0.1')),
                'risk_per_trade': float(os.getenv('RISK_PER_TRADE', '0.02')),
                'stop_loss_multiplier': float(os.getenv('STOP_LOSS_MULTIPLIER', '2.0')),
                'take_profit_multiplier': float(os.getenv('TAKE_PROFIT_MULTIPLIER', '3.0'))
            }
        }
        
        # Load YAML configuration if available
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r') as f:
                    yaml_config = yaml.safe_load(f)
                    if yaml_config:
                        self._merge_config(self._config, yaml_config)
            except Exception as e:
                logger.warning(f"Failed to load YAML config from {config_path}: {e}")
        
        # Override with environment variables (highest priority)
        self._override_from_env()
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]):
        """Recursively merge configuration dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _override_from_env(self):
        """Override configuration with environment variables"""
        # API overrides
        if os.getenv('API_HOST'):
            self._config['api']['host'] = os.getenv('API_HOST')
        if os.getenv('API_PORT'):
            self._config['api']['port'] = int(os.getenv('API_PORT'))
        
        # Database overrides
        if os.getenv('DATABASE_URL'):
            self._config['database']['url'] = os.getenv('DATABASE_URL')
        
        # Security overrides
        if os.getenv('JWT_SECRET'):
            self._config['security']['jwt_secret'] = os.getenv('JWT_SECRET')
        
        # Trading overrides
        if os.getenv('PAPER_TRADING'):
            self._config['trading']['paper_trading'] = os.getenv('PAPER_TRADING').lower() == 'true'
    
    def _validate_configuration(self):
        """Validate the loaded configuration"""
        errors = []
        
        # Validate required fields only in production
        environment = self.get('app', 'environment', 'development')
        if environment == 'production':
            if not self._config['security']['jwt_secret']:
                errors.append("JWT_SECRET is required in production")
            
            if self._config['security']['jwt_secret'] and len(self._config['security']['jwt_secret']) < 32:
                errors.append("JWT_SECRET must be at least 32 characters in production")
        
        if self._config['api']['port'] < 1 or self._config['api']['port'] > 65535:
            errors.append("API_PORT must be between 1 and 65535")
        
        if errors:
            raise ConfigurationError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by section and key.
        
        Args:
            section: Configuration section (e.g., 'api', 'database')
            key: Configuration key within the section
            default: Default value if not found
        
        Returns:
            Configuration value
        """
        try:
            return self._config[section][key]
        except KeyError:
            return default
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get an entire configuration section.
        
        Args:
            section: Configuration section name
        
        Returns:
            Dictionary containing the section configuration
        """
        return self._config.get(section, {})
    
    def set(self, section: str, key: str, value: Any):
        """
        Set a configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Value to set
        """
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value
    
    def get_technical_analysis_config(self) -> TechnicalAnalysisConfig:
        """Get technical analysis configuration as a dataclass"""
        return TechnicalAnalysisConfig()
    
    def get_data_provider_config(self) -> DataProviderConfig:
        """Get data provider configuration as a dataclass"""
        return DataProviderConfig()
    
    def get_trading_strategy_config(self) -> TradingStrategyConfig:
        """Get trading strategy configuration as a dataclass"""
        return TradingStrategyConfig()
    
    def get_api_config(self) -> APIConfig:
        """Get API configuration as a dataclass"""
        return APIConfig(
            host=self.get('api', 'host'),
            port=self.get('api', 'port'),
            cors_origins=self.get('api', 'cors_origins'),
            rate_limit_requests=self.get('api', 'rate_limit_requests'),
            rate_limit_window=self.get('api', 'rate_limit_window'),
            request_timeout=self.get('api', 'request_timeout')
        )
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration as a dataclass"""
        return DatabaseConfig(
            url=self.get('database', 'url'),
            use_supabase=self.get('database', 'use_supabase'),
            pool_size=self.get('database', 'pool_size'),
            max_overflow=self.get('database', 'max_overflow'),
            echo=self.get('database', 'echo')
        )
    
    def get_redis_config(self) -> RedisConfig:
        """Get Redis configuration as a dataclass"""
        return RedisConfig(
            url=self.get('redis', 'url'),
            enabled=self.get('redis', 'enabled'),
            pool_size=self.get('redis', 'pool_size'),
            max_connections=self.get('redis', 'max_connections'),
            password=self.get('redis', 'password')
        )
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration as a dataclass"""
        return SecurityConfig(
            jwt_secret=self.get('security', 'jwt_secret'),
            jwt_algorithm=self.get('security', 'jwt_algorithm'),
            jwt_access_token_expire_minutes=self.get('security', 'jwt_access_token_expire_minutes'),
            jwt_refresh_token_expire_days=self.get('security', 'jwt_refresh_token_expire_days'),
            rate_limit_enabled=self.get('security', 'rate_limit_enabled'),
            cors_allow_credentials=self.get('security', 'cors_allow_credentials')
        )
    
    def reload(self):
        """Reload configuration from sources"""
        self._load_configuration(self._config_path)
        self._validate_configuration()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return self._config.copy()
    
    def __str__(self) -> str:
        """String representation of configuration"""
        return f"TradingBotConfig(environment={self.get('app', 'environment')}, debug={self.get('app', 'debug')})"

# Global configuration instance
config = TradingBotConfig()

@lru_cache(maxsize=1)
def get_config() -> TradingBotConfig:
    """
    Get the global configuration instance.
    
    Returns:
        TradingBotConfig instance
    """
    return config

def reload_config():
    """Reload the global configuration"""
    global config
    config.reload()
    get_config.cache_clear()