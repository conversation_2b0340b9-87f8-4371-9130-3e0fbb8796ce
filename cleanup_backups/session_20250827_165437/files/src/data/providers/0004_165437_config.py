"""
Data Providers Configuration Module

Handles configuration for all data providers with validation and defaults.
Uses the core config manager for base functionality.
"""

from typing import Dict, Any, List
from dataclasses import dataclass

from src.core.config_manager import BaseConfig, ConfigValidation


@dataclass
class DataProvidersConfig(BaseConfig):
    """Data providers-specific configuration"""
    
    def _load_domain_config(self):
        """Load data providers configuration from environment or defaults"""
        # Provider enablement
        self.yahoo_finance_enabled = self.core._get_bool("YAHOO_FINANCE_ENABLED", True)
        self.polygon_enabled = self.core._get_bool("POLYGON_ENABLED", True)
        self.finnhub_enabled = self.core._get_bool("FINNHUB_ENABLED", True)
        self.alpha_vantage_enabled = self.core._get_bool("ALPHA_VANTAGE_ENABLED", True)
        
        # Rate limiting (requests per minute)
        self.yahoo_finance_rate_limit = self.core._get_int("YAHOO_FINANCE_RATE_LIMIT", 5)
        self.polygon_rate_limit = self.core._get_int("POLYGON_RATE_LIMIT", 5)
        self.finnhub_rate_limit = self.core._get_int("FINNHUB_RATE_LIMIT", 30)
        self.alpha_vantage_rate_limit = self.core._get_int("ALPHA_VANTAGE_RATE_LIMIT", 5)
        
        # Timeouts (seconds)
        self.yahoo_finance_timeout = self.core._get_float("YAHOO_FINANCE_TIMEOUT", 10.0)
        self.polygon_timeout = self.core._get_float("POLYGON_TIMEOUT", 8.0)
        self.finnhub_timeout = self.core._get_float("FINNHUB_TIMEOUT", 5.0)
        self.alpha_vantage_timeout = self.core._get_float("ALPHA_VANTAGE_TIMEOUT", 10.0)
        
        # API Keys
        self.polygon_api_key = self.core._get_string("POLYGON_API_KEY", "")
        self.finnhub_api_key = self.core._get_string("FINNHUB_API_KEY", "")
        self.alpha_vantage_api_key = self.core._get_string("ALPHA_VANTAGE_API_KEY", "")
    
    def _validate_domain_config(self):
        """Validate data providers configuration"""
        # Check at least one provider is enabled
        enabled_providers = [
            self.yahoo_finance_enabled,
            self.polygon_enabled,
            self.finnhub_enabled,
            self.alpha_vantage_enabled
        ]
        if not any(enabled_providers):
            self._validation.add_error("At least one data provider must be enabled")
        
        # Validate rate limits
        for provider, rate_limit in [
            ("Yahoo Finance", self.yahoo_finance_rate_limit),
            ("Polygon", self.polygon_rate_limit),
            ("Finnhub", self.finnhub_rate_limit),
            ("Alpha Vantage", self.alpha_vantage_rate_limit)
        ]:
            if rate_limit <= 0:
                self._validation.add_error(f"{provider} rate limit must be positive")
        
        # Validate timeouts
        for provider, timeout in [
            ("Yahoo Finance", self.yahoo_finance_timeout),
            ("Polygon", self.polygon_timeout),
            ("Finnhub", self.finnhub_timeout),
            ("Alpha Vantage", self.alpha_vantage_timeout)
        ]:
            if timeout <= 0:
                self._validation.add_error(f"{provider} timeout must be positive")
        
        # Validate API keys for enabled providers
        if self.polygon_enabled and not self.polygon_api_key:
            self._validation.add_warning("Polygon is enabled but no API key is set")
        if self.finnhub_enabled and not self.finnhub_api_key:
            self._validation.add_warning("Finnhub is enabled but no API key is set")
        if self.alpha_vantage_enabled and not self.alpha_vantage_api_key:
            self._validation.add_warning("Alpha Vantage is enabled but no API key is set")
    
    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """Get configuration for a specific data provider"""
        provider_configs = {
            "yahoo_finance": {
                "enabled": self.yahoo_finance_enabled,
                "rate_limit": self.yahoo_finance_rate_limit,
                "timeout": self.yahoo_finance_timeout
            },
            "polygon": {
                "enabled": self.polygon_enabled,
                "rate_limit": self.polygon_rate_limit,
                "timeout": self.polygon_timeout,
                "api_key": self.polygon_api_key
            },
            "finnhub": {
                "enabled": self.finnhub_enabled,
                "rate_limit": self.finnhub_rate_limit,
                "timeout": self.finnhub_timeout,
                "api_key": self.finnhub_api_key
            },
            "alpha_vantage": {
                "enabled": self.alpha_vantage_enabled,
                "rate_limit": self.alpha_vantage_rate_limit,
                "timeout": self.alpha_vantage_timeout,
                "api_key": self.alpha_vantage_api_key
            }
        }
        return provider_configs.get(provider_name, {})
    
    def get_enabled_providers(self) -> List[str]:
        """Get list of enabled provider names"""
        providers = []
        if self.yahoo_finance_enabled:
            providers.append("yahoo_finance")
        if self.polygon_enabled:
            providers.append("polygon")
        if self.finnhub_enabled:
            providers.append("finnhub")
        if self.alpha_vantage_enabled:
            providers.append("alpha_vantage")
        return providers
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get data providers configuration summary"""
        return {
            "enabled_providers": self.get_enabled_providers(),
            "total_providers": len(self.get_enabled_providers()),
            "rate_limits": {
                "yahoo_finance": self.yahoo_finance_rate_limit,
                "polygon": self.polygon_rate_limit,
                "finnhub": self.finnhub_rate_limit,
                "alpha_vantage": self.alpha_vantage_rate_limit
            },
            "timeouts": {
                "yahoo_finance": self.yahoo_finance_timeout,
                "polygon": self.polygon_timeout,
                "finnhub": self.finnhub_timeout,
                "alpha_vantage": self.alpha_vantage_timeout
            }
        }


# Global data providers config instance
def get_data_providers_config() -> DataProvidersConfig:
    """Get data providers configuration instance"""
    from src.core.config_manager import get_config
    return DataProvidersConfig(get_config())