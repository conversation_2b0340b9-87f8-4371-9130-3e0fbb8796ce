"""
API Configuration Module

Handles API-specific configuration with validation and defaults.
Uses the core config manager for base functionality.
"""

from typing import Dict, Any, List
from dataclasses import dataclass

from src.core.config_manager import BaseConfig, ConfigValidation


@dataclass
class APIConfig(BaseConfig):
    """API-specific configuration"""
    
    def _load_domain_config(self):
        """Load API configuration from environment or defaults"""
        # API server settings - use environment variable or safe default
        import os
        self.api_host = os.getenv("API_HOST") or self.core._get_string("API_HOST", "0.0.0.0")
        
        # Validate host binding for production
        if self.api_host == "0.0.0.0" and os.getenv("ENVIRONMENT") == "production":
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("Binding to 0.0.0.0 in production environment. Ensure proper firewall rules are in place.")
        
        self.api_port = self.core._get_int("API_PORT", 8000)
        self.api_debug = self.core._get_bool("API_DEBUG", False)
        
        # CORS settings
        self.cors_origins = self.core._get_list("CORS_ORIGINS", ["*"])
        self.cors_allow_credentials = self.core._get_bool("CORS_ALLOW_CREDENTIALS", True)
        self.cors_allow_methods = self.core._get_list("CORS_ALLOW_METHODS", ["*"])
        self.cors_allow_headers = self.core._get_list("CORS_ALLOW_HEADERS", ["*"])
        
        # Rate limiting
        self.rate_limit_requests = self.core._get_int("RATE_LIMIT_REQUESTS", 100)
        self.rate_limit_window = self.core._get_int("RATE_LIMIT_WINDOW", 60)
        
        # Security headers
        self.enable_security_headers = self.core._get_bool("ENABLE_SECURITY_HEADERS", True)
        self.enable_https_redirect = self.core._get_bool("ENABLE_HTTPS_REDIRECT", False)
    
    def _validate_domain_config(self):
        """Validate API configuration"""
        if self.api_port < 1 or self.api_port > 65535:
            self._validation.add_error("API port must be between 1 and 65535")
        
        if self.rate_limit_requests <= 0:
            self._validation.add_error("Rate limit requests must be positive")
        
        if self.rate_limit_window <= 0:
            self._validation.add_error("Rate limit window must be positive")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get API configuration summary"""
        return {
            "host": self.api_host,
            "port": self.api_port,
            "debug": self.api_debug,
            "cors_origins": self.cors_origins,
            "rate_limiting": {
                "requests": self.rate_limit_requests,
                "window": self.rate_limit_window
            }
        }


# Global API config instance
def get_api_config() -> APIConfig:
    """Get API configuration instance"""
    from src.core.config_manager import get_config
    return APIConfig(get_config())