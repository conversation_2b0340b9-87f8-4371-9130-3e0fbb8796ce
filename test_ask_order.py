#!/usr/bin/env python3
"""
Test script to verify the correct order of operations in the /ask command.
"""

import asyncio
import sys
import os
from unittest.mock import MagicMock, AsyncMock, patch

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

async def test_ask_order_of_operations():
    """Test that the /ask command follows the correct order of operations."""
    print("🔍 Testing /ask command order of operations...")
    
    try:
        # Mock the complex dependencies
        mock_modules = {
            'pydantic': MagicMock(),
            'openai': MagicMock(),
            'aiohttp': MagicMock(),
            'discord': MagicMock(),
            'discord.ext': MagicMock(),
            'discord.ext.commands': MagicMock(),
        }
        
        with patch.dict('sys.modules', mock_modules):
            # Track the order of operations
            operation_order = []
            
            # Mock the enhanced AI processor to track tool execution
            with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.enhanced_ai_processor') as mock_enhanced:
                mock_enhanced.process_expert_conversation = AsyncMock()
                mock_enhanced.process_expert_conversation.return_value = {
                    "content": "📊 **Expert Technical Analysis for AAPL**\n\nBased on real-time data and technical indicators:\n\n**Current Status:**\n• Price: $150.25 (+2.50, +1.69%)\n• Volume: 2,500,000\n• RSI: 65.4 (Neutral)\n\n**Technical Indicators:**\n• 20 SMA: $148.75 | 50 SMA: $145.20\n• MACD: 2.15 (Signal: 1.85)\n• Bollinger Bands: $145.30 - $155.20\n\n**Expert Insights:**\n• Current momentum suggests bullish bias\n• Key resistance at $155.00\n• Volume confirms buying pressure",
                    "tools_used": ["real_time_price", "technical_indicators", "support_resistance"],
                    "intent": "technical_analysis",
                    "analysis_depth": "standard"
                }
                
                # Track when enhanced processor is called
                def track_enhanced_call(*args, **kwargs):
                    operation_order.append("enhanced_ai_processor_called")
                    return mock_enhanced.process_expert_conversation.return_value
                
                mock_enhanced.process_expert_conversation.side_effect = track_enhanced_call
                
                # Import and test the processor
                from src.bot.pipeline.commands.ask.stages.ai_chat_processor import FlexibleAIChatProcessor
                
                processor = FlexibleAIChatProcessor()
                operation_order.append("processor_created")
                
                # Test processing
                result = await processor.process("Analyze AAPL technical indicators")
                operation_order.append("process_completed")
                
                print(f"✅ Operation order: {' -> '.join(operation_order)}")
                print(f"✅ Result keys: {list(result.keys())}")
                print(f"✅ Tools used: {result.get('tools_used', [])}")
                print(f"✅ Response preview: {result.get('response', '')[:100]}...")
                
                # Verify correct order
                expected_order = [
                    "processor_created",
                    "enhanced_ai_processor_called", 
                    "process_completed"
                ]
                
                if operation_order == expected_order:
                    print("✅ Order of operations is correct!")
                    return True
                else:
                    print(f"❌ Wrong order. Expected: {expected_order}, Got: {operation_order}")
                    return False
                    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_pipeline_integration():
    """Test the full pipeline integration."""
    print("\n🔧 Testing full pipeline integration...")
    
    try:
        mock_modules = {
            'pydantic': MagicMock(),
            'openai': MagicMock(),
            'aiohttp': MagicMock(),
            'discord': MagicMock(),
            'discord.ext': MagicMock(),
            'discord.ext.commands': MagicMock(),
        }
        
        with patch.dict('sys.modules', mock_modules):
            # Mock the context and pipeline components
            with patch('src.bot.pipeline.commands.ask.pipeline.PipelineContext') as mock_context_class:
                with patch('src.bot.pipeline.commands.ask.stages.ai_chat_processor.enhanced_ai_processor') as mock_enhanced:
                    
                    # Setup enhanced processor mock
                    mock_enhanced.process_expert_conversation = AsyncMock()
                    mock_enhanced.process_expert_conversation.return_value = {
                        "content": "Based on technical analysis, TSLA shows strong bullish momentum with RSI at 68.2 and volume confirmation.",
                        "tools_used": ["real_time_price", "technical_indicators"],
                        "intent": "technical_analysis"
                    }
                    
                    # Setup context mock
                    mock_context = MagicMock()
                    mock_context.processing_results = {}
                    mock_context.status.value = "completed"
                    mock_context_class.return_value = mock_context
                    
                    # Import and test pipeline
                    from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
                    
                    result = await execute_ask_pipeline(
                        query="What's the technical analysis for TSLA?",
                        user_id="test_user",
                        guild_id="test_guild"
                    )
                    
                    print("✅ Pipeline executed successfully")
                    print(f"✅ Context type: {type(result)}")
                    
                    # Check if enhanced processor was called
                    if mock_enhanced.process_expert_conversation.called:
                        print("✅ Enhanced AI processor was called (tools will be executed)")
                        call_args = mock_enhanced.process_expert_conversation.call_args
                        print(f"✅ Query passed to enhanced processor: {call_args[1]['user_message']}")
                    else:
                        print("❌ Enhanced AI processor was not called")
                        return False
                    
                    return True
                    
    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all order of operations tests."""
    print("🚀 Testing /ask command order of operations...\n")
    
    test1_passed = await test_ask_order_of_operations()
    test2_passed = await test_pipeline_integration()
    
    if test1_passed and test2_passed:
        print("\n🎉 Order of operations is correct!")
        print("\n📋 Correct Flow:")
        print("1. ✅ Discord /ask command receives query")
        print("2. ✅ execute_ask_pipeline() called")
        print("3. ✅ AskPipeline.run() called")
        print("4. ✅ AIChatProcessor.process() called")
        print("5. ✅ Enhanced AI processor analyzes intent")
        print("6. ✅ Tools are selected and executed")
        print("7. ✅ AI synthesizes tool results")
        print("8. ✅ Response returned to Discord")
        print("\n🔧 Your /ask command now properly:")
        print("  • Analyzes user intent with AI")
        print("  • Executes relevant trading tools")
        print("  • Synthesizes results into expert responses")
    else:
        print("\n❌ Order of operations test failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
