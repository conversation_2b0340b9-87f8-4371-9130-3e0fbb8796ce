# ============================================================================
# SECURE ENVIRONMENT CONFIGURATION
# ============================================================================
# This file contains ALL environment variables for the secure Docker architecture
# Replace hardcoded values throughout the codebase with these variables

# ============================================================================
# APPLICATION SETTINGS
# ============================================================================
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO
APP_NAME=TradingView Automation Bot
APP_VERSION=2.0.0

# ============================================================================
# NETWORK & SECURITY SETTINGS
# ============================================================================
# Frontend URL (replace hardcoded localhost:3000)
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGIN=http://localhost:3000

# Webhook settings (replace hardcoded localhost:8001)
WEBHOOK_ORIGIN=http://webhook-proxy:8001
WEBHOOK_SECRET=your_webhook_secret_here

# API settings (replace hardcoded ports)
API_HOST=0.0.0.0
API_PORT=8000
API_RATE_LIMIT=100
API_RATE_WINDOW=60

# ============================================================================
# DATABASE CONFIGURATION (replace hardcoded SQLite/localhost)
# ============================================================================
# Primary database (Supabase)
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres
USE_SUPABASE=true
SUPABASE_URL=https://sgxjackuhalscowqrulv.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTk2MTE0MywiZXhwIjoyMDY1NTM3MTQzfQ.4Nz4q6HN3XGgd23l_xCSkZWD1qDh3U0UWY4m-aDbqrA
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8h8sE
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_ECHO=false

# ============================================================================
# REDIS CONFIGURATION (replace hardcoded localhost:6379)
# ============================================================================
# Redis cache (Docker service)
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
REDIS_ENABLED=true
REDIS_POOL_SIZE=10
REDIS_MAX_CONNECTIONS=20
REDIS_PASSWORD=6d9a1c3f5e7b9d2f4a6c8e0b2f1a3c5e

# ============================================================================
# EXTERNAL API KEYS (already secure)
# ============================================================================
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTk2MTE0MywiZXhwIjoyMDY1NTM3MTQzfQ.4Nz4q6HN3XGgd23l_xCSkZWD1qDh3U0UWY4m-aDbqrA
DISCORD_BOT_TOKEN=MTQwNDUwNjk2MTc3NjQ4MDMxNg.GQfRXC.460Em5NkJlWGCLWsZJMC3e8yo2ngozNl2dlZKg
OPENROUTER_API_KEY=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
POLYGON_API_KEY=********************************
FINNHUB_API_KEY=your_finnhub_key_here
ALPACA_API_KEY=your_alpaca_key_here
JWT_SECRET=4b7f9c3d2e6a8f0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4

# ============================================================================
# DOCKER SERVICE NAMES (replace hardcoded IPs)
# ============================================================================
# Database services - REMOVED: Using Supabase Cloud instead of local PostgreSQL
# POSTGRES_HOST=postgres
# POSTGRES_PORT=5432
# POSTGRES_DB=tradingview_data
# POSTGRES_USER=tradingview_user
# POSTGRES_PASSWORD=tradingview_pass

# Cache services
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Webhook services
WEBHOOK_HOST=webhook-proxy
WEBHOOK_PORT=8001
WEBHOOK_INGEST_HOST=webhook-ingest
WEBHOOK_INGEST_PORT=8001
NGROK_AUTHTOKEN=30I3DmIoKUoLSo1S6s2VR9hJbGT_51zxMZtp5pGKqR5sdGKHT

# ============================================================================
# CRITICAL TRADING PARAMETERS (replace hardcoded fallback values)
# ============================================================================
# Risk management (replace hardcoded 2% risk per trade)
RISK_PER_TRADE=0.02
MAX_POSITION_SIZE=0.1
STOP_LOSS_MULTIPLIER=2.0
TAKE_PROFIT_MULTIPLIER=3.0
MAX_OPEN_POSITIONS=5
MINIMUM_VOLUME_THRESHOLD=100000.0
PRICE_CHANGE_THRESHOLD=0.05

# Technical analysis thresholds (replace hardcoded 5% support/resistance)
SUPPORT_RESISTANCE_PERCENTAGE=0.05
CONFIDENCE_MIN_THRESHOLD=30
CONFIDENCE_MAX_THRESHOLD=90
DATA_QUALITY_MIN_THRESHOLD=50
ACTION_THRESHOLD_BASE=2.0

# ============================================================================
# SECURITY SETTINGS
# ============================================================================
# JWT settings
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS settings
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=600

# Rate limiting
RATE_LIMIT_ENABLED=true
WEBHOOK_RATE_LIMIT=10
API_RATE_LIMIT=30

# ============================================================================
# MONITORING & HEALTH CHECKS
# ============================================================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# CPU/Memory thresholds
MAX_CPU_THRESHOLD=90.0
MAX_MEMORY_THRESHOLD=90.0
MAX_DISK_THRESHOLD=90.0

# ============================================================================
# LOGGING & DEBUGGING
# ============================================================================
LOG_FORMAT=json
LOG_FILE_PATH=/app/logs
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# ============================================================================
# DEVELOPMENT OVERRIDES
# ============================================================================
# Uncomment for development (NOT for production)
# DEBUG=true
# LOG_LEVEL=DEBUG
# DB_ECHO=true 