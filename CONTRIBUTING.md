# Contributing to TradingView Automation

## 🚀 Quick Start

This is a **Docker-based application**. You don't need Python installed locally!

### Prerequisites
- Docker
- Docker Compose
- Git

### Running the Application
```bash
# Clone the repository
git clone <your-repo-url>
cd tradingview-automation

# Start all services
docker-compose up --build

# The application will be available at:
# - API: http://localhost:8000
# - Bot: Running in Discord
```

## 🚫 Important: Never Use pip install

**This project uses Docker for dependency management. Never run:**
- ❌ `pip install package-name`
- ❌ `pip freeze`
- ❌ `python -m pip install`

**Instead, use:**
- ✅ Edit `requirements.txt`
- ✅ Run `docker-compose up --build`

## 🔧 Development Workflow

### 1. Make Changes
Edit the source code in the `src/` directory.

### 2. Test Changes
```bash
# Rebuild and restart services
docker-compose up --build

# View logs for debugging
docker-compose logs bot
docker-compose logs api
```

### 3. Add New Dependencies
1. Add package to `requirements.txt`
2. Rebuild: `docker-compose up --build`
3. Test functionality

### 4. Commit and Push
```bash
git add .
git commit -m "Description of changes"
git push
```

## 🐳 Docker Services

### Bot Service
- **Purpose**: Discord bot for trading automation
- **Command**: `python -m src.bot.client`
- **Logs**: `docker-compose logs bot`

### API Service
- **Purpose**: FastAPI backend for market data
- **Command**: `uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload`
- **Port**: 8000
- **Logs**: `docker-compose logs api`

### Database Service
- **Purpose**: PostgreSQL database
- **Port**: 5432
- **Health Check**: `docker-compose logs db`

### Redis Service
- **Purpose**: Caching and session management
- **Port**: 6379
- **Health Check**: `docker-compose logs redis`

## 🧪 Testing

### Run Tests
```bash
# Run all tests
docker-compose run --rm security_tests

# Or run specific test file
docker-compose run --rm security_tests python -m pytest tests/test_file.py -v
```

### Test Individual Services
```bash
# Test API health
curl http://localhost:8000/health/health

# Check bot status
docker-compose logs bot | grep "connected to Discord"
```

## 📁 Project Structure

```
tradingview-automation/
├── src/
│   ├── bot/           # Discord bot
│   ├── api/           # FastAPI backend
│   ├── core/          # Core utilities
│   └── modules/       # Trading modules
├── docker-compose.yml # Service orchestration
├── requirements.txt   # Python dependencies
└── .cursor/          # Cursor IDE configuration
```

## 🐛 Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
docker-compose logs <service-name>

# Rebuild from scratch
docker-compose down
docker-compose up --build
```

**Port conflicts:**
```bash
# Stop all services
docker-compose down

# Check what's using the port
netstat -tulpn | grep :8000
```

**Bot not responding:**
```bash
# Check bot logs
docker-compose logs bot

# Verify Discord token is set
echo $DISCORD_BOT_TOKEN
```

## 📝 Code Style

- Follow PEP 8 for Python code
- Use meaningful variable names
- Add docstrings to functions
- Include type hints where possible

## 🤝 Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with Docker
5. Submit a pull request

## 📞 Getting Help

- Check the logs: `docker-compose logs <service>`
- Review this contributing guide
- Check the main README.md
- Open an issue for bugs

## 🎯 Remember

**This is a Docker-first project. Everything runs in containers. Never suggest pip install or local Python setup!** 