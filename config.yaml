# TradingView Automation Bot Configuration

# Core Application Settings
app:
  name: TradingView Automation Bot
  version: 2.0.0
  environment: development
  debug: true
  
  # Logging Configuration
  log_level: DEBUG  # Changed from INFO to capture more detailed logs
  log_file_enabled: true
  log_max_size: 10485760  # 10MB
  log_backup_count: 5

  # Monitoring Configuration
  monitoring_enabled: true
  performance_tracking_enabled: true
  metrics_enabled: true
  max_cpu_threshold: 80.0
  max_memory_threshold: 90.0
  max_disk_threshold: 90.0

  # Performance Tracking Configuration
  performance_log_threshold: 0.5  # Log operations taking longer than 0.5 seconds
  performance_warning_threshold: 2.0  # Warn about operations taking longer than 2 seconds
  performance_critical_threshold: 5.0  # Critical alert for operations taking longer than 5 seconds

  # Retry Configuration
  retry_max_attempts: 3
  retry_initial_delay: 1.0
  retry_max_delay: 60.0
  retry_backoff_factor: 2.0
  retry_jitter: true
  retry_timeout: 30.0
  retry_on_exceptions: []

  # Fallback Configuration
  fallback_max_attempts: 3
  fallback_timeout: 10.0
  fallback_circuit_breaker: true
  fallback_circuit_breaker_threshold: 5
  fallback_circuit_breaker_timeout: 60

# API Configuration
api:
  host: 0.0.0.0
  port: 8000
  cors_origins: 
    - http://localhost:3000
    - https://tradingview-bot.example.com

# Database Configuration
database:
  url: sqlite:///./local_dev.db
  use_supabase: false
  pool_size: 5
  max_overflow: 10

# Pipeline and AI Configuration
pipeline:
  timeout: 30.0
  max_retries: 3
  parallel_execution: false
  stage_timeout: 15.0
  model: moonshotai/kimi-k2:free
  temperature: 0.7
  max_tokens: 2000
  openrouter_api_key: ${OPENROUTER_API_KEY}  # Use environment variable
  openrouter_base_url: https://openrouter.ai/api/v1

# Data Provider Configuration
data_providers:
  alpha_vantage:
    enabled: true
    api_key: ${ALPHA_VANTAGE_API_KEY}  # Use environment variable
    rate_limit: 5
    timeout: 10.0
    cache_ttl: 300  # 5 minutes
  yahoo_finance:
    enabled: true
    rate_limit: 5
  polygon:
    enabled: true
    rate_limit: 5
  finnhub:
    enabled: true
    rate_limit: 30 