# Core Application Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.7  # PostgreSQL adapter
redis==5.0.1
yfinance==0.2.18
pandas==2.1.1
numpy==1.24.3
aiohttp==3.9.1  # Async HTTP client for ticker data refresh
# Note: ta-lib requires system-level installation
# Install via: sudo apt-get install libta-lib0 python3-ta-lib
discord.py==2.3.2
python-jose[cryptography]==3.3.0  # JWT token support
passlib[bcrypt]==1.7.4
pyotp==2.9.0
PyJWT==2.8.0  # JWT token support
psutil==5.9.6  # System monitoring
email-validator==2.1.0  # Email validation for Pydantic
python-multipart==0.0.6  # Form data support for FastAPI

# AI and Machine Learning Dependencies
openai==1.3.7  # OpenAI API client for OpenRouter integration
python-dotenv==1.0.0  # Environment variable management

# Testing Dependencies
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio==0.23.2
httpx==0.24.1

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

pydantic-settings==2.1.0  # Configuration management
tenacity==8.2.3  # Retry library for API calls and data fetching

celery==5.3.4  # Asynchronous task queue for background processing

supabase==2.1.0  # Supabase client library

# Add aiohttp for async HTTP requests to data providers
aiohttp>=3.8.0

# Scheduling and Background Tasks
APScheduler==3.10.4  # Advanced Python Scheduler for cache warming jobs
pytz==2023.3  # Timezone support for scheduler

# Monitoring and Metrics
prometheus-client==0.19.0  # Prometheus metrics collection and exposition

# Secrets Management
cryptography==41.0.3  # For secure secret generation and management

yfinance
