#!/usr/bin/env python3
"""
Simple Docker test for /ask command order of operations.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

async def test_ask_command_simple():
    """Test the /ask command in Docker environment."""
    print("🐳 Testing /ask command in Docker...")
    
    try:
        # Test 1: Import the pipeline function
        print("📦 Testing imports...")
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        print("✅ execute_ask_pipeline imported successfully")
        
        from src.bot.pipeline.commands.ask.config import AskPipelineConfig
        print("✅ AskPipelineConfig imported successfully")
        
        from src.bot.pipeline.commands.ask.stages.ai_service_wrapper import AIChatProcessor
        print("✅ AIChatProcessor imported successfully")
        
        # Test 2: Create configuration
        print("\n🔧 Testing configuration...")
        config = AskPipelineConfig()
        print(f"✅ Config created: model={config.model}, enabled={config.enabled}")
        
        # Test 3: Test AI processor
        print("\n🤖 Testing AI processor...")
        processor = AIChatProcessor(config.to_dict())
        print("✅ AIChatProcessor created successfully")
        
        # Test 4: Test simple processing
        print("\n💬 Testing simple query processing...")
        test_query = "What is RSI indicator?"
        result = await processor.process(test_query)
        print(f"✅ Processor returned result type: {type(result)}")
        print(f"✅ Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict) and 'response' in result:
            response = result['response']
            print(f"✅ Got response: {response[:100]}...")
            
            # Check if it mentions tools or analysis
            if any(word in response.lower() for word in ['analysis', 'indicator', 'technical', 'market']):
                print("✅ Response contains relevant trading content")
            else:
                print("⚠️ Response may not contain trading analysis")
        else:
            print("⚠️ No 'response' key in result")
        
        # Test 5: Test full pipeline
        print("\n🚀 Testing full pipeline...")
        pipeline_result = await execute_ask_pipeline(
            query="Analyze AAPL technical indicators",
            user_id="test_user_123",
            guild_id="test_guild_456"
        )
        
        print(f"✅ Pipeline executed, result type: {type(pipeline_result)}")
        
        if hasattr(pipeline_result, 'processing_results'):
            print(f"✅ Pipeline has processing_results: {list(pipeline_result.processing_results.keys())}")
            
            if 'response' in pipeline_result.processing_results:
                pipeline_response = pipeline_result.processing_results['response']
                print(f"✅ Pipeline response: {pipeline_response[:100]}...")
            else:
                print("⚠️ No 'response' in pipeline processing_results")
        else:
            print("⚠️ Pipeline result has no processing_results attribute")
        
        if hasattr(pipeline_result, 'status'):
            print(f"✅ Pipeline status: {pipeline_result.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_discord_integration():
    """Test Discord command integration."""
    print("\n🤖 Testing Discord integration...")
    
    try:
        from src.bot.commands.ask import SmartAskCommand
        print("✅ SmartAskCommand imported successfully")
        
        # Create mock bot
        class MockBot:
            pass
        
        # Create command instance
        ask_command = SmartAskCommand(MockBot())
        print("✅ SmartAskCommand instance created")
        
        # Test response extraction with mock context
        class MockContext:
            def __init__(self):
                self.processing_results = {
                    'response': 'AAPL technical analysis shows bullish momentum with RSI at 65.4.',
                    'tools_used': ['real_time_price', 'technical_indicators'],
                    'intent': 'technical_analysis'
                }
                self.status = type('Status', (), {'value': 'completed'})()
        
        mock_context = MockContext()
        extracted_response = ask_command._extract_response_from_context(mock_context)
        print(f"✅ Response extraction works: {extracted_response[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Discord integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Docker /ask command tests...\n")
    
    test1_passed = await test_ask_command_simple()
    test2_passed = await test_discord_integration()
    
    if test1_passed and test2_passed:
        print("\n🎉 All Docker tests passed!")
        print("\n📋 Summary:")
        print("✅ All imports working in Docker")
        print("✅ Configuration system working")
        print("✅ AI processor working")
        print("✅ Pipeline execution working")
        print("✅ Discord integration working")
        print("\n🔧 Your /ask command should now work properly!")
        print("Try testing with real Discord bot commands like:")
        print("  /ask What's the RSI indicator?")
        print("  /ask Analyze AAPL technical indicators")
        print("  /ask What's the risk level for TSLA?")
    else:
        print("\n❌ Some tests failed in Docker environment.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
