#!/usr/bin/env python3
"""
Duplicate Utility Detector for Task 4.2
Detects duplicate utility functions and classes across the codebase
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict
import hashlib

class DuplicateUtilityDetector:
    """Detects duplicate utility functions and classes across Python files."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.function_signatures = defaultdict(list)
        self.class_signatures = defaultdict(list)
        self.function_hashes = defaultdict(list)
        self.class_hashes = defaultdict(list)
        
    def analyze_file(self, file_path: Path) -> Tuple[List[Dict], List[Dict]]:
        """Analyze a single Python file for functions and classes."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            functions = []
            classes = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = self._extract_function_info(node, file_path)
                    if func_info:
                        functions.append(func_info)
                        
                elif isinstance(node, ast.ClassDef):
                    class_info = self._extract_class_info(node, file_path)
                    if class_info:
                        classes.append(class_info)
            
            return functions, classes
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return [], []
    
    def _extract_function_info(self, node: ast.FunctionDef, file_path: Path) -> Optional[Dict]:
        """Extract information about a function."""
        try:
            # Get function signature
            args = [arg.arg for arg in node.args.args]
            signature = f"{node.name}({', '.join(args)})"
            
            # Get function body hash (for similarity detection)
            body_lines = []
            for line in ast.unparse(node).split('\n'):
                if line.strip() and not line.strip().startswith('def '):
                    body_lines.append(line.strip())
            
            body_text = '\n'.join(body_lines)
            body_hash = hashlib.md5(body_text.encode()).hexdigest()[:8]
            
            return {
                'name': node.name,
                'signature': signature,
                'args': args,
                'body_hash': body_hash,
                'file': str(file_path),
                'line': node.lineno,
                'body_lines': len(body_lines)
            }
        except Exception:
            return None
    
    def _extract_class_info(self, node: ast.ClassDef, file_path: Path) -> Optional[Dict]:
        """Extract information about a class."""
        try:
            # Get class methods
            methods = []
            for item in node.body:
                if isinstance(item, ast.FunctionDef):
                    methods.append(item.name)
            
            # Get class signature
            bases = [ast.unparse(base) for base in node.bases]
            signature = f"{node.name}({', '.join(bases)})" if bases else node.name
            
            # Get class body hash
            body_lines = []
            for line in ast.unparse(node).split('\n'):
                if line.strip() and not line.strip().startswith('class '):
                    body_lines.append(line.strip())
            
            body_text = '\n'.join(body_lines)
            body_hash = hashlib.md5(body_text.encode()).hexdigest()[:8]
            
            return {
                'name': node.name,
                'signature': signature,
                'bases': bases,
                'methods': methods,
                'body_hash': body_hash,
                'file': str(file_path),
                'line': node.lineno,
                'body_lines': len(body_lines)
            }
        except Exception:
            return None
    
    def build_signature_indexes(self):
        """Build indexes of functions and classes by signature."""
        python_files = list(self.src_dir.rglob("*.py"))
        
        for file_path in python_files:
            functions, classes = self.analyze_file(file_path)
            
            # Index functions by signature
            for func in functions:
                self.function_signatures[func['signature']].append(func)
            
            # Index classes by signature
            for cls in classes:
                self.class_signatures[cls['signature']].append(cls)
    
    def find_duplicate_functions(self) -> List[List[Dict]]:
        """Find functions with identical signatures."""
        duplicates = []
        
        for signature, funcs in self.function_signatures.items():
            if len(funcs) > 1:
                # Group by body hash to find truly similar functions
                hash_groups = defaultdict(list)
                for func in funcs:
                    hash_groups[func['body_hash']].append(func)
                
                # Find groups with multiple functions (potential duplicates)
                for body_hash, func_group in hash_groups.items():
                    if len(func_group) > 1:
                        duplicates.append(func_group)
        
        return duplicates
    
    def find_duplicate_classes(self) -> List[List[Dict]]:
        """Find classes with identical signatures."""
        duplicates = []
        
        for signature, classes in self.class_signatures.items():
            if len(classes) > 1:
                # Group by body hash to find truly similar classes
                hash_groups = defaultdict(list)
                for cls in classes:
                    hash_groups[cls['body_hash']].append(cls)
                
                # Find groups with multiple classes (potential duplicates)
                for body_hash, class_group in hash_groups.items():
                    if len(class_group) > 1:
                        duplicates.append(class_group)
        
        return duplicates
    
    def find_similar_functions(self, similarity_threshold: float = 0.8) -> List[List[Dict]]:
        """Find functions with similar implementations."""
        similar_groups = []
        processed = set()
        
        for signature, funcs in self.function_signatures.items():
            if len(funcs) < 2:
                continue
                
            for i, func1 in enumerate(funcs):
                for j, func2 in enumerate(funcs[i+1:], i+1):
                    if (func1['file'], func1['line']) in processed or (func2['file'], func2['line']) in processed:
                        continue
                    
                    # Calculate similarity based on body lines and hash
                    if func1['body_hash'] == func2['body_hash']:
                        similarity = 1.0
                    else:
                        # Simple similarity based on body length
                        len_diff = abs(func1['body_lines'] - func2['body_lines'])
                        max_len = max(func1['body_lines'], func2['body_lines'])
                        similarity = 1.0 - (len_diff / max_len) if max_len > 0 else 0.0
                    
                    if similarity >= similarity_threshold:
                        similar_groups.append([func1, func2])
                        processed.add((func1['file'], func1['line']))
                        processed.add((func2['file'], func2['line']))
        
        return similar_groups
    
    def generate_report(self) -> Dict[str, any]:
        """Generate a comprehensive report of duplicate utilities."""
        self.build_signature_indexes()
        
        duplicate_functions = self.find_duplicate_functions()
        duplicate_classes = self.find_duplicate_classes()
        similar_functions = self.find_similar_functions()
        
        # Calculate statistics
        total_functions = sum(len(funcs) for funcs in self.function_signatures.values())
        total_classes = sum(len(classes) for classes in self.class_signatures.values())
        
        duplicate_func_count = sum(len(group) for group in duplicate_functions)
        duplicate_class_count = sum(len(group) for group in duplicate_classes)
        
        return {
            "duplicate_functions": duplicate_functions,
            "duplicate_classes": duplicate_classes,
            "similar_functions": similar_functions,
            "statistics": {
                "total_functions": total_functions,
                "total_classes": total_classes,
                "duplicate_functions": duplicate_func_count,
                "duplicate_classes": duplicate_class_count,
                "duplicate_function_groups": len(duplicate_functions),
                "duplicate_class_groups": len(duplicate_classes),
                "similar_function_pairs": len(similar_functions)
            }
        }

def main():
    """Main function to run duplicate utility detection."""
    print("🔍 Starting Duplicate Utility Detection for Task 4.2")
    print("=" * 60)
    
    detector = DuplicateUtilityDetector()
    report = detector.generate_report()
    
    stats = report["statistics"]
    print(f"📊 Analysis Complete!")
    print(f"Total functions analyzed: {stats['total_functions']}")
    print(f"Total classes analyzed: {stats['total_classes']}")
    print(f"Duplicate functions found: {stats['duplicate_functions']}")
    print(f"Duplicate classes found: {stats['duplicate_classes']}")
    print()
    
    # Report duplicate functions
    if report["duplicate_functions"]:
        print("🚨 Duplicate Functions Found:")
        print("-" * 40)
        for i, group in enumerate(report["duplicate_functions"], 1):
            print(f"{i}. Function: {group[0]['name']}")
            for func in group:
                print(f"   - {func['file']}:{func['line']} (body hash: {func['body_hash']})")
            print()
    else:
        print("✅ No duplicate functions detected!")
        print()
    
    # Report duplicate classes
    if report["duplicate_classes"]:
        print("🚨 Duplicate Classes Found:")
        print("-" * 40)
        for i, group in enumerate(report["duplicate_classes"], 1):
            print(f"{i}. Class: {group[0]['name']}")
            for cls in group:
                print(f"   - {cls['file']}:{cls['line']} (body hash: {cls['body_hash']})")
            print()
    else:
        print("✅ No duplicate classes detected!")
        print()
    
    # Report similar functions
    if report["similar_functions"]:
        print("⚠️  Similar Functions (Potential Duplicates):")
        print("-" * 40)
        for i, pair in enumerate(report["similar_functions"][:10], 1):  # Show first 10
            func1, func2 = pair
            print(f"{i}. {func1['name']} vs {func2['name']}")
            print(f"   - {func1['file']}:{func1['line']}")
            print(f"   - {func2['file']}:{func2['line']}")
            print()
    else:
        print("✅ No similar functions detected!")
        print()
    
    print("🎯 Recommendations:")
    print("-" * 40)
    if report["duplicate_functions"] or report["duplicate_classes"]:
        print("1. Consolidate duplicate functions into shared utility modules")
        print("2. Merge duplicate classes using inheritance or composition")
        print("3. Update all references to use consolidated implementations")
    
    if report["similar_functions"]:
        print("4. Review similar functions for potential consolidation")
        print("5. Consider creating parameterized versions for similar logic")
    
    print("6. Use __init__.py files to expose consolidated utilities")
    print("7. Add deprecation warnings before removing old implementations")
    
    return report

if __name__ == "__main__":
    main() 