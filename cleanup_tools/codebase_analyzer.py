#!/usr/bin/env python3
"""
Codebase Analyzer for Cleanup Operations

Analyzes the codebase to identify cleanup opportunities and assess
the risk level of potential changes.
"""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class ImportAnalysis:
    """Analysis of imports in a file"""
    file_path: str
    used_imports: Set[str] = field(default_factory=set)
    unused_imports: Set[str] = field(default_factory=set)
    circular_imports: List[str] = field(default_factory=list)
    missing_imports: Set[str] = field(default_factory=set)
    import_order_issues: List[str] = field(default_factory=list)

@dataclass
class ConfigAnalysis:
    """Analysis of configuration usage"""
    hardcoded_values: List[Dict[str, Any]] = field(default_factory=list)
    config_classes: List[str] = field(default_factory=list)
    env_var_usage: List[str] = field(default_factory=list)
    duplicate_configs: List[Tuple[str, str]] = field(default_factory=list)

@dataclass
class DuplicationAnalysis:
    """Analysis of code duplication"""
    duplicate_functions: List[Dict[str, Any]] = field(default_factory=list)
    duplicate_classes: List[Dict[str, Any]] = field(default_factory=list)
    similar_files: List[Tuple[str, str, float]] = field(default_factory=list)

@dataclass
class CleanupOpportunity:
    """Represents a cleanup opportunity"""
    file_path: str
    opportunity_type: str
    description: str
    risk_level: str  # 'low', 'medium', 'high'
    estimated_impact: str
    details: Dict[str, Any] = field(default_factory=dict)

class CodebaseAnalyzer:
    """Analyzes codebase for cleanup opportunities"""
    
    def __init__(self, root_path: str = "src"):
        """
        Initialize codebase analyzer
        
        Args:
            root_path: Root path to analyze
        """
        self.root_path = Path(root_path)
        self.python_files = []
        self.analysis_results = {}
        
        # Patterns for detecting hardcoded values
        self.hardcoded_patterns = {
            'localhost': re.compile(r'["\']localhost["\']|["\']127\.0\.0\.1["\']'),
            'ports': re.compile(r':\s*(?:8000|8001|3000|6379)\b'),
            'urls': re.compile(r'["\'](?:redis://|sqlite://|postgresql://)[^"\']*["\']'),
            'api_keys': re.compile(r'["\'][A-Za-z0-9]{20,}["\']'),
        }
        
        logger.info(f"Codebase analyzer initialized for: {self.root_path}")
    
    def scan_python_files(self) -> List[str]:
        """Scan for all Python files in the codebase"""
        self.python_files = []
        
        for file_path in self.root_path.rglob("*.py"):
            if not any(part.startswith('.') for part in file_path.parts):
                self.python_files.append(str(file_path))
        
        logger.info(f"Found {len(self.python_files)} Python files")
        return self.python_files
    
    def analyze_imports(self, file_path: str) -> ImportAnalysis:
        """Analyze imports in a Python file"""
        analysis = ImportAnalysis(file_path=file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            # Extract imports
            imports = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            imports.add(f"{node.module}.{alias.name}")
            
            # Find used names in code
            used_names = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    used_names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # Handle attribute access like module.function
                    if isinstance(node.value, ast.Name):
                        used_names.add(node.value.id)
            
            # Determine unused imports (simplified heuristic)
            for imp in imports:
                base_name = imp.split('.')[0]
                if base_name in used_names or any(name.startswith(base_name) for name in used_names):
                    analysis.used_imports.add(imp)
                else:
                    analysis.unused_imports.add(imp)
            
            # Check import order (simplified)
            import_lines = []
            for line_num, line in enumerate(content.split('\n'), 1):
                stripped = line.strip()
                if stripped.startswith(('import ', 'from ')) and not stripped.startswith('#'):
                    import_lines.append((line_num, stripped))
            
            # Basic import order check
            stdlib_imports = []
            third_party_imports = []
            local_imports = []
            
            for line_num, import_line in import_lines:
                if 'from src.' in import_line or 'import src.' in import_line:
                    local_imports.append(line_num)
                elif any(lib in import_line for lib in ['os', 'sys', 'json', 'asyncio', 'logging', 'datetime', 'typing']):
                    stdlib_imports.append(line_num)
                else:
                    third_party_imports.append(line_num)
            
            # Check if imports are properly ordered
            all_import_lines = stdlib_imports + third_party_imports + local_imports
            if import_lines and all_import_lines != sorted([line[0] for line in import_lines]):
                analysis.import_order_issues.append("Imports not properly ordered (stdlib, third-party, local)")
            
        except Exception as e:
            logger.warning(f"Failed to analyze imports in {file_path}: {e}")
        
        return analysis
    
    def analyze_configuration(self) -> ConfigAnalysis:
        """Analyze configuration usage across the codebase"""
        analysis = ConfigAnalysis()
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find hardcoded values
                for pattern_name, pattern in self.hardcoded_patterns.items():
                    matches = pattern.finditer(content)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        analysis.hardcoded_values.append({
                            'file': file_path,
                            'line': line_num,
                            'type': pattern_name,
                            'value': match.group(),
                            'context': content.split('\n')[line_num-1].strip()
                        })
                
                # Find configuration classes
                if 'Config' in content and 'class' in content:
                    for line_num, line in enumerate(content.split('\n'), 1):
                        if 'class' in line and 'Config' in line:
                            class_name = re.search(r'class\s+(\w*Config\w*)', line)
                            if class_name:
                                analysis.config_classes.append(f"{file_path}:{line_num}:{class_name.group(1)}")
                
                # Find environment variable usage
                env_patterns = [
                    r'os\.getenv\(["\']([^"\']+)["\']',
                    r'os\.environ\[["\']([^"\']+)["\']',
                    r'getenv\(["\']([^"\']+)["\']'
                ]
                
                for pattern in env_patterns:
                    matches = re.finditer(pattern, content)
                    for match in matches:
                        analysis.env_var_usage.append(f"{file_path}:{match.group(1)}")
                
            except Exception as e:
                logger.warning(f"Failed to analyze configuration in {file_path}: {e}")
        
        return analysis
    
    def analyze_duplication(self) -> DuplicationAnalysis:
        """Analyze code duplication across the codebase"""
        analysis = DuplicationAnalysis()
        
        # Find duplicate file names (simplified check)
        file_names = defaultdict(list)
        for file_path in self.python_files:
            name = os.path.basename(file_path)
            file_names[name].append(file_path)
        
        for name, paths in file_names.items():
            if len(paths) > 1:
                # Calculate similarity (simplified - just check file size)
                sizes = [(path, os.path.getsize(path)) for path in paths]
                for i, (path1, size1) in enumerate(sizes):
                    for path2, size2 in sizes[i+1:]:
                        similarity = 1.0 - abs(size1 - size2) / max(size1, size2)
                        if similarity > 0.8:  # 80% similar by size
                            analysis.similar_files.append((path1, path2, similarity))
        
        # Find duplicate function names (simplified)
        function_signatures = defaultdict(list)
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # Create simple signature
                        args = [arg.arg for arg in node.args.args]
                        signature = f"{node.name}({', '.join(args)})"
                        function_signatures[signature].append(file_path)
                
            except Exception as e:
                logger.warning(f"Failed to analyze functions in {file_path}: {e}")
        
        # Find functions with same signature in multiple files
        for signature, files in function_signatures.items():
            if len(files) > 1:
                analysis.duplicate_functions.append({
                    'signature': signature,
                    'files': files,
                    'count': len(files)
                })
        
        return analysis
    
    def identify_cleanup_opportunities(self) -> List[CleanupOpportunity]:
        """Identify all cleanup opportunities in the codebase"""
        opportunities = []
        
        # Scan files first
        self.scan_python_files()
        
        # Analyze imports for each file
        for file_path in self.python_files:
            import_analysis = self.analyze_imports(file_path)
            
            if import_analysis.unused_imports:
                opportunities.append(CleanupOpportunity(
                    file_path=file_path,
                    opportunity_type="unused_imports",
                    description=f"Remove {len(import_analysis.unused_imports)} unused imports",
                    risk_level="low",
                    estimated_impact="Reduced file size, cleaner code",
                    details={"unused_imports": list(import_analysis.unused_imports)}
                ))
            
            if import_analysis.import_order_issues:
                opportunities.append(CleanupOpportunity(
                    file_path=file_path,
                    opportunity_type="import_order",
                    description="Fix import ordering",
                    risk_level="low",
                    estimated_impact="Better code organization",
                    details={"issues": import_analysis.import_order_issues}
                ))
        
        # Analyze configuration
        config_analysis = self.analyze_configuration()
        
        if config_analysis.hardcoded_values:
            for hardcoded in config_analysis.hardcoded_values:
                opportunities.append(CleanupOpportunity(
                    file_path=hardcoded['file'],
                    opportunity_type="hardcoded_values",
                    description=f"Replace hardcoded {hardcoded['type']} with configuration",
                    risk_level="medium",
                    estimated_impact="Better configurability and security",
                    details=hardcoded
                ))
        
        # Analyze duplication
        duplication_analysis = self.analyze_duplication()
        
        for similar_files in duplication_analysis.similar_files:
            opportunities.append(CleanupOpportunity(
                file_path=similar_files[0],
                opportunity_type="duplicate_files",
                description=f"Consolidate similar files (similarity: {similar_files[2]:.1%})",
                risk_level="high",
                estimated_impact="Reduced maintenance burden",
                details={
                    "similar_file": similar_files[1],
                    "similarity": similar_files[2]
                }
            ))
        
        for duplicate_func in duplication_analysis.duplicate_functions:
            if duplicate_func['count'] > 1:
                opportunities.append(CleanupOpportunity(
                    file_path=duplicate_func['files'][0],
                    opportunity_type="duplicate_functions",
                    description=f"Consolidate duplicate function: {duplicate_func['signature']}",
                    risk_level="medium",
                    estimated_impact="Reduced code duplication",
                    details=duplicate_func
                ))
        
        logger.info(f"Identified {len(opportunities)} cleanup opportunities")
        return opportunities
    
    def generate_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""
        opportunities = self.identify_cleanup_opportunities()
        
        # Categorize opportunities by risk level
        risk_categories = defaultdict(list)
        type_categories = defaultdict(list)
        
        for opp in opportunities:
            risk_categories[opp.risk_level].append(opp)
            type_categories[opp.opportunity_type].append(opp)
        
        report = {
            "summary": {
                "total_files_analyzed": len(self.python_files),
                "total_opportunities": len(opportunities),
                "by_risk_level": {level: len(opps) for level, opps in risk_categories.items()},
                "by_type": {type_name: len(opps) for type_name, opps in type_categories.items()}
            },
            "opportunities": [
                {
                    "file_path": opp.file_path,
                    "type": opp.opportunity_type,
                    "description": opp.description,
                    "risk_level": opp.risk_level,
                    "estimated_impact": opp.estimated_impact,
                    "details": opp.details
                }
                for opp in opportunities
            ],
            "recommendations": self._generate_recommendations(opportunities)
        }
        
        return report
    
    def _generate_recommendations(self, opportunities: List[CleanupOpportunity]) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        # Count opportunities by type and risk
        low_risk_count = sum(1 for opp in opportunities if opp.risk_level == "low")
        medium_risk_count = sum(1 for opp in opportunities if opp.risk_level == "medium")
        high_risk_count = sum(1 for opp in opportunities if opp.risk_level == "high")
        
        if low_risk_count > 0:
            recommendations.append(f"Start with {low_risk_count} low-risk improvements (import cleanup, formatting)")
        
        if medium_risk_count > 0:
            recommendations.append(f"Address {medium_risk_count} medium-risk items (configuration, minor refactoring)")
        
        if high_risk_count > 0:
            recommendations.append(f"Carefully plan {high_risk_count} high-risk changes (file consolidation, major refactoring)")
        
        # Specific recommendations
        unused_imports = sum(1 for opp in opportunities if opp.opportunity_type == "unused_imports")
        if unused_imports > 5:
            recommendations.append("Consider automated import cleanup tools for efficiency")
        
        hardcoded_values = sum(1 for opp in opportunities if opp.opportunity_type == "hardcoded_values")
        if hardcoded_values > 0:
            recommendations.append("Prioritize configuration cleanup for better security and deployment flexibility")
        
        return recommendations

# Global analyzer instance
analyzer = CodebaseAnalyzer()