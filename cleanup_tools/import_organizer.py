#!/usr/bin/env python3
"""
Import Organization Standardizer for Task 3.3
Organizes imports according to PEP 8 standards and ensures consistency
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict
import re

class ImportOrganizer:
    """Organizes imports according to PEP 8 standards."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.import_patterns = defaultdict(list)
        self.import_issues = []
        
    def analyze_file(self, file_path: Path) -> Dict[str, any]:
        """Analyze import organization in a single Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            file_analysis = {
                'imports': [],
                'from_imports': [],
                'issues': [],
                'has_type_hints': False
            }
            
            # Extract imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        file_analysis['imports'].append({
                            'module': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })
                        
                elif isinstance(node, ast.ImportFrom):
                    file_analysis['from_imports'].append({
                        'module': node.module or '',
                        'names': [name.name for name in node.names],
                        'line': node.lineno
                    })
                    
                elif isinstance(node, ast.AnnAssign) or isinstance(node, ast.arg):
                    # Check for type hints
                    if hasattr(node, 'annotation') and node.annotation:
                        file_analysis['has_type_hints'] = True
            
            # Analyze import organization
            issues = self._analyze_import_organization(file_analysis, file_path)
            file_analysis['issues'] = issues
            
            return file_analysis
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return {'imports': [], 'from_imports': [], 'issues': [], 'has_type_hints': False}
    
    def _analyze_import_organization(self, analysis: Dict, file_path: Path) -> List[Dict]:
        """Analyze import organization for PEP 8 compliance."""
        issues = []
        
        # Check import order
        all_imports = []
        for imp in analysis['imports']:
            all_imports.append(('import', imp['module'], imp['line']))
        
        for imp in analysis['from_imports']:
            all_imports.append(('from', imp['module'], imp['line']))
        
        # Sort by line number to check order
        all_imports.sort(key=lambda x: x[2])
        
        # Check if imports are grouped correctly
        stdlib_imports = []
        third_party_imports = []
        local_imports = []
        
        for imp_type, module, line in all_imports:
            if self._is_stdlib_module(module):
                stdlib_imports.append((imp_type, module, line))
            elif self._is_local_module(module):
                local_imports.append((imp_type, module, line))
            else:
                third_party_imports.append((imp_type, module, line))
        
        # Check PEP 8 ordering: stdlib -> third-party -> local
        expected_order = stdlib_imports + third_party_imports + local_imports
        
        if all_imports != expected_order:
            issues.append({
                'type': 'import_order',
                'message': 'Imports not in PEP 8 order (stdlib -> third-party -> local)',
                'line': all_imports[0][2] if all_imports else 0
            })
        
        # Check for blank lines between import groups
        if stdlib_imports and third_party_imports:
            if not self._has_blank_line_between(stdlib_imports[-1][2], third_party_imports[0][2], file_path):
                issues.append({
                    'type': 'missing_blank_line',
                    'message': 'Missing blank line between stdlib and third-party imports',
                    'line': third_party_imports[0][2]
                })
        
        if third_party_imports and local_imports:
            if not self._has_blank_line_between(third_party_imports[-1][2], local_imports[0][2], file_path):
                issues.append({
                    'type': 'missing_blank_line',
                    'message': 'Missing blank line between third-party and local imports',
                    'line': local_imports[0][2]
                })
        
        # Check for unused imports
        for imp in analysis['imports']:
            if not self._is_import_used(imp['module'], imp['alias'], file_path):
                issues.append({
                    'type': 'unused_import',
                    'message': f'Unused import: {imp["module"]}',
                    'line': imp['line']
                })
        
        return issues
    
    def _is_stdlib_module(self, module: str) -> bool:
        """Check if a module is part of Python standard library."""
        stdlib_modules = {
            'os', 'sys', 're', 'json', 'datetime', 'time', 'logging', 'pathlib',
            'typing', 'collections', 'asyncio', 'functools', 'itertools',
            'abc', 'contextlib', 'dataclasses', 'enum', 'hashlib', 'random',
            'traceback', 'urllib', 'http', 'ssl', 'socket', 'threading',
            'multiprocessing', 'subprocess', 'signal', 'tempfile', 'shutil',
            'glob', 'fnmatch', 'pickle', 'copy', 'weakref', 'inspect',
            'ast', 'tokenize', 'keyword', 'token', 'builtins', 'types'
        }
        
        # Handle submodules
        base_module = module.split('.')[0]
        return base_module in stdlib_modules
    
    def _is_local_module(self, module: str) -> bool:
        """Check if a module is a local project module."""
        local_prefixes = {'src.', 'bot.', 'api.', 'core.', 'shared.', 'analysis.', 'database.'}
        return any(module.startswith(prefix) for prefix in local_prefixes)
    
    def _has_blank_line_between(self, line1: int, line2: int, file_path: Path) -> bool:
        """Check if there's a blank line between two line numbers."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # Check lines between line1 and line2 (1-indexed)
            for i in range(line1, line2 - 1):
                if i < len(lines) and lines[i].strip() == '':
                    return True
            return False
        except Exception:
            return False
    
    def _is_import_used(self, module: str, alias: str, file_path: Path) -> bool:
        """Check if an import is actually used in the file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Simple check - look for the module name or alias in the content
            search_term = alias if alias else module.split('.')[-1]
            return search_term in content
        except Exception:
            return False
    
    def generate_organized_imports(self, file_path: Path) -> str:
        """Generate organized imports for a file."""
        analysis = self.analyze_file(file_path)
        
        # Group imports by type
        stdlib_imports = []
        third_party_imports = []
        local_imports = []
        
        for imp in analysis['imports']:
            if self._is_stdlib_module(imp['module']):
                stdlib_imports.append(imp)
            elif self._is_local_module(imp['module']):
                local_imports.append(imp)
            else:
                third_party_imports.append(imp)
        
        for imp in analysis['from_imports']:
            if self._is_stdlib_module(imp['module']):
                stdlib_imports.append(imp)
            elif self._is_local_module(imp['module']):
                local_imports.append(imp)
            else:
                third_party_imports.append(imp)
        
        # Sort imports within each group
        def sort_key(imp):
            if isinstance(imp, dict) and 'module' in imp:
                return imp['module']
            return imp.get('module', '')
        
        stdlib_imports.sort(key=sort_key)
        third_party_imports.sort(key=sort_key)
        local_imports.sort(key=sort_key)
        
        # Generate organized import text
        organized_imports = []
        
        # Standard library imports
        if stdlib_imports:
            for imp in stdlib_imports:
                if isinstance(imp, dict) and 'module' in imp:
                    if 'names' in imp:  # from import
                        names_str = ', '.join(imp['names'])
                        organized_imports.append(f"from {imp['module']} import {names_str}")
                    else:  # regular import
                        if imp.get('alias'):
                            organized_imports.append(f"import {imp['module']} as {imp['alias']}")
                        else:
                            organized_imports.append(f"import {imp['module']}")
            organized_imports.append('')
        
        # Third-party imports
        if third_party_imports:
            for imp in third_party_imports:
                if isinstance(imp, dict) and 'module' in imp:
                    if 'names' in imp:  # from import
                        names_str = ', '.join(imp['names'])
                        organized_imports.append(f"from {imp['module']} import {names_str}")
                    else:  # regular import
                        if imp.get('alias'):
                            organized_imports.append(f"import {imp['module']} as {imp['alias']}")
                        else:
                            organized_imports.append(f"import {imp['module']}")
            organized_imports.append('')
        
        # Local imports
        if local_imports:
            for imp in local_imports:
                if isinstance(imp, dict) and 'module' in imp:
                    if 'names' in imp:  # from import
                        names_str = ', '.join(imp['names'])
                        organized_imports.append(f"from {imp['module']} import {names_str}")
                    else:  # regular import
                        if imp.get('alias'):
                            organized_imports.append(f"import {imp['module']} as {imp['alias']}")
                        else:
                            organized_imports.append(f"import {imp['module']}")
        
        return '\n'.join(organized_imports)
    
    def analyze_codebase(self) -> Dict[str, any]:
        """Analyze import organization across the entire codebase."""
        python_files = list(self.src_dir.rglob("*.py"))
        
        total_files = len(python_files)
        files_with_issues = 0
        total_issues = 0
        import_stats = {
            'total_imports': 0,
            'stdlib_imports': 0,
            'third_party_imports': 0,
            'local_imports': 0,
            'files_with_type_hints': 0
        }
        
        for file_path in python_files:
            analysis = self.analyze_file(file_path)
            
            if analysis['issues']:
                files_with_issues += 1
                total_issues += len(analysis['issues'])
            
            import_stats['total_imports'] += len(analysis['imports']) + len(analysis['from_imports'])
            
            if analysis['has_type_hints']:
                import_stats['files_with_type_hints'] += 1
        
        return {
            'total_files': total_files,
            'files_with_issues': files_with_issues,
            'total_issues': total_issues,
            'import_stats': import_stats,
            'files': python_files
        }

def main():
    """Main function to run import organization analysis."""
    print("🔍 Starting Import Organization Analysis for Task 3.3")
    print("=" * 60)
    
    organizer = ImportOrganizer()
    results = organizer.analyze_codebase()
    
    print(f"📊 Analysis Complete!")
    print(f"Total Python files: {results['total_files']}")
    print(f"Files with import issues: {results['files_with_issues']}")
    print(f"Total import issues: {results['total_issues']}")
    print()
    
    stats = results['import_stats']
    print(f"📈 Import Statistics:")
    print(f"Total imports: {stats['total_imports']}")
    print(f"Files with type hints: {stats['files_with_type_hints']}")
    print()
    
    if results['files_with_issues'] > 0:
        print("🚨 Import Organization Issues Found:")
        print("-" * 40)
        print(f"• {results['files_with_issues']} files need import reorganization")
        print(f"• {results['total_issues']} total issues to fix")
        print()
        
        print("🎯 Recommendations:")
        print("-" * 40)
        print("1. Reorganize imports according to PEP 8 standards")
        print("2. Group imports: stdlib -> third-party -> local")
        print("3. Add blank lines between import groups")
        print("4. Remove unused imports")
        print("5. Add type hints where missing")
        print("6. Use consistent import formatting")
    else:
        print("✅ All imports are properly organized!")
    
    return results

if __name__ == "__main__":
    main() 