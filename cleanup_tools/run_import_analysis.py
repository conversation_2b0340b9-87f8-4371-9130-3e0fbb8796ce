#!/usr/bin/env python3
"""
Import Analysis Runner for Task 3.1
Analyzes and identifies unused imports in the codebase
"""

import sys
import os
import ast
from pathlib import Path
from typing import Dict, List, Set, <PERSON>ple

def analyze_file_imports(file_path: str) -> Tuple[Set[str], Set[str], List[str]]:
    """
    Analyze imports in a single Python file
    
    Returns:
        Tuple of (used_imports, unused_imports, import_order_issues)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse AST
        tree = ast.parse(content)
        
        # Extract all imports
        imports = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.add(f"{node.module}.{alias.name}")
        
        # Find used names in code
        used_names = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                used_names.add(node.id)
            elif isinstance(node, ast.Attribute):
                # Handle attribute access like module.function
                if isinstance(node.value, ast.Name):
                    used_names.add(node.value.id)
        
        # Determine unused imports (simplified heuristic)
        used_imports = set()
        unused_imports = set()
        
        for imp in imports:
            base_name = imp.split('.')[0]
            if base_name in used_names or any(name.startswith(base_name) for name in used_names):
                used_imports.add(imp)
            else:
                unused_imports.add(imp)
        
        # Check import order
        import_order_issues = []
        lines = content.split('\n')
        import_lines = []
        
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith(('import ', 'from ')) and not stripped.startswith('#'):
                import_lines.append((line_num, stripped))
        
        # Basic import order validation
        if len(import_lines) > 1:
            for i in range(1, len(import_lines)):
                prev_line = import_lines[i-1][1]
                curr_line = import_lines[i][1]
                
                # Check for blank lines between import groups
                prev_line_num = import_lines[i-1][0]
                curr_line_num = import_lines[i][0]
                
                if curr_line_num - prev_line_num > 1:
                    # Check if there should be a blank line
                    if not any(line.strip() == '' for line in lines[prev_line_num:curr_line_num-1]):
                        import_order_issues.append(f"Missing blank line between imports at line {curr_line_num}")
        
        return used_imports, unused_imports, import_order_issues
        
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return set(), set(), []

def main():
    """Main analysis function"""
    print("🔍 Starting Import Analysis for Task 3.1")
    print("=" * 60)
    
    # Find all Python files
    src_dir = Path("src")
    python_files = list(src_dir.rglob("*.py"))
    
    print(f"Found {len(python_files)} Python files to analyze")
    print()
    
    total_unused = 0
    total_import_order_issues = 0
    files_with_issues = []
    
    for file_path in python_files:
        used_imports, unused_imports, import_order_issues = analyze_file_imports(str(file_path))
        
        if unused_imports or import_order_issues:
            files_with_issues.append({
                'file': str(file_path),
                'unused_imports': unused_imports,
                'import_order_issues': import_order_issues
            })
            
            total_unused += len(unused_imports)
            total_import_order_issues += len(import_order_issues)
    
    # Print summary
    print(f"📊 Analysis Complete!")
    print(f"Files with issues: {len(files_with_issues)}")
    print(f"Total unused imports: {total_unused}")
    print(f"Total import order issues: {total_import_order_issues}")
    print()
    
    # Print detailed results
    if files_with_issues:
        print("📋 Files with import issues:")
        print("-" * 40)
        
        for file_info in files_with_issues:
            print(f"\n📁 {file_info['file']}")
            
            if file_info['unused_imports']:
                print("  ❌ Unused imports:")
                for imp in sorted(file_info['unused_imports']):
                    print(f"    - {imp}")
            
            if file_info['import_order_issues']:
                print("  ⚠️  Import order issues:")
                for issue in file_info['import_order_issues']:
                    print(f"    - {issue}")
    
    print()
    print("🎯 Next steps:")
    print("1. Review unused imports for safe removal")
    print("2. Fix import order issues")
    print("3. Run tests to ensure no functionality is broken")
    
    return files_with_issues

if __name__ == "__main__":
    main() 