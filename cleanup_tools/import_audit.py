#!/usr/bin/env python3
"""
Import Audit Script for Task 3.1
Detailed analysis of unused imports to understand patterns and root causes
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict

def analyze_import_usage(file_path: str) -> Dict[str, Any]:
    """
    Detailed analysis of import usage in a single file
    
    Returns:
        Dictionary with detailed import analysis
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse AST
        tree = ast.parse(content)
        
        # Extract all imports with line numbers
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append({
                        'type': 'import',
                        'name': alias.name,
                        'asname': alias.asname,
                        'line': node.lineno,
                        'full_import': f"import {alias.name}"
                    })
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append({
                            'type': 'from_import',
                            'module': node.module,
                            'name': alias.name,
                            'asname': alias.asname,
                            'line': node.lineno,
                            'full_import': f"from {node.module} import {alias.name}"
                        })
        
        # Find all names used in the code
        used_names = set()
        name_usage = defaultdict(list)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                used_names.add(node.id)
                name_usage[node.id].append(node.lineno)
            elif isinstance(node, ast.Attribute):
                if isinstance(node.value, ast.Name):
                    used_names.add(node.value.id)
                    name_usage[node.value.id].append(node.lineno)
        
        # Analyze each import
        import_analysis = []
        for imp in imports:
            if imp['type'] == 'import':
                base_name = imp['name']
                as_name = imp['asname'] if imp['asname'] else imp['name']
            else:  # from_import
                base_name = imp['module']
                as_name = imp['asname'] if imp['asname'] else imp['name']
            
            # Check if this import is used
            is_used = False
            usage_lines = []
            
            if as_name in used_names:
                is_used = True
                usage_lines = name_usage[as_name]
            elif base_name in used_names:
                is_used = True
                usage_lines = name_usage[base_name]
            else:
                # Check for partial matches (e.g., if importing 'pandas' and using 'pd')
                for used_name in used_names:
                    if used_name.startswith(base_name) or base_name.startswith(used_name):
                        is_used = True
                        usage_lines = name_usage[used_name]
                        break
            
            import_analysis.append({
                **imp,
                'is_used': is_used,
                'usage_lines': usage_lines,
                'base_name': base_name,
                'as_name': as_name
            })
        
        # Categorize imports
        used_imports = [imp for imp in import_analysis if imp['is_used']]
        unused_imports = [imp for imp in import_analysis if not imp['is_used']]
        
        # Analyze patterns
        patterns = {
            'total_imports': len(imports),
            'used_imports': len(used_imports),
            'unused_imports': len(unused_imports),
            'unused_percentage': (len(unused_imports) / len(imports) * 100) if imports else 0,
            'import_types': {
                'stdlib': len([imp for imp in unused_imports if not imp['full_import'].startswith(('from src', 'import src'))]),
                'third_party': len([imp for imp in unused_imports if any(pkg in imp['full_import'] for pkg in ['fastapi', 'pandas', 'numpy', 'sqlalchemy', 'discord', 'openai', 'yfinance', 'requests', 'bs4', 'textblob', 'celery', 'apscheduler'])])
            }
        }
        
        return {
            'file_path': file_path,
            'imports': import_analysis,
            'used_imports': used_imports,
            'unused_imports': unused_imports,
            'patterns': patterns,
            'content_preview': content[:500] + "..." if len(content) > 500 else content
        }
        
    except Exception as e:
        return {
            'file_path': file_path,
            'error': str(e),
            'imports': [],
            'used_imports': [],
            'unused_imports': [],
            'patterns': {}
        }

def categorize_unused_imports(analysis_results: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Categorize unused imports by type and reason
    """
    categories = {
        'unused_stdlib': [],
        'unused_third_party': [],
        'unused_local': [],
        'unused_type_hints': [],
        'unused_legacy': [],
        'unused_placeholder': []
    }
    
    for result in analysis_results:
        if 'error' in result:
            continue
            
        for imp in result['unused_imports']:
            import_line = imp['full_import']
            
            # Categorize by type
            if import_line.startswith(('from src', 'import src')):
                categories['unused_local'].append({
                    'file': result['file_path'],
                    'import': import_line,
                    'line': imp['line']
                })
            elif any(pkg in import_line for pkg in ['typing', 'dataclasses', 'enum', 'abc', 'collections', 'functools', 'contextlib', 'pathlib', 'datetime', 'logging', 'json', 'hashlib', 'statistics']):
                categories['unused_stdlib'].append({
                    'file': result['file_path'],
                    'import': import_line,
                    'line': imp['line']
                })
            elif any(pkg in import_line for pkg in ['fastapi', 'pandas', 'numpy', 'sqlalchemy', 'discord', 'openai', 'yfinance', 'requests', 'bs4', 'textblob', 'celery', 'apscheduler']):
                categories['unused_third_party'].append({
                    'file': result['file_path'],
                    'import': import_line,
                    'line': imp['line']
                })
            else:
                categories['unused_stdlib'].append({
                    'file': result['file_path'],
                    'import': import_line,
                    'line': imp['line']
                })
    
    return categories

def main():
    """Main audit function"""
    print("🔍 Starting Detailed Import Audit for Task 3.1")
    print("=" * 70)
    
    # Find all Python files
    src_dir = Path("src")
    python_files = list(src_dir.rglob("*.py"))
    
    print(f"Found {len(python_files)} Python files to audit")
    print()
    
    # Analyze each file
    analysis_results = []
    total_unused = 0
    
    for file_path in python_files:
        print(f"Analyzing: {file_path}")
        result = analyze_import_usage(str(file_path))
        analysis_results.append(result)
        
        if 'error' not in result:
            total_unused += result['patterns']['unused_imports']
    
    print(f"\n📊 Analysis Complete!")
    print(f"Total unused imports found: {total_unused}")
    print()
    
    # Categorize unused imports
    categories = categorize_unused_imports(analysis_results)
    
    # Print summary by category
    print("📋 Unused Imports by Category:")
    print("-" * 50)
    
    for category, imports in categories.items():
        if imports:
            print(f"\n{category.replace('_', ' ').title()}: {len(imports)} imports")
            for imp in imports[:5]:  # Show first 5 examples
                print(f"  - {imp['file']}:{imp['line']} - {imp['import']}")
            if len(imports) > 5:
                print(f"  ... and {len(imports) - 5} more")
    
    # Find files with most unused imports
    print(f"\n📁 Top 10 Files with Most Unused Imports:")
    print("-" * 50)
    
    files_by_unused = [(r['file_path'], r['patterns']['unused_imports']) 
                       for r in analysis_results 
                       if 'error' not in r and r['patterns']['unused_imports'] > 0]
    
    files_by_unused.sort(key=lambda x: x[1], reverse=True)
    
    for i, (file_path, count) in enumerate(files_by_unused[:10], 1):
        print(f"{i:2d}. {file_path}: {count} unused imports")
    
    # Analyze patterns
    print(f"\n🔍 Pattern Analysis:")
    print("-" * 50)
    
    total_files = len([r for r in analysis_results if 'error' not in r])
    files_with_unused = len([r for r in analysis_results if 'error' not in r and r['patterns']['unused_imports'] > 0])
    
    print(f"Files with unused imports: {files_with_unused}/{total_files} ({files_with_unused/total_files*100:.1f}%)")
    
    # Find common unused imports
    all_unused = []
    for result in analysis_results:
        if 'error' not in result:
            all_unused.extend([imp['full_import'] for imp in result['unused_imports']])
    
    import_counts = defaultdict(int)
    for imp in all_unused:
        import_counts[imp] += 1
    
    print(f"\n🔍 Most Common Unused Imports:")
    print("-" * 50)
    
    for imp, count in sorted(import_counts.items(), key=lambda x: x[1], reverse=True)[:15]:
        print(f"{count:3d}x - {imp}")
    
    print(f"\n🎯 Audit Recommendations:")
    print("-" * 50)
    print("1. Focus on files with 10+ unused imports first")
    print("2. Check if unused imports are from incomplete implementations")
    print("3. Verify if imports are needed for type hints or future use")
    print("4. Look for patterns in specific modules (e.g., all analysis modules)")
    print("5. Consider if unused imports indicate dead code")
    
    return analysis_results

if __name__ == "__main__":
    main() 