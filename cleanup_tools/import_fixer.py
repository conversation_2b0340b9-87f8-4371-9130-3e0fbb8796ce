#!/usr/bin/env python3
"""
Import Fixer for Task 3.3
Automatically fixes import organization according to PEP 8 standards
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
import re

class ImportFixer:
    """Automatically fixes import organization in Python files."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.fixed_files = []
        self.skipped_files = []
        
    def fix_file(self, file_path: Path) -> bool:
        """Fix import organization in a single Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse the file
            tree = ast.parse(content)
            
            # Extract all imports
            imports = self._extract_imports(tree)
            
            if not imports:
                return False  # No imports to fix
            
            # Organize imports
            organized_imports = self._organize_imports(imports)
            
            # Generate new content
            new_content = self._replace_imports(content, organized_imports)
            
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return True
            
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
            return False
    
    def _extract_imports(self, tree: ast.AST) -> List[Dict]:
        """Extract all import statements from AST."""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append({
                        'type': 'import',
                        'module': alias.name,
                        'alias': alias.asname,
                        'line': node.lineno,
                        'end_line': node.end_lineno
                    })
                    
            elif isinstance(node, ast.ImportFrom):
                names = [name.name for name in node.names]
                imports.append({
                    'type': 'from',
                    'module': node.module or '',
                    'names': names,
                    'line': node.lineno,
                    'end_line': node.end_lineno
                })
        
        return imports
    
    def _organize_imports(self, imports: List[Dict]) -> List[str]:
        """Organize imports according to PEP 8 standards."""
        # Group imports by type
        stdlib_imports = []
        third_party_imports = []
        local_imports = []
        
        for imp in imports:
            if self._is_stdlib_module(imp['module']):
                stdlib_imports.append(imp)
            elif self._is_local_module(imp['module']):
                local_imports.append(imp)
            else:
                third_party_imports.append(imp)
        
        # Sort imports within each group
        def sort_key(imp):
            if imp['type'] == 'from':
                return f"{imp['module']}.{'.'.join(imp['names'])}"
            return imp['module']
        
        stdlib_imports.sort(key=sort_key)
        third_party_imports.sort(key=sort_key)
        local_imports.sort(key=sort_key)
        
        # Generate organized import text
        organized = []
        
        # Standard library imports
        if stdlib_imports:
            organized.extend(self._format_imports(stdlib_imports))
            organized.append('')
        
        # Third-party imports
        if third_party_imports:
            organized.extend(self._format_imports(third_party_imports))
            organized.append('')
        
        # Local imports
        if local_imports:
            organized.extend(self._format_imports(local_imports))
        
        return organized
    
    def _format_imports(self, imports: List[Dict]) -> List[str]:
        """Format a list of imports as strings."""
        formatted = []
        
        for imp in imports:
            if imp['type'] == 'import':
                if imp['alias']:
                    formatted.append(f"import {imp['module']} as {imp['alias']}")
                else:
                    formatted.append(f"import {imp['module']}")
            else:  # from import
                names_str = ', '.join(imp['names'])
                formatted.append(f"from {imp['module']} import {names_str}")
        
        return formatted
    
    def _is_stdlib_module(self, module: str) -> bool:
        """Check if a module is part of Python standard library."""
        stdlib_modules = {
            'os', 'sys', 're', 'json', 'datetime', 'time', 'logging', 'pathlib',
            'typing', 'collections', 'asyncio', 'functools', 'itertools',
            'abc', 'contextlib', 'dataclasses', 'enum', 'hashlib', 'random',
            'traceback', 'urllib', 'http', 'ssl', 'socket', 'threading',
            'multiprocessing', 'subprocess', 'signal', 'tempfile', 'shutil',
            'glob', 'fnmatch', 'pickle', 'copy', 'weakref', 'inspect',
            'ast', 'tokenize', 'keyword', 'token', 'builtins', 'types'
        }
        
        # Handle submodules
        base_module = module.split('.')[0]
        return base_module in stdlib_modules
    
    def _is_local_module(self, module: str) -> bool:
        """Check if a module is a local project module."""
        local_prefixes = {'src.', 'bot.', 'api.', 'core.', 'shared.', 'analysis.', 'database.'}
        return any(module.startswith(prefix) for prefix in local_prefixes)
    
    def _replace_imports(self, content: str, organized_imports: List[str]) -> str:
        """Replace imports in file content with organized versions."""
        lines = content.split('\n')
        
        # Find import section boundaries
        import_start = None
        import_end = None
        
        for i, line in enumerate(lines):
            if line.strip().startswith(('import ', 'from ')):
                if import_start is None:
                    import_start = i
                import_end = i
        
        if import_start is None:
            return content  # No imports found
        
        # Find the end of the import section (look for blank line or non-import)
        for i in range(import_end + 1, len(lines)):
            if lines[i].strip() == '' or not lines[i].strip().startswith(('import ', 'from ')):
                import_end = i
                break
        
        # Replace the import section
        new_lines = lines[:import_start]
        new_lines.extend(organized_imports)
        new_lines.extend(lines[import_end:])
        
        return '\n'.join(new_lines)
    
    def fix_codebase(self) -> Dict[str, any]:
        """Fix import organization across the entire codebase."""
        python_files = list(self.src_dir.rglob("*.py"))
        
        total_files = len(python_files)
        fixed_count = 0
        
        for file_path in python_files:
            if self.fix_file(file_path):
                self.fixed_files.append(str(file_path))
                fixed_count += 1
            else:
                self.skipped_files.append(str(file_path))
        
        return {
            'total_files': total_files,
            'fixed_files': fixed_count,
            'skipped_files': len(self.skipped_files),
            'fixed_file_list': self.fixed_files,
            'skipped_file_list': self.skipped_files
        }

def main():
    """Main function to run import fixing."""
    print("🔧 Starting Import Organization Fixing for Task 3.3")
    print("=" * 60)
    
    fixer = ImportFixer()
    results = fixer.fix_codebase()
    
    print(f"📊 Fixing Complete!")
    print(f"Total Python files: {results['total_files']}")
    print(f"Files fixed: {results['fixed_files']}")
    print(f"Files skipped: {results['skipped_files']}")
    print()
    
    if results['fixed_files'] > 0:
        print("✅ Successfully fixed import organization in:")
        for file_path in results['fixed_file_list'][:10]:  # Show first 10
            print(f"  - {file_path}")
        
        if len(results['fixed_file_list']) > 10:
            print(f"  ... and {len(results['fixed_file_list']) - 10} more files")
    
    if results['skipped_files'] > 0:
        print(f"\n⚠️  Skipped {results['skipped_files']} files (no imports or errors)")
    
    print(f"\n🎯 Import organization now follows PEP 8 standards:")
    print("- Standard library imports first")
    print("- Third-party imports second")
    print("- Local imports last")
    print("- Proper blank lines between groups")
    print("- Consistent formatting")
    
    return results

if __name__ == "__main__":
    main() 