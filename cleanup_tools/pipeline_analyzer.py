#!/usr/bin/env python3
"""
Pipeline Overhead Analyzer for Task 5.1
Analyzes pipeline complexity and identifies optimization opportunities
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict

class PipelineAnalyzer:
    """Analyzes pipeline overhead and complexity."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.pipeline_files = []
        self.simple_operations = []
        self.complex_operations = []
        
    def analyze_file(self, file_path: Path) -> Dict[str, any]:
        """Analyze pipeline complexity in a single Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            file_analysis = {
                'pipeline_classes': [],
                'simple_operations': [],
                'complex_operations': [],
                'stage_count': 0,
                'has_async': False
            }
            
            # Look for pipeline-related classes and functions
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    if self._is_pipeline_class(node):
                        pipeline_info = self._analyze_pipeline_class(node, file_path)
                        file_analysis['pipeline_classes'].append(pipeline_info)
                        file_analysis['stage_count'] += pipeline_info.get('stages', 0)
                        
                elif isinstance(node, ast.FunctionDef):
                    if self._is_simple_operation(node):
                        file_analysis['simple_operations'].append({
                            'name': node.name,
                            'line': node.lineno,
                            'complexity': self._calculate_complexity(node),
                            'file': str(file_path)
                        })
                    elif self._is_complex_operation(node):
                        file_analysis['complex_operations'].append({
                            'name': node.name,
                            'line': node.lineno,
                            'complexity': self._calculate_complexity(node),
                            'file': str(file_path)
                        })
                
                elif isinstance(node, ast.AsyncFunctionDef):
                    file_analysis['has_async'] = True
            
            return file_analysis
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return {'pipeline_classes': [], 'simple_operations': [], 'complex_operations': [], 'stage_count': 0, 'has_async': False}
    
    def _is_pipeline_class(self, node: ast.ClassDef) -> bool:
        """Check if a class is pipeline-related."""
        pipeline_keywords = ['pipeline', 'stage', 'processor', 'orchestrator', 'engine']
        
        # Check class name
        if any(keyword in node.name.lower() for keyword in pipeline_keywords):
            return True
        
        # Check base classes
        for base in node.bases:
            if isinstance(base, ast.Name) and any(keyword in base.id.lower() for keyword in pipeline_keywords):
                return True
        
        # Check docstring
        docstring = ast.get_docstring(node)
        if docstring and any(keyword in docstring.lower() for keyword in pipeline_keywords):
            return True
        
        return False
    
    def _analyze_pipeline_class(self, node: ast.ClassDef, file_path: Path) -> Dict:
        """Analyze a pipeline class for complexity."""
        stages = []
        methods = []
        
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                methods.append(item.name)
                if 'stage' in item.name.lower() or 'process' in item.name.lower():
                    stages.append({
                        'name': item.name,
                        'complexity': self._calculate_complexity(item)
                    })
        
        return {
            'name': node.name,
            'file': str(file_path),
            'line': node.lineno,
            'stages': len(stages),
            'methods': len(methods),
            'stage_details': stages,
            'docstring': ast.get_docstring(node)
        }
    
    def _is_simple_operation(self, node: ast.FunctionDef) -> bool:
        """Check if a function is a simple operation."""
        simple_keywords = ['get', 'fetch', 'retrieve', 'check', 'validate', 'price', 'quote']
        
        # Check function name
        if any(keyword in node.name.lower() for keyword in simple_keywords):
            # Check if it's simple (few lines, few branches)
            complexity = self._calculate_complexity(node)
            return complexity < 5
        
        return False
    
    def _is_complex_operation(self, node: ast.FunctionDef) -> bool:
        """Check if a function is a complex operation."""
        complex_keywords = ['analyze', 'process', 'transform', 'calculate', 'compute', 'generate']
        
        # Check function name
        if any(keyword in node.name.lower() for keyword in complex_keywords):
            # Check if it's complex (many lines, many branches)
            complexity = self._calculate_complexity(node)
            return complexity > 10
        
        return False
    
    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, ast.With):
                complexity += 1
            elif isinstance(child, ast.Assert):
                complexity += 1
        
        return complexity
    
    def analyze_codebase(self) -> Dict[str, any]:
        """Analyze pipeline complexity across the entire codebase."""
        python_files = list(self.src_dir.rglob("*.py"))
        
        total_files = len(python_files)
        pipeline_files = 0
        total_stages = 0
        simple_ops = 0
        complex_ops = 0
        
        for file_path in python_files:
            analysis = self.analyze_file(file_path)
            
            if analysis['pipeline_classes']:
                pipeline_files += 1
                total_stages += analysis['stage_count']
            
            simple_ops += len(analysis['simple_operations'])
            complex_ops += len(analysis['complex_operations'])
        
        return {
            'total_files': total_files,
            'pipeline_files': pipeline_files,
            'total_stages': total_stages,
            'simple_operations': simple_ops,
            'complex_operations': complex_ops,
            'files': python_files
        }

def main():
    """Main function to run pipeline analysis."""
    print("🔍 Starting Pipeline Overhead Analysis for Task 5.1")
    print("=" * 60)
    
    analyzer = PipelineAnalyzer()
    results = analyzer.analyze_codebase()
    
    print(f"📊 Analysis Complete!")
    print(f"Total Python files: {results['total_files']}")
    print(f"Pipeline-related files: {results['pipeline_files']}")
    print(f"Total pipeline stages: {results['total_stages']}")
    print(f"Simple operations: {results['simple_operations']}")
    print(f"Complex operations: {results['complex_operations']}")
    print()
    
    if results['pipeline_files'] > 0:
        print("🚀 Pipeline Analysis Results:")
        print("-" * 40)
        print(f"• {results['pipeline_files']} files contain pipeline logic")
        print(f"• {results['total_stages']} total pipeline stages identified")
        print(f"• {results['simple_operations']} simple operations found")
        print(f"• {results['complex_operations']} complex operations found")
        print()
        
        print("🎯 Optimization Opportunities:")
        print("-" * 40)
        print("1. Create lightweight paths for simple operations")
        print("2. Bypass pipeline for basic queries (price checks, etc.)")
        print("3. Maintain full pipeline for complex analysis")
        print("4. Implement operation complexity detection")
        print("5. Add fast-path routing based on operation type")
    else:
        print("✅ No pipeline complexity found!")
    
    return results

if __name__ == "__main__":
    main() 