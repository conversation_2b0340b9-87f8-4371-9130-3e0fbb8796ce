#!/usr/bin/env python3
"""
Circular Import Detector for Task 3.2
Detects circular import dependencies in the codebase
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict, deque

class CircularImportDetector:
    """Detects circular import dependencies in Python files."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.import_graph = defaultdict(set)
        self.file_imports = {}
        self.circular_paths = []
        
    def analyze_file(self, file_path: Path) -> Set[str]:
        """Analyze imports in a single Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            imports = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        # Handle relative imports
                        if node.module.startswith('.'):
                            # Convert relative import to absolute
                            relative_level = len(node.module) - len(node.module.lstrip('.'))
                            current_dir = file_path.parent
                            for _ in range(relative_level):
                                current_dir = current_dir.parent
                            
                            # Try to find the actual module
                            module_name = str(current_dir.relative_to(self.src_dir)).replace('/', '.')
                            if module_name:
                                imports.add(module_name)
                        else:
                            imports.add(node.module)
            
            return imports
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return set()
    
    def build_import_graph(self):
        """Build the complete import graph."""
        python_files = list(self.src_dir.rglob("*.py"))
        
        for file_path in python_files:
            # Convert file path to module name
            module_name = str(file_path.relative_to(self.src_dir)).replace('/', '.').replace('.py', '')
            if module_name.startswith('.'):
                module_name = module_name[1:]
            
            imports = self.analyze_file(file_path)
            self.file_imports[module_name] = imports
            
            # Add edges to import graph
            for imp in imports:
                # Only add edges for local imports
                if imp.startswith('src.') or imp.startswith('bot.') or imp.startswith('api.') or imp.startswith('core.') or imp.startswith('shared.'):
                    self.import_graph[module_name].add(imp)
    
    def find_circular_imports(self) -> List[List[str]]:
        """Find circular import paths using DFS."""
        visited = set()
        rec_stack = set()
        path = []
        
        def dfs(node: str, current_path: List[str]):
            if node in rec_stack:
                # Found a cycle
                cycle_start = current_path.index(node)
                cycle = current_path[cycle_start:] + [node]
                self.circular_paths.append(cycle)
                return
            
            if node in visited:
                return
            
            visited.add(node)
            rec_stack.add(node)
            current_path.append(node)
            
            # Create a copy of the neighbors to avoid modification during iteration
            neighbors = list(self.import_graph[node])
            for neighbor in neighbors:
                dfs(neighbor, current_path.copy())
            
            rec_stack.remove(node)
            current_path.pop()
        
        # Create a copy of the keys to avoid modification during iteration
        nodes = list(self.import_graph.keys())
        for node in nodes:
            if node not in visited:
                dfs(node, [])
        
        return self.circular_paths
    
    def analyze_import_chains(self) -> Dict[str, List[str]]:
        """Analyze import chains to understand dependency depth."""
        import_chains = {}
        
        for module in self.import_graph:
            chain = self._get_import_chain(module, set())
            if chain:
                import_chains[module] = chain
        
        return import_chains
    
    def _get_import_chain(self, module: str, visited: Set[str], max_depth: int = 10) -> Optional[List[str]]:
        """Get the import chain for a module."""
        if module in visited or len(visited) > max_depth:
            return None
        
        visited.add(module)
        chain = [module]
        
        for imp in self.import_graph[module]:
            if imp in self.file_imports:  # Only follow local imports
                sub_chain = self._get_import_chain(imp, visited.copy(), max_depth)
                if sub_chain:
                    chain.extend(sub_chain[1:])  # Avoid duplicate module
        
        return chain
    
    def generate_report(self) -> Dict[str, any]:
        """Generate a comprehensive report of import analysis."""
        self.build_import_graph()
        circular_paths = self.find_circular_imports()
        import_chains = self.analyze_import_chains()
        
        # Find modules with most dependencies
        dependency_counts = {module: len(imports) for module, imports in self.import_graph.items()}
        high_dependency_modules = sorted(dependency_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Find modules that are imported by many others
        reverse_deps = defaultdict(set)
        for module, imports in self.import_graph.items():
            for imp in imports:
                if imp in self.file_imports:
                    reverse_deps[imp].add(module)
        
        most_imported = sorted(reverse_deps.items(), key=lambda x: len(x[1]), reverse=True)[:10]
        
        return {
            "circular_imports": circular_paths,
            "high_dependency_modules": high_dependency_modules,
            "most_imported_modules": most_imported,
            "import_chains": import_chains,
            "total_modules": len(self.file_imports),
            "total_imports": sum(len(imports) for imports in self.import_graph.values())
        }

def main():
    """Main function to run circular import detection."""
    print("🔄 Starting Circular Import Detection for Task 3.2")
    print("=" * 60)
    
    detector = CircularImportDetector()
    report = detector.generate_report()
    
    print(f"📊 Analysis Complete!")
    print(f"Total modules analyzed: {report['total_modules']}")
    print(f"Total import relationships: {report['total_imports']}")
    print()
    
    # Report circular imports
    if report['circular_imports']:
        print("🚨 Circular Import Dependencies Found:")
        print("-" * 40)
        for i, cycle in enumerate(report['circular_imports'], 1):
            print(f"{i}. {' -> '.join(cycle)}")
        print()
    else:
        print("✅ No circular imports detected!")
        print()
    
    # Report high dependency modules
    print("📈 Top 10 Modules with Most Dependencies:")
    print("-" * 40)
    for module, count in report['high_dependency_modules']:
        print(f"{module}: {count} dependencies")
    print()
    
    # Report most imported modules
    print("📥 Top 10 Most Imported Modules:")
    print("-" * 40)
    for module, importers in report['most_imported_modules']:
        print(f"{module}: imported by {len(importers)} modules")
    print()
    
    # Report long import chains
    print("🔗 Longest Import Chains (potential issues):")
    print("-" * 40)
    long_chains = [(module, chain) for module, chain in report['import_chains'].items() if len(chain) > 5]
    long_chains.sort(key=lambda x: len(x[1]), reverse=True)
    
    for module, chain in long_chains[:5]:
        print(f"{module}: {len(chain)} levels deep")
        print(f"  Chain: {' -> '.join(chain)}")
        print()
    
    if not long_chains:
        print("✅ No excessively long import chains found!")
    
    print("🎯 Recommendations:")
    print("-" * 40)
    if report['circular_imports']:
        print("1. Break circular imports by restructuring modules")
        print("2. Use dependency injection or lazy imports")
        print("3. Consider moving shared code to common modules")
    
    if long_chains:
        print("4. Reduce import chain depth by flattening module structure")
        print("5. Use absolute imports instead of deep relative imports")
    
    print("6. Consider using __init__.py files to expose only necessary interfaces")
    
    return report

if __name__ == "__main__":
    main() 