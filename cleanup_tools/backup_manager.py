#!/usr/bin/env python3
"""
Backup and Rollback Manager for Safe Codebase Cleanup

Provides comprehensive backup and rollback capabilities to ensure
all cleanup operations can be safely reverted if needed.
"""

import os
import shutil
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class BackupOperation:
    """Represents a single backup operation"""
    operation_id: str
    timestamp: str
    file_path: str
    backup_path: str
    operation_type: str
    description: str
    file_hash: str

@dataclass
class BackupManifest:
    """Manifest of all backup operations"""
    created_at: str
    operations: List[BackupOperation]
    total_files: int
    total_size_bytes: int

class BackupManager:
    """Manages backup and rollback operations for safe cleanup"""
    
    def __init__(self, backup_root: str = "cleanup_backups"):
        """
        Initialize backup manager
        
        Args:
            backup_root: Root directory for storing backups
        """
        self.backup_root = Path(backup_root)
        self.backup_root.mkdir(exist_ok=True)
        
        # Create session-specific backup directory
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_dir = self.backup_root / f"session_{self.session_id}"
        self.session_dir.mkdir(exist_ok=True)
        
        self.manifest_path = self.session_dir / "manifest.json"
        self.operations: List[BackupOperation] = []
        
        logger.info(f"Backup manager initialized - Session: {self.session_id}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of file contents"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            logger.warning(f"Could not calculate hash for {file_path}: {e}")
            return ""
    
    def backup_file(self, file_path: str, operation_type: str, description: str) -> str:
        """
        Create backup of a file before modification
        
        Args:
            file_path: Path to file to backup
            operation_type: Type of operation being performed
            description: Description of the operation
            
        Returns:
            Backup operation ID
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Generate operation ID
        operation_id = f"{len(self.operations):04d}_{datetime.now().strftime('%H%M%S')}"
        
        # Create backup path
        rel_path = os.path.relpath(file_path)
        backup_subdir = self.session_dir / "files" / os.path.dirname(rel_path)
        backup_subdir.mkdir(parents=True, exist_ok=True)
        
        backup_filename = f"{operation_id}_{os.path.basename(file_path)}"
        backup_path = backup_subdir / backup_filename
        
        # Copy file to backup location
        shutil.copy2(file_path, backup_path)
        
        # Calculate file hash for integrity verification
        file_hash = self._calculate_file_hash(file_path)
        
        # Create backup operation record
        operation = BackupOperation(
            operation_id=operation_id,
            timestamp=datetime.now().isoformat(),
            file_path=file_path,
            backup_path=str(backup_path),
            operation_type=operation_type,
            description=description,
            file_hash=file_hash
        )
        
        self.operations.append(operation)
        self._save_manifest()
        
        logger.info(f"Backed up {file_path} -> {backup_path} (ID: {operation_id})")
        return operation_id
    
    def rollback_operation(self, operation_id: str) -> bool:
        """
        Rollback a specific operation
        
        Args:
            operation_id: ID of operation to rollback
            
        Returns:
            True if rollback successful, False otherwise
        """
        operation = next((op for op in self.operations if op.operation_id == operation_id), None)
        if not operation:
            logger.error(f"Operation {operation_id} not found")
            return False
        
        try:
            # Restore file from backup
            shutil.copy2(operation.backup_path, operation.file_path)
            
            # Verify integrity
            current_hash = self._calculate_file_hash(operation.file_path)
            if current_hash == operation.file_hash:
                logger.info(f"Successfully rolled back operation {operation_id}")
                return True
            else:
                logger.warning(f"Hash mismatch after rollback of {operation_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to rollback operation {operation_id}: {e}")
            return False
    
    def rollback_all(self) -> Dict[str, bool]:
        """
        Rollback all operations in reverse order
        
        Returns:
            Dictionary mapping operation IDs to rollback success status
        """
        results = {}
        
        # Rollback in reverse order (most recent first)
        for operation in reversed(self.operations):
            results[operation.operation_id] = self.rollback_operation(operation.operation_id)
        
        return results
    
    def _save_manifest(self):
        """Save backup manifest to disk"""
        total_size = 0
        for operation in self.operations:
            try:
                total_size += os.path.getsize(operation.backup_path)
            except OSError:
                pass
        
        manifest = BackupManifest(
            created_at=datetime.now().isoformat(),
            operations=self.operations,
            total_files=len(self.operations),
            total_size_bytes=total_size
        )
        
        with open(self.manifest_path, 'w') as f:
            json.dump(asdict(manifest), f, indent=2)
    
    def get_backup_info(self) -> Dict[str, Any]:
        """Get information about current backup session"""
        total_size = sum(
            os.path.getsize(op.backup_path) 
            for op in self.operations 
            if os.path.exists(op.backup_path)
        )
        
        return {
            "session_id": self.session_id,
            "session_dir": str(self.session_dir),
            "total_operations": len(self.operations),
            "total_size_mb": total_size / (1024 * 1024),
            "operations": [asdict(op) for op in self.operations]
        }
    
    def cleanup_old_backups(self, keep_sessions: int = 5):
        """
        Clean up old backup sessions, keeping only the most recent ones
        
        Args:
            keep_sessions: Number of recent sessions to keep
        """
        if not self.backup_root.exists():
            return
        
        # Get all session directories
        session_dirs = [
            d for d in self.backup_root.iterdir() 
            if d.is_dir() and d.name.startswith("session_")
        ]
        
        # Sort by creation time (newest first)
        session_dirs.sort(key=lambda x: x.stat().st_ctime, reverse=True)
        
        # Remove old sessions
        for old_session in session_dirs[keep_sessions:]:
            try:
                shutil.rmtree(old_session)
                logger.info(f"Cleaned up old backup session: {old_session.name}")
            except Exception as e:
                logger.warning(f"Failed to clean up {old_session}: {e}")

# Global backup manager instance
backup_manager = BackupManager()