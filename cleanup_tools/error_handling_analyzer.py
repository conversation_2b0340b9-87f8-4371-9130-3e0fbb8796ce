#!/usr/bin/env python3
"""
Error Handling Pattern Analyzer for Task 4.3
Analyzes error handling patterns across the codebase
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict

class ErrorHandlingAnalyzer:
    """Analyzes error handling patterns in Python files."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.error_patterns = defaultdict(list)
        self.exception_types = defaultdict(list)
        self.error_responses = defaultdict(list)
        self.try_blocks = defaultdict(list)
        
    def analyze_file(self, file_path: Path) -> Dict[str, any]:
        """Analyze error handling in a single Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            file_analysis = {
                'exceptions': [],
                'try_blocks': [],
                'error_responses': [],
                'custom_exceptions': []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Try):
                    try_info = self._analyze_try_block(node, file_path)
                    if try_info:
                        file_analysis['try_blocks'].append(try_info)
                        
                elif isinstance(node, ast.Raise):
                    raise_info = self._analyze_raise_statement(node, file_path)
                    if raise_info:
                        file_analysis['exceptions'].append(raise_info)
                        
                elif isinstance(node, ast.ClassDef):
                    if self._is_exception_class(node):
                        exception_info = self._analyze_exception_class(node, file_path)
                        if exception_info:
                            file_analysis['custom_exceptions'].append(exception_info)
                            
                elif isinstance(node, ast.FunctionDef):
                    error_response_info = self._analyze_error_response_function(node, file_path)
                    if error_response_info:
                        file_analysis['error_responses'].append(error_response_info)
            
            return file_analysis
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return {'exceptions': [], 'try_blocks': [], 'error_responses': [], 'custom_exceptions': []}
    
    def _analyze_try_block(self, node: ast.Try, file_path: Path) -> Optional[Dict]:
        """Analyze a try-except block."""
        try:
            handlers = []
            for handler in node.handlers:
                if handler.type:
                    if isinstance(handler.type, ast.Name):
                        exception_type = handler.type.id
                    elif isinstance(handler.type, ast.Attribute):
                        exception_type = f"{handler.type.value.id}.{handler.type.attr}"
                    else:
                        exception_type = "unknown"
                else:
                    exception_type = "bare_except"
                
                handlers.append({
                    'type': exception_type,
                    'line': handler.lineno,
                    'body_lines': len(handler.body)
                })
            
            return {
                'file': str(file_path),
                'line': node.lineno,
                'handlers': handlers,
                'has_else': bool(node.orelse),
                'has_finally': bool(node.finalbody)
            }
        except Exception:
            return None
    
    def _analyze_raise_statement(self, node: ast.Raise, file_path: Path) -> Optional[Dict]:
        """Analyze a raise statement."""
        try:
            if node.exc:
                if isinstance(node.exc, ast.Name):
                    exception_type = node.exc.id
                elif isinstance(node.exc, ast.Call):
                    if isinstance(node.exc.func, ast.Name):
                        exception_type = node.exc.func.id
                    elif isinstance(node.exc.func, ast.Attribute):
                        exception_type = f"{node.exc.func.value.id}.{node.exc.func.attr}"
                    else:
                        exception_type = "unknown"
                else:
                    exception_type = "unknown"
            else:
                exception_type = "re_raise"
            
            return {
                'file': str(file_path),
                'line': node.lineno,
                'type': exception_type,
                'has_message': bool(node.msg)
            }
        except Exception:
            return None
    
    def _is_exception_class(self, node: ast.ClassDef) -> bool:
        """Check if a class is an exception class."""
        # Check if it inherits from Exception
        for base in node.bases:
            if isinstance(base, ast.Name) and base.id in ['Exception', 'BaseException']:
                return True
            elif isinstance(base, ast.Attribute):
                if base.attr in ['Exception', 'BaseException']:
                    return True
        return False
    
    def _analyze_exception_class(self, node: ast.ClassDef, file_path: Path) -> Optional[Dict]:
        """Analyze a custom exception class."""
        try:
            methods = []
            for item in node.body:
                if isinstance(item, ast.FunctionDef):
                    methods.append(item.name)
            
            return {
                'file': str(file_path),
                'line': node.lineno,
                'name': node.name,
                'methods': methods,
                'docstring': ast.get_docstring(node)
            }
        except Exception:
            return None
    
    def _analyze_error_response_function(self, node: ast.FunctionDef, file_path: Path) -> Optional[Dict]:
        """Analyze functions that might return error responses."""
        try:
            # Check if function name suggests error handling
            error_keywords = ['error', 'exception', 'fail', 'invalid', 'validation']
            if any(keyword in node.name.lower() for keyword in error_keywords):
                return {
                    'file': str(file_path),
                    'line': node.lineno,
                    'name': node.name,
                    'docstring': ast.get_docstring(node)
                }
        except Exception:
            pass
        return None
    
    def analyze_codebase(self) -> Dict[str, any]:
        """Analyze error handling patterns across the entire codebase."""
        python_files = list(self.src_dir.rglob("*.py"))
        
        all_exceptions = []
        all_try_blocks = []
        all_custom_exceptions = []
        all_error_responses = []
        
        for file_path in python_files:
            analysis = self.analyze_file(file_path)
            all_exceptions.extend(analysis['exceptions'])
            all_try_blocks.extend(analysis['try_blocks'])
            all_custom_exceptions.extend(analysis['custom_exceptions'])
            all_error_responses.extend(analysis['error_responses'])
        
        # Analyze patterns
        exception_types = defaultdict(int)
        for exc in all_exceptions:
            exception_types[exc['type']] += 1
        
        handler_patterns = defaultdict(int)
        for try_block in all_try_blocks:
            pattern = tuple(sorted(h['type'] for h in try_block['handlers']))
            handler_patterns[pattern] += 1
        
        return {
            'total_files': len(python_files),
            'exceptions': all_exceptions,
            'try_blocks': all_try_blocks,
            'custom_exceptions': all_custom_exceptions,
            'error_responses': all_error_responses,
            'exception_type_counts': dict(exception_types),
            'handler_patterns': dict(handler_patterns),
            'statistics': {
                'total_exceptions': len(all_exceptions),
                'total_try_blocks': len(all_try_blocks),
                'total_custom_exceptions': len(all_custom_exceptions),
                'total_error_responses': len(all_error_responses)
            }
        }

def main():
    """Main function to run error handling analysis."""
    print("🔍 Starting Error Handling Pattern Analysis for Task 4.3")
    print("=" * 60)
    
    analyzer = ErrorHandlingAnalyzer()
    results = analyzer.analyze_codebase()
    
    stats = results['statistics']
    print(f"📊 Analysis Complete!")
    print(f"Total files analyzed: {results['total_files']}")
    print(f"Total exceptions raised: {stats['total_exceptions']}")
    print(f"Total try-except blocks: {stats['total_try_blocks']}")
    print(f"Total custom exceptions: {stats['total_custom_exceptions']}")
    print(f"Total error response functions: {stats['total_error_responses']}")
    print()
    
    # Report exception types
    print("🚨 Top Exception Types Used:")
    print("-" * 40)
    sorted_exceptions = sorted(results['exception_type_counts'].items(), key=lambda x: x[1], reverse=True)
    for exc_type, count in sorted_exceptions[:10]:
        print(f"{exc_type}: {count} times")
    print()
    
    # Report handler patterns
    print("🔄 Top Try-Except Handler Patterns:")
    print("-" * 40)
    sorted_patterns = sorted(results['handler_patterns'].items(), key=lambda x: x[1], reverse=True)
    for pattern, count in sorted_patterns[:10]:
        pattern_str = " -> ".join(pattern) if pattern else "bare_except"
        print(f"{pattern_str}: {count} times")
    print()
    
    # Report custom exceptions
    if results['custom_exceptions']:
        print("🏗️  Custom Exception Classes Found:")
        print("-" * 40)
        for exc in results['custom_exceptions'][:10]:
            print(f"{exc['name']} in {exc['file']}:{exc['line']}")
            if exc['methods']:
                print(f"  Methods: {', '.join(exc['methods'])}")
        print()
    
    # Report error response functions
    if results['error_responses']:
        print("📝 Error Response Functions Found:")
        print("-" * 40)
        for func in results['error_responses'][:10]:
            print(f"{func['name']} in {func['file']}:{func['line']}")
        print()
    
    print("🎯 Recommendations:")
    print("-" * 40)
    print("1. Standardize exception types across modules")
    print("2. Use consistent error response formats")
    print("3. Implement proper exception hierarchy")
    print("4. Add structured error logging")
    print("5. Use custom exceptions for domain-specific errors")
    print("6. Implement error response templates")
    print("7. Add error correlation IDs for debugging")
    
    return results

if __name__ == "__main__":
    main() 