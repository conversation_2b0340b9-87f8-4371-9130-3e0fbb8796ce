#!/usr/bin/env python3
"""
Validation Framework for Cleanup Operations

Provides comprehensive validation capabilities to ensure cleanup
operations don't break existing functionality.
"""

import ast
import subprocess
import sys
import importlib.util
import tempfile
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import logging
import time

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of a validation check"""
    check_name: str
    success: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationSuite:
    """Collection of validation results"""
    suite_name: str
    results: List[ValidationResult] = field(default_factory=list)
    overall_success: bool = True
    total_execution_time: float = 0.0

class ValidationFramework:
    """Framework for validating cleanup operations"""
    
    def __init__(self, project_root: str = "."):
        """
        Initialize validation framework
        
        Args:
            project_root: Root directory of the project
        """
        self.project_root = Path(project_root)
        self.python_executable = sys.executable
        
        logger.info(f"Validation framework initialized for: {self.project_root}")
    
    def validate_syntax(self, file_path: str) -> ValidationResult:
        """
        Validate Python syntax of a file
        
        Args:
            file_path: Path to Python file to validate
            
        Returns:
            ValidationResult with syntax check results
        """
        start_time = time.time()
        result = ValidationResult(check_name="syntax_validation", success=True)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to parse the AST
            ast.parse(content, filename=file_path)
            result.details["message"] = "Syntax is valid"
            
        except SyntaxError as e:
            result.success = False
            result.errors.append(f"Syntax error at line {e.lineno}: {e.msg}")
            result.details["syntax_error"] = {
                "line": e.lineno,
                "column": e.offset,
                "message": e.msg
            }
        except Exception as e:
            result.success = False
            result.errors.append(f"Failed to validate syntax: {str(e)}")
        
        result.execution_time = time.time() - start_time
        return result
    
    def validate_imports(self, file_path: str) -> ValidationResult:
        """
        Validate that all imports in a file can be resolved
        
        Args:
            file_path: Path to Python file to validate
            
        Returns:
            ValidationResult with import validation results
        """
        start_time = time.time()
        result = ValidationResult(check_name="import_validation", success=True)
        
        try:
            # Use subprocess to test imports without affecting current process
            cmd = [
                self.python_executable, "-c",
                f"import ast; "
                f"exec(compile(open('{file_path}').read(), '{file_path}', 'exec'))"
            ]
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=self.project_root
            )
            
            if process.returncode == 0:
                result.details["message"] = "All imports resolved successfully"
            else:
                result.success = False
                result.errors.append(f"Import error: {process.stderr.strip()}")
                result.details["stderr"] = process.stderr
                result.details["stdout"] = process.stdout
        
        except subprocess.TimeoutExpired:
            result.success = False
            result.errors.append("Import validation timed out after 30 seconds")
        except Exception as e:
            result.success = False
            result.errors.append(f"Failed to validate imports: {str(e)}")
        
        result.execution_time = time.time() - start_time
        return result
    
    def validate_configuration_loading(self) -> ValidationResult:
        """
        Validate that configuration can be loaded successfully
        
        Returns:
            ValidationResult with configuration validation results
        """
        start_time = time.time()
        result = ValidationResult(check_name="configuration_validation", success=True)
        
        try:
            # Test configuration loading
            config_test_script = """
import sys
import os
sys.path.insert(0, '.')

try:
    from src.core.config_manager import get_config
    config = get_config()
    
    # Test basic config access
    debug = config.get('app', 'debug', False)
    db_url = config.get('database', 'url', 'sqlite:///./test.db')
    
    print("Configuration loaded successfully")
    print(f"Debug mode: {debug}")
    print(f"Database URL type: {type(db_url)}")
    
except Exception as e:
    print(f"Configuration error: {e}")
    sys.exit(1)
"""
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(config_test_script)
                temp_script = f.name
            
            try:
                process = subprocess.run(
                    [self.python_executable, temp_script],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=self.project_root
                )
                
                if process.returncode == 0:
                    result.details["message"] = "Configuration loaded successfully"
                    result.details["output"] = process.stdout
                else:
                    result.success = False
                    result.errors.append(f"Configuration loading failed: {process.stderr.strip()}")
                    result.details["stderr"] = process.stderr
            
            finally:
                os.unlink(temp_script)
        
        except Exception as e:
            result.success = False
            result.errors.append(f"Failed to validate configuration: {str(e)}")
        
        result.execution_time = time.time() - start_time
        return result
    
    def validate_data_provider_imports(self) -> ValidationResult:
        """
        Validate that data provider imports work correctly
        
        Returns:
            ValidationResult with data provider validation results
        """
        start_time = time.time()
        result = ValidationResult(check_name="data_provider_validation", success=True)
        
        provider_test_script = """
import sys
sys.path.insert(0, '.')

providers_tested = []
errors = []

# Test data provider imports
try:
    from src.data.providers.manager import DataProviderManager
    providers_tested.append("DataProviderManager")
except Exception as e:
    errors.append(f"DataProviderManager: {e}")

try:
    from src.shared.data_providers.aggregator import DataProviderAggregator
    providers_tested.append("DataProviderAggregator")
except Exception as e:
    errors.append(f"DataProviderAggregator: {e}")

try:
    from src.shared.data_providers.alpha_vantage import AlphaVantageProvider
    providers_tested.append("AlphaVantageProvider")
except Exception as e:
    errors.append(f"AlphaVantageProvider: {e}")

try:
    from src.shared.data_providers.yfinance_provider import YFinanceProvider
    providers_tested.append("YFinanceProvider")
except Exception as e:
    errors.append(f"YFinanceProvider: {e}")

print(f"Successfully imported: {', '.join(providers_tested)}")
if errors:
    print(f"Import errors: {'; '.join(errors)}")
    sys.exit(1)
"""
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(provider_test_script)
                temp_script = f.name
            
            try:
                process = subprocess.run(
                    [self.python_executable, temp_script],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=self.project_root
                )
                
                if process.returncode == 0:
                    result.details["message"] = "Data provider imports successful"
                    result.details["output"] = process.stdout
                else:
                    result.success = False
                    result.errors.append(f"Data provider import failed: {process.stderr.strip()}")
                    result.details["stderr"] = process.stderr
            
            finally:
                os.unlink(temp_script)
        
        except Exception as e:
            result.success = False
            result.errors.append(f"Failed to validate data providers: {str(e)}")
        
        result.execution_time = time.time() - start_time
        return result
    
    def validate_pipeline_imports(self) -> ValidationResult:
        """
        Validate that pipeline imports work correctly
        
        Returns:
            ValidationResult with pipeline validation results
        """
        start_time = time.time()
        result = ValidationResult(check_name="pipeline_validation", success=True)
        
        pipeline_test_script = """
import sys
sys.path.insert(0, '.')

components_tested = []
errors = []

# Test pipeline imports
try:
    from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
    components_tested.append("execute_ask_pipeline")
except Exception as e:
    errors.append(f"execute_ask_pipeline: {e}")

try:
    from src.core.pipeline_engine import Pipeline, PipelineStage
    components_tested.append("Pipeline/PipelineStage")
except Exception as e:
    errors.append(f"Pipeline/PipelineStage: {e}")

try:
    from src.core.exceptions import PipelineError, DataProviderError
    components_tested.append("Exception classes")
except Exception as e:
    errors.append(f"Exception classes: {e}")

print(f"Successfully imported: {', '.join(components_tested)}")
if errors:
    print(f"Import errors: {'; '.join(errors)}")
    sys.exit(1)
"""
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(pipeline_test_script)
                temp_script = f.name
            
            try:
                process = subprocess.run(
                    [self.python_executable, temp_script],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=self.project_root
                )
                
                if process.returncode == 0:
                    result.details["message"] = "Pipeline imports successful"
                    result.details["output"] = process.stdout
                else:
                    result.success = False
                    result.errors.append(f"Pipeline import failed: {process.stderr.strip()}")
                    result.details["stderr"] = process.stderr
            
            finally:
                os.unlink(temp_script)
        
        except Exception as e:
            result.success = False
            result.errors.append(f"Failed to validate pipeline: {str(e)}")
        
        result.execution_time = time.time() - start_time
        return result
    
    def run_existing_tests(self) -> ValidationResult:
        """
        Run existing test suite to ensure functionality is preserved
        
        Returns:
            ValidationResult with test execution results
        """
        start_time = time.time()
        result = ValidationResult(check_name="existing_tests", success=True)
        
        try:
            # Look for test files
            test_files = []
            for pattern in ["test_*.py", "*_test.py"]:
                test_files.extend(self.project_root.glob(pattern))
                test_files.extend(self.project_root.glob(f"**/{pattern}"))
            
            if not test_files:
                result.warnings.append("No test files found")
                result.details["message"] = "No existing tests to run"
                result.execution_time = time.time() - start_time
                return result
            
            # Try to run pytest if available
            try:
                process = subprocess.run(
                    [self.python_executable, "-m", "pytest", "--tb=short", "-v"],
                    capture_output=True,
                    text=True,
                    timeout=300,  # 5 minutes timeout
                    cwd=self.project_root
                )
                
                result.details["test_output"] = process.stdout
                result.details["test_errors"] = process.stderr
                
                if process.returncode == 0:
                    result.details["message"] = "All tests passed"
                else:
                    result.success = False
                    result.errors.append("Some tests failed")
                    
            except subprocess.TimeoutExpired:
                result.success = False
                result.errors.append("Test execution timed out after 5 minutes")
            except FileNotFoundError:
                # pytest not available, try running individual test files
                result.warnings.append("pytest not available, skipping test execution")
                result.details["message"] = "Could not run tests (pytest not found)"
        
        except Exception as e:
            result.success = False
            result.errors.append(f"Failed to run tests: {str(e)}")
        
        result.execution_time = time.time() - start_time
        return result
    
    def run_comprehensive_validation(self, files_to_check: Optional[List[str]] = None) -> ValidationSuite:
        """
        Run comprehensive validation suite
        
        Args:
            files_to_check: Optional list of specific files to validate
            
        Returns:
            ValidationSuite with all validation results
        """
        suite = ValidationSuite(suite_name="comprehensive_validation")
        start_time = time.time()
        
        logger.info("Starting comprehensive validation suite")
        
        # Configuration validation
        config_result = self.validate_configuration_loading()
        suite.results.append(config_result)
        if not config_result.success:
            suite.overall_success = False
        
        # Data provider validation
        provider_result = self.validate_data_provider_imports()
        suite.results.append(provider_result)
        if not provider_result.success:
            suite.overall_success = False
        
        # Pipeline validation
        pipeline_result = self.validate_pipeline_imports()
        suite.results.append(pipeline_result)
        if not pipeline_result.success:
            suite.overall_success = False
        
        # File-specific validation
        if files_to_check:
            for file_path in files_to_check:
                if os.path.exists(file_path) and file_path.endswith('.py'):
                    # Syntax validation
                    syntax_result = self.validate_syntax(file_path)
                    suite.results.append(syntax_result)
                    if not syntax_result.success:
                        suite.overall_success = False
                    
                    # Import validation (only if syntax is valid)
                    if syntax_result.success:
                        import_result = self.validate_imports(file_path)
                        suite.results.append(import_result)
                        if not import_result.success:
                            suite.overall_success = False
        
        # Run existing tests
        test_result = self.run_existing_tests()
        suite.results.append(test_result)
        if not test_result.success:
            suite.overall_success = False
        
        suite.total_execution_time = time.time() - start_time
        
        # Log summary
        passed = sum(1 for r in suite.results if r.success)
        total = len(suite.results)
        logger.info(f"Validation complete: {passed}/{total} checks passed")
        
        return suite
    
    def generate_validation_report(self, suite: ValidationSuite) -> Dict[str, Any]:
        """
        Generate detailed validation report
        
        Args:
            suite: ValidationSuite to generate report for
            
        Returns:
            Dictionary containing detailed validation report
        """
        passed_checks = [r for r in suite.results if r.success]
        failed_checks = [r for r in suite.results if not r.success]
        
        report = {
            "summary": {
                "suite_name": suite.suite_name,
                "overall_success": suite.overall_success,
                "total_checks": len(suite.results),
                "passed_checks": len(passed_checks),
                "failed_checks": len(failed_checks),
                "total_execution_time": suite.total_execution_time
            },
            "results": [
                {
                    "check_name": r.check_name,
                    "success": r.success,
                    "errors": r.errors,
                    "warnings": r.warnings,
                    "execution_time": r.execution_time,
                    "details": r.details
                }
                for r in suite.results
            ],
            "recommendations": []
        }
        
        # Generate recommendations
        if failed_checks:
            report["recommendations"].append("Address failed validation checks before proceeding with cleanup")
        
        if any(r.warnings for r in suite.results):
            report["recommendations"].append("Review validation warnings for potential issues")
        
        if suite.overall_success:
            report["recommendations"].append("All validations passed - safe to proceed with cleanup operations")
        
        return report

# Global validation framework instance
validator = ValidationFramework()