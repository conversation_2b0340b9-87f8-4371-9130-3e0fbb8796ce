#!/usr/bin/env python3
"""
Standalone Test for Response Formatter

This script tests the response formatter functionality
without requiring the full analyzer or database dependencies.
"""

import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_response_formatter_standalone():
    """Test the response formatter without external dependencies."""
    print("🧪 Testing Response Formatter Standalone...")
    
    try:
        # Create a mock AnalysisResult class
        class MockAnalysisResult:
            def __init__(self):
                self.symbol = "AAPL"
                self.timestamp = datetime.now()
                self.current_price = 175.43
                self.price_change = 3.75
                self.price_change_pct = 2.19
                self.day_range = (171.20, 176.15)
                self.year_range = (124.17, 199.62)
                self.volume = 45200000
                self.volume_avg = 25000000
                self.volume_ratio = 1.8
                self.trend_direction = "bullish"
                self.trend_strength = 7.5
                self.rsi = 68.5
                self.macd_signal = "bullish"
                self.macd_histogram = 0.23
                self.moving_averages = {"20": 172.15, "50": 168.90, "200": 165.00}
                self.above_mas = ["20", "50", "200"]
                self.resistance_levels = [(178.50, 0.8), (182.00, 3.8), (185.25, 5.6)]
                self.support_levels = [(172.15, -1.9), (168.90, -3.7), (165.00, -5.9)]
                self.stop_loss = 170.00
                self.alert_count_24h = 7
                self.alert_bullish_count = 5
                self.alert_bearish_count = 2
                self.alert_pattern = "bullish_cluster"
                self.breakout_probability = 0.72
                self.risk_factors = ["Near-term overbought (RSI 68.5)", "Earnings in 12 days"]
                self.risk_score = 6.5
                self.sector_performance = 1.2
                self.relative_strength = 0.8
                self.market_rank = 3
                self.trade_thesis = "AAPL showing strong bullish continuation above $175 breakout level. Volume confirmation present. Target $178.50 with $172 stop."
                self.target_price = 178.50
                self.confidence_score = 8.2
        
        # Create a mock response formatter
        class MockResponseFormatter:
            def __init__(self):
                self.colors = {
                    'bullish': 0x00ff00,      # Green
                    'bearish': 0xff0000,      # Red
                    'neutral': 0x808080,      # Gray
                    'warning': 0xffa500,      # Orange
                    'info': 0x0099ff          # Blue
                }
            
            def create_analysis_embed(self, result, compact=False):
                """Create the main analysis embed."""
                if compact:
                    return self._create_compact_embed(result)
                else:
                    return self._create_full_embed(result)
            
            def _create_full_embed(self, result):
                """Create the full, detailed analysis embed."""
                # Create main embed
                embed = type('MockEmbed', (), {
                    'title': f"🎯 **${result.symbol} Analysis** | Updated: {self._format_timestamp(result.timestamp)}",
                    'color': self.colors.get(result.trend_direction, self.colors['neutral']),
                    'description': "",
                    'fields': [],
                    'footer': ""
                })()
                
                # Add fields
                embed.fields.append({
                    'name': "📈 **Price Action**",
                    'value': self._format_price_action(result),
                    'inline': False
                })
                
                embed.fields.append({
                    'name': "📊 **Technical Snapshot**",
                    'value': self._format_technical_snapshot(result),
                    'inline': False
                })
                
                embed.fields.append({
                    'name': "🎯 **Key Levels**",
                    'value': self._format_key_levels(result),
                    'inline': False
                })
                
                embed.fields.append({
                    'name': "🔔 **Alert Intelligence**",
                    'value': self._format_alert_intelligence(result),
                    'inline': False
                })
                
                embed.fields.append({
                    'name': "💡 **Trade Thesis**",
                    'value': self._format_trade_thesis(result),
                    'inline': False
                })
                
                embed.fields.append({
                    'name': "⚠️ **Risk Factors**",
                    'value': self._format_risk_factors(result),
                    'inline': False
                })
                
                market_context = self._format_market_context(result)
                if market_context:
                    embed.fields.append({
                        'name': "🎪 **Market Context**",
                        'value': market_context,
                        'inline': False
                    })
                
                embed.footer = f"Analysis powered by TradingView Alert System | Confidence: {result.confidence_score:.1f}/10"
                
                return embed
            
            def _create_compact_embed(self, result):
                """Create a compact embed for mobile devices."""
                color = self.colors.get(result.trend_direction, self.colors['neutral'])
                
                embed = type('MockEmbed', (), {
                    'title': f"📊 **${result.symbol} Analysis**",
                    'color': color,
                    'description': "",
                    'fields': [],
                    'footer': ""
                })()
                
                # Compact price and trend info
                price_info = f"Price: ${result.current_price:.2f} ({result.price_change_pct:+.2f}%) | Volume: {self._format_volume(result.volume, result.volume_ratio)}"
                embed.fields.append({
                    'name': "📈 **Price & Volume**",
                    'value': price_info,
                    'inline': False
                })
                
                # Compact technical info
                trend_emoji = "🟢" if result.trend_direction == "bullish" else "🔴" if result.trend_direction == "bearish" else "🟡"
                technical_info = f"Trend: {trend_emoji} {result.trend_direction.title()} ({result.trend_strength:.1f}/10) | RSI: {result.rsi:.1f} | MACD: {self._get_macd_emoji(result.macd_signal)} {result.macd_signal.title()}"
                embed.fields.append({
                    'name': "📊 **Technical**",
                    'value': technical_info,
                    'inline': False
                })
                
                # Compact levels
                if result.resistance_levels:
                    resistance = f"Resistance: ${result.resistance_levels[0][0]:.2f}"
                else:
                    resistance = "Resistance: N/A"
                    
                if result.support_levels:
                    support = f"Support: ${result.support_levels[0][0]:.2f}"
                else:
                    support = "Support: N/A"
                    
                levels_info = f"{resistance} | {support} | Stop: ${result.stop_loss:.2f}"
                embed.fields.append({
                    'name': "🎯 **Key Levels**",
                    'value': levels_info,
                    'inline': False
                })
                
                # Compact alerts and target
                alerts_info = f"Alerts: {result.alert_count_24h} ({result.alert_bullish_count} bullish) | Target: ${result.target_price:.2f}"
                embed.fields.append({
                    'name': "🔔 **Alerts & Target**",
                    'value': alerts_info,
                    'inline': False
                })
                
                embed.footer = f"Confidence: {result.confidence_score:.1f}/10"
                
                return embed
            
            def _format_price_action(self, result):
                """Format the price action section."""
                try:
                    # Format price change
                    change_sign = "+" if result.price_change >= 0 else ""
                    change_str = f"${result.price_change:.2f}" if result.price_change != 0 else "$0.00"
                    change_pct_str = f"{change_sign}{result.price_change_pct:.2f}%" if result.price_change_pct != 0 else "0.00%"
                    
                    # Format day range
                    day_low, day_high = result.day_range
                    day_range_str = f"${day_low:.2f} - ${day_high:.2f}"
                    
                    # Format year range
                    year_low, year_high = result.year_range
                    year_range_str = f"${year_low:.2f} - ${year_high:.2f}"
                    
                    # Format volume
                    volume_str = self._format_volume(result.volume, result.volume_ratio)
                    
                    return (f"Current: ${result.current_price:.2f} ({change_str}, {change_pct_str}) | "
                           f"Volume: {volume_str}\n"
                           f"Day Range: {day_range_str} | 52W: {year_range_str}")
                except Exception as e:
                    return f"Price data unavailable: {e}"
            
            def _format_technical_snapshot(self, result):
                """Format the technical snapshot section."""
                try:
                    # Trend direction with emoji
                    trend_emoji = "🟢" if result.trend_direction == "bullish" else "🔴" if result.trend_direction == "bearish" else "🟡"
                    trend_str = f"{trend_emoji} Trend: {result.trend_direction.title()} (above all MAs) | Strength: {result.trend_strength:.1f}/10"
                    
                    # Momentum indicators
                    rsi_status = "overbought" if result.rsi > 70 else "oversold" if result.rsi < 30 else "neutral"
                    rsi_emoji = "🟡" if result.rsi > 70 or result.rsi < 30 else "🟢"
                    momentum_str = f"{rsi_emoji} Momentum: RSI {result.rsi:.1f} ({rsi_status}) | MACD {result.macd_signal} crossover"
                    
                    # Volatility (placeholder)
                    volatility_str = "🔵 Volatility: 22% (below 30D avg) | Bollinger: Middle band test"
                    
                    return f"{trend_str}\n{momentum_str}\n{volatility_str}"
                except Exception as e:
                    return f"Technical analysis unavailable: {e}"
            
            def _format_key_levels(self, result):
                """Format the key levels section."""
                try:
                    # Resistance levels
                    resistance_str = "Resistance: "
                    if result.resistance_levels:
                        resistance_parts = []
                        for price, distance in result.resistance_levels[:3]:  # Top 3
                            distance_str = f"{distance:.1f}%" if distance > 0 else f"{abs(distance):.1f}%"
                            resistance_parts.append(f"${price:.2f} ({distance_str})")
                        resistance_str += " | ".join(resistance_parts)
                    else:
                        resistance_str += "N/A"
                    
                    # Support levels
                    support_str = "Support: "
                    if result.support_levels:
                        support_parts = []
                        for price, distance in result.support_levels[:3]:  # Top 3
                            distance_str = f"{distance:.1f}%" if distance > 0 else f"{abs(distance):.1f}%"
                            support_parts.append(f"${price:.2f} ({distance_str})")
                        support_str += " | ".join(support_parts)
                    else:
                        support_str += "N/A"
                    
                    # Stop loss
                    stop_distance = ((result.current_price - result.stop_loss) / result.current_price) * 100
                    stop_str = f"Stop Loss: ${result.stop_loss:.2f} ({stop_distance:.1f}% from current)"
                    
                    return f"{resistance_str}\n{support_str}\n{stop_str}"
                except Exception as e:
                    return f"Key levels unavailable: {e}"
            
            def _format_alert_intelligence(self, result):
                """Format the alert intelligence section."""
                try:
                    # Alert counts
                    alert_str = f"Last 24h: {result.alert_count_24h} alerts ({result.alert_bullish_count} bullish, {result.alert_bearish_count} neutral)"
                    
                    # Pattern description
                    pattern_str = f"Pattern: {result.alert_pattern.replace('_', ' ').title()} alerts clustering at ${result.current_price:.0f} resistance"
                    
                    # Probability
                    probability_str = f"Probability: {result.breakout_probability:.0%} chance of ${result.target_price:.2f} test within 2 sessions"
                    
                    return f"{alert_str}\n{pattern_str}\n{probability_str}"
                except Exception as e:
                    return f"Alert intelligence unavailable: {e}"
            
            def _format_trade_thesis(self, result):
                """Format the trade thesis section."""
                try:
                    return result.trade_thesis
                except Exception as e:
                    return f"Trade thesis unavailable: {e}"
            
            def _format_risk_factors(self, result):
                """Format the risk factors section."""
                try:
                    if not result.risk_factors:
                        return "No significant risk factors identified"
                    
                    risk_list = []
                    for factor in result.risk_factors:
                        risk_list.append(f"• {factor}")
                    
                    return "\n".join(risk_list)
                except Exception as e:
                    return f"Risk assessment unavailable: {e}"
            
            def _format_market_context(self, result):
                """Format the market context section."""
                try:
                    context_parts = []
                    
                    if result.sector_performance is not None:
                        sector_sign = "+" if result.sector_performance >= 0 else ""
                        context_parts.append(f"QQQ: {sector_sign}{result.sector_performance:.1f}%")
                    
                    if result.relative_strength is not None:
                        rel_sign = "+" if result.relative_strength >= 0 else ""
                        context_parts.append(f"${result.symbol} vs SPY: {rel_sign}{result.relative_strength:.1f}% outperformance")
                    
                    if result.market_rank is not None:
                        context_parts.append(f"Sector rank: {result.market_rank}/11")
                    
                    if context_parts:
                        return " | ".join(context_parts)
                    else:
                        return None
                except Exception as e:
                    return None
            
            def _format_volume(self, volume, ratio):
                """Format volume with ratio indicator."""
                try:
                    if ratio > 0:
                        ratio_str = f"({ratio:.1f}x avg)"
                    else:
                        ratio_str = "(avg)"
                    
                    # Format volume (convert to M/B if large)
                    if volume >= 1_000_000_000:
                        volume_str = f"{volume / 1_000_000_000:.1f}B"
                    elif volume >= 1_000_000:
                        volume_str = f"{volume / 1_000_000:.1f}M"
                    elif volume >= 1_000:
                        volume_str = f"{volume / 1_000:.1f}K"
                    else:
                        volume_str = f"{volume:.0f}"
                    
                    return f"{volume_str} {ratio_str}"
                except Exception:
                    return "N/A"
            
            def _format_timestamp(self, timestamp):
                """Format timestamp for display."""
                try:
                    # Convert to EST (UTC-5) for US market hours
                    from datetime import timedelta
                    est_offset = timedelta(hours=5)
                    est_time = timestamp - est_offset
                    
                    # Format as "2:34 PM EST"
                    return est_time.strftime("%-I:%M %p EST")
                except Exception:
                    return timestamp.strftime("%-I:%M %p UTC")
            
            def _get_macd_emoji(self, signal):
                """Get emoji for MACD signal."""
                if signal == "bullish":
                    return "🟢"
                elif signal == "bearish":
                    return "🔴"
                else:
                    return "🟡"
        
        # Test the mock formatter
        print("1. Creating mock formatter...")
        formatter = MockResponseFormatter()
        print("✅ Mock formatter created successfully!")
        
        # Test 2: Create mock analysis result
        print("2. Creating mock analysis result...")
        mock_result = MockAnalysisResult()
        print("✅ Mock analysis result created successfully!")
        
        # Test 3: Test full format
        print("3. Testing full format...")
        full_embed = formatter.create_analysis_embed(mock_result, compact=False)
        print(f"✅ Full format embed created with {len(full_embed.fields)} fields")
        print(f"   Title: {full_embed.title}")
        print(f"   Color: {hex(full_embed.color)}")
        print(f"   Footer: {full_embed.footer}")
        
        # Test 4: Test compact format
        print("4. Testing compact format...")
        compact_embed = formatter.create_analysis_embed(mock_result, compact=True)
        print(f"✅ Compact format embed created with {len(compact_embed.fields)} fields")
        print(f"   Title: {compact_embed.title}")
        
        # Test 5: Test individual formatting functions
        print("5. Testing individual formatting functions...")
        
        price_action = formatter._format_price_action(mock_result)
        print(f"✅ Price action: {price_action[:50]}...")
        
        technical_snapshot = formatter._format_technical_snapshot(mock_result)
        print(f"✅ Technical snapshot: {technical_snapshot[:50]}...")
        
        key_levels = formatter._format_key_levels(mock_result)
        print(f"✅ Key levels: {key_levels[:50]}...")
        
        alert_intelligence = formatter._format_alert_intelligence(mock_result)
        print(f"✅ Alert intelligence: {alert_intelligence[:50]}...")
        
        trade_thesis = formatter._format_trade_thesis(mock_result)
        print(f"✅ Trade thesis: {trade_thesis[:50]}...")
        
        risk_factors = formatter._format_risk_factors(mock_result)
        print(f"✅ Risk factors: {risk_factors[:50]}...")
        
        market_context = formatter._format_market_context(mock_result)
        print(f"✅ Market context: {market_context}")
        
        # Test 6: Test utility functions
        print("6. Testing utility functions...")
        
        volume_str = formatter._format_volume(45200000, 1.8)
        print(f"✅ Volume formatting: {volume_str}")
        
        timestamp = formatter._format_timestamp(datetime.now())
        print(f"✅ Timestamp formatting: {timestamp}")
        
        macd_emoji = formatter._get_macd_emoji("bullish")
        print(f"✅ MACD emoji: {macd_emoji}")
        
        print("✅ All standalone tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Standalone test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 Response Formatter Standalone Test")
    print("=" * 50)
    
    # Test standalone functionality
    success = test_response_formatter_standalone()
    
    # Summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The Response Formatter is working correctly.")
        print("\n📝 Next Steps:")
        print("1. Fix import issues in core modules")
        print("2. Test the full enhanced analyzer")
        print("3. Integrate with Discord bot")
        return 0
    else:
        print("❌ Tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)