# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=tradingview
POSTGRES_USER=tradingview
# POSTGRES_PASSWORD must be set in .env.secure
# DO NOT set a default password here

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Security
# All secrets must be set in .env.secure
# Required secrets:
# - POSTGRES_PASSWORD
# - DISCORD_BOT_TOKEN
# - OPENAI_API_KEY
# - TRADINGVIEW_TOKEN