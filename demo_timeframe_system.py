#!/usr/bin/env python3
"""
Demonstration of the Timeframe Service System

This script shows how the system processes 1-minute data and builds
higher timeframes for analysis.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.timeframe_service import TimeframeEngine
from services.alert_service import HeartbeatMonitor
from services.market_stream import MarketStream

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_data():
    """Create realistic sample market data for demonstration."""
    base_time = datetime.now().replace(second=0, microsecond=0)
    base_price = 175.0
    
    # Create 20 minutes of 1-minute data with some volatility
    candles = []
    for i in range(20):
        # Simulate some price movement
        price_change = (i % 5 - 2) * 0.1  # Oscillating pattern
        volume_spike = 1.5 if i in [5, 10, 15] else 1.0  # Volume spikes every 5 minutes
        
        candle = type('MockCandle', (), {
            'timestamp': base_time + timedelta(minutes=i),
            'open': base_price + price_change,
            'high': base_price + price_change + 0.3,
            'low': base_price + price_change - 0.2,
            'close': base_price + price_change + 0.1,
            'volume': int(500000 * volume_spike),
            'ticker': 'AAPL',
            'timeframe': '1m'
        })()
        candles.append(candle)
    
    return candles

def demonstrate_timeframe_building():
    """Demonstrate how timeframes are built from 1-minute data."""
    print("🏗️  **Timeframe Building Demonstration**")
    print("=" * 50)
    
    # Create engine
    engine = TimeframeEngine(base_resolution="1m", secondary_tf="5m")
    
    # Create sample data
    sample_candles = create_sample_data()
    print(f"📊 Created {len(sample_candles)} sample 1-minute candles")
    
    # Store the data
    for candle in sample_candles:
        engine._store_test_candle("AAPL", "1m", candle)
    
    print("💾 Stored sample data in test storage")
    
    # Build higher timeframes
    print("\n🔄 Building higher timeframes...")
    
    # Get all timeframes
    all_timeframes = engine.get_all_timeframes("AAPL")
    
    for tf, candles in all_timeframes.items():
        if candles:
            print(f"  • {tf}: {len(candles)} candles")
            if len(candles) > 0:
                latest = candles[-1]
                print(f"    Latest: ${latest.close:.2f} @ {latest.timestamp.strftime('%H:%M')}")
        else:
            print(f"  • {tf}: No data")
    
    return engine, all_timeframes

def demonstrate_alert_monitoring(engine, timeframes):
    """Demonstrate alert monitoring capabilities."""
    print("\n🔔 **Alert Monitoring Demonstration**")
    print("=" * 50)
    
    # Create alert monitor
    monitor = HeartbeatMonitor()
    
    # Check for alerts
    alert_data = monitor.check_ticker("AAPL", engine)
    
    print("📊 Alert Analysis Results:")
    for alert_type, data in alert_data.items():
        if alert_type == "timestamp":
            continue
        if data and isinstance(data, dict) and "error" not in data:
            print(f"  • {alert_type}: {len(data)} alerts detected")
        elif data and isinstance(data, dict) and "error" in data:
            print(f"  • {alert_type}: {data['error']}")
        else:
            print(f"  • {alert_type}: No alerts")
    
    # Generate summary
    summary = monitor.generate_alert_summary("AAPL", alert_data)
    print(f"\n📋 Alert Summary:\n{summary}")
    
    return monitor

def demonstrate_market_stream():
    """Demonstrate market stream processing."""
    print("\n🌊 **Market Stream Demonstration**")
    print("=" * 50)
    
    # Create stream
    stream = MarketStream()
    
    # Create sample webhook data
    webhook_data = {
        'symbol': 'TSLA',
        'timestamp': datetime.now().isoformat(),
        'open': 250.0,
        'high': 251.5,
        'low': 249.2,
        'close': 250.8,
        'volume': 750000,
        'timeframe': '1m'
    }
    
    print("📡 Processing sample TradingView webhook...")
    
    # Process webhook
    success = stream.process_tradingview_webhook(webhook_data)
    
    if success:
        print("✅ Webhook processed successfully")
    else:
        print("❌ Webhook processing failed")
    
    # Get stream status
    status = stream.get_status()
    print(f"📊 Stream Status: Running={status['is_running']}")
    
    return stream

async def demonstrate_integration():
    """Demonstrate full system integration."""
    print("\n🔗 **System Integration Demonstration**")
    print("=" * 50)
    
    # Create all components
    engine = TimeframeEngine()
    monitor = HeartbeatMonitor()
    stream = MarketStream()
    
    # Connect components
    stream.set_timeframe_engine(engine)
    stream.set_alert_monitor(monitor)
    
    print("✅ All components connected")
    
    # Start stream
    stream.start_monitoring()
    print("🚀 Market stream started")
    
    # Process some data
    print("📊 Processing sample market data...")
    
    # Simulate multiple tickers
    tickers = ["AAPL", "TSLA", "NVDA", "MSFT"]
    for ticker in tickers:
        await stream.simulate_market_data(ticker, "1m")
        print(f"  • {ticker}: Data processed")
    
    # Get system status
    status = stream.get_status()
    print(f"\n📊 System Status:")
    print(f"  • Stream Running: {status['is_running']}")
    print(f"  • Subscribers: {status['subscribers']}")
    print(f"  • Engine Connected: {status['timeframe_engine_connected']}")
    print(f"  • Monitor Connected: {status['alert_monitor_connected']}")
    
    # Stop stream
    stream.stop_monitoring()
    print("🛑 Market stream stopped")
    
    return True

async def main():
    """Run the complete demonstration."""
    print("🚀 **Timeframe Service System Demonstration**")
    print("=" * 60)
    print("This demonstration shows how the system:")
    print("• Builds higher timeframes from 1-minute data")
    print("• Monitors for real-time alerts")
    print("• Processes market data streams")
    print("• Integrates all components seamlessly")
    print("=" * 60)
    
    try:
        # Demonstrate timeframe building
        engine, timeframes = demonstrate_timeframe_building()
        
        # Demonstrate alert monitoring
        monitor = demonstrate_alert_monitoring(engine, timeframes)
        
        # Demonstrate market stream
        stream = demonstrate_market_stream()
        
        # Demonstrate integration
        await demonstrate_integration()
        
        print("\n🎉 **Demonstration Complete!**")
        print("The timeframe service system is working correctly and ready for production use.")
        
    except Exception as e:
        print(f"\n❌ **Demonstration Failed:** {e}")
        logger.error(f"Demonstration error: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main()) 