from datetime import datetime
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field, validator


class FeedbackSubmission(BaseModel):
    """
    Schema for submitting user feedback
    """
    response: Dict[str, Any] = Field(..., description="The original AI response")
    rating: int = Field(..., ge=1, le=5, description="User rating (1-5)")
    comment: Optional[str] = Field(None, description="Optional user comment")
    feedback_type: Optional[str] = Field("general", description="Type of feedback")

    @validator('rating')
    def validate_rating(cls, rating):
        """Validate rating is between 1 and 5"""
        if rating < 1 or rating > 5:
            raise ValueError("Rating must be between 1 and 5")
        return rating

class FeedbackReport(BaseModel):
    """
    Comprehensive feedback report schema
    """
    total_feedback_entries: int = Field(0, description="Total number of feedback entries")
    feedback_by_type: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, 
        description="Feedback metrics by response type"
    )
    average_ratings: Dict[str, float] = Field(
        default_factory=dict, 
        description="Average ratings by response type"
    )
    feedback_trends: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Feedback trends over time"
    )
    sentiment_analysis: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Sentiment analysis of feedback"
    )
    confidence_metrics: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Confidence-related metrics"
    )
    response_length_metrics: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Response length metrics"
    )
    timestamp: Optional[datetime] = Field(
        default_factory=datetime.now, 
        description="Timestamp of report generation"
    )
    insights: Optional[Dict[str, Any]] = Field(
        default_factory=dict, 
        description="Actionable insights derived from feedback"
    )

    class Config:
        """Pydantic configuration"""
        json_schema_extra = {
            "example": {
                "total_feedback_entries": 500,
                "feedback_by_type": {
                    "stock_analysis": {
                        "total_entries": 300,
                        "average_rating": 4.2
                    }
                },
                "sentiment_analysis": {
                    "positive_count": 350,
                    "negative_count": 100,
                    "average_sentiment_score": 0.7
                }
            }
        } 