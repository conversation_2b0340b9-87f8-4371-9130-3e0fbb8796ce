import time
from typing import Callable, Optional

from fastapi import Request, HTTPException, status
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response

from src.core.config_manager import get_config
from src.core.logger import get_logger
from src.core.security import decode_token, hash_password

logger = get_logger(__name__)

def _get_client_ip(request: Request) -> str:
    """
    Resolve client IP, trusting X-Forwarded-For when present (first hop).
    Use only when behind a trusted proxy.
    """
    xff = request.headers.get("x-forwarded-for")
    if xff:
        return xff.split(",")[0].strip()
    return request.client.host

class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware to implement rate limiting for API requests.
    Uses an in-memory tracking mechanism.
    """
    def __init__(
        self, 
        app, 
        rate_limit: int = 100,  # requests per window
        window: int = 60,  # seconds
        exempt_routes: Optional[list] = None
    ):
        super().__init__(app)
        self.rate_limit = rate_limit
        self.window = window
        self.request_counts = {}
        # Only exempt metrics endpoint, apply rate limiting to health checks
        self.exempt_routes = exempt_routes or ['/metrics']
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        # Check if route is exempt from rate limiting
        if any(route in request.url.path for route in self.exempt_routes):
            return await call_next(request)
        
        # Get client IP (trust X-Forwarded-For when present)
        client_ip = _get_client_ip(request)
        current_time = time.time()
        
        # Clean up old request records - handle None values safely
        self.request_counts = {
            ip: [t for t in times if t is not None and current_time - t <= self.window]
            for ip, times in self.request_counts.items()
            if times is not None
        }
        
        # Check rate limit
        if client_ip in self.request_counts:
            if len(self.request_counts[client_ip]) >= self.rate_limit:
                logger.warning(f"Rate limit exceeded for IP {client_ip}")
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Too many requests. Please slow down."
                )
            self.request_counts[client_ip].append(current_time)
        else:
            self.request_counts[client_ip] = [current_time]
        
        return await call_next(request)

class JWTAuthMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle JWT authentication and authorization.
    """
    def __init__(
        self, 
        app, 
        exempt_routes: Optional[list] = None,
        admin_routes: Optional[list] = None
    ):
        super().__init__(app)
        # Narrow exemptions; do not blanket-exempt market routes
        # Exempt key public routes; substring match used in dispatch()
        self.exempt_routes = exempt_routes or [
            '/health',
            '/test',  # Add test endpoint to exemptions
            '/docs',
            '/openapi.json',
            '/metrics',           # allow metrics without auth (e.g., /api/v1/metrics/*)
        ]
        self.admin_routes = admin_routes or ['/admin', '/management']
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        # Exempt routes from authentication
        if any(route in request.url.path for route in self.exempt_routes):
            return await call_next(request)
        
        # Check for Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        
        try:
            # Extract and verify JWT token
            token = auth_header.split(' ')[1] if len(auth_header.split(' ')) > 1 else None
            if not token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token format"
                )
            
            # Verify token
            payload = decode_token(token)
            
            # Check admin routes
            if any(route in request.url.path for route in self.admin_routes):
                if not payload.get('is_admin', False):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Admin access required"
                    )
            
            # Attach user info to request state
            request.state.user = payload
        
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )
        
        return await call_next(request)

class SecurityHeaderMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security-related HTTP headers.
    """
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        response = await call_next(request)
        
        # Security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        # Only set HSTS when behind HTTPS (e.g., via reverse proxy header)
        if request.headers.get("x-forwarded-proto", "").lower() == "https":
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Remove server information
        response.headers['Server'] = ''
        
        return response

def setup_security_middleware(app):
    """
    Configure and add security middleware to FastAPI application.
    """
    config = get_config()
    
    # TODO: Implement proper rate limiting middleware
    # For now, comment out the problematic rate limiter
    # app.add_middleware(
    #     RateLimiter,
    #     per_second=10,
    #     burst_limit=30,
    #     on_error=lambda: None  # Gracefully handle `None`
    # )
    
    # JWT Authentication
    app.add_middleware(JWTAuthMiddleware)
    
    # Security Headers
    app.add_middleware(SecurityHeaderMiddleware)
    
    # Trusted Host (optional, configure in settings)
    allowed_hosts = config.get("security", "ALLOWED_HOSTS", [])
    if allowed_hosts:
        app.add_middleware(
            TrustedHostMiddleware, 
            allowed_hosts=allowed_hosts
        )
    
    return app 