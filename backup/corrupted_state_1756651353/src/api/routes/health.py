from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status
from sqlalchemy import text

from src.core.config_manager import get_config
from src.core.logger import get_logger
from src.core.monitoring import SystemMonitor
from src.database.connection import engine

            # Use the initialized engine from src.database.connection which
            # already applied fallback/normalization logic
            with engine.connect() as connection:
                connection.execute(text("SELECT 1"))
            database_status = "healthy"
        except Exception as db_error:
            logger.warning(f"Database health check failed: {db_error}")
            database_status = "unreachable"
            database_error = str(db_error)
        
        # Determine overall health status
        # Don't fail health check just because database is unreachable
        # Safely extract metrics with fallbacks for error cases
        cpu_percent = system_health.get('cpu_metrics', {}).get('cpu_percent', 0)
        memory_percent = system_health.get('memory_metrics', {}).get('memory_percent', 0)
        disk_percent = system_health.get('disk_metrics', {}).get('disk_percent', 0)
        
        # Check if any metrics have errors
        has_errors = any([
            'error' in system_health.get('cpu_metrics', {}),
            'error' in system_health.get('memory_metrics', {}),
            'error' in system_health.get('disk_metrics', {})
        ])
        
        if has_errors:
            system_healthy = False
        else:
            # Use reasonable defaults for thresholds
            max_cpu = 90  # 90% CPU threshold
            max_memory = 90  # 90% memory threshold
            max_disk = 90  # 90% disk threshold
            
            system_healthy = all([
                cpu_percent < max_cpu,
                memory_percent < max_memory,
                disk_percent < max_disk
            ])
        
        # Overall status is healthy if system is healthy, regardless of database
        overall_status = "healthy" if system_healthy else "degraded"
        
        # Combine health checks
        health_status = {
            "status": overall_status,
            "system": system_health,
            "database": {
                "status": database_status,
                "error": database_error
            },
            "services": {
                "api": "healthy",
                "bot": "healthy"  # Add more service checks as needed
            }
        }
        
        return health_status
    
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE, 
            detail="System health check failed"
        )

@router.get("/test", tags=["System"])
async def test_endpoint():
    """Simple test endpoint to verify API functionality"""
    return {"message": "API test successful", "timestamp": "2025-08-26"} 