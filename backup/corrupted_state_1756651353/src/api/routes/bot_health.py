import asyncio
from datetime import datetime, timedelta
import time
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, status
import psutil

from src.api.routes.health import health_check
from src.bot.client import TradingBot
from src.core.logger import get_logger
from src.core.monitoring import SystemMonitor
        
        system_data = await system_health()
        bot_data = await bot_health_check()
        
        # Combine and enhance
        combined_health = {
            "overall_status": "healthy",
            "services": {
                "api": system_data.get("services", {}).get("api", "unknown"),
                "bot": bot_data["bot"]["mode"],
                "database": system_data.get("database", {}).get("status", "unknown"),
                "system": system_data.get("status", "unknown")
            },
            "details": {
                "system": system_data,
                "bot": bot_data
            },
            "metrics": {
                "process_uptime": get_process_uptime(),
                "system_uptime": get_system_uptime(),
                "memory_usage": psutil.virtual_memory().percent,
                "cpu_usage": psutil.cpu_percent(interval=1),
                "disk_usage": psutil.disk_usage('/').percent
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Determine overall status
        critical_services = ["api", "system"]
        degraded_services = []
        
        for service, status_val in combined_health["services"].items():
            if status_val in ["unhealthy", "unreachable", "error"]:
                combined_health["overall_status"] = "unhealthy"
                degraded_services.append(service)
            elif status_val in ["degraded", "no_bot_instance"]:
                if combined_health["overall_status"] == "healthy":
                    combined_health["overall_status"] = "degraded"
                degraded_services.append(service)
        
        if degraded_services:
            combined_health["degraded_services"] = degraded_services
        
        return combined_health
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Detailed health check failed: {str(e)}"
        )

@router.get("/health/metrics", tags=["Monitoring"], status_code=status.HTTP_200_OK)
async def system_metrics() -> Dict[str, Any]:
    """
    Get real-time system metrics.
    
    Returns:
        Current system resource usage and performance metrics
    """
    try:
        # Get system metrics
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Network stats
        net_io = psutil.net_io_counters()
        
        # Process info
        process = psutil.Process()
        process_memory = process.memory_info()
        
        metrics = {
            "cpu": {
                "percent": cpu_percent,
                "count": psutil.cpu_count(),
                "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "process": {
                    "rss": process_memory.rss,
                    "vms": process_memory.vms
                }
            },
            "disk": {
                "total": disk.total,
                "free": disk.free,
                "used": disk.used,
                "percent": disk.percent
            },
            "network": {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv
            },
            "process": {
                "pid": process.pid,
                "threads": process.num_threads(),
                "connections": len(process.connections()),
                "open_files": len(process.open_files()),
                "uptime": time.time() - process.create_time()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Metrics collection failed: {str(e)}"
        )

def get_process_uptime() -> float:
    """Get current process uptime in seconds"""
    try:
        return time.time() - psutil.Process().create_time()
    except:
        return 0

def get_system_uptime() -> float:
    """Get system uptime in seconds"""
    try:
        with open('/proc/uptime', 'r') as f:
            return float(f.readline().split()[0])
    except:
        return 0