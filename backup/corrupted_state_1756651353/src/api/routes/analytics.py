"""
Analytics API routes for market data analysis and quality assessment.
"""

from datetime import datetime, timedelta
from datetime import timezone
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Query, Depends
import pandas as pd
import pandas as pd

from src.api.data.cache import MarketDataCache
from src.api.data.providers.data_source_manager import DataSourceManager
from src.core.data_quality import score_data_quality, QualityScore, get_quality_level_description
from src.core.logger import get_logger
from src.shared.data_validation import detect_data_gaps
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)
        
        duration = end_time - start_time
        return duration.total_seconds() / 3600  # Convert to hours
        
    except Exception:
        return None