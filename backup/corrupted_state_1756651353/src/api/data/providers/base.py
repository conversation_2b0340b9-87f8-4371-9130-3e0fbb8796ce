"""
Base classes for market data providers with enhanced metadata and attribution.
"""

from abc import ABC, abstractmethod
import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import logging
from typing import Dict, List, Optional, Any

from src.core.exceptions import DataProviderError, DataProviderTimeoutError, DataProviderRateLimitError
from src.shared.data_providers.unified_base import UnifiedDataProvider


class ProviderType(Enum):
    """Enumeration of provider types."""
    REST_API = "rest_api"
    WEBSOCKET = "websocket"
    FILE = "file"
    DATABASE = "database"


@dataclass
class ProviderMetadata:
    """Metadata for provider operations."""
    provider_name: str
    provider_type: ProviderType
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    response_time_ms: Optional[float] = None
    data_freshness: Optional[str] = None
    confidence_score: Optional[float] = None


class BaseMarketDataProvider(ABC):
    """Base class for market data providers."""
    
    def __init__(self, provider_name: str, provider_type: ProviderType):
        self.provider_name = provider_name
        self.provider_type = provider_type
        self.is_available = True
        self.success_count = 0
        self.error_count = 0
        self.avg_response_time = 0.0
        self.last_error = None
        self.logger = logging.getLogger(f"{__name__}.{provider_name}")
    
    @abstractmethod
    async def get_stock_data(self, symbol: str, **kwargs) -> Any:
        """Get stock data for a given symbol."""
        pass
    
    def _create_response(self, symbol: str, price: float, timestamp: datetime, **kwargs):
        """Create a standardized response."""
        return UnifiedDataProvider._create_response(
            self,
            symbol=symbol,
            price=price,
            timestamp=timestamp,
            **kwargs
        )
    
    async def _measure_response_time(self, func, *args, **kwargs):
        """Measure response time for provider operations."""
        start_time = asyncio.get_event_loop().time()
        try:
            result = await func(*args, **kwargs)
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000  # Convert to ms
            
            # Update provider statistics
            self.success_count += 1
            self.avg_response_time = (
                (self.avg_response_time * (self.success_count - 1) + response_time) / self.success_count
            )
            
            # Add response time to metadata if result has metadata
            if hasattr(result, 'metadata') and result.metadata:
                result.metadata.response_time_ms = response_time
            elif isinstance(result, list) and result and hasattr(result[0], 'metadata'):
                for item in result:
                    if hasattr(item, 'metadata') and item.metadata:
                        item.metadata.response_time_ms = response_time
            
            return result
            
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            raise
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """Get provider performance statistics."""
        return {
            'provider_name': self.provider_name,
            'provider_type': self.provider_type.value,
            'is_available': self.is_available,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'avg_response_time_ms': self.avg_response_time,
            'last_error': self.last_error,
            'success_rate': (
                self.success_count / (self.success_count + self.error_count) * 100
                if (self.success_count + self.error_count) > 0 else 0
            )
        } 