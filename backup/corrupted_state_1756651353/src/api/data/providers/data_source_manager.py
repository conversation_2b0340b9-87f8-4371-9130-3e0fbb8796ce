"""
Advanced Data Source Manager

Enterprise-grade data pipeline with comprehensive auditing, security, and fallback mechanisms.
Modular design for maintainability and extensibility.
Updated to use consolidated data providers.
"""

import asyncio
from collections import defaultdict
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import json
import logging
import os
import random
import random
import time
from typing import Dict, Any, Optional, List, Tuple

import httpx
import uuid
import yfinance as yf
import yfinance as yf

from src.api.data.providers.finnhub import FinnhubProvider
from src.api.data.providers.polygon import PolygonProvider
from src.shared.data_providers.alpaca_provider import AlpacaProvider
from src.shared.data_providers.yfinance_provider import YFinanceProvider
        
        provider_results = []
        
        # Prioritize providers: Polygon and Finnhub first, Yahoo Finance last
        # This ensures we get the best data first and only fall back to rate-limited Yahoo Finance if needed
        provider_priority = []
        
        # Add high-priority providers first
        if 'polygon' in self.providers:
            provider_priority.append('polygon')
        if 'finnhub' in self.providers:
            provider_priority.append('finnhub')
        
        # Add Yahoo Finance as last resort (only if other providers fail)
        if 'yahoo' in self.providers:
            provider_priority.append('yahoo')
        
        # If no providers available, return None
        if not provider_priority:
            logger.error("❌ No data providers available")
            return None
        
        logger.info(f"🔍 Provider priority for {symbol}: {provider_priority}")
        
        for provider_name in provider_priority:
            # Skip rate-limited providers
            if self._is_provider_rate_limited(provider_name):
                logger.warning(f"⚠️ Skipping rate-limited provider: {provider_name}")
                continue
            
            provider = self.providers[provider_name]
            
            try:
                start_time = time.time()
                
                # Different retry strategies based on provider priority
                if provider_name == 'yahoo':
                    # Yahoo Finance: Only 1 attempt (last resort)
                    max_retries = 1
                    logger.info(f"🔄 Trying Yahoo Finance as last resort for {symbol}")
                else:
                    # Polygon and Finnhub: More attempts for reliability
                    max_retries = 2
                
                for attempt in range(max_retries):
                    try:
                        # Add random jitter to prevent synchronized requests
                        await asyncio.sleep(random.uniform(0.1, 0.5 * (attempt + 1)))
                        
                        data = await provider.get_stock_data(symbol)
                        
                        # More lenient validation
                        if data and data.get('current_price') is not None:
                            # Extremely lenient validation
                            if (data.get('current_price', 0) > 0):
                                
                                # Add provider metadata
                                data['provider'] = provider_name
                                data['response_time'] = time.time() - start_time
                                data['status'] = DataStatus.SUCCESS.value
                                
                                provider_results.append((data, data['response_time']))
                                logger.info(f"✅ {provider_name} provided usable data for {symbol}")
                                
                                # If we got data from a high-priority provider, return immediately
                                if provider_name in ['polygon', 'finnhub']:
                                    logger.info(f"🚀 High-priority provider {provider_name} succeeded - returning data immediately")
                                    return data
                                
                                break
                            else:
                                logger.warning(f"⚠️ {provider_name} data partially invalid for {symbol}")
                        
                    except Exception as e:
                        # More tolerant error handling
                        if "429" in str(e):  # Rate limit error
                            # Mark provider as rate limited for 5 minutes
                            self.rate_limited_providers[provider_name] = datetime.now()
                            logger.warning(f"🚫 {provider_name} rate limited for {symbol}: {e}")
                            break
                        else:
                            logger.warning(f"⚠️ {provider_name} failed for {symbol}: {e}")
                            break
                
            except Exception as e:
                logger.warning(f"❌ Unexpected error with {provider_name} for {symbol}: {e}")
                continue
        
        if not provider_results:
            # No real data available from any provider
            logger.error(f"❌ No real data available for {symbol} from any provider")
            return None
        
        # Return best result (lowest response time)
        best_result = min(provider_results, key=lambda x: x[1])
        return best_result[0]
    
    def _get_cached_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached data if available and fresh"""
        cache_key = f"stock_{symbol.upper()}"
        
        if cache_key in self.cache:
            data, timestamp = self.cache[cache_key]
            if (datetime.now() - timestamp).seconds < self.cache_ttl:
                return data
        
        return None
    
    def _cache_data(self, symbol: str, data: Dict[str, Any]):
        """Cache data with timestamp"""
        cache_key = f"stock_{symbol.upper()}"
        self.cache[cache_key] = (data, datetime.now())
    
    async def fetch_market_data(self) -> Dict[str, Any]:
        """Fetch market indices data"""
        start_time = time.time()
        
        try:
            # Try providers first
            for provider_name, provider in self.providers.items():
                try:
                    data = await provider.get_market_data()
                    if data and 'market_indices' in data:
                        data['status'] = DataStatus.SUCCESS.value
                        data['source'] = provider_name
                        
                        self._log_data_fetch_success("market_indices", "provider", time.time() - start_time)
                        return data
                        
                except Exception as e:
                    logger.warning(f"⚠️ {provider_name} market data failed: {e}")
                    continue
            
            # No fallback market data - only real data allowed
            logger.error("❌ No real market data available from any provider")
            raise Exception("Failed to fetch real market data from any available provider")
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ Market data fetching failed: {e}")
            
            self._log_data_fetch_error("market_indices", str(e), execution_time)
            return {'status': DataStatus.ERROR.value, 'error': str(e)}
    
    def _log_data_fetch_success(self, symbol: str, source: str, execution_time: float):
        """Log successful data fetch audit event"""
        self.auditor.log_event(AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            event_type="data_fetch_success",
            severity="INFO",
            details={
                "symbol": symbol,
                "source": source,
                "cache_hit": source == "cache"
            },
            performance_metrics={
                "execution_time": execution_time,
                "source": source
            },
            user_context={}
        ))
    
    def _log_data_fetch_error(self, symbol: str, error: str, execution_time: float):
        """Log data fetch error audit event"""
        self.auditor.log_event(AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            event_type="data_fetch_error",
            severity="ERROR",
            details={
                "symbol": symbol,
                "error": error
            },
            performance_metrics={
                "execution_time": execution_time
            },
            user_context={}
        ))
    
    def get_provider_metrics(self) -> Dict[str, ProviderMetrics]:
        """Get performance metrics for all providers"""
        return {name: provider.metrics for name, provider in self.providers.items()}
    
    def get_audit_summary(self) -> Dict[str, Any]:
        """Get comprehensive audit and performance summary"""
        return {
            'audit_summary': self.auditor.get_audit_summary(),
            'provider_metrics': self.get_provider_metrics(),
            'cache_stats': {
                'cached_items': len(self.cache),
                'cache_ttl': self.cache_ttl
            }
        }
    
    def clear_cache(self):
        """Clear all cached data"""
        self.cache.clear()
        logger.info("🗑️ Cache cleared")
    
    def set_cache_ttl(self, ttl_seconds: int):
        """Set cache TTL in seconds"""
        self.cache_ttl = ttl_seconds
        logger.info(f"⏰ Cache TTL set to {ttl_seconds} seconds")
    
    def reload_configuration(self):
        """Hot reload configuration and reinitialize providers"""
        try:
            logger.info("🔄 Hot reloading data source configuration...")
            
            # Clear cache
            self.clear_cache()
            
            # Reinitialize providers with new config
            self._initialize_providers()
            
            logger.info("✅ Configuration hot reloaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Configuration hot reload failed: {e}")
            raise

# =============================================================================
# CONVENIENCE FUNCTIONS
# =============================================================================

async def get_stock_data(symbol: str) -> Dict[str, Any]:
    """Convenience function to get stock data"""
    manager = DataSourceManager()
    return await manager.fetch_stock_data(symbol)

async def get_market_data() -> Dict[str, Any]:
    """Convenience function to get market data"""
    manager = DataSourceManager()
    return await manager.fetch_market_data() 