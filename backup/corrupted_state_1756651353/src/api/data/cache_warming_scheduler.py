import asyncio
from datetime import datetime, time
import logging
from typing import Dict, Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
import pytz

from src.api.data.cache import MarketDataCache, warm_top_symbols_cache
from src.api.data.cache import TOP_SYMBOLS
from src.api.data.metrics import cache_metrics
from src.core.config_manager import get_config
from src.api.data.cache import TOP_SYMBOLS 