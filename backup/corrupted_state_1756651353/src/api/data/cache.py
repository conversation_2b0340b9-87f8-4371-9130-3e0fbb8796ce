import asyncio
from datetime import datetime, timed<PERSON><PERSON>
import json
from typing import Optional, List, Any, Union, Dict

from pydantic import BaseModel, Field
import redis.asyncio as aioredis

from src.api.data.providers.base import MarketDataResponse
from src.api.data.providers.data_source_manager import DataSourceManager
from src.core.config_manager import get_config
from src.core.logger import get_logger
            manager = DataSourceManager()
            
            # Try to fetch data with retry logic
            for attempt in range(3):
                try:
                    data = await manager.fetch_historical_data(symbol, days=days)
                    if data:
                        return data
                except Exception as e:
                    if attempt == 2:  # Last attempt
                        raise e
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    
            return []
            
        except Exception as e:
            logger.error(f"Failed to fetch historical data for {symbol}: {e}")
            return []

    async def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics for monitoring.
        """
        redis = await self._get_redis_client()
        
        try:
            info = await redis.info('memory')
            stats = {
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
            }
            
            # Calculate hit rate
            hits = stats['keyspace_hits']
            misses = stats['keyspace_misses']
            total = hits + misses
            
            stats['hit_rate'] = (hits / total * 100) if total > 0 else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {}


# Top 50 symbols configuration - can be moved to config file
TOP_SYMBOLS = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK.B', 'UNH', 'JNJ',
    'XOM', 'V', 'PG', 'JPM', 'HD', 'CVX', 'MA', 'PFE', 'ABBV', 'BAC',
    'KO', 'AVGO', 'PEP', 'TMO', 'COST', 'MRK', 'WMT', 'ACN', 'LLY', 'DHR',
    'VZ', 'ABT', 'ADBE', 'NKE', 'TXN', 'NEE', 'BMY', 'PM', 'RTX', 'QCOM',
    'T', 'LOW', 'HON', 'UPS', 'SBUX', 'AMD', 'LMT', 'IBM', 'CAT', 'GS'
]


async def warm_top_symbols_cache(cache: MarketDataCache) -> Dict[str, bool]:
    """
    Convenience function to warm cache for top 50 symbols.
    This will be called by the scheduled job.
    """
    logger.info("Starting cache warming for top 50 symbols")
    start_time = datetime.now()
    
    results = await cache.warm_cache_for_symbols(
        symbols=TOP_SYMBOLS,
        days=30,  # Load 30 days of historical data
        ttl=86400,  # Cache for 24 hours
        batch_size=10  # Process 10 symbols concurrently
    )
    
    duration = (datetime.now() - start_time).total_seconds()
    success_count = sum(1 for success in results.values() if success)
    
    logger.info(
        f"Cache warming completed in {duration:.2f}s: "
        f"{success_count}/{len(TOP_SYMBOLS)} symbols successful"
    )
    
    return results


# Global cache instance
market_data_cache = MarketDataCache() 