"""
Error handling decorators for Task 4.3.
Provides consistent error handling patterns across functions and methods.
"""

import asyncio
import functools
import logging
import time
import time
from typing import Any, Callable, Dict, Optional, Type, Union

from response_templates import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, create_error_response

            
            last_exception = None
            current_delay = delay_seconds
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # Check if we should retry this error
                    if retry_exceptions and not isinstance(e, retry_exceptions):
                        raise
                    
                    # Log retry attempt
                    if attempt < max_attempts - 1:
                        logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {current_delay}s...",
                            extra={
                                'function': func.__name__,
                                'attempt': attempt + 1,
                                'correlation_id': correlation_id
                            }
                        )
                        
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        # Final attempt failed
                        logger.error(
                            f"All {max_attempts} attempts failed for {func.__name__}: {e}",
                            extra={
                                'function': func.__name__,
                                'correlation_id': correlation_id
                            }
                        )
            
            # All attempts failed
            return ErrorHandler.handle_generic_error(
                error=last_exception,
                operation=f"{func.__name__} (after {max_attempts} attempts)",
                correlation_id=correlation_id
            )
        
        return wrapper
    return decorator

def log_errors(
    log_level: str = "ERROR",
    include_args: bool = False,
    correlation_id: Optional[str] = None
):
    """
    Decorator to log errors with standardized format.
    
    Args:
        log_level: Logging level for errors
        include_args: Whether to include function arguments in logs
        correlation_id: Correlation ID for error tracking
        
    Returns:
        Decorated function with error logging
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Prepare log message
                log_msg = f"Error in {func.__name__}: {e}"
                
                # Add arguments if requested
                if include_args:
                    args_str = ", ".join(str(arg) for arg in args)
                    kwargs_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
                    log_msg += f" (args: [{args_str}], kwargs: {{{kwargs_str}}})"
                
                # Log at specified level
                log_method = getattr(logger, log_level.lower(), logger.error)
                log_method(
                    log_msg,
                    extra={
                        'function': func.__name__,
                        'correlation_id': correlation_id,
                        'error_type': type(e).__name__
                    }
                )
                
                # Re-raise the exception
                raise
        
        return wrapper
    return decorator 