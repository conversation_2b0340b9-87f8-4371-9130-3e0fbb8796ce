"""
Standardized error response templates for Task 4.3.
Ensures consistent error handling across API and Discord interfaces.
"""

import sys
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Union

class ErrorResponseTemplate:
    """Standardized error response template."""
    
    @staticmethod
    def create_error_response(
        error: Exception,
        error_code: str = None,
        user_message: str = None,
        correlation_id: str = None,
        context: Dict[str, Any] = None,
        include_traceback: bool = False
    ) -> Dict[str, Any]:
        """
        Create a standardized error response.
        
        Args:
            error: The exception that occurred
            error_code: Optional error code for categorization
            user_message: User-friendly error message
            correlation_id: Optional correlation ID for tracking
            context: Additional context information
            include_traceback: Whether to include traceback in response
            
        Returns:
            Standardized error response dictionary
        """
        # Get error details
        error_type = type(error).__name__
        error_message = str(error)
        
        # Create base response
        response = {
            "success": False,
            "error": {
                "type": error_type,
                "code": error_code or "UNKNOWN_ERROR",
                "message": user_message or error_message,
                "timestamp": datetime.utcnow().isoformat(),
                "correlation_id": correlation_id
            }
        }
        
        # Add context if provided
        if context:
            response["error"]["context"] = context
        
        # Add traceback if requested (for debugging)
        if include_traceback:
            response["error"]["traceback"] = ''.join(traceback.format_tb(sys.exc_info()[2]))
        
        return response
    
    @staticmethod
    def create_api_error_response(
        error: Exception,
        status_code: int = 500,
        error_code: str = None,
        user_message: str = None,
        correlation_id: str = None
    ) -> Dict[str, Any]:
        """Create standardized API error response."""
        response = ErrorResponseTemplate.create_error_response(
            error=error,
            error_code=error_code,
            user_message=user_message,
            correlation_id=correlation_id,
            include_traceback=False
        )
        
        # Add HTTP-specific fields
        response["status_code"] = status_code
        response["error"]["http_status"] = status_code
        
        return response
    
    @staticmethod
    def create_discord_error_response(
        error: Exception,
        error_code: str = None,
        user_message: str = None,
        correlation_id: str = None
    ) -> Dict[str, Any]:
        """Create standardized Discord error response."""
        response = ErrorResponseTemplate.create_error_response(
            error=error,
            error_code=error_code,
            user_message=user_message,
            correlation_id=correlation_id,
            include_traceback=False
        )
        
        # Add Discord-specific fields
        response["platform"] = "discord"
        response["error"]["user_visible"] = True
        
        return response
    
    @staticmethod
    def create_validation_error_response(
        field: str,
        value: Any,
        constraint: str,
        correlation_id: str = None
    ) -> Dict[str, Any]:
        """Create standardized validation error response."""
        error = ValueError(f"Validation failed for {field}: {constraint}")
        
        response = ErrorResponseTemplate.create_error_response(
            error=error,
            error_code="VALIDATION_ERROR",
            user_message=f"Invalid {field}: {constraint}",
            correlation_id=correlation_id,
            context={
                "field": field,
                "value": str(value),
                "constraint": constraint
            }
        )
        
        return response
    
    @staticmethod
    def create_timeout_error_response(
        operation: str,
        timeout_seconds: float,
        correlation_id: str = None
    ) -> Dict[str, Any]:
        """Create standardized timeout error response."""
        error = TimeoutError(f"Operation '{operation}' timed out after {timeout_seconds}s")
        
        response = ErrorResponseTemplate.create_error_response(
            error=error,
            error_code="TIMEOUT_ERROR",
            user_message=f"Operation '{operation}' took too long to complete",
            correlation_id=correlation_id,
            context={
                "operation": operation,
                "timeout_seconds": timeout_seconds
            }
        )
        
        return response

class ErrorHandler:
    """Standardized error handler with consistent patterns."""
    
    @staticmethod
    def handle_generic_error(
        error: Exception,
        operation: str,
        correlation_id: str = None,
        log_error: bool = True
    ) -> Dict[str, Any]:
        """
        Handle generic errors with standardized response.
        
        Args:
            error: The exception that occurred
            operation: Description of the operation that failed
            correlation_id: Optional correlation ID for tracking
            log_error: Whether to log the error
            
        Returns:
            Standardized error response
        """
        # Log error if requested
        if log_error:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(
                f"Error in operation '{operation}': {error}",
                extra={
                    'correlation_id': correlation_id,
                    'operation': operation,
                    'error_type': type(error).__name__
                }
            )
        
        # Create error response
        return ErrorResponseTemplate.create_error_response(
            error=error,
            error_code="OPERATION_FAILED",
            user_message=f"Operation '{operation}' failed: {str(error)}",
            correlation_id=correlation_id,
            context={'operation': operation}
        )
    
    @staticmethod
    def handle_validation_error(
        field: str,
        value: Any,
        constraint: str,
        correlation_id: str = None
    ) -> Dict[str, Any]:
        """Handle validation errors with standardized response."""
        return ErrorResponseTemplate.create_validation_error_response(
            field=field,
            value=value,
            constraint=constraint,
            correlation_id=correlation_id
        )
    
    @staticmethod
    def handle_timeout_error(
        operation: str,
        timeout_seconds: float,
        correlation_id: str = None
    ) -> Dict[str, Any]:
        """Handle timeout errors with standardized response."""
        return ErrorResponseTemplate.create_timeout_error_response(
            operation=operation,
            timeout_seconds=timeout_seconds,
            correlation_id=correlation_id
        )

# Convenience functions for backward compatibility
def create_error_response(error: Exception, **kwargs) -> Dict[str, Any]:
    """Create standardized error response (convenience function)."""
    return ErrorResponseTemplate.create_error_response(error, **kwargs)

def create_api_error_response(error: Exception, **kwargs) -> Dict[str, Any]:
    """Create standardized API error response (convenience function)."""
    return ErrorResponseTemplate.create_api_error_response(error, **kwargs)

def create_discord_error_response(error: Exception, **kwargs) -> Dict[str, Any]:
    """Create standardized Discord error response (convenience function)."""
    return ErrorResponseTemplate.create_discord_error_response(error, **kwargs) 