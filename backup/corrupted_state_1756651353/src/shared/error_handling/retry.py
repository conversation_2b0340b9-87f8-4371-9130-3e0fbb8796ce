"""
Retry mechanisms with exponential backoff and configurable strategies.
Provides comprehensive retry logic for network operations and transient failures.
"""

import asyncio
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps
import logging
import random
import time
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union

from config_utils import get_config, RetryConfig


T = TypeVar('T')
logger = logging.getLogger(__name__)

class RetryManager:
    """
    Manages retry operations with exponential backoff and configurable policies.
    Supports both sync and async operations with comprehensive error tracking.
    """
    
    def __init__(self, config: Optional[RetryConfig] = None):
        if config is None:
            config = get_config()
            retry_config = RetryConfig(
                max_attempts=config.get('app', 'retry_max_attempts', 3),
                initial_delay=config.get('app', 'retry_initial_delay', 1.0),
                max_delay=config.get('app', 'retry_max_delay', 60.0),
                backoff_factor=config.get('app', 'retry_backoff_factor', 2.0),
                jitter=config.get('app', 'retry_jitter', True)
            )
            self.config = retry_config
        else:
            self.config = config
        self.retry_stats: Dict[str, Dict[str, int]] = {}
        
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay with exponential backoff and optional jitter"""
        delay = self.config.initial_delay * (self.config.backoff_factor ** (attempt - 1))
        
        if self.config.jitter:
            delay = delay * (0.5 + random.random() * 0.5)  # Add 50-150% jitter
        
        return min(delay, self.config.max_delay)
    
    def _should_retry(self, error: Exception) -> bool:
        """Determine if an error should trigger a retry"""
        # Always retry if no specific exceptions are specified
        if not self.config.retry_on and not self.config.retry_on_exceptions:
            return True
            
        # Check specific exception types
        if self.config.retry_on:
            for exc_type in self.config.retry_on:
                if isinstance(error, exc_type):
                    return True
        
        # Check exception type names (for dynamic loading or string-based config)
        if self.config.retry_on_exceptions:
            error_type_name = type(error).__name__
            if error_type_name in self.config.retry_on_exceptions:
                return True
                
        return False
    
    def _update_stats(self, operation_name: str, success: bool, error_type: Optional[str] = None):
        """Update retry statistics for monitoring"""
        if operation_name not in self.retry_stats:
            self.retry_stats[operation_name] = {
                'total_attempts': 0,
                'successful_attempts': 0,
                'failed_attempts': 0,
                'error_types': {}
            }
        
        stats = self.retry_stats[operation_name]
        stats['total_attempts'] += 1
        
        if success:
            stats['successful_attempts'] += 1
        else:
            stats['failed_attempts'] += 1
            if error_type:
                if error_type not in stats['error_types']:
                    stats['error_types'][error_type] = 0
                stats['error_types'][error_type] += 1
    
    async def execute_with_retry_async(
        self,
        operation_name: str,
        operation: Callable[[], Any],
        should_retry: Optional[Callable[[Exception], bool]] = None
    ) -> Any:
        """
        Execute an async operation with retry logic.
        
        Args:
            operation_name: Name of the operation for logging and statistics
            operation: Async callable to execute
            should_retry: Optional custom function to determine retry behavior
            
        Returns:
            Result from the operation
            
        Raises:
            Exception: If all retry attempts fail
        """
        last_error = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                logger.debug(
                    f"Attempt {attempt}/{self.config.max_attempts} for {operation_name}",
                    extra={'operation': operation_name, 'attempt': attempt}
                )
                
                if self.config.timeout:
                    result = await asyncio.wait_for(operation(), timeout=self.config.timeout)
                else:
                    result = await operation()
                
                self._update_stats(operation_name, True)
                logger.info(
                    f"Successfully completed {operation_name} on attempt {attempt}",
                    extra={'operation': operation_name, 'attempt': attempt}
                )
                return result
                
            except Exception as e:
                last_error = e
                error_type = type(e).__name__
                
                # Check if we should retry
                custom_retry = should_retry(e) if should_retry else True
                should_retry_this = custom_retry and self._should_retry(e)
                
                self._update_stats(operation_name, False, error_type)
                logger.warning(
                    f"Attempt {attempt} failed for {operation_name}: {str(e)}",
                    extra={
                        'operation': operation_name, 
                        'attempt': attempt, 
                        'error': str(e),
                        'error_type': error_type
                    }
                )
                
                if not should_retry_this or attempt == self.config.max_attempts:
                    break
                
                # Calculate and wait for delay
                delay = self._calculate_delay(attempt)
                logger.debug(
                    f"Waiting {delay:.2f}s before retry {attempt + 1} for {operation_name}",
                    extra={'operation': operation_name, 'delay': delay, 'next_attempt': attempt + 1}
                )
                await asyncio.sleep(delay)
        
        # All attempts failed
        error_msg = f"All {self.config.max_attempts} attempts failed for {operation_name}"
        logger.error(error_msg, extra={
            'operation': operation_name, 
            'max_attempts': self.config.max_attempts,
            'last_error': str(last_error) if last_error else 'Unknown error'
        })
        raise last_error or Exception(error_msg)
    
    def execute_with_retry_sync(
        self,
        operation_name: str,
        operation: Callable[[], Any],
        should_retry: Optional[Callable[[Exception], bool]] = None
    ) -> Any:
        """
        Execute a sync operation with retry logic.
        
        Args:
            operation_name: Name of the operation for logging and statistics
            operation: Sync callable to execute
            should_retry: Optional custom function to determine retry behavior
            
        Returns:
            Result from the operation
            
        Raises:
            Exception: If all retry attempts fail
        """
        last_error = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                logger.debug(
                    f"Attempt {attempt}/{self.config.max_attempts} for {operation_name}",
                    extra={'operation': operation_name, 'attempt': attempt}
                )
                
                result = operation()
                
                self._update_stats(operation_name, True)
                logger.info(
                    f"Successfully completed {operation_name} on attempt {attempt}",
                    extra={'operation': operation_name, 'attempt': attempt}
                )
                return result
                
            except Exception as e:
                last_error = e
                error_type = type(e).__name__
                
                # Check if we should retry
                custom_retry = should_retry(e) if should_retry else True
                should_retry_this = custom_retry and self._should_retry(e)
                
                self._update_stats(operation_name, False, error_type)
                logger.warning(
                    f"Attempt {attempt} failed for {operation_name}: {str(e)}",
                    extra={
                        'operation': operation_name, 
                        'attempt': attempt, 
                        'error': str(e),
                        'error_type': error_type
                    }
                )
                
                if not should_retry_this or attempt == self.config.max_attempts:
                    break
                
                # Calculate and wait for delay
                delay = self._calculate_delay(attempt)
                logger.debug(
                    f"Waiting {delay:.2f}s before retry {attempt + 1} for {operation_name}",
                    extra={'operation': operation_name, 'delay': delay, 'next_attempt': attempt + 1}
                )
                time.sleep(delay)
        
        # All attempts failed
        error_msg = f"All {self.config.max_attempts} attempts failed for {operation_name}"
        logger.error(error_msg, extra={
            'operation': operation_name, 
            'max_attempts': self.config.max_attempts,
            'last_error': str(last_error) if last_error else 'Unknown error'
        })
        raise last_error or Exception(error_msg)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current retry statistics"""
        return self.retry_stats

def retry_with_backoff(
    max_attempts: Optional[int] = None,
    initial_delay: Optional[float] = None,
    max_delay: Optional[float] = None,
    backoff_factor: Optional[float] = None,
    jitter: Optional[bool] = None,
    timeout: Optional[float] = None,
    retry_on_exceptions: Optional[List[Type[Exception]]] = None
):
    """
    Decorator for retrying functions with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        backoff_factor: Multiplier for delay increase
        jitter: Whether to add random jitter to delays
        timeout: Overall timeout for all retry attempts
        retry_on_exceptions: List of exception types to retry on
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            config = get_config()
            retry_config = RetryConfig(
                max_attempts=max_attempts or config.get('app', 'retry_max_attempts', 3),
                initial_delay=initial_delay or config.get('app', 'retry_initial_delay', 1.0),
                max_delay=max_delay or config.get('app', 'retry_max_delay', 60.0),
                backoff_factor=backoff_factor or config.get('app', 'retry_backoff_factor', 2.0),
                jitter=jitter if jitter is not None else config.get('app', 'retry_jitter', True),
                timeout=timeout or config.get('app', 'retry_timeout'),
                retry_on_exceptions=retry_on_exceptions or config.get('app', 'retry_on_exceptions', [])
            )
            
            retry_manager = RetryManager(retry_config)
            return retry_manager.execute_sync(func, *args, **kwargs)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            config = get_config()
            retry_config = RetryConfig(
                max_attempts=max_attempts or config.get('app', 'retry_max_attempts', 3),
                initial_delay=initial_delay or config.get('app', 'retry_initial_delay', 1.0),
                max_delay=max_delay or config.get('app', 'retry_max_delay', 60.0),
                backoff_factor=backoff_factor or config.get('app', 'retry_backoff_factor', 2.0),
                jitter=jitter if jitter is not None else config.get('app', 'retry_jitter', True),
                timeout=timeout or config.get('app', 'retry_timeout'),
                retry_on_exceptions=retry_on_exceptions or config.get('app', 'retry_on_exceptions', [])
            )
            
            retry_manager = RetryManager(retry_config)
            return await retry_manager.execute_async(func, *args, **kwargs)
        
        # Return the appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

@asynccontextmanager
async def retry_context(
    operation_name: str,
    max_attempts: Optional[int] = None,
    initial_delay: Optional[float] = None,
    max_delay: Optional[float] = None,
    backoff_factor: Optional[float] = None,
    jitter: Optional[bool] = None
):
    """
    Async context manager for retry operations.
    
    Example:
        async with retry_context("data_fetch", max_attempts=3) as retry:
            result = await some_async_operation()
    """
    config_obj = get_config()
    config = RetryConfig(
        max_attempts=max_attempts or config_obj.get('app', 'retry_max_attempts', 3),
        initial_delay=initial_delay or config_obj.get('app', 'retry_initial_delay', 1.0),
        max_delay=max_delay or config_obj.get('app', 'retry_max_delay', 60.0),
        backoff_factor=backoff_factor or config_obj.get('app', 'retry_backoff_factor', 2.0),
        jitter=jitter if jitter is not None else config_obj.get('app', 'retry_jitter', True)
    )
    
    manager = RetryManager(config)
    last_error = None
    
    for attempt in range(1, config.max_attempts + 1):
        try:
            logger.debug(
                f"Attempt {attempt}/{config.max_attempts} for {operation_name}",
                extra={'operation': operation_name, 'attempt': attempt}
            )
            
            yield attempt
            
            manager._update_stats(operation_name, True)
            logger.info(
                f"Successfully completed {operation_name} on attempt {attempt}",
                extra={'operation': operation_name, 'attempt': attempt}
            )
            return
            
        except Exception as e:
            last_error = e
            error_type = type(e).__name__
            
            manager._update_stats(operation_name, False, error_type)
            logger.warning(
                f"Attempt {attempt} failed for {operation_name}: {str(e)}",
                extra={
                    'operation': operation_name, 
                    'attempt': attempt, 
                    'error': str(e),
                    'error_type': error_type
                }
            )
            
            if attempt == config.max_attempts:
                break
                
            # Calculate and wait for delay
            delay = manager._calculate_delay(attempt)
            logger.debug(
                f"Waiting {delay:.2f}s before retry {attempt + 1} for {operation_name}",
                extra={'operation': operation_name, 'delay': delay, 'next_attempt': attempt + 1}
            )
            await asyncio.sleep(delay)
    
    # All attempts failed
    error_msg = f"All {config.max_attempts} attempts failed for {operation_name}"
    logger.error(error_msg, extra={
        'operation': operation_name, 
        'max_attempts': config.max_attempts,
        'last_error': str(last_error) if last_error else 'Unknown error'
    })
    raise last_error or Exception(error_msg)