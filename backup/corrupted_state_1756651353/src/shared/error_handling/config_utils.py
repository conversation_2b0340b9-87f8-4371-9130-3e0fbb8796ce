"""
Shared configuration utilities for error handling modules.
Consolidates duplicate configuration logic.
"""

import os

try:
    from src.core.config_manager import config
    def get_config():
        return config
except ImportError:
    # Fallback to environment-based configuration
    def get_config():
        class FallbackConfig:
            def get(self, key, default=None):
                return os.environ.get(key, default)
        return FallbackConfig()

class FallbackConfig:
    """Configuration for fallback behavior"""
    max_attempts: int = 3
    timeout_per_attempt: float = 10.0
    fallback_order: list = None
    enable_circuit_breaker: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60

class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    initial_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True
    timeout: float = None
    retry_on: list = None
    retry_on_exceptions: list = None 