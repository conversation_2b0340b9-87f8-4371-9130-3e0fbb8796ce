"""
Fallback mechanisms for error handling and service degradation.
Provides circuit breaker patterns, fallback strategies, and graceful degradation.
"""

import asyncio
from dataclasses import dataclass
from functools import wraps
import logging
import time
from typing import Any, Callable, Dict, List, Optional, TypeVar

from .config_utils import get_config, FallbackConfig


T = TypeVar('T')
logger = logging.getLogger(__name__)

class FallbackManager:
    """
    Manages fallback between multiple providers or operations.
    Implements circuit breaker pattern and provides detailed error tracking.
    """
    
    def __init__(self, name: str, config: Optional[FallbackConfig] = None):
        self.name = name
        self.config = config or FallbackConfig()
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        self.error_stats: Dict[str, Dict[str, int]] = {}
        self.last_success: Dict[str, float] = {}
        
    def _update_circuit_breaker(self, provider_name: str, success: bool):
        """Update circuit breaker state for a provider"""
        if not self.config.enable_circuit_breaker:
            return
            
        if provider_name not in self.circuit_breakers:
            self.circuit_breakers[provider_name] = {
                'failures': 0,
                'last_failure': 0,
                'state': 'closed'
            }
            
        cb = self.circuit_breakers[provider_name]
        
        if success:
            cb['failures'] = 0
            cb['state'] = 'closed'
            self.last_success[provider_name] = time.time()
        else:
            cb['failures'] += 1
            cb['last_failure'] = time.time()
            
            if cb['failures'] >= self.config.circuit_breaker_threshold:
                cb['state'] = 'open'
                logger.warning(
                    f"Circuit breaker opened for {provider_name} in {self.name}",
                    extra={'provider': provider_name, 'fallback_manager': self.name}
                )
    
    def _is_circuit_open(self, provider_name: str) -> bool:
        """Check if circuit breaker is open for a provider"""
        if not self.config.enable_circuit_breaker:
            return False
            
        if provider_name not in self.circuit_breakers:
            return False
            
        cb = self.circuit_breakers[provider_name]
        
        if cb['state'] == 'open':
            # Check if timeout has passed to attempt half-open
            if time.time() - cb['last_failure'] > self.config.circuit_breaker_timeout:
                cb['state'] = 'half-open'
                return False
            return True
            
        return False
    
    def _update_error_stats(self, provider_name: str, error_type: str):
        """Update error statistics for monitoring"""
        if provider_name not in self.error_stats:
            self.error_stats[provider_name] = {}
            
        if error_type not in self.error_stats[provider_name]:
            self.error_stats[provider_name][error_type] = 0
            
        self.error_stats[provider_name][error_type] += 1
    
    async def execute_with_fallback(
        self, 
        operation_name: str,
        providers: List[str],
        operation: Callable[[str], Any],
        should_retry: Optional[Callable[[Exception], bool]] = None
    ) -> Any:
        """
        Execute an operation with fallback between multiple providers.
        
        Args:
            operation_name: Name of the operation for logging
            providers: List of provider names in fallback order
            operation: Callable that takes provider name and returns result
            should_retry: Optional function to determine if an error should trigger fallback
            
        Returns:
            Result from the first successful provider
            
        Raises:
            Exception: If all providers fail
        """
        providers_to_try = providers.copy()
        if self.config.fallback_order:
            providers_to_try = [p for p in self.config.fallback_order if p in providers]
        
        errors = []
        
        for attempt in range(self.config.max_attempts):
            for provider in providers_to_try:
                if self._is_circuit_open(provider):
                    logger.debug(
                        f"Skipping {provider} due to open circuit breaker",
                        extra={'provider': provider, 'attempt': attempt}
                    )
                    continue
                
                try:
                    logger.info(
                        f"Attempting {operation_name} with {provider} (attempt {attempt + 1})",
                        extra={'provider': provider, 'attempt': attempt, 'operation': operation_name}
                    )
                    
                    result = await asyncio.wait_for(
                        operation(provider),
                        timeout=self.config.timeout_per_attempt
                    )
                    
                    self._update_circuit_breaker(provider, True)
                    logger.info(
                        f"Successfully completed {operation_name} with {provider}",
                        extra={'provider': provider, 'operation': operation_name}
                    )
                    return result
                    
                except asyncio.TimeoutError:
                    error_msg = f"Timeout executing {operation_name} with {provider}"
                    self._update_error_stats(provider, 'timeout')
                    self._update_circuit_breaker(provider, False)
                    errors.append(f"{provider}: {error_msg}")
                    logger.warning(error_msg, extra={'provider': provider})
                    
                except Exception as e:
                    error_msg = f"Error executing {operation_name} with {provider}: {str(e)}"
                    self._update_error_stats(provider, type(e).__name__)
                    self._update_circuit_breaker(provider, False)
                    errors.append(f"{provider}: {str(e)}")
                    logger.error(error_msg, extra={'provider': provider, 'error': str(e)})
                    
                    # Check if we should continue with fallback
                    if should_retry and not should_retry(e):
                        raise
        
        # All attempts failed
        error_summary = "\n".join(errors)
        raise Exception(
            f"All {self.config.max_attempts} attempts failed for {operation_name}:\n{error_summary}"
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current fallback statistics"""
        return {
            'circuit_breakers': self.circuit_breakers,
            'error_stats': self.error_stats,
            'last_success': self.last_success
        }

def with_fallback(providers: List[str], operation_name: str = None):
    """
    Decorator for functions that should use fallback between providers.
    
    Args:
        providers: List of provider names in fallback order
        operation_name: Name of the operation (defaults to function name)
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            nonlocal operation_name
            if operation_name is None:
                operation_name = func.__name__
                
            # Extract provider from kwargs or use first provider
            current_provider = kwargs.get('provider', providers[0])
            
            config_obj = get_config()
            fallback_config = FallbackConfig(
                max_attempts=config_obj.get('app', 'fallback_max_attempts', 3),
                timeout_per_attempt=config_obj.get('app', 'fallback_timeout', 10.0),
                enable_circuit_breaker=config_obj.get('app', 'fallback_circuit_breaker', True),
                circuit_breaker_threshold=config_obj.get('app', 'fallback_circuit_breaker_threshold', 5),
                circuit_breaker_timeout=config_obj.get('app', 'fallback_circuit_breaker_timeout', 60)
            )
            
            manager = FallbackManager(operation_name, fallback_config)
            
            async def operation(provider):
                return await func(*args, **{**kwargs, 'provider': provider})
            
            return await manager.execute_with_fallback(
                operation_name, providers, operation
            )
        return wrapper
    return decorator

# Update the configuration access at the bottom
config_obj = get_config()
fallback_config = FallbackConfig(
    max_attempts=config_obj.get('app', 'fallback_max_attempts', 3),
    timeout_per_attempt=config_obj.get('app', 'fallback_timeout', 10.0),
    enable_circuit_breaker=config_obj.get('app', 'fallback_circuit_breaker', True),
    circuit_breaker_threshold=config_obj.get('app', 'fallback_circuit_breaker_threshold', 5),
    circuit_breaker_timeout=config_obj.get('app', 'fallback_circuit_breaker_timeout', 60)
)

fallback_manager = FallbackManager("default", fallback_config)