"""
Comprehensive error logging setup with configurable levels and integration with centralized configuration.
Provides structured logging with error context, correlation tracking, and specialized error handlers.
"""

import asyncio
from datetime import datetime
from functools import wraps
import json
import logging
from logging.handlers import RotatingFileHandler
import os
import time
import traceback
from typing import Optional, Dict, Any, Callable

import uuid

from src.core.config_manager import config
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Initialize error logging when module is imported
try:
    config_obj = get_config()
    error_logger = setup_error_logging(
        log_level=config_obj.get('app', 'log_level', 'ERROR'),
        log_file=config_obj.get('app', 'log_file', 'logs/error.log'),
        enable_console=config_obj.get('app', 'log_file_enabled', True)
    )
except Exception as e:
    # Fallback initialization if configuration fails
    try:
        error_logger = setup_error_logging(
            log_level='ERROR',
            log_file=None,  # Don't try to create log files
            enable_console=True
        )
    except Exception:
        # Ultimate fallback - just create a basic logger
        error_logger = logging.getLogger('error_logger')
        error_logger.setLevel(logging.ERROR)
        if not error_logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
            error_logger.addHandler(handler)