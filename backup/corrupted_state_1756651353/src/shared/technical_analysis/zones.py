"""
Supply and Demand Zone Detection Module.

This module provides comprehensive detection of supply and demand zones
using multiple methodologies including swing point analysis, volume profile,
and order flow patterns.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
import pandas as pd


logger = logging.getLogger(__name__)


class SupplyDemandZone:
    """Represents a supply or demand zone with detailed characteristics."""
    
    def __init__(
        self,
        zone_type: str,  # 'supply' or 'demand'
        top_price: float,
        bottom_price: float,
        strength: float,
        volume_profile: Dict[str, float],
        touches: int = 0,
        last_tested: Optional[pd.Timestamp] = None,
        is_active: bool = True
    ):
        self.zone_type = zone_type
        self.top_price = top_price
        self.bottom_price = bottom_price
        self.strength = strength
        self.volume_profile = volume_profile
        self.touches = touches
        self.last_tested = last_tested
        self.is_active = is_active
        self.center_price = (top_price + bottom_price) / 2
        
    @property
    def height(self) -> float:
        """Get zone height in price units."""
        return abs(self.top_price - self.bottom_price)
        
    @property
    def percentage_height(self) -> float:
        """Get zone height as percentage of center price."""
        return (self.height / self.center_price) * 100
        
    def contains_price(self, price: float) -> bool:
        """Check if price falls within this zone."""
        return min(self.top_price, self.bottom_price) <= price <= max(self.top_price, self.bottom_price)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert zone to dictionary for serialization."""
        return {
            'zone_type': self.zone_type,
            'top_price': self.top_price,
            'bottom_price': self.bottom_price,
            'center_price': self.center_price,
            'strength': self.strength,
            'height': self.height,
            'percentage_height': self.percentage_height,
            'touches': self.touches,
            'last_tested': self.last_tested.isoformat() if self.last_tested else None,
            'is_active': self.is_active,
            'volume_profile': self.volume_profile
        }


class SupplyDemandDetector:
    """Advanced supply and demand zone detection system."""
    
    def __init__(
        self,
        swing_window: int = 5,
        min_zone_height_pct: float = 0.5,
        volume_threshold: float = 1.5,
        lookback_period: int = 100
    ):
        """
        Initialize the detector with configurable parameters.
        
        Args:
            swing_window: Window size for swing point detection
            min_zone_height_pct: Minimum zone height as percentage of price
            volume_threshold: Volume threshold for high volume nodes
            lookback_period: Period for historical analysis
        """
        self.swing_window = swing_window
        self.min_zone_height_pct = min_zone_height_pct
        self.volume_threshold = volume_threshold
        self.lookback_period = lookback_period
        
    def detect_zones(
        self,
        df: pd.DataFrame,
        symbol: str = ""
    ) -> Dict[str, Any]:
        """
        Detect supply and demand zones using multiple methodologies.
        
        Args:
            df: DataFrame with OHLCV data
            symbol: Symbol for logging
            
        Returns:
            Dict containing detected zones and analysis metrics
        """
        if df is None or df.empty:
            logger.warning(f"Empty DataFrame provided for {symbol}")
            return {"zones": [], "methodology": "multi", "error": "No data"}
            
        try:
            # Ensure we have required columns
            required_columns = ['high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                return {"zones": [], "methodology": "multi", "error": f"Missing columns: {missing_cols}"}
                
            # Limit to lookback period
            df_recent = df.tail(min(len(df), self.lookback_period))
            
            # Detect zones using multiple methods
            swing_zones = self._detect_swing_zones(df_recent)
            volume_zones = self._detect_volume_zones(df_recent)
            fibonacci_zones = self._detect_fibonacci_zones(df_recent)
            
            # Combine and validate zones
            all_zones = swing_zones + volume_zones + fibonacci_zones
            validated_zones = self._validate_and_merge_zones(all_zones)

            # Convert SupplyDemandZone objects to dicts for consistent output
            validated_zone_dicts = []
            for z in validated_zones:
                try:
                    zdict = z.to_dict() if hasattr(z, 'to_dict') else dict(z)
                except Exception:
                    # Attempt best-effort conversion
                    try:
                        zdict = {k: getattr(z, k) for k in ('zone_type', 'top_price', 'bottom_price', 'strength', 'touches', 'last_tested') if hasattr(z, k)}
                    except Exception:
                        continue

                # Backward-compatibility mappings for older code/tests
                # legacy keys: 'type' -> zone_type, 'price' -> center_price, 'last_touched' -> last_tested, 'touch_count' -> touches
                try:
                    zdict.setdefault('zone_type', zdict.get('zone_type') or zdict.get('type'))
                    # compute center_price if missing
                    if 'center_price' not in zdict:
                        tp = zdict.get('top_price')
                        bp = zdict.get('bottom_price')
                        if tp is not None and bp is not None:
                            try:
                                zdict['center_price'] = (float(tp) + float(bp)) / 2
                            except Exception:
                                zdict['center_price'] = None

                    # Legacy aliases
                    zdict['type'] = zdict.get('type') or zdict.get('zone_type')
                    zdict['price'] = zdict.get('price') or zdict.get('center_price') or zdict.get('top_price') or zdict.get('bottom_price')
                    zdict['last_touched'] = zdict.get('last_touched') or zdict.get('last_tested')
                    zdict['touch_count'] = zdict.get('touch_count') or zdict.get('touches')
                except Exception:
                    # ignore mapping failures
                    pass

                validated_zone_dicts.append(zdict)

            # Calculate additional metrics using dicts
            metrics = self._calculate_zone_metrics(validated_zone_dicts, df_recent)

            return {
                "zones": validated_zone_dicts,
                "methodology": "multi",
                "metrics": metrics,
                "zone_count": len(validated_zone_dicts),
                "symbol": symbol
            }
            
        except Exception as e:
            logger.error(f"Error detecting zones for {symbol}: {e}")
            return {"zones": [], "methodology": "multi", "error": str(e)}
            
    def _detect_swing_zones(self, df: pd.DataFrame) -> List[SupplyDemandZone]:
        """Detect zones using swing point analysis."""
        zones = []
        
        # Calculate swing highs and lows
        swing_highs = self._find_swing_points(df['high'], 'high')
        swing_lows = self._find_swing_points(df['low'], 'low')
        
        # Create supply zones from swing highs
        for idx, price in swing_highs:
            zone = self._create_swing_zone(
                'supply', price, df.iloc[max(0, idx-self.swing_window):idx+self.swing_window]
            )
            if zone:
                zones.append(zone)
                
        # Create demand zones from swing lows
        for idx, price in swing_lows:
            zone = self._create_swing_zone(
                'demand', price, df.iloc[max(0, idx-self.swing_window):idx+self.swing_window]
            )
            if zone:
                zones.append(zone)
                
        return zones
        
    def _find_swing_points(self, prices: pd.Series, point_type: str) -> List[Tuple[int, float]]:
        """Find swing high or low points."""
        points = []
        
        for i in range(self.swing_window, len(prices) - self.swing_window):
            window = prices.iloc[i-self.swing_window:i+self.swing_window+1]
            
            if point_type == 'high':
                if prices.iloc[i] == window.max():
                    points.append((i, float(prices.iloc[i])))
            else:  # low
                if prices.iloc[i] == window.min():
                    points.append((i, float(prices.iloc[i])))
                    
        return points
        
    def _create_swing_zone(
        self,
        zone_type: str,
        base_price: float,
        context_data: pd.DataFrame
    ) -> Optional[SupplyDemandZone]:
        """Create a zone based on swing point and context."""
        if len(context_data) < 3:
            return None
            
        # Calculate zone boundaries based on volatility
        volatility = context_data['high'].max() - context_data['low'].min()
        zone_height = max(volatility * 0.1, base_price * self.min_zone_height_pct / 100)
        
        if zone_type == 'supply':
            top_price = base_price
            bottom_price = base_price - zone_height
        else:  # demand
            top_price = base_price + zone_height
            bottom_price = base_price
            
        # Calculate zone strength
        avg_volume = context_data['volume'].mean()
        max_volume = context_data['volume'].max()
        strength = min(100.0, (max_volume / avg_volume) * 25) if avg_volume > 0 else 50.0
        
        return SupplyDemandZone(
            zone_type=zone_type,
            top_price=top_price,
            bottom_price=bottom_price,
            strength=strength,
            volume_profile={
                'avg_volume': avg_volume,
                'max_volume': max_volume,
                'volume_ratio': max_volume / avg_volume if avg_volume > 0 else 1.0
            }
        )
        
    def _detect_volume_zones(self, df: pd.DataFrame) -> List[SupplyDemandZone]:
        """Detect zones using volume profile analysis."""
        zones = []
        
        # Calculate volume profile
        price_range = df['high'].max() - df['low'].min()
        bin_size = max(price_range * 0.005, 0.01)  # 0.5% of range or minimum 0.01
        
        # Create price bins
        price_bins = np.arange(df['low'].min(), df['high'].max(), bin_size)
        volume_profile = []
        
        for i in range(len(price_bins) - 1):
            mask = (df['close'] >= price_bins[i]) & (df['close'] < price_bins[i + 1])
            volume_sum = df[mask]['volume'].sum()
            volume_profile.append({
                'price_low': price_bins[i],
                'price_high': price_bins[i + 1],
                'volume': volume_sum,
                'count': mask.sum()
            })
            
        # Find high volume nodes
        if volume_profile:
            avg_volume = np.mean([v['volume'] for v in volume_profile])
            high_volume_nodes = [
                v for v in volume_profile 
                if v['volume'] > avg_volume * self.volume_threshold and v['count'] > 0
            ]
            
            # Create zones from high volume nodes
            for node in high_volume_nodes:
                # Determine zone type based on position relative to current price
                current_price = df['close'].iloc[-1]
                center_price = (node['price_low'] + node['price_high']) / 2
                
                if center_price > current_price:
                    zone_type = 'supply'
                else:
                    zone_type = 'demand'
                    
                strength = min(100.0, (node['volume'] / avg_volume) * 20)
                
                zones.append(SupplyDemandZone(
                    zone_type=zone_type,
                    top_price=node['price_high'],
                    bottom_price=node['price_low'],
                    strength=strength,
                    volume_profile={'volume': node['volume'], 'count': node['count']}
                ))
                
        return zones
        
    def _detect_fibonacci_zones(self, df: pd.DataFrame) -> List[SupplyDemandZone]:
        """Detect zones using Fibonacci retracement levels."""
        zones = []
        
        if len(df) < 20:
            return zones
            
        # Find recent high and low
        recent_period = min(20, len(df))
        recent_high = df['high'].iloc[-recent_period:].max()
        recent_low = df['low'].iloc[-recent_period:].min()
        
        if recent_high <= recent_low:
            return zones
            
        # Fibonacci ratios
        fib_ratios = [0.236, 0.382, 0.5, 0.618, 0.786]
        range_size = recent_high - recent_low
        
        for ratio in fib_ratios:
            level = recent_high - (range_size * ratio)
            
            # Create zone around fib level
            zone_height = max(range_size * 0.02, level * self.min_zone_height_pct / 100)
            
            # Determine zone type based on ratio
            if ratio <= 0.5:
                zone_type = 'demand'  # Support levels
            else:
                zone_type = 'supply'  # Resistance levels
                
            # Calculate strength based on fibonacci level importance
            strength = 60 + (abs(ratio - 0.5) * 40)  # Stronger at 0.382 and 0.618
            
            zones.append(SupplyDemandZone(
                zone_type=zone_type,
                top_price=level + zone_height/2,
                bottom_price=level - zone_height/2,
                strength=min(100.0, strength),
                volume_profile={'fibonacci_ratio': ratio, 'retracement_level': level}
            ))
            
        return zones
        
    def _validate_and_merge_zones(self, zones: List[SupplyDemandZone]) -> List[SupplyDemandZone]:
        """Validate zone quality and merge overlapping zones."""
        if not zones:
            return zones
            
        # Sort by strength
        zones.sort(key=lambda x: x.strength, reverse=True)
        
        validated_zones = []
        for zone in zones:
            if zone.strength < 20:  # Minimum strength threshold
                continue
                
            # Check for overlap with existing validated zones
            overlap = False
            for validated in validated_zones:
                if self._zones_overlap(zone, validated):
                    overlap = True
                    break
                    
            if not overlap:
                validated_zones.append(zone)
                
        return validated_zones
        
    def _zones_overlap(self, zone1: SupplyDemandZone, zone2: SupplyDemandZone) -> bool:
        """Check if two zones overlap."""
        # Check if zone types match (only merge same type zones)
        if zone1.zone_type != zone2.zone_type:
            return False
            
        # Check price overlap
        z1_low, z1_high = sorted([zone1.bottom_price, zone1.top_price])
        z2_low, z2_high = sorted([zone2.bottom_price, zone2.top_price])
        
        return not (z1_high < z2_low or z2_high < z1_low)
        
    def _calculate_zone_metrics(
        self,
        zones: List[Dict[str, Any]], # Changed to List[Dict[str, Any]]
        df: pd.DataFrame
    ) -> Dict[str, Any]:
        """Calculate additional metrics for zone analysis."""
        # Ensure current_price is a numeric float (defend against accidental Timestamp values)
        try:
            current_price = df['close'].iloc[-1]
            current_price = float(current_price)
        except Exception:
            try:
                current_price = float(pd.to_numeric(df['close'].iloc[-1], errors='coerce'))
            except Exception:
                current_price = 0.0
        
        # Categorize zones
        supply_zones = [z for z in zones if z['zone_type'] == 'supply']
        demand_zones = [z for z in zones if z['zone_type'] == 'demand']
        
        # Find nearest zones
        nearest_supply = None
        nearest_demand = None
        
        if supply_zones:
            # Defensive float coercion for center_price and current_price to avoid Timestamp subtraction
            nearest_supply = min(
                supply_zones,
                key=lambda z: abs(float(z.get('center_price', 0) or 0.0) - float(current_price or 0.0))
            )
            
        if demand_zones:
            nearest_demand = min(
                demand_zones,
                key=lambda z: abs(float(z.get('center_price', 0) or 0.0) - float(current_price or 0.0))
            )
            
        return {
            'current_price': current_price,
            'supply_zone_count': len(supply_zones),
            'demand_zone_count': len(demand_zones),
            'nearest_supply': nearest_supply,
            'nearest_demand': nearest_demand,
            'avg_supply_strength': np.mean([z['strength'] for z in supply_zones]) if supply_zones else 0,
            'avg_demand_strength': np.mean([z['strength'] for z in demand_zones]) if demand_zones else 0
        }


# Global instance for easy access
supply_demand_detector = SupplyDemandDetector()


class EnhancedZoneAnalyzer:
    """Enhanced zone analysis with advanced detection methods and metrics."""
    
    def __init__(self):
        """Initialize enhanced zone analyzer."""
        self.detector = SupplyDemandDetector()
        self.zone_validation_threshold = 0.3  # 30% price movement for valid zones
        self.min_zone_strength = 25.0  # Minimum zone strength
        
    def detect_enhanced_zones(self, df: pd.DataFrame, symbol: str = "") -> Dict[str, Any]:
        """
        Detect enhanced supply and demand zones with comprehensive analysis.
        
        Args:
            df: DataFrame with OHLCV data
            symbol: Stock symbol for logging
            
        Returns:
            Enhanced zone analysis with multiple detection methods
        """
        if df is None or df.empty:
            logger.warning(f"Empty DataFrame provided for enhanced zone analysis: {symbol}")
            return {"zones": [], "analysis": {}, "recommendations": []}
        
        try:
            # Get standard zones from the base detector
            standard_zones = self.detector.detect_zones(df, symbol)
            
            # Apply enhanced detection methods
            enhanced_zones = []
            
            # Method 1: Swing point analysis
            swing_zones = self._detect_swing_enhanced_zones(df)
            enhanced_zones.extend(swing_zones)
            
            # Method 2: Volume profile analysis
            volume_zones = self._detect_volume_enhanced_zones(df)
            enhanced_zones.extend(volume_zones)
            
            # Method 3: Fibonacci retracement analysis
            fib_zones = self._detect_fibonacci_enhanced_zones(df)
            enhanced_zones.extend(fib_zones)
            
            # Method 4: Support/resistance confluence
            confluence_zones = self._detect_confluence_zones(df, enhanced_zones)
            enhanced_zones.extend(confluence_zones)
            
            # Validate and merge zones
            validated_zones = self._validate_and_enhance_zones(enhanced_zones)
            
            # Generate zone analysis
            zone_analysis = self._analyze_zone_strengths(validated_zones, df)
            
            # Generate trading recommendations
            recommendations = self._generate_zone_recommendations(validated_zones, df)
            
            return {
                "zones": validated_zones,
                "analysis": zone_analysis,
                "recommendations": recommendations,
                "detection_methods": ["swing_points", "volume_profile", "fibonacci", "confluence"],
                "zone_count": len(validated_zones),
                "symbol": symbol,
                "timestamp": pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in enhanced zone detection for {symbol}: {e}")
            return {"zones": [], "analysis": {}, "recommendations": [], "error": str(e)}
    
    def _detect_swing_enhanced_zones(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect enhanced zones using swing point analysis with additional metrics."""
        zones = []
        
        try:
            # Calculate swing highs and lows with enhanced logic
            swing_highs = self._find_enhanced_swing_points(df['high'], 'high')
            swing_lows = self._find_enhanced_swing_points(df['low'], 'low')
            
            # Create enhanced supply zones from swing highs
            for idx, price, volume in swing_highs:
                zone = self._create_enhanced_swing_zone(
                    'supply', price, volume,
                    df.iloc[max(0, idx-10):idx+10], 'high'
                )
                if zone:
                    zones.append(zone)
                    
            # Create enhanced demand zones from swing lows
            for idx, price, volume in swing_lows:
                zone = self._create_enhanced_swing_zone(
                    'demand', price, volume,
                    df.iloc[max(0, idx-10):idx+10], 'low'
                )
                if zone:
                    zones.append(zone)
                    
        except Exception as e:
            logger.error(f"Error in enhanced swing zone detection: {e}")
        
        return zones
    
    def _find_enhanced_swing_points(self, prices: pd.Series, point_type: str) -> List[Tuple[int, float, float]]:
        """Find enhanced swing points with volume consideration."""
        points = []
        
        for i in range(5, len(prices) - 5):
            # Look at larger window for more reliable swings
            window = prices.iloc[i-5:i+6]
            
            if point_type == 'high':
                if prices.iloc[i] == window.max():
                    # Get corresponding volume for this point
                    volume = self._get_average_volume_at_index(i, len(prices))
                    points.append((i, float(prices.iloc[i]), volume))
            else:  # low
                if prices.iloc[i] == window.min():
                    volume = self._get_average_volume_at_index(i, len(prices))
                    points.append((i, float(prices.iloc[i]), volume))
                    
        return points
    
    def _get_average_volume_at_index(self, price_index: int, total_length: int) -> float:
        """Get average volume around a price index."""
        try:
            # Get volume data for the same period
            volume_window = max(1, min(5, total_length))
            start_idx = max(0, price_index - volume_window)
            end_idx = min(total_length, price_index + volume_window + 1)
            
            # Since we don't have access to the volume data here, return a default
            # The actual volume will be calculated in _create_enhanced_swing_zone
            return 5000000.0  # Default volume if not available
                
        except Exception:
            return 5000000.0
    
    def _create_enhanced_swing_zone(self, zone_type: str, base_price: float, volume: float,
                                   context_data: pd.DataFrame, point_type: str) -> Optional[Dict[str, Any]]:
        """Create enhanced zone with comprehensive metrics."""
        if len(context_data) < 3:
            return None
            
        # Calculate zone boundaries based on volatility and volume
        volatility = context_data['high'].max() - context_data['low'].min()
        avg_volume = context_data['volume'].mean()
        
        # Zone height based on volatility and volume strength
        volume_multiplier = min(2.0, volume / avg_volume) if avg_volume > 0 else 1.0
        zone_height = max(volatility * 0.1 * volume_multiplier, base_price * 0.02)
        
        if zone_type == 'supply':
            top_price = base_price
            bottom_price = base_price - zone_height
        else:  # demand
            top_price = base_price + zone_height
            bottom_price = base_price
            
        # Calculate comprehensive zone strength
        strength = self._calculate_enhanced_zone_strength(
            context_data, zone_type, base_price, volume, avg_volume
        )
        
        if strength < self.min_zone_strength:
            return None
        
        # Calculate zone reliability metrics
        reliability = self._calculate_zone_reliability(context_data, zone_type)
        
        # Find zone confluence with other technical levels
        confluence_score = self._calculate_confluence_score(context_data, base_price)
        
        return {
            'zone_type': zone_type,
            'top_price': top_price,
            'bottom_price': bottom_price,
            'center_price': (top_price + bottom_price) / 2,
            'strength': strength,
            'reliability': reliability,
            'confluence_score': confluence_score,
            'volume_at_formation': volume,
            'avg_volume_context': avg_volume,
            'zone_height': zone_height,
            'method': 'enhanced_swing',
            'touch_count': 0,  # Will be updated during validation
            'last_tested': None,
            'formation_index': context_data.index[0] if not context_data.empty else None,
            'volatility_context': volatility,
            'price_distance_to_current': None,  # Will be calculated later
            'timeframe': 'multi-day',
            'confidence': min(100.0, strength + (reliability * 0.5) + (confluence_score * 0.3))
        }
    
    def _calculate_enhanced_zone_strength(self, context_data: pd.DataFrame, zone_type: str,
                                        base_price: float, volume: float, avg_volume: float) -> float:
        """Calculate enhanced zone strength based on multiple factors."""
        try:
            strength = 50.0  # Base strength
            
            # Volume contribution (0-25 points)
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1.0
            strength += min(25.0, volume_ratio * 10)
            
            # Volatility contribution (0-15 points)
            volatility = context_data['high'].max() - context_data['low'].min()
            price_range = context_data['close'].max() - context_data['close'].min()
            if price_range > 0:
                volatility_ratio = volatility / price_range
                strength += min(15.0, volatility_ratio * 20)
            
            # Price movement contribution (0-20 points)
            if zone_type == 'supply':
                # For supply zones, strength increases if price moved away from zone
                recent_low = context_data['close'].tail(5).min()
                if base_price > recent_low:
                    distance_ratio = (base_price - recent_low) / base_price
                    strength += min(20.0, distance_ratio * 50)
            else:  # demand
                # For demand zones, strength increases if price moved away from zone
                recent_high = context_data['close'].tail(5).max()
                if base_price < recent_high:
                    distance_ratio = (recent_high - base_price) / base_price
                    strength += min(20.0, distance_ratio * 50)
            
            # Time factor (0-10 points) - older zones get slight bonus
            if len(context_data) > 10:
                strength += 5.0
            
            return min(100.0, strength)
            
        except Exception:
            return 50.0
    
    def _calculate_zone_reliability(self, context_data: pd.DataFrame, zone_type: str) -> float:
        """Calculate zone reliability based on historical testing."""
        try:
            reliability = 50.0  # Base reliability
            
            # Data quality factor
            if len(context_data) >= 20:
                reliability += 15.0
            elif len(context_data) >= 10:
                reliability += 10.0
            
            # Volume consistency
            volume_std = context_data['volume'].std()
            volume_mean = context_data['volume'].mean()
            if volume_mean > 0:
                volume_cv = volume_std / volume_mean
                if volume_cv < 0.5:  # Low coefficient of variation
                    reliability += 10.0
                elif volume_cv < 1.0:
                    reliability += 5.0
            
            # Price action consistency
            price_range = context_data['high'].max() - context_data['low'].min()
            close_range = context_data['close'].max() - context_data['close'].min()
            if price_range > 0:
                price_action_consistency = 1 - (close_range / price_range)
                reliability += min(10.0, price_action_consistency * 20)
            
            return min(100.0, reliability)
            
        except Exception:
            return 50.0
    
    def _calculate_confluence_score(self, context_data: pd.DataFrame, price_level: float) -> float:
        """Calculate confluence score with other technical levels."""
        try:
            confluence = 0.0
            
            # Check for confluence with moving averages
            if len(context_data) >= 20:
                sma_20 = context_data['close'].rolling(window=20).mean().iloc[-1]
                if abs(sma_20 - price_level) / price_level < 0.02:  # Within 2%
                    confluence += 25.0
            
            # Check for confluence with recent highs/lows
            recent_high = context_data['high'].tail(5).max()
            recent_low = context_data['low'].tail(5).min()
            
            if abs(recent_high - price_level) / price_level < 0.01:  # Within 1%
                confluence += 25.0
            elif abs(recent_low - price_level) / price_level < 0.01:
                confluence += 25.0
            
            return min(100.0, confluence)
            
        except Exception:
            return 0.0
    
    def _detect_volume_enhanced_zones(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect zones using enhanced volume profile analysis."""
        zones = []
        
        try:
            if 'volume' not in df.columns or df['volume'].sum() == 0:
                return zones
            
            # Create volume profile with enhanced binning
            price_range = df['high'].max() - df['low'].min()
            bin_size = max(price_range * 0.002, 0.01)  # 0.2% of range or minimum
            
            # Create price bins
            price_bins = np.arange(df['low'].min(), df['high'].max(), bin_size)
            volume_profile = []
            
            for i in range(len(price_bins) - 1):
                mask = (df['close'] >= price_bins[i]) & (df['close'] < price_bins[i + 1])
                volume_sum = df[mask]['volume'].sum()
                point_count = mask.sum()
                
                if volume_sum > 0:
                    volume_profile.append({
                        'price_low': price_bins[i],
                        'price_high': price_bins[i + 1],
                        'volume': volume_sum,
                        'point_count': point_count,
                        'avg_price': (price_bins[i] + price_bins[i + 1]) / 2
                    })
            
            if not volume_profile:
                return zones
            
            # Find volume nodes (high volume areas)
            total_volume = sum(v['volume'] for v in volume_profile)
            avg_volume = total_volume / len(volume_profile)
            
            # Volume threshold based on standard deviation
            volumes = [v['volume'] for v in volume_profile]
            volume_std = np.std(volumes)
            volume_threshold = avg_volume + volume_std
            
            # Identify high volume nodes
            high_volume_nodes = [
                v for v in volume_profile
                if v['volume'] > volume_threshold and v['point_count'] > 0
            ]
            
            # Create zones from high volume nodes
            for node in high_volume_nodes:
                # Determine zone type based on price position
                current_price = df['close'].iloc[-1]
                center_price = node['avg_price']
                
                if center_price > current_price:
                    zone_type = 'supply'
                else:
                    zone_type = 'demand'
                
                # Calculate zone strength based on volume
                volume_strength = min(100.0, (node['volume'] / avg_volume) * 15)
                
                zones.append({
                    'zone_type': zone_type,
                    'top_price': node['price_high'],
                    'bottom_price': node['price_low'],
                    'center_price': center_price,
                    'strength': volume_strength,
                    'reliability': 75.0,  # Volume zones are generally reliable
                    'confluence_score': 0.0,
                    'volume_at_formation': node['volume'],
                    'avg_volume_context': avg_volume,
                    'zone_height': node['price_high'] - node['price_low'],
                    'method': 'volume_profile',
                    'touch_count': 0,
                    'last_tested': None,
                    'formation_index': df.index[-1],
                    'volatility_context': 0.0,
                    'price_distance_to_current': abs(float(center_price) - float(current_price)),
                    'timeframe': 'intraday',
                    'confidence': min(100.0, volume_strength + 25.0),
                    'volume_point_count': node['point_count'],
                    'volume_ratio': node['volume'] / total_volume
                })
            
        except Exception as e:
            logger.error(f"Error in enhanced volume zone detection: {e}")
        
        return zones
    
    def _detect_fibonacci_enhanced_zones(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect zones using enhanced Fibonacci retracement analysis."""
        zones = []
        
        try:
            if len(df) < 20:
                return zones
            
            # Find significant swing points for Fibonacci calculation
            lookback_period = min(50, len(df))
            recent_data = df.tail(lookback_period)
            
            # Find highest high and lowest low in the period
            high_idx = recent_data['high'].idxmax()
            low_idx = recent_data['low'].idxmin()
            
            # Ensure we have proper high-low sequence
            if high_idx > low_idx:  # Uptrend
                swing_high = recent_data.loc[high_idx, 'high']
                swing_low = recent_data.loc[low_idx, 'low']
                trend_direction = 'uptrend'
            elif low_idx > high_idx:  # Downtrend
                swing_high = recent_data.loc[high_idx, 'high']
                swing_low = recent_data.loc[low_idx, 'low']
                trend_direction = 'downtrend'
            else:
                return zones  # Invalid pattern
            
            # Calculate Fibonacci levels
            price_range = swing_high - swing_low
            fib_ratios = [0.236, 0.382, 0.5, 0.618, 0.786]
            
            for ratio in fib_ratios:
                level = swing_high - (price_range * ratio)
                
                # Create zone around Fibonacci level
                zone_height = max(price_range * 0.015, level * 0.005)  # 1.5% of range or 0.5% of price
                
                # Determine zone type based on trend and level
                if trend_direction == 'uptrend':
                    if ratio <= 0.5:
                        zone_type = 'demand'  # Support levels in uptrend
                    else:
                        zone_type = 'supply'  # Resistance levels in uptrend
                else:  # downtrend
                    if ratio <= 0.5:
                        zone_type = 'supply'  # Resistance levels in downtrend
                    else:
                        zone_type = 'demand'  # Support levels in downtrend
                
                # Calculate Fibonacci-based strength
                fib_strength = 60 + (abs(ratio - 0.5) * 40)  # Stronger at 0.382 and 0.618
                
                zones.append({
                    'zone_type': zone_type,
                    'top_price': level + zone_height/2,
                    'bottom_price': level - zone_height/2,
                    'center_price': level,
                    'strength': min(100.0, fib_strength),
                    'reliability': 85.0,  # Fibonacci zones are highly reliable
                    'confluence_score': 0.0,
                    'volume_at_formation': 0.0,
                    'avg_volume_context': 0.0,
                    'zone_height': zone_height,
                    'method': 'fibonacci',
                    'touch_count': 0,
                    'last_tested': None,
                    'formation_index': df.index[-1],
                    'volatility_context': price_range,
                    'price_distance_to_current': abs(level - df['close'].iloc[-1]),
                    'timeframe': 'swing',
                    'confidence': min(100.0, fib_strength + 15.0),
                    'fibonacci_ratio': ratio,
                    'swing_high': swing_high,
                    'swing_low': swing_low,
                    'trend_direction': trend_direction
                })
            
        except Exception as e:
            logger.error(f"Error in enhanced Fibonacci zone detection: {e}")
        
        return zones
    
    def _detect_confluence_zones(self, df: pd.DataFrame, existing_zones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect zones where multiple technical levels converge."""
        confluence_zones = []
        
        try:
            if len(existing_zones) < 2:
                return confluence_zones
            
            # Group zones by proximity
            zone_clusters = self._cluster_zones_by_proximity(existing_zones)
            
            # Create confluence zones from clusters
            for cluster in zone_clusters:
                if len(cluster) >= 2:  # At least 2 zones for confluence
                    confluence_zone = self._create_confluence_zone(cluster)
                    if confluence_zone:
                        confluence_zones.append(confluence_zone)
            
        except Exception as e:
            logger.error(f"Error in confluence zone detection: {e}")
        
        return confluence_zones
    
    def _cluster_zones_by_proximity(self, zones: List[Dict[str, Any]], proximity_threshold: float = 0.02) -> List[List[Dict[str, Any]]]:
        """Cluster zones based on price proximity."""
        clusters = []
        used_zones = set()
        
        for i, zone1 in enumerate(zones):
            if i in used_zones:
                continue
                
            cluster = [zone1]
            used_zones.add(i)
            
            center1 = zone1['center_price']
            
            for j, zone2 in enumerate(zones[i+1:], i+1):
                if j in used_zones:
                    continue
                    
                center2 = zone2['center_price']
                distance = abs(center1 - center2)
                avg_price = (center1 + center2) / 2
                
                # Check if zones are within proximity threshold
                if distance / avg_price <= proximity_threshold:
                    cluster.append(zone2)
                    used_zones.add(j)
            
            clusters.append(cluster)
        
        return clusters
    
    def _create_confluence_zone(self, zone_cluster: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Create a confluence zone from a cluster of zones."""
        try:
            # Determine zone type (majority rules)
            zone_types = [z['zone_type'] for z in zone_cluster]
            dominant_type = max(set(zone_types), key=zone_types.count)
            
            # Calculate weighted average center price
            total_strength = sum(z['strength'] for z in zone_cluster)
            weighted_center = sum(z['center_price'] * z['strength'] for z in zone_cluster) / total_strength
            
            # Calculate combined strength
            combined_strength = min(100.0, sum(z['strength'] for z in zone_cluster) / len(zone_cluster) + 20)
            
            # Calculate boundaries
            min_bottom = min(z['bottom_price'] for z in zone_cluster)
            max_top = max(z['top_price'] for z in zone_cluster)
            
            return {
                'zone_type': dominant_type,
                'top_price': max_top,
                'bottom_price': min_bottom,
                'center_price': weighted_center,
                'strength': combined_strength,
                'reliability': min(100.0, sum(z['reliability'] for z in zone_cluster) / len(zone_cluster) + 15),
                'confluence_score': min(100.0, len(zone_cluster) * 25),
                'volume_at_formation': sum(z['volume_at_formation'] for z in zone_cluster),
                'avg_volume_context': sum(z['avg_volume_context'] for z in zone_cluster) / len(zone_cluster),
                'zone_height': max_top - min_bottom,
                'method': 'confluence',
                'touch_count': 0,
                'last_tested': None,
                'formation_index': zone_cluster[0].get('formation_index'),
                'volatility_context': sum(z['volatility_context'] for z in zone_cluster) / len(zone_cluster),
                'price_distance_to_current': sum(z['price_distance_to_current'] for z in zone_cluster) / len(zone_cluster),
                'timeframe': 'confluence',
                'confidence': min(100.0, combined_strength + 25),
                'component_zones': len(zone_cluster),
                'component_methods': list(set(z['method'] for z in zone_cluster))
            }
            
        except Exception:
            return None
    
    def _validate_and_enhance_zones(self, zones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate zone quality and enhance with additional metrics."""
        if not zones:
            return zones
        
        # Sort by strength
        zones.sort(key=lambda x: x['strength'], reverse=True)
        
        validated_zones = []
        
        for zone in zones:
            # Apply strength filter
            if zone['strength'] < self.min_zone_strength:
                continue
            
            # Check for overlap with existing validated zones
            overlap = False
            for validated in validated_zones:
                if self._zones_overlap_enhanced(zone, validated):
                    # Merge zones if they overlap and the new one is stronger
                    if zone['strength'] > validated['strength']:
                        validated_zones.remove(validated)
                        validated_zones.append(zone)
                    overlap = True
                    break
            
            if not overlap:
                # Add enhanced metrics
                zone = self._add_zone_enhancement_metrics(zone)
                validated_zones.append(zone)
        
        # Calculate price distances to current price
        current_price = zones[0].get('formation_index', 0)
        if hasattr(current_price, 'iloc'):
            current_price = current_price.iloc[-1] if hasattr(current_price, 'iloc') else 0
        
        # Ensure current_price is a numeric value, not a Timestamp
        try:
            current_price = float(current_price) if current_price is not None else 0.0
        except (ValueError, TypeError):
            current_price = 0.0
        
        for zone in validated_zones:
            zone['price_distance_to_current'] = abs(float(zone['center_price']) - current_price)
        
        return validated_zones
    
    def _zones_overlap_enhanced(self, zone1: Dict[str, Any], zone2: Dict[str, Any]) -> bool:
        """Check if two zones overlap with enhanced logic."""
        # Only merge zones of the same type
        if zone1['zone_type'] != zone2['zone_type']:
            return False
        
        # Check price overlap with tolerance
        z1_low, z1_high = sorted([zone1['bottom_price'], zone1['top_price']])
        z2_low, z2_high = sorted([zone2['bottom_price'], zone2['top_price']])
        
        overlap_threshold = 0.05  # 5% overlap threshold
        
        # Calculate overlap percentage
        overlap_start = max(z1_low, z2_low)
        overlap_end = min(z1_high, z2_high)
        
        if overlap_start < overlap_end:
            overlap_size = overlap_end - overlap_start
            z1_size = z1_high - z1_low
            z2_size = z2_high - z2_low
            
            # Consider overlapping if significant overlap exists
            if (overlap_size / z1_size >= overlap_threshold or
                overlap_size / z2_size >= overlap_threshold):
                return True
        
        return False
    
    def _add_zone_enhancement_metrics(self, zone: Dict[str, Any]) -> Dict[str, Any]:
        """Add enhancement metrics to a zone."""
        try:
            # Add zone age factor
            zone['age_factor'] = 1.0  # Will be calculated based on actual data
            
            # Add zone width category
            zone_width_pct = zone['zone_height'] / zone['center_price']
            if zone_width_pct < 0.01:  # Less than 1%
                zone['width_category'] = 'tight'
            elif zone_width_pct < 0.03:  # Less than 3%
                zone['width_category'] = 'normal'
            else:
                zone['width_category'] = 'wide'
            
            # Add zone quality score
            zone['quality_score'] = (zone['strength'] * 0.4 +
                                   zone['reliability'] * 0.3 +
                                   zone['confluence_score'] * 0.3)
            
            # Add zone priority
            if zone['strength'] >= 80 and zone['quality_score'] >= 75:
                zone['priority'] = 'high'
            elif zone['strength'] >= 60 and zone['quality_score'] >= 60:
                zone['priority'] = 'medium'
            else:
                zone['priority'] = 'low'
            
            # Add zone formation quality
            zone['formation_quality'] = 'high' if zone['quality_score'] >= 70 else 'medium' if zone['quality_score'] >= 50 else 'low'
            
        except Exception:
            # If enhancement fails, use default values
            zone['age_factor'] = 1.0
            zone['width_category'] = 'normal'
            zone['quality_score'] = zone['strength']
            zone['priority'] = 'medium'
            zone['formation_quality'] = 'medium'
        
        return zone
    
    def _analyze_zone_strengths(self, zones: List[Dict[str, Any]], df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze zone strengths and provide insights."""
        if not zones:
            return {}
        
        try:
            current_price = df['close'].iloc[-1]
            
            # Categorize zones
            supply_zones = [z for z in zones if z['zone_type'] == 'supply']
            demand_zones = [z for z in zones if z['zone_type'] == 'demand']
            
            # Find nearest zones
            nearest_supply = None
            nearest_demand = None
            min_supply_distance = float('inf')
            min_demand_distance = float('inf')
            
            for zone in supply_zones:
                distance = abs(zone['center_price'] - current_price)
                if distance < min_supply_distance:
                    min_supply_distance = distance
                    nearest_supply = zone
            
            for zone in demand_zones:
                distance = abs(zone['center_price'] - current_price)
                if distance < min_demand_distance:
                    min_demand_distance = distance
                    nearest_demand = zone
            
            # Calculate zone statistics
            stats = {
                'total_zones': len(zones),
                'supply_zones': len(supply_zones),
                'demand_zones': len(demand_zones),
                'avg_supply_strength': np.mean([z['strength'] for z in supply_zones]) if supply_zones else 0,
                'avg_demand_strength': np.mean([z['strength'] for z in demand_zones]) if demand_zones else 0,
                'highest_strength_zone': max(zones, key=lambda x: x['strength']) if zones else None,
                'nearest_supply': nearest_supply,
                'nearest_demand': nearest_demand,
                'current_price': current_price,
                'zone_density': len(zones) / (df['high'].max() - df['low'].min()) * 1000,  # Zones per 1000 price units
                'strong_zones_count': len([z for z in zones if z['strength'] >= 70]),
                'high_priority_zones': len([z for z in zones if z['priority'] == 'high'])
            }
            
            # Add market structure analysis
            stats['market_structure'] = self._analyze_market_structure(zones, current_price)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing zone strengths: {e}")
            return {}
    
    def _analyze_market_structure(self, zones: List[Dict[str, Any]], current_price: float) -> str:
        """Analyze overall market structure based on zones."""
        try:
            supply_zones = [z for z in zones if z['zone_type'] == 'supply']
            demand_zones = [z for z in zones if z['zone_type'] == 'demand']
            
            if not supply_zones and not demand_zones:
                return 'undefined'
            
            # Count zones above and below current price
            zones_above = len([z for z in zones if z['center_price'] > current_price])
            zones_below = len([z for z in zones if z['center_price'] < current_price])
            
            # Analyze zone strength distribution
            avg_strength = np.mean([z['strength'] for z in zones])
            
            if zones_above > zones_below and avg_strength > 60:
                return 'resistance_dominated'
            elif zones_below > zones_above and avg_strength > 60:
                return 'support_dominated'
            elif abs(zones_above - zones_below) <= 1 and avg_strength > 60:
                return 'balanced'
            else:
                return 'weak_structure'
                
        except Exception:
            return 'undefined'
    
    def _generate_zone_recommendations(self, zones: List[Dict[str, Any]], df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate trading recommendations based on zones."""
        recommendations = []
        
        try:
            current_price = df['close'].iloc[-1]
            
            # Sort zones by priority and strength
            sorted_zones = sorted(zones, key=lambda x: (x['priority'], x['strength']), reverse=True)
            
            for zone in sorted_zones[:5]:  # Top 5 zones only
                recommendation = self._generate_zone_recommendation(zone, current_price)
                if recommendation:
                    recommendations.append(recommendation)
            
            # Add general market structure recommendation
            structure_rec = self._generate_structure_recommendation(zones, current_price)
            if structure_rec:
                recommendations.append(structure_rec)
            
        except Exception as e:
            logger.error(f"Error generating zone recommendations: {e}")
        
        return recommendations
    
    def _generate_zone_recommendation(self, zone: Dict[str, Any], current_price: float) -> Optional[Dict[str, Any]]:
        """Generate a specific recommendation for a zone."""
        try:
            distance = abs(zone['center_price'] - current_price)
            distance_pct = distance / current_price
            
            # Only recommend zones within reasonable distance
            if distance_pct > 0.10:  # More than 10% away
                return None
            
            recommendation = {
                'zone_type': zone['zone_type'],
                'zone_center': zone['center_price'],
                'current_price': current_price,
                'distance': distance,
                'distance_pct': distance_pct,
                'strength': zone['strength'],
                'priority': zone['priority'],
                'confidence': zone['confidence'],
                'method': zone['method'],
                'recommendation_type': None,
                'action': None,
                'reasoning': None,
                'risk_level': None
            }
            
            # Generate recommendation based on zone type and price position
            if zone['zone_type'] == 'demand':
                if current_price <= zone['center_price'] * 1.02:  # Very close to or below demand zone
                    recommendation['recommendation_type'] = 'entry'
                    recommendation['action'] = 'consider_buying'
                    recommendation['reasoning'] = f'Price near strong demand zone (strength: {zone["strength"]:.0f})'
                    recommendation['risk_level'] = 'medium' if zone['strength'] >= 70 else 'high'
                elif current_price > zone['center_price'] * 1.02 and distance_pct < 0.05:
                    recommendation['recommendation_type'] = 'watch'
                    recommendation['action'] = 'watch_for_pullback'
                    recommendation['reasoning'] = f'Price above demand zone but within 5% - watch for pullback'
                    recommendation['risk_level'] = 'low'
            
            elif zone['zone_type'] == 'supply':
                if current_price >= zone['center_price'] * 0.98:  # Very close to or above supply zone
                    recommendation['recommendation_type'] = 'entry'
                    recommendation['action'] = 'consider_selling'
                    recommendation['reasoning'] = f'Price near strong supply zone (strength: {zone["strength"]:.0f})'
                    recommendation['risk_level'] = 'medium' if zone['strength'] >= 70 else 'high'
                elif current_price < zone['center_price'] * 0.98 and distance_pct < 0.05:
                    recommendation['recommendation_type'] = 'watch'
                    recommendation['action'] = 'watch_for_rally'
                    recommendation['reasoning'] = f'Price below supply zone but within 5% - watch for rally'
                    recommendation['risk_level'] = 'low'
            
            # Only return valid recommendations
            if recommendation['recommendation_type']:
                return recommendation
            else:
                return None
                
        except Exception:
            return None
    
    def _generate_structure_recommendation(self, zones: List[Dict[str, Any]], current_price: float) -> Optional[Dict[str, Any]]:
        """Generate general market structure recommendation."""
        try:
            if not zones:
                return None
            
            supply_zones = [z for z in zones if z['zone_type'] == 'supply']
            demand_zones = [z for z in zones if z['zone_type'] == 'demand']
            
            avg_supply_strength = np.mean([z['strength'] for z in supply_zones]) if supply_zones else 0
            avg_demand_strength = np.mean([z['strength'] for z in demand_zones]) if demand_zones else 0
            
            # Count zones above and below current price
            zones_above = len([z for z in zones if z['center_price'] > current_price])
            zones_below = len([z for z in zones if z['center_price'] < current_price])
            
            recommendation = {
                'zone_type': 'structure',
                'recommendation_type': 'structure',
                'action': None,
                'reasoning': None,
                'risk_level': 'medium',
                'market_structure': None
            }
            
            # Analyze market structure
            if zones_above > zones_below and avg_supply_strength > 60:
                recommendation['market_structure'] = 'resistance_dominated'
                recommendation['action'] = 'cautious_bullish'
                recommendation['reasoning'] = f'More resistance zones above current price (avg strength: {avg_supply_strength:.0f})'
            
            elif zones_below > zones_above and avg_demand_strength > 60:
                recommendation['market_structure'] = 'support_dominated'
                recommendation['action'] = 'cautious_bearish'
                recommendation['reasoning'] = f'More support zones below current price (avg strength: {avg_demand_strength:.0f})'
            
            elif abs(zones_above - zones_below) <= 1:
                recommendation['market_structure'] = 'balanced'
                recommendation['action'] = 'neutral'
                recommendation['reasoning'] = 'Balanced zone structure around current price'
            
            else:
                recommendation['market_structure'] = 'weak_structure'
                recommendation['action'] = 'wait'
                recommendation['reasoning'] = 'Weak zone structure - wait for clearer signals'
            
            return recommendation if recommendation['action'] else None
            
        except Exception:
            return None


# Global enhanced zone analyzer instance
enhanced_zone_analyzer = EnhancedZoneAnalyzer()