from typing import Dict, List, Optional

import numpy as np
import pandas as pd

from src.core.logger import get_logger
from src.core.risk_management.atr_calculator import TradeDirection, RiskLevel
from src.shared.technical_analysis.indicators import calculate_atr
from src.shared.technical_analysis.indicators import calculate_supertrend

logger = get_logger(__name__)

class PositionStrategyCalculator:
    """
    Standardized long/short entry strategy with ATR-based calculations
    and estimated call/put targets.
    """
    
    def __init__(self, atr_period: int = 14, risk_multiplier: float = 2.0):
        """
        Initialize strategy calculator.
        
        Args:
            atr_period: Period for ATR calculation (default 14)
            risk_multiplier: Multiplier for risk management (default 2.0)
        """
        self.atr_period = atr_period
        self.risk_multiplier = risk_multiplier
    
    def calculate_entry_strategy(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series
    ) -> Dict[str, Optional[float]]:
        """
        Calculate entry strategy with enhanced Supertrend tracking.
        """
        # Existing implementation, now with Supertrend details
        try:
            # Calculate ATR
            atr = calculate_atr(high, low, close, period=self.atr_period)
            
            if atr is None:
                return {
                    "direction": None,
                    "entry_price": None,
                    "stop_loss": None,
                    "take_profit": None,
                    "risk_level": None,
                    "supertrend": None
                }
            
            current_price = close.iloc[-1]
            
            # Determine trade direction with Supertrend
            direction = self._determine_trade_direction(high, low, close)
            
            # Calculate Supertrend with full details
            supertrend = calculate_supertrend(high, low, close)
            
            # Calculate stop loss
            stop_loss = self._calculate_stop_loss(current_price, atr, direction)
            
            # Calculate take profit
            take_profit = self._calculate_take_profit(current_price, atr, direction)
            
            # Estimate risk level
            risk_level = self._classify_risk(atr, current_price)
            
            return {
                "direction": direction.value,
                "entry_price": current_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "risk_level": risk_level.value,
                "atr": atr,
                "supertrend": {
                    "trend": supertrend['trend'],
                    "value": supertrend['value'],
                    "direction": supertrend['direction'],
                    "flip_price": supertrend['flip_price'],
                    "flip_timestamp": supertrend['flip_timestamp'],
                    "upper_band": supertrend['upper_band'],
                    "lower_band": supertrend['lower_band']
                }
            }
        
        except Exception as e:
            logger.error(f"Error in entry strategy calculation: {e}")
            return {
                "direction": None,
                "entry_price": None,
                "stop_loss": None,
                "take_profit": None,
                "risk_level": None,
                "atr": None,
                "supertrend": None
            }
    
    def _determine_trade_direction(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series
    ) -> TradeDirection:
        """
        Determine trade direction using Supertrend and moving averages.
        
        Args:
            high: Series of high prices
            low: Series of low prices
            close: Series of closing prices
        
        Returns:
            Trade direction (LONG or SHORT)
        """
        # Track previous trend state
        prev_trend = getattr(self, '_last_supertrend_trend', None)
        
        # Calculate Supertrend with previous trend tracking
        supertrend = calculate_supertrend(
            high, low, close, 
            prev_trend=prev_trend
        )
        
        # Store current trend for next iteration
        self._last_supertrend_trend = supertrend['trend']
        
        # Calculate moving averages as secondary confirmation
        ma_short = close.rolling(window=5).mean()
        ma_long = close.rolling(window=20).mean()
        
        # Log trend flip for analytics
        if supertrend['flip_price'] is not None:
            logger.info(
                f"Supertrend Trend Flip: "
                f"From {prev_trend} to {supertrend['trend']} "
                f"at price {supertrend['flip_price']} "
                f"on {supertrend['flip_timestamp']}"
            )
        
        # Determine direction based on Supertrend and moving averages
        if (supertrend['trend'] == 'up' and ma_short.iloc[-1] > ma_long.iloc[-1]):
            return TradeDirection.LONG
        elif (supertrend['trend'] == 'down' and ma_short.iloc[-1] < ma_long.iloc[-1]):
            return TradeDirection.SHORT
        
        # Fallback to previous logic if Supertrend is inconclusive
        return TradeDirection.LONG if ma_short.iloc[-1] > ma_long.iloc[-1] else TradeDirection.SHORT
    
    def _calculate_stop_loss(
        self, 
        current_price: float, 
        atr: float, 
        direction: TradeDirection
    ) -> float:
        """
        Calculate stop loss based on ATR and trade direction.
        
        Args:
            current_price: Current market price
            atr: Average True Range
            direction: Trade direction
        
        Returns:
            Stop loss price
        """
        if direction == TradeDirection.LONG:
            return current_price - (atr * self.risk_multiplier)
        else:
            return current_price + (atr * self.risk_multiplier)
    
    def _calculate_take_profit(
        self, 
        current_price: float, 
        atr: float, 
        direction: TradeDirection
    ) -> float:
        """
        Calculate take profit based on ATR and trade direction.
        
        Args:
            current_price: Current market price
            atr: Average True Range
            direction: Trade direction
        
        Returns:
            Take profit price
        """
        if direction == TradeDirection.LONG:
            return current_price + (atr * (self.risk_multiplier * 2))
        else:
            return current_price - (atr * (self.risk_multiplier * 2))
    
    def _classify_risk(self, atr: float, current_price: float) -> RiskLevel:
        """
        Classify risk based on ATR percentage.
        
        Args:
            atr: Average True Range
            current_price: Current market price
        
        Returns:
            Risk level classification
        """
        atr_percentage = (atr / current_price) * 100
        
        if atr_percentage < 1:
            return RiskLevel.LOW
        elif atr_percentage < 2:
            return RiskLevel.MEDIUM
        elif atr_percentage < 3:
            return RiskLevel.HIGH
        else:
            return RiskLevel.EXTREME
    
    def estimate_options_strategy(
        self, 
        entry_strategy: Dict[str, Optional[float]]
    ) -> Dict[str, Optional[str]]:
        """
        Estimate call/put strategy based on entry strategy.
        
        Args:
            entry_strategy: Dictionary with entry strategy details
        
        Returns:
            Dictionary with options strategy recommendations
        """
        if not entry_strategy or entry_strategy['direction'] is None:
            return {
                "recommended_option": None,
                "rationale": "Insufficient data for options recommendation"
            }
        
        direction = entry_strategy['direction']
        entry_price = entry_strategy['entry_price']
        take_profit = entry_strategy['take_profit']
        
        if direction == TradeDirection.LONG.value:
            return {
                "recommended_option": "Call",
                "rationale": f"Long position with target at {take_profit}, "
                             f"suggesting call option near current price {entry_price}"
            }
        else:
            return {
                "recommended_option": "Put",
                "rationale": f"Short position with target at {take_profit}, "
                             f"suggesting put option near current price {entry_price}"
            } 