"""
Multi-Timeframe Technical Analysis Engine

Analyzes symbols across multiple timeframes (1m, 5m, 15m, 1h, 4h, 1d, 1w, 1M)
and provides comprehensive technical analysis with cross-timeframe correlations.
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import logging
from typing import Dict, List, Optional, Any, Tuple


logger = logging.getLogger(__name__)


class Timeframe(Enum):
    """Supported timeframes for analysis."""
    ONE_MINUTE = "1m"
    FIVE_MINUTES = "5m"
    FIFTEEN_MINUTES = "15m"
    ONE_HOUR = "1h"
    FOUR_HOURS = "4h"
    ONE_DAY = "1d"
    ONE_WEEK = "1w"
    ONE_MONTH = "1M"
    
    @classmethod
    def get_all_timeframes(cls) -> List['Timeframe']:
        """Get all available timeframes."""
        return list(cls)
    
    @classmethod
    def get_quick_timeframes(cls) -> List['Timeframe']:
        """Get timeframes suitable for quick analysis."""
        return [cls.FIVE_MINUTES, cls.FIFTEEN_MINUTES, cls.ONE_HOUR]
    
    @classmethod
    def get_standard_timeframes(cls) -> List['Timeframe']:
        """Get timeframes suitable for standard analysis."""
        return [cls.FIVE_MINUTES, cls.FIFTEEN_MINUTES, cls.ONE_HOUR, cls.FOUR_HOURS, cls.ONE_DAY]
    
    @classmethod
    def get_deep_timeframes(cls) -> List['Timeframe']:
        """Get all timeframes for deep analysis."""
        return cls.get_all_timeframes()


@dataclass
class TimeframeData:
    """Data for a specific timeframe."""
    timeframe: Timeframe
    opens: List[float]
    highs: List[float]
    lows: List[float]
    closes: List[float]
    volumes: List[float]
    timestamps: List[datetime]
    
    def __post_init__(self):
        """Validate data consistency."""
        lengths = [len(self.opens), len(self.highs), len(self.lows), 
                  len(self.closes), len(self.volumes), len(self.timestamps)]
        
        if len(set(lengths)) != 1:
            raise ValueError(f"Inconsistent data lengths: {lengths}")
        
        if len(self.closes) == 0:
            raise ValueError("No data points provided")
    
    def get_latest_price(self) -> float:
        """Get the most recent closing price."""
        return self.closes[-1] if self.closes else 0.0
    
    def get_price_change(self) -> Tuple[float, float]:
        """Get price change and percentage change."""
        if len(self.closes) < 2:
            return 0.0, 0.0
        
        change = self.closes[-1] - self.closes[0]
        change_percent = (change / self.closes[0]) * 100
        return change, change_percent
    
    def get_high_low_range(self) -> Tuple[float, float]:
        """Get the high and low prices for the period."""
        if not self.highs or not self.lows:
            return 0.0, 0.0
        
        return max(self.highs), min(self.lows)


@dataclass
class TimeframeAnalysis:
    """Analysis results for a specific timeframe."""
    timeframe: Timeframe
    indicators: Dict[str, Any]
    patterns: List[Dict[str, Any]]
    support_resistance: Dict[str, List[float]]
    trend: str
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    timestamp: datetime


@dataclass
class CrossTimeframeCorrelation:
    """Correlation between different timeframes."""
    timeframe1: Timeframe
    timeframe2: Timeframe
    correlation_score: float  # -1.0 to 1.0
    trend_alignment: str  # "aligned", "diverging", "neutral"
    strength: float  # 0.0 to 1.0


class MultiTimeframeAnalyzer:
    """Analyzes symbols across multiple timeframes."""
    
    def __init__(self):
        self.timeframes = Timeframe.get_all_timeframes()
        self.indicators = {}  # Will be populated with indicator calculators
        
    async def analyze_symbol(self, symbol: str, timeframe_data: Dict[Timeframe, TimeframeData], 
                            depth: str = "standard") -> Dict[str, Any]:
        """Run comprehensive multi-timeframe analysis."""
        logger.info(f"Starting multi-timeframe analysis for {symbol} (depth: {depth})")
        
        # Select timeframes based on depth
        selected_timeframes = self._select_timeframes_for_depth(depth)
        
        results = {
            "symbol": symbol,
            "analysis_depth": depth,
            "timeframes_analyzed": [tf.value for tf in selected_timeframes],
            "analysis_timestamp": datetime.now().isoformat(),
            "timeframe_analyses": {},
            "cross_timeframe_correlations": [],
            "overall_trend": "neutral",
            "overall_strength": 0.0,
            "overall_confidence": 0.0,
            "key_insights": []
        }
        
        # Analyze each selected timeframe
        for timeframe in selected_timeframes:
            if timeframe in timeframe_data:
                try:
                    analysis = await self._analyze_single_timeframe(timeframe, timeframe_data[timeframe])
                    results["timeframe_analyses"][timeframe.value] = analysis
                except Exception as e:
                    logger.error(f"Error analyzing {timeframe.value} for {symbol}: {e}")
                    results["timeframe_analyses"][timeframe.value] = {
                        "error": str(e),
                        "status": "failed"
                    }
        
        # Calculate cross-timeframe correlations
        if len(results["timeframe_analyses"]) > 1:
            correlations = await self._calculate_cross_timeframe_correlations(
                results["timeframe_analyses"]
            )
            results["cross_timeframe_correlations"] = correlations
        
        # Calculate overall metrics
        overall_metrics = await self._calculate_overall_metrics(results["timeframe_analyses"])
        results.update(overall_metrics)
        
        # Generate key insights
        results["key_insights"] = await self._generate_key_insights(results)
        
        logger.info(f"Completed multi-timeframe analysis for {symbol}")
        return results
    
    def _select_timeframes_for_depth(self, depth: str) -> List[Timeframe]:
        """Select appropriate timeframes based on analysis depth."""
        if depth == "quick":
            return Timeframe.get_quick_timeframes()
        elif depth == "standard":
            return Timeframe.get_standard_timeframes()
        else:  # deep_dive
            return Timeframe.get_deep_timeframes()
    
    async def _analyze_single_timeframe(self, timeframe: Timeframe, 
                                      data: TimeframeData) -> TimeframeAnalysis:
        """Analyze a single timeframe."""
        logger.debug(f"Analyzing {timeframe.value} timeframe")
        
        # Calculate technical indicators
        indicators = await self._calculate_indicators(data)
        
        # Detect patterns
        patterns = await self._detect_patterns(data)
        
        # Calculate support and resistance
        support_resistance = await self._calculate_support_resistance(data)
        
        # Determine trend and strength
        trend, strength = await self._determine_trend_and_strength(data)
        
        # Calculate confidence based on data quality and indicator agreement
        confidence = await self._calculate_confidence(data, indicators, patterns)
        
        return TimeframeAnalysis(
            timeframe=timeframe,
            indicators=indicators,
            patterns=patterns,
            support_resistance=support_resistance,
            trend=trend,
            strength=strength,
            confidence=confidence,
            timestamp=datetime.now()
        )
    
    async def _calculate_indicators(self, data: TimeframeData) -> Dict[str, Any]:
        """Calculate technical indicators for the timeframe."""
        indicators = {}
        
        try:
            # Basic price indicators
            if len(data.closes) >= 2:
                indicators["price_change"] = data.get_price_change()
                indicators["high_low_range"] = data.get_high_low_range()
            
            # Moving averages
            if len(data.closes) >= 20:
                indicators["sma_20"] = self._calculate_sma(data.closes, 20)
                indicators["sma_50"] = self._calculate_sma(data.closes, 50)
            
            if len(data.closes) >= 12:
                indicators["ema_12"] = self._calculate_ema(data.closes, 12)
                indicators["ema_26"] = self._calculate_ema(data.closes, 26)
            
            # RSI
            if len(data.closes) >= 14:
                indicators["rsi"] = self._calculate_rsi(data.closes, 14)
            
            # MACD
            if len(data.closes) >= 26:
                indicators["macd"] = self._calculate_macd(data.closes)
            
            # Bollinger Bands
            if len(data.closes) >= 20:
                indicators["bollinger_bands"] = self._calculate_bollinger_bands(data.closes, 20)
            
            # Volume indicators
            if len(data.volumes) >= 20:
                indicators["volume_sma"] = self._calculate_sma(data.volumes, 20)
                indicators["volume_ratio"] = self._calculate_volume_ratio(data.volumes)
            
            # Volatility
            if len(data.closes) >= 20:
                indicators["volatility"] = self._calculate_volatility(data.closes, 20)
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            indicators["error"] = str(e)
        
        return indicators
    
    async def _detect_patterns(self, data: TimeframeData) -> List[Dict[str, Any]]:
        """Detect chart patterns in the timeframe."""
        patterns = []
        
        try:
            # Basic pattern detection
            if len(data.closes) >= 5:
                # Trend patterns
                if self._is_uptrend(data.closes):
                    patterns.append({
                        "type": "uptrend",
                        "confidence": 0.7,
                        "description": "Consistent upward price movement"
                    })
                elif self._is_downtrend(data.closes):
                    patterns.append({
                        "type": "downtrend",
                        "confidence": 0.7,
                        "description": "Consistent downward price movement"
                    })
                
                # Reversal patterns
                if self._is_double_bottom(data.lows):
                    patterns.append({
                        "type": "double_bottom",
                        "confidence": 0.6,
                        "description": "Potential reversal pattern"
                    })
                elif self._is_double_top(data.highs):
                    patterns.append({
                        "type": "double_top",
                        "confidence": 0.6,
                        "description": "Potential reversal pattern"
                    })
        
        except Exception as e:
            logger.error(f"Error detecting patterns: {e}")
        
        return patterns
    
    async def _calculate_support_resistance(self, data: TimeframeData) -> Dict[str, List[float]]:
        """Calculate support and resistance levels."""
        support_resistance = {
            "support_levels": [],
            "resistance_levels": []
        }
        
        try:
            if len(data.closes) >= 20:
                # Simple support/resistance based on recent highs and lows
                recent_highs = data.highs[-20:]
                recent_lows = data.lows[-20:]
                
                # Find clusters of highs and lows
                resistance_clusters = self._find_price_clusters(recent_highs, tolerance=0.02)
                support_clusters = self._find_price_clusters(recent_lows, tolerance=0.02)
                
                support_resistance["resistance_levels"] = [cluster["price"] for cluster in resistance_clusters]
                support_resistance["support_levels"] = [cluster["price"] for cluster in support_clusters]
        
        except Exception as e:
            logger.error(f"Error calculating support/resistance: {e}")
        
        return support_resistance
    
    async def _determine_trend_and_strength(self, data: TimeframeData) -> Tuple[str, float]:
        """Determine trend direction and strength."""
        if len(data.closes) < 5:
            return "neutral", 0.0
        
        try:
            # Calculate trend using linear regression
            trend_slope = self._calculate_trend_slope(data.closes)
            
            # Determine trend direction
            if trend_slope > 0.01:
                trend = "uptrend"
            elif trend_slope < -0.01:
                trend = "downtrend"
            else:
                trend = "sideways"
            
            # Calculate trend strength (0.0 to 1.0)
            strength = min(abs(trend_slope) * 100, 1.0)
            
            return trend, strength
            
        except Exception as e:
            logger.error(f"Error determining trend: {e}")
            return "neutral", 0.0
    
    async def _calculate_confidence(self, data: TimeframeData, indicators: Dict[str, Any], 
                                  patterns: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the analysis."""
        confidence = 0.0
        
        try:
            # Data quality factor
            data_quality = min(len(data.closes) / 50.0, 1.0)  # More data = higher quality
            
            # Indicator completeness factor
            indicator_count = len([k for k in indicators.keys() if k != "error"])
            indicator_completeness = min(indicator_count / 10.0, 1.0)
            
            # Pattern confidence factor
            pattern_confidence = 0.0
            if patterns:
                pattern_confidence = sum(p.get("confidence", 0.0) for p in patterns) / len(patterns)
            
            # Calculate weighted confidence
            confidence = (data_quality * 0.4 + 
                         indicator_completeness * 0.4 + 
                         pattern_confidence * 0.2)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            confidence = 0.0
        
        return max(0.0, min(1.0, confidence))
    
    async def _calculate_cross_timeframe_correlations(self, timeframe_analyses: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Calculate correlations between different timeframes."""
        correlations = []
        
        try:
            timeframes = list(timeframe_analyses.keys())
            
            for i in range(len(timeframes)):
                for j in range(i + 1, len(timeframes)):
                    tf1 = timeframes[i]
                    tf2 = timeframes[j]
                    
                    analysis1 = timeframe_analyses[tf1]
                    analysis2 = timeframe_analyses[tf2]
                    
                    if "error" not in analysis1 and "error" not in analysis2:
                        correlation = self._calculate_timeframe_correlation(analysis1, analysis2)
                        correlations.append(correlation)
        
        except Exception as e:
            logger.error(f"Error calculating cross-timeframe correlations: {e}")
        
        return correlations
    
    def _calculate_timeframe_correlation(self, analysis1: Dict[str, Any], 
                                       analysis2: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate correlation between two timeframe analyses."""
        try:
            # Compare trend alignment
            trend1 = analysis1.get("trend", "neutral")
            trend2 = analysis2.get("trend", "neutral")
            
            if trend1 == trend2:
                trend_alignment = "aligned"
                correlation_score = 0.8
            elif (trend1 == "uptrend" and trend2 == "downtrend") or (trend1 == "downtrend" and trend2 == "uptrend"):
                trend_alignment = "diverging"
                correlation_score = -0.6
            else:
                trend_alignment = "neutral"
                correlation_score = 0.0
            
            # Compare strength
            strength1 = analysis1.get("strength", 0.0)
            strength2 = analysis2.get("strength", 0.0)
            strength_correlation = 1.0 - abs(strength1 - strength2)
            
            # Overall correlation
            overall_correlation = (correlation_score + strength_correlation) / 2
            
            return {
                "timeframe1": analysis1.get("timeframe", "unknown"),
                "timeframe2": analysis2.get("timeframe", "unknown"),
                "correlation_score": overall_correlation,
                "trend_alignment": trend_alignment,
                "strength": strength_correlation
            }
            
        except Exception as e:
            logger.error(f"Error calculating timeframe correlation: {e}")
            return {
                "timeframe1": "unknown",
                "timeframe2": "unknown",
                "correlation_score": 0.0,
                "trend_alignment": "neutral",
                "strength": 0.0
            }
    
    async def _calculate_overall_metrics(self, timeframe_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall analysis metrics."""
        try:
            valid_analyses = [a for a in timeframe_analyses.values() if "error" not in a]
            
            if not valid_analyses:
                return {
                    "overall_trend": "neutral",
                    "overall_strength": 0.0,
                    "overall_confidence": 0.0
                }
            
            # Overall trend (majority vote)
            trends = [a.get("trend", "neutral") for a in valid_analyses]
            trend_counts = {}
            for trend in trends:
                trend_counts[trend] = trend_counts.get(trend, 0) + 1
            
            overall_trend = max(trend_counts.items(), key=lambda x: x[1])[0]
            
            # Overall strength (average)
            strengths = [a.get("strength", 0.0) for a in valid_analyses]
            overall_strength = sum(strengths) / len(strengths)
            
            # Overall confidence (weighted average by timeframe importance)
            confidences = []
            weights = []
            
            for analysis in valid_analyses:
                timeframe = analysis.get("timeframe", "1d")
                confidence = analysis.get("confidence", 0.0)
                
                # Weight higher timeframes more heavily
                if timeframe in ["1d", "1w", "1M"]:
                    weight = 2.0
                elif timeframe in ["1h", "4h"]:
                    weight = 1.5
                else:
                    weight = 1.0
                
                confidences.append(confidence)
                weights.append(weight)
            
            if weights:
                overall_confidence = sum(c * w for c, w in zip(confidences, weights)) / sum(weights)
            else:
                overall_confidence = 0.0
            
            return {
                "overall_trend": overall_trend,
                "overall_strength": overall_strength,
                "overall_confidence": overall_confidence
            }
            
        except Exception as e:
            logger.error(f"Error calculating overall metrics: {e}")
            return {
                "overall_trend": "neutral",
                "overall_strength": 0.0,
                "overall_confidence": 0.0
            }
    
    async def _generate_key_insights(self, results: Dict[str, Any]) -> List[str]:
        """Generate key insights from the multi-timeframe analysis."""
        insights = []
        
        try:
            # Trend consistency
            if results.get("overall_confidence", 0.0) > 0.7:
                trend = results.get("overall_trend", "neutral")
                insights.append(f"Strong {trend} trend across multiple timeframes")
            
            # Divergence detection
            correlations = results.get("cross_timeframe_correlations", [])
            diverging_count = sum(1 for c in correlations if c.get("trend_alignment") == "diverging")
            if diverging_count > 0:
                insights.append(f"Trend divergence detected in {diverging_count} timeframe pairs")
            
            # High confidence timeframes
            timeframe_analyses = results.get("timeframe_analyses", {})
            high_confidence_timeframes = [
                tf for tf, analysis in timeframe_analyses.items()
                if analysis.get("confidence", 0.0) > 0.8
            ]
            if high_confidence_timeframes:
                insights.append(f"High confidence analysis in: {', '.join(high_confidence_timeframes)}")
            
            # Pattern insights
            all_patterns = []
            for analysis in timeframe_analyses.values():
                if "error" not in analysis:
                    patterns = analysis.get("patterns", [])
                    all_patterns.extend(patterns)
            
            if all_patterns:
                pattern_types = [p.get("type", "unknown") for p in all_patterns]
                unique_patterns = list(set(pattern_types))
                insights.append(f"Detected patterns: {', '.join(unique_patterns)}")
        
        except Exception as e:
            logger.error(f"Error generating insights: {e}")
        
        return insights
    
    # Helper methods for technical calculations
    def _calculate_sma(self, prices: List[float], period: int) -> float:
        """Calculate Simple Moving Average."""
        if len(prices) < period:
            return 0.0
        return sum(prices[-period:]) / period
    
    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """Calculate Exponential Moving Average."""
        if len(prices) < period:
            return 0.0
        
        alpha = 2.0 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        
        return ema
    
    def _calculate_rsi(self, prices: List[float], period: int) -> float:
        """Calculate Relative Strength Index."""
        if len(prices) < period + 1:
            return 50.0
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0.0)
            else:
                gains.append(0.0)
                losses.append(abs(change))
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_macd(self, prices: List[float]) -> Dict[str, float]:
        """Calculate MACD."""
        if len(prices) < 26:
            return {"macd": 0.0, "signal": 0.0, "histogram": 0.0}
        
        ema12 = self._calculate_ema(prices, 12)
        ema26 = self._calculate_ema(prices, 26)
        macd_line = ema12 - ema26
        
        # For simplicity, use a 9-period EMA of MACD as signal line
        # In practice, you'd want to maintain a running list of MACD values
        signal_line = macd_line  # Simplified
        
        histogram = macd_line - signal_line
        
        return {
            "macd": macd_line,
            "signal": signal_line,
            "histogram": histogram
        }
    
    def _calculate_bollinger_bands(self, prices: List[float], period: int) -> Dict[str, float]:
        """Calculate Bollinger Bands."""
        if len(prices) < period:
            return {"upper": 0.0, "middle": 0.0, "lower": 0.0}
        
        sma = self._calculate_sma(prices, period)
        
        # Calculate standard deviation
        squared_diff_sum = sum((p - sma) ** 2 for p in prices[-period:])
        std_dev = (squared_diff_sum / period) ** 0.5
        
        upper_band = sma + (2 * std_dev)
        lower_band = sma - (2 * std_dev)
        
        return {
            "upper": upper_band,
            "middle": sma,
            "lower": lower_band
        }
    
    def _calculate_volatility(self, prices: List[float], period: int) -> float:
        """Calculate price volatility."""
        if len(prices) < period:
            return 0.0
        
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                returns.append((prices[i] - prices[i-1]) / prices[i-1])
        
        if not returns:
            return 0.0
        
        return sum(returns) / len(returns)
    
    def _calculate_volume_ratio(self, volumes: List[float]) -> float:
        """Calculate current volume vs average volume ratio."""
        if len(volumes) < 20:
            return 1.0
        
        current_volume = volumes[-1]
        avg_volume = sum(volumes[-20:]) / 20
        
        if avg_volume == 0:
            return 1.0
        
        return current_volume / avg_volume
    
    def _is_uptrend(self, prices: List[float]) -> bool:
        """Check if prices are in an uptrend."""
        if len(prices) < 5:
            return False
        
        # Check if last 5 prices are generally increasing
        increasing_count = 0
        for i in range(1, len(prices)):
            if prices[i] >= prices[i-1]:
                increasing_count += 1
        
        return increasing_count >= 3
    
    def _is_downtrend(self, prices: List[float]) -> bool:
        """Check if prices are in a downtrend."""
        if len(prices) < 5:
            return False
        
        # Check if last 5 prices are generally decreasing
        decreasing_count = 0
        for i in range(1, len(prices)):
            if prices[i] <= prices[i-1]:
                decreasing_count += 1
        
        return decreasing_count >= 3
    
    def _is_double_bottom(self, lows: List[float]) -> bool:
        """Check for double bottom pattern."""
        if len(lows) < 10:
            return False
        
        # Simplified double bottom detection
        recent_lows = lows[-10:]
        min_low = min(recent_lows)
        min_indices = [i for i, low in enumerate(recent_lows) if low == min_low]
        
        return len(min_indices) >= 2
    
    def _is_double_top(self, highs: List[float]) -> bool:
        """Check for double top pattern."""
        if len(highs) < 10:
            return False
        
        # Simplified double top detection
        recent_highs = highs[-10:]
        max_high = max(recent_highs)
        max_indices = [i for i, high in enumerate(recent_highs) if high == max_high]
        
        return len(max_indices) >= 2
    
    def _find_price_clusters(self, prices: List[float], tolerance: float = 0.02) -> List[Dict[str, Any]]:
        """Find clusters of similar prices."""
        if not prices:
            return []
        
        clusters = []
        sorted_prices = sorted(prices)
        
        current_cluster = [sorted_prices[0]]
        
        for price in sorted_prices[1:]:
            if abs(price - current_cluster[-1]) / current_cluster[-1] <= tolerance:
                current_cluster.append(price)
            else:
                # Finalize current cluster
                if len(current_cluster) >= 2:  # Only keep clusters with 2+ points
                    clusters.append({
                        "price": sum(current_cluster) / len(current_cluster),
                        "count": len(current_cluster),
                        "prices": current_cluster
                    })
                
                # Start new cluster
                current_cluster = [price]
        
        # Don't forget the last cluster
        if len(current_cluster) >= 2:
            clusters.append({
                "price": sum(current_cluster) / len(current_cluster),
                "count": len(current_cluster),
                "prices": current_cluster
            })
        
        return clusters
    
    def _calculate_trend_slope(self, prices: List[float]) -> float:
        """Calculate trend slope using linear regression."""
        if len(prices) < 5:
            return 0.0
        
        n = len(prices)
        x_values = list(range(n))
        
        # Calculate means
        x_mean = sum(x_values) / n
        y_mean = sum(prices) / n
        
        # Calculate slope
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, prices))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        return slope


# Convenience function for creating analyzer
def create_multi_timeframe_analyzer() -> MultiTimeframeAnalyzer:
    """Create a new multi-timeframe analyzer instance."""
    return MultiTimeframeAnalyzer() 