"""
Volume Analysis Engine

Analyzes volume patterns and detects market anomalies:
- Volume profile analysis
- Unusual volume detection
- Volume clusters and zones
- Volume trend analysis
- Volume-based indicators
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Any, Tuple

import math
import numpy as np
from shared_indicators import calculate_mfi
from shared_indicators import calculate_obv

        return calculate_mfi(prices, volumes, period)
    
    def _calculate_volume_roc(self, volumes: List[float], period: int) -> float:
        """Calculate Volume Rate of Change."""
        try:
            if len(volumes) < period + 1:
                return 0.0
            
            current_volume = volumes[-1]
            volume_periods_ago = volumes[-period-1]
            
            if volume_periods_ago == 0:
                return 0.0
            
            roc = ((current_volume - volume_periods_ago) / volume_periods_ago) * 100
            return roc
            
        except Exception as e:
            self.logger.error(f"Error calculating Volume ROC: {e}")
            return 0.0
    
    def _calculate_ad_line(self, prices: List[float], volumes: List[float]) -> float:
        """Calculate Accumulation/Distribution Line."""
        try:
            if len(prices) < 2 or len(volumes) < 2:
                return 0.0
            
            ad_line = 0.0
            
            for i in range(len(prices)):
                if i < len(volumes):
                    # Simplified calculation - in practice you'd use actual high/low
                    high = prices[i] + 1
                    low = prices[i] - 1
                    close = prices[i]
                    volume = volumes[i]
                    
                    if high != low:
                        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
                    else:
                        money_flow_multiplier = 0
                    
                    ad_line += money_flow_multiplier * volume
            
            return ad_line
            
        except Exception as e:
            self.logger.error(f"Error calculating AD Line: {e}")
            return 0.0
    
    def _calculate_standard_deviation(self, values: List[float]) -> float:
        """Calculate standard deviation of a list of values."""
        try:
            if len(values) < 2:
                return 0.0
            
            mean = sum(values) / len(values)
            squared_diff_sum = sum((x - mean) ** 2 for x in values)
            variance = squared_diff_sum / len(values)
            
            return math.sqrt(variance)
            
        except Exception as e:
            self.logger.error(f"Error calculating standard deviation: {e}")
            return 0.0
    
    def detect_volume_divergence(self, prices: List[float], volumes: List[float]) -> Optional[VolumeAnomaly]:
        """Detect volume-price divergence."""
        try:
            if len(prices) < 20 or len(volumes) < 20:
                return None
            
            # Calculate price and volume trends
            price_trend = self._calculate_trend_slope(prices[-20:])
            volume_trend = self._calculate_trend_slope(volumes[-20:])
            
            # Detect divergence
            if price_trend > 0.01 and volume_trend < -0.01:  # Price up, volume down
                return VolumeAnomaly(
                    anomaly_type="divergence",
                    severity=0.7,
                    description="Bearish divergence: Price rising, volume declining",
                    timestamp=datetime.now(),
                    price_level=prices[-1],
                    volume_ratio=volumes[-1] / (sum(volumes[-20:]) / 20),
                    confidence=0.7
                )
            elif price_trend < -0.01 and volume_trend > 0.01:  # Price down, volume up
                return VolumeAnomaly(
                    anomaly_type="divergence",
                    severity=0.7,
                    description="Bullish divergence: Price declining, volume increasing",
                    timestamp=datetime.now(),
                    price_level=prices[-1],
                    volume_ratio=volumes[-1] / (sum(volumes[-20:]) / 20),
                    confidence=0.7
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error detecting volume divergence: {e}")
            return None
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate trend slope using linear regression."""
        try:
            if len(values) < 5:
                return 0.0
            
            n = len(values)
            x_values = list(range(n))
            
            # Calculate means
            x_mean = sum(x_values) / n
            y_mean = sum(values) / n
            
            # Calculate slope
            numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
            denominator = sum((x - x_mean) ** 2 for x in x_values)
            
            if denominator == 0:
                return 0.0
            
            slope = numerator / denominator
            return slope
            
        except Exception as e:
            self.logger.error(f"Error calculating trend slope: {e}")
            return 0.0
    
    def get_volume_summary(self, volume_profile: VolumeProfile) -> Dict[str, Any]:
        """Get a summary of volume analysis."""
        try:
            if not volume_profile:
                return {}
            
            summary = {
                "vwap": volume_profile.vwap,
                "volume_trend": volume_profile.volume_trend,
                "high_volume_nodes_count": len(volume_profile.high_volume_nodes),
                "low_volume_nodes_count": len(volume_profile.low_volume_nodes),
                "total_volume_zones": len(volume_profile.volume_zones),
                "unusual_volume_detected": volume_profile.unusual_volume is not None
            }
            
            if volume_profile.unusual_volume:
                summary["unusual_volume_type"] = volume_profile.unusual_volume.anomaly_type
                summary["unusual_volume_severity"] = volume_profile.unusual_volume.severity
            
            # Add key volume indicators
            for key, value in volume_profile.volume_indicators.items():
                summary[f"indicator_{key}"] = value
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting volume summary: {e}")
            return {}


# Convenience function for creating volume analyzer
def create_volume_analyzer() -> VolumeAnalyzer:
    """Create a new volume analyzer instance."""
    return VolumeAnalyzer() 