"""
Shared technical analysis indicators to eliminate duplication.
Consolidates common indicator calculations used across multiple modules.
"""

import logging
from typing import List


logger = logging.getLogger(__name__)

def calculate_mfi(prices: List[float], volumes: List[float], period: int) -> float:
    """Calculate Money Flow Index."""
    try:
        if len(prices) < period + 1:
            return 50.0
        
        positive_money_flow = 0
        negative_money_flow = 0
        
        for i in range(1, len(prices)):
            if i < len(volumes):
                price_change = prices[i] - prices[i-1]
                volume = volumes[i]
                
                if price_change > 0:
                    positive_money_flow += price_change * volume
                elif price_change < 0:
                    negative_money_flow += abs(price_change) * volume
        
        if negative_money_flow == 0:
            return 100.0
        
        money_ratio = positive_money_flow / negative_money_flow
        mfi = 100 - (100 / (1 + money_ratio))
        
        return mfi
        
    except Exception as e:
        logger.error(f"Error calculating MFI: {e}")
        return 50.0

def calculate_obv(prices: List[float], volumes: List[float]) -> float:
    """Calculate On-Balance Volume."""
    try:
        if len(prices) < 2 or len(volumes) < 2:
            return 0.0
        
        obv = 0.0
        
        for i in range(1, len(prices)):
            if i < len(volumes):
                if prices[i] > prices[i-1]:
                    obv += volumes[i]
                elif prices[i] < prices[i-1]:
                    obv -= volumes[i]
        
        return obv
        
    except Exception as e:
        logger.error(f"Error calculating OBV: {e}")
        return 0.0

def calculate_ad_line(prices: List[float], volumes: List[float]) -> float:
    """Calculate Accumulation/Distribution Line."""
    try:
        if len(prices) < 2 or len(volumes) < 2:
            return 0.0
        
        ad_line = 0.0
        
        for i in range(len(prices)):
            if i < len(volumes):
                # Simplified calculation - in practice you'd use actual high/low
                high = prices[i] + 1
                low = prices[i] - 1
                close = prices[i]
                volume = volumes[i]
                
                if high != low:
                    money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
                else:
                    money_flow_multiplier = 0
                
                ad_line += money_flow_multiplier * volume
        
        return ad_line
        
    except Exception as e:
        logger.error(f"Error calculating AD Line: {e}")
        return 0.0 