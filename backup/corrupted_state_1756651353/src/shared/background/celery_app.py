"""
Celery application configuration for background tasks.
Provides distributed task processing for market data collection and analysis.
"""

import logging
import os

from celery import Celery
import importlib

from src.core.config_manager import config
    importlib.import_module("src.shared.background.tasks.market_intelligence")
    importlib.import_module("src.shared.background.tasks.indicators")
except Exception:  # pragma: no cover
    pass

# Celery configuration from centralized config
app.conf.beat_schedule = {
    # Proactive market scan every 15 minutes
    'proactive-market-scan': {
        'task': 'src.shared.background.tasks.market_intelligence.proactive_market_scan',
        'schedule': 900.0,  # 15 minutes
    },
    # Update indicator cache every hour
    'update-indicator-cache': {
        'task': 'src.shared.background.tasks.indicators.update_indicator_cache',
        'schedule': 3600.0,  # 60 minutes
    },
    # Update market cache every 30 minutes
    'update-market-cache': {
        'task': 'src.shared.background.tasks.market_intelligence.update_market_cache',
        'schedule': 1800.0,  # 30 minutes
    },
}

if __name__ == "__main__":
    app.start()