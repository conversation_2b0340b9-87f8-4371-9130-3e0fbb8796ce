"""
Signal type definitions and constants for market analysis.

This module defines the various signal types supported by the market analysis system,
including reversal signals, bounce signals, and momentum continuation signals.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum, auto
from typing import Optional, Dict, Any



class SignalType(Enum):
    """Enumeration of supported signal types."""
    BULLISH_REVERSAL = auto()
    BEARISH_REVERSAL = auto()
    OVERSOLD_BOUNCE = auto()
    OVERBOUGHT_BOUNCE = auto()
    MOMENTUM_CONTINUATION = auto()


class SignalDirection(Enum):
    """Enumeration of signal directions."""
    BULLISH = auto()
    BEARISH = auto()
    NEUTRAL = auto()


@dataclass
class MarketSignal:
    """Data class representing a market signal with confidence scoring."""
    signal_type: SignalType
    direction: SignalDirection
    confidence_score: float  # 0-100%
    symbol: str
    timeframe: str
    timestamp: datetime
    indicators_used: Dict[str, Any]
    price_level: Optional[float] = None
    volume: Optional[float] = None
    description: Optional[str] = None
    
    def __post_init__(self):
        """Validate confidence score range."""
        if not 0 <= self.confidence_score <= 100:
            raise ValueError("Confidence score must be between 0 and 100")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary for serialization."""
        return {
            'signal_type': self.signal_type.name,
            'direction': self.direction.name,
            'confidence_score': self.confidence_score,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'price_level': self.price_level,
            'volume': self.volume,
            'description': self.description,
            'indicators_used': self.indicators_used
        }


# Signal type descriptions for better readability
SIGNAL_DESCRIPTIONS = {
    SignalType.BULLISH_REVERSAL: "Potential bullish reversal pattern detected",
    SignalType.BEARISH_REVERSAL: "Potential bearish reversal pattern detected", 
    SignalType.OVERSOLD_BOUNCE: "Oversold condition suggesting potential bounce",
    SignalType.OVERBOUGHT_BOUNCE: "Overbought condition suggesting potential pullback",
    SignalType.MOMENTUM_CONTINUATION: "Momentum suggesting continuation of current trend"
}


# Default confidence thresholds
DEFAULT_CONFIDENCE_THRESHOLDS = {
    'high_confidence': 70.0,
    'medium_confidence': 50.0,
    'low_confidence': 30.0
}