"""
Environment Variable Validators
Provides robust validation functions for environment variables with type safety and custom validation rules.
"""

from enum import Enum
import logging
import os
import re
from typing import Any, Optional, Union, Callable, List, Dict

from src.core.exceptions import ValidationError, ConfigValidationError

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Validation severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationResult:
    """Result of a validation operation"""
    
    def __init__(self, is_valid: bool = True, message: str = "", severity: ValidationSeverity = ValidationSeverity.ERROR):
        self.is_valid = is_valid
        self.message = message
        self.severity = severity
    
    def __bool__(self):
        return self.is_valid
    
    def __str__(self):
        return f"{self.severity.value}: {self.message}" if self.message else "Validation passed"


def validate_env_var(
    key: str,
    value: Any,
    expected_type: type,
    required: bool = False,
    default: Any = None,
    min_value: Optional[Union[int, float]] = None,
    max_value: Optional[Union[int, float]] = None,
    choices: Optional[List[Any]] = None,
    pattern: Optional[str] = None,
    custom_validator: Optional[Callable[[Any], ValidationResult]] = None
) -> ValidationResult:
    """
    Validate an environment variable with comprehensive type and value checking.
    
    Args:
        key: Environment variable name
        value: Value to validate
        expected_type: Expected Python type (str, int, float, bool)
        required: Whether the variable is required
        default: Default value if not required and value is None
        min_value: Minimum value for numeric types
        max_value: Maximum value for numeric types
        choices: List of allowed values
        pattern: Regex pattern for string validation
        custom_validator: Custom validation function
    
    Returns:
        ValidationResult indicating success or failure with message
    """
    
    # Handle missing values
    if value is None:
        if required:
            return ValidationResult(False, f"Environment variable {key} is required", ValidationSeverity.ERROR)
        return ValidationResult(True, f"Using default value for {key}: {default}", ValidationSeverity.INFO)
    
    # Type validation
    try:
        if expected_type == bool:
            # Handle boolean values
            if isinstance(value, bool):
                converted_value = value
            else:
                str_value = str(value).lower()
                if str_value in ("true", "1", "yes", "on"):
                    converted_value = True
                elif str_value in ("false", "0", "no", "off"):
                    converted_value = False
                else:
                    return ValidationResult(False, f"Invalid boolean value for {key}: {value}", ValidationSeverity.ERROR)
        else:
            # Convert to expected type
            converted_value = expected_type(value)
    except (ValueError, TypeError):
        return ValidationResult(False, f"Invalid {expected_type.__name__} value for {key}: {value}", ValidationSeverity.ERROR)
    
    # Range validation for numeric types
    if expected_type in (int, float) and converted_value is not None:
        if min_value is not None and converted_value < min_value:
            return ValidationResult(False, f"{key} must be at least {min_value}, got {converted_value}", ValidationSeverity.ERROR)
        if max_value is not None and converted_value > max_value:
            return ValidationResult(False, f"{key} must be at most {max_value}, got {converted_value}", ValidationSeverity.ERROR)
    
    # Choices validation
    if choices is not None and converted_value not in choices:
        return ValidationResult(False, f"{key} must be one of {choices}, got {converted_value}", ValidationSeverity.ERROR)
    
    # Pattern validation for strings
    if expected_type == str and pattern is not None:
        if not re.match(pattern, converted_value):
            return ValidationResult(False, f"{key} must match pattern {pattern}, got {converted_value}", ValidationSeverity.ERROR)
    
    # Custom validation
    if custom_validator is not None:
        custom_result = custom_validator(converted_value)
        if not custom_result:
            return custom_result
    
    return ValidationResult(True, f"{key} validation passed: {converted_value}")


def validate_string(key: str, value: Any, required: bool = False, default: str = "", **kwargs) -> str:
    """Validate string environment variable"""
    result = validate_env_var(key, value, str, required, default, **kwargs)
    if not result:
        raise ConfigValidationError(result.message)
    return str(value) if value is not None else default


def validate_int(key: str, value: Any, required: bool = False, default: int = 0, **kwargs) -> int:
    """Validate integer environment variable"""
    result = validate_env_var(key, value, int, required, default, **kwargs)
    if not result:
        raise ConfigValidationError(result.message)
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default


def validate_float(key: str, value: Any, required: bool = False, default: float = 0.0, **kwargs) -> float:
    """Validate float environment variable"""
    result = validate_env_var(key, value, float, required, default, **kwargs)
    if not result:
        raise ConfigValidationError(result.message)
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default


def validate_bool(key: str, value: Any, required: bool = False, default: bool = False, **kwargs) -> bool:
    """Validate boolean environment variable"""
    result = validate_env_var(key, value, bool, required, default, **kwargs)
    if not result:
        raise ConfigValidationError(result.message)
    
    if value is None:
        return default
    
    if isinstance(value, bool):
        return value
    
    str_value = str(value).lower()
    if str_value in ("true", "1", "yes", "on"):
        return True
    elif str_value in ("false", "0", "no", "off"):
        return False
    else:
        return default


def validate_database_url(value: str) -> ValidationResult:
    """Custom validator for database URLs"""
    if not value:
        return ValidationResult(False, "Database URL cannot be empty", ValidationSeverity.ERROR)
    
    # Basic validation for common database URLs
    if not re.match(r'^(postgresql|mysql|sqlite)://', value):
        return ValidationResult(False, "Database URL must start with postgresql://, mysql://, or sqlite://", ValidationSeverity.ERROR)
    
    return ValidationResult(True, "Database URL validation passed")


def validate_redis_url(value: str) -> ValidationResult:
    """Custom validator for Redis URLs"""
    if not value:
        return ValidationResult(False, "Redis URL cannot be empty", ValidationSeverity.ERROR)
    
    if not re.match(r'^redis://', value):
        return ValidationResult(False, "Redis URL must start with redis://", ValidationSeverity.ERROR)
    
    return ValidationResult(True, "Redis URL validation passed")


def validate_api_key(value: str, provider: str) -> ValidationResult:
    """Custom validator for API keys"""
    if not value:
        return ValidationResult(False, f"{provider} API key cannot be empty", ValidationSeverity.ERROR)
    
    # Basic length validation
    if len(value) < 10:
        return ValidationResult(False, f"{provider} API key appears to be invalid (too short)", ValidationSeverity.WARNING)
    
    return ValidationResult(True, f"{provider} API key validation passed")


def validate_positive_number(value: Union[int, float]) -> ValidationResult:
    """Validate that a number is positive"""
    if value <= 0:
        return ValidationResult(False, f"Value must be positive, got {value}", ValidationSeverity.ERROR)
    return ValidationResult(True, "Positive number validation passed")


def validate_percentage(value: float) -> ValidationResult:
    """Validate that a value is a valid percentage (0-100)"""
    if value < 0 or value > 100:
        return ValidationResult(False, f"Percentage must be between 0 and 100, got {value}", ValidationSeverity.ERROR)
    return ValidationResult(True, "Percentage validation passed")


def validate_rsi_thresholds(oversold: float, overbought: float) -> ValidationResult:
    """Validate RSI thresholds (oversold < overbought)"""
    if oversold >= overbought:
        return ValidationResult(False, f"Oversold RSI ({oversold}) must be less than overbought RSI ({overbought})", ValidationSeverity.ERROR)
    return ValidationResult(True, "RSI thresholds validation passed")


def validate_configuration(config_dict: Dict[str, Any]) -> List[ValidationResult]:
    """
    Validate a complete configuration dictionary
    Returns list of validation results for all configuration values
    """
    results = []
    
    # Database validation
    if 'database_url' in config_dict:
        results.append(validate_database_url(config_dict['database_url']))
    
    # Redis validation
    if 'redis_url' in config_dict:
        results.append(validate_redis_url(config_dict['redis_url']))
    
    # API key validation
    if 'alpha_vantage_api_key' in config_dict and config_dict.get('alpha_vantage_api_key'):
        results.append(validate_api_key(config_dict['alpha_vantage_api_key'], "Alpha Vantage"))
    
    if 'yahoo_finance_api_key' in config_dict and config_dict.get('yahoo_finance_api_key'):
        results.append(validate_api_key(config_dict['yahoo_finance_api_key'], "Yahoo Finance"))
    
    # Technical analysis validation
    if 'tech_analysis_sma_window' in config_dict:
        results.append(validate_env_var(
            "TECH_ANALYSIS_SMA_WINDOW", 
            config_dict['tech_analysis_sma_window'], 
            int, 
            min_value=1
        ))
    
    if 'tech_analysis_rsi_period' in config_dict:
        results.append(validate_env_var(
            "TECH_ANALYSIS_RSI_PERIOD", 
            config_dict['tech_analysis_rsi_period'], 
            int, 
            min_value=1
        ))
    
    # Market analysis validation
    if 'market_analysis_oversold_rsi' in config_dict and 'market_analysis_overbought_rsi' in config_dict:
        results.append(validate_rsi_thresholds(
            config_dict['market_analysis_oversold_rsi'],
            config_dict['market_analysis_overbought_rsi']
        ))
    
    if 'market_analysis_min_confidence' in config_dict:
        results.append(validate_percentage(config_dict['market_analysis_min_confidence']))
    
    return results


def get_env_var(key: str, expected_type: type, default: Any = None, **kwargs) -> Any:
    """
    Get and validate an environment variable in one step
    """
    value = os.getenv(key)
    if value is None and default is not None:
        return default
    
    if expected_type == str:
        return validate_string(key, value, default=default, **kwargs)
    elif expected_type == int:
        return validate_int(key, value, default=default, **kwargs)
    elif expected_type == float:
        return validate_float(key, value, default=default, **kwargs)
    elif expected_type == bool:
        return validate_bool(key, value, default=default, **kwargs)
    else:
        raise ConfigValidationError(f"Unsupported type: {expected_type}")