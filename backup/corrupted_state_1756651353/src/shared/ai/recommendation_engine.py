"""
Automated Recommendation Generation Engine

This module generates actionable trading recommendations based on
technical analysis, sentiment analysis, and risk assessment data.
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import json
import logging
from typing import Dict, List, Optional, Any, Tuple


logger = logging.getLogger(__name__)


class RecommendationType(Enum):
    """Types of trading recommendations."""
    
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    BUY_DIP = "buy_dip"
    SELL_RALLY = "sell_rally"
    SCALP = "scalp"
    SWING = "swing"
    POSITION_SIZE = "position_size"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class RiskLevel(Enum):
    """Risk levels for recommendations."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


class TimeHorizon(Enum):
    """Time horizons for recommendations."""
    
    INTRADAY = "intraday"      # Same day
    SHORT_TERM = "short_term"  # 1-5 days
    MEDIUM_TERM = "medium_term"  # 1-4 weeks
    LONG_TERM = "long_term"    # 1-3 months


@dataclass
class TradingRecommendation:
    """Individual trading recommendation."""
    
    recommendation_type: RecommendationType
    symbol: str
    action: str
    confidence: float  # 0.0 to 1.0
    risk_level: RiskLevel
    time_horizon: TimeHorizon
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: Optional[str] = None
    reasoning: List[str] = None
    technical_signals: List[str] = None
    sentiment_signals: List[str] = None
    risk_factors: List[str] = None
    timestamp: datetime = None
    expiration: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.reasoning is None:
            self.reasoning = []
        if self.technical_signals is None:
            self.technical_signals = []
        if self.sentiment_signals is None:
            self.sentiment_signals = []
        if self.risk_factors is None:
            self.risk_factors = []
        if self.expiration is None:
            self.expiration = self.timestamp + timedelta(days=7)  # Default 7-day expiration


@dataclass
class RecommendationSet:
    """Complete set of recommendations for a symbol."""
    
    symbol: str
    primary_recommendation: TradingRecommendation
    secondary_recommendations: List[TradingRecommendation]
    risk_assessment: Dict[str, Any]
    market_context: str
    confidence_score: float
    last_updated: datetime
    data_quality: str  # "excellent", "good", "fair", "poor"
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now()


class RecommendationEngine:
    """Main engine for generating trading recommendations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.risk_calculator = RiskCalculator()
        self.technical_analyzer = TechnicalSignalAnalyzer()
        self.sentiment_analyzer = SentimentSignalAnalyzer()
        self.position_sizer = PositionSizer()
        self.logger.info("RecommendationEngine initialized")
    
    def generate_recommendations(self, symbol: str, 
                               technical_data: Dict[str, Any],
                               sentiment_data: Dict[str, Any],
                               market_data: Dict[str, Any]) -> RecommendationSet:
        """Generate comprehensive trading recommendations."""
        try:
            # Analyze technical signals
            technical_signals = self.technical_analyzer.analyze_signals(technical_data)
            
            # Analyze sentiment signals
            sentiment_signals = self.sentiment_analyzer.analyze_signals(sentiment_data)
            
            # Calculate risk assessment
            risk_assessment = self.risk_calculator.assess_risk(
                symbol, technical_data, sentiment_data, market_data
            )
            
            # Generate primary recommendation
            primary_recommendation = self._generate_primary_recommendation(
                symbol, technical_signals, sentiment_signals, risk_assessment, market_data
            )
            
            # Generate secondary recommendations
            secondary_recommendations = self._generate_secondary_recommendations(
                symbol, technical_signals, sentiment_signals, risk_assessment
            )
            
            # Calculate overall confidence
            confidence_score = self._calculate_overall_confidence(
                technical_signals, sentiment_signals, risk_assessment
            )
            
            # Assess data quality
            data_quality = self._assess_data_quality(technical_data, sentiment_data, market_data)
            
            # Build market context
            market_context = self._build_market_context(market_data, technical_data, sentiment_data)
            
            return RecommendationSet(
                symbol=symbol,
                primary_recommendation=primary_recommendation,
                secondary_recommendations=secondary_recommendations,
                risk_assessment=risk_assessment,
                market_context=market_context,
                confidence_score=confidence_score,
                last_updated=datetime.now(),
                data_quality=data_quality
            )
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations for {symbol}: {e}")
            raise
    
    def _generate_primary_recommendation(self, symbol: str,
                                       technical_signals: Dict[str, Any],
                                       sentiment_signals: Dict[str, Any],
                                       risk_assessment: Dict[str, Any],
                                       market_data: Dict[str, Any]) -> TradingRecommendation:
        """Generate the primary trading recommendation."""
        
        # Determine recommendation type based on signals
        recommendation_type = self._determine_recommendation_type(technical_signals, sentiment_signals)
        
        # Calculate confidence based on signal strength
        confidence = self._calculate_recommendation_confidence(technical_signals, sentiment_signals)
        
        # Determine risk level
        risk_level = self._determine_risk_level(risk_assessment)
        
        # Determine time horizon
        time_horizon = self._determine_time_horizon(technical_signals, sentiment_signals)
        
        # Generate action description
        action = self._generate_action_description(recommendation_type, technical_signals, sentiment_signals)
        
        # Calculate entry, stop loss, and take profit levels
        entry_price, stop_loss, take_profit = self._calculate_price_levels(
            technical_signals, market_data
        )
        
        # Calculate position size
        position_size = self.position_sizer.calculate_position_size(
            risk_assessment, entry_price, stop_loss
        )
        
        # Build reasoning
        reasoning = self._build_reasoning(technical_signals, sentiment_signals, risk_assessment)
        
        return TradingRecommendation(
            recommendation_type=recommendation_type,
            symbol=symbol,
            action=action,
            confidence=confidence,
            risk_level=risk_level,
            time_horizon=time_horizon,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size=position_size,
            reasoning=reasoning,
            technical_signals=list(technical_signals.keys()),
            sentiment_signals=list(sentiment_signals.keys()),
            risk_factors=list(risk_assessment.get("risk_factors", {}).keys())
        )
    
    def _generate_secondary_recommendations(self, symbol: str,
                                          technical_signals: Dict[str, Any],
                                          sentiment_signals: Dict[str, Any],
                                          risk_assessment: Dict[str, Any]) -> List[TradingRecommendation]:
        """Generate secondary trading recommendations."""
        secondary_recommendations = []
        
        # Position sizing recommendation
        if risk_assessment.get("risk_score", 0) > 0.6:
            position_recommendation = TradingRecommendation(
                recommendation_type=RecommendationType.POSITION_SIZE,
                symbol=symbol,
                action="Reduce position size due to elevated risk",
                confidence=0.8,
                risk_level=RiskLevel.MEDIUM,
                time_horizon=TimeHorizon.SHORT_TERM,
                reasoning=["High risk environment", "Volatility concerns", "Uncertain market conditions"],
                technical_signals=["risk_management"],
                sentiment_signals=["risk_management"],
                risk_factors=["elevated_risk"]
            )
            secondary_recommendations.append(position_recommendation)
        
        # Stop loss recommendation
        if technical_signals.get("trend_direction") in ["downtrend", "sideways"]:
            stop_recommendation = TradingRecommendation(
                recommendation_type=RecommendationType.STOP_LOSS,
                symbol=symbol,
                action="Set tight stop loss below key support",
                confidence=0.75,
                risk_level=RiskLevel.LOW,
                time_horizon=TimeHorizon.INTRADAY,
                reasoning=["Weak trend", "Support level nearby", "Risk management priority"],
                technical_signals=["support_levels", "trend_weakness"],
                sentiment_signals=["risk_management"],
                risk_factors=["trend_weakness"]
            )
            secondary_recommendations.append(stop_recommendation)
        
        # Take profit recommendation
        if technical_signals.get("trend_direction") == "uptrend" and sentiment_signals.get("overall_sentiment", 0) > 30:
            profit_recommendation = TradingRecommendation(
                recommendation_type=RecommendationType.TAKE_PROFIT,
                symbol=symbol,
                action="Set take profit at resistance levels",
                confidence=0.7,
                risk_level=RiskLevel.LOW,
                time_horizon=TimeHorizon.SHORT_TERM,
                reasoning=["Strong uptrend", "Positive sentiment", "Resistance approaching"],
                technical_signals=["uptrend", "resistance_levels"],
                sentiment_signals=["positive_sentiment"],
                risk_factors=["profit_taking"]
            )
            secondary_recommendations.append(profit_recommendation)
        
        return secondary_recommendations
    
    def _determine_recommendation_type(self, technical_signals: Dict[str, Any],
                                     sentiment_signals: Dict[str, Any]) -> RecommendationType:
        """Determine the primary recommendation type based on signals."""
        
        # Strong buy signals
        if (technical_signals.get("trend_direction") == "uptrend" and
            technical_signals.get("momentum", 0) > 0.7 and
            sentiment_signals.get("overall_sentiment", 0) > 20):
            return RecommendationType.BUY
        
        # Strong sell signals
        if (technical_signals.get("trend_direction") == "downtrend" and
            technical_signals.get("momentum", 0) < -0.7 and
            sentiment_signals.get("overall_sentiment", 0) < -20):
            return RecommendationType.SELL
        
        # Buy dip opportunity
        if (technical_signals.get("oversold", False) and
            technical_signals.get("support_nearby", False) and
            sentiment_signals.get("overall_sentiment", 0) > -10):
            return RecommendationType.BUY_DIP
        
        # Sell rally opportunity
        if (technical_signals.get("overbought", False) and
            technical_signals.get("resistance_nearby", False) and
            sentiment_signals.get("overall_sentiment", 0) < 10):
            return RecommendationType.SELL_RALLY
        
        # Scalping opportunity
        if (technical_signals.get("volatility", 0) > 0.6 and
            technical_signals.get("trend_direction") == "sideways"):
            return RecommendationType.SCALP
        
        # Swing trading opportunity
        if (technical_signals.get("trend_direction") in ["uptrend", "downtrend"] and
            technical_signals.get("momentum", 0) > 0.5):
            return RecommendationType.SWING
        
        # Default to hold
        return RecommendationType.HOLD
    
    def _calculate_recommendation_confidence(self, technical_signals: Dict[str, Any],
                                          sentiment_signals: Dict[str, Any]) -> float:
        """Calculate confidence level for the recommendation."""
        confidence = 0.5  # Base confidence
        
        # Technical signal confidence
        if technical_signals.get("trend_strength", 0) > 0.7:
            confidence += 0.2
        if technical_signals.get("momentum", 0) > 0.6:
            confidence += 0.15
        if technical_signals.get("volume_confirmation", False):
            confidence += 0.1
        
        # Sentiment signal confidence
        sentiment_strength = abs(sentiment_signals.get("overall_sentiment", 0)) / 100
        confidence += sentiment_strength * 0.15
        
        # Signal consistency
        signal_count = len([s for s in technical_signals.values() if s])
        if signal_count >= 5:
            confidence += 0.1
        elif signal_count >= 3:
            confidence += 0.05
        
        return min(confidence, 1.0)
    
    def _determine_risk_level(self, risk_assessment: Dict[str, Any]) -> RiskLevel:
        """Determine risk level based on risk assessment."""
        risk_score = risk_assessment.get("risk_score", 0.5)
        
        if risk_score < 0.3:
            return RiskLevel.LOW
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM
        elif risk_score < 0.8:
            return RiskLevel.HIGH
        else:
            return RiskLevel.EXTREME
    
    def _determine_time_horizon(self, technical_signals: Dict[str, Any],
                              sentiment_signals: Dict[str, Any]) -> TimeHorizon:
        """Determine appropriate time horizon for the recommendation."""
        
        # High volatility suggests shorter time horizon
        if technical_signals.get("volatility", 0) > 0.7:
            return TimeHorizon.INTRADAY
        
        # Strong trends suggest medium-term
        if technical_signals.get("trend_strength", 0) > 0.8:
            return TimeHorizon.MEDIUM_TERM
        
        # Sideways markets suggest short-term
        if technical_signals.get("trend_direction") == "sideways":
            return TimeHorizon.SHORT_TERM
        
        # Default to short-term
        return TimeHorizon.SHORT_TERM
    
    def _generate_action_description(self, recommendation_type: RecommendationType,
                                  technical_signals: Dict[str, Any],
                                  sentiment_signals: Dict[str, Any]) -> str:
        """Generate human-readable action description."""
        
        action_descriptions = {
            RecommendationType.BUY: "Buy {symbol} - Strong uptrend with positive momentum and bullish sentiment",
            RecommendationType.SELL: "Sell {symbol} - Strong downtrend with negative momentum and bearish sentiment",
            RecommendationType.BUY_DIP: "Buy {symbol} on dip - Oversold conditions near support with improving sentiment",
            RecommendationType.SELL_RALLY: "Sell {symbol} on rally - Overbought conditions near resistance with deteriorating sentiment",
            RecommendationType.SCALP: "Scalp {symbol} - High volatility in sideways market, quick profit taking",
            RecommendationType.SWING: "Swing trade {symbol} - Strong trend with momentum, hold for 1-5 days",
            RecommendationType.HOLD: "Hold {symbol} - Mixed signals, wait for clearer direction",
            RecommendationType.POSITION_SIZE: "Adjust position size for {symbol} - Risk management priority",
            RecommendationType.STOP_LOSS: "Set stop loss for {symbol} - Protect against downside risk",
            RecommendationType.TAKE_PROFIT: "Set take profit for {symbol} - Lock in gains at resistance"
        }
        
        base_description = action_descriptions.get(recommendation_type, "Analyze {symbol}")
        
        # Customize based on signals
        if technical_signals.get("trend_direction") == "uptrend":
            base_description += " - Uptrend confirmed by multiple indicators"
        elif technical_signals.get("trend_direction") == "downtrend":
            base_description += " - Downtrend confirmed by multiple indicators"
        
        if sentiment_signals.get("overall_sentiment", 0) > 30:
            base_description += " - Strong positive sentiment"
        elif sentiment_signals.get("overall_sentiment", 0) < -30:
            base_description += " - Strong negative sentiment"
        
        return base_description
    
    def _calculate_price_levels(self, technical_signals: Dict[str, Any],
                              market_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """Calculate entry, stop loss, and take profit levels."""
        
        current_price = market_data.get("current_price", 0)
        if not current_price:
            return None, None, None
        
        # Entry price (current price for immediate execution)
        entry_price = current_price
        
        # Stop loss calculation
        stop_loss = None
        if technical_signals.get("support_levels"):
            support_levels = technical_signals["support_levels"]
            if support_levels:
                # Use closest support level
                stop_loss = max(support_levels)
                # Add small buffer below support
                stop_loss = stop_loss * 0.98
        
        # Take profit calculation
        take_profit = None
        if technical_signals.get("resistance_levels"):
            resistance_levels = technical_signals["resistance_levels"]
            if resistance_levels:
                # Use closest resistance level
                take_profit = min(resistance_levels)
                # Add small buffer above resistance
                take_profit = take_profit * 1.02
        
        return entry_price, stop_loss, take_profit
    
    def _build_reasoning(self, technical_signals: Dict[str, Any],
                        sentiment_signals: Dict[str, Any],
                        risk_assessment: Dict[str, Any]) -> List[str]:
        """Build reasoning for the recommendation."""
        reasoning = []
        
        # Technical reasoning
        if technical_signals.get("trend_direction"):
            reasoning.append(f"Trend: {technical_signals['trend_direction']} with strength {technical_signals.get('trend_strength', 0):.2f}")
        
        if technical_signals.get("momentum"):
            momentum = technical_signals["momentum"]
            if momentum > 0.5:
                reasoning.append(f"Strong momentum: {momentum:.2f}")
            elif momentum < -0.5:
                reasoning.append(f"Weak momentum: {momentum:.2f}")
        
        if technical_signals.get("volume_confirmation"):
            reasoning.append("Volume confirms price action")
        
        # Sentiment reasoning
        sentiment = sentiment_signals.get("overall_sentiment", 0)
        if abs(sentiment) > 20:
            direction = "bullish" if sentiment > 0 else "bearish"
            reasoning.append(f"Market sentiment: {direction} ({sentiment:.1f})")
        
        # Risk reasoning
        risk_score = risk_assessment.get("risk_score", 0.5)
        if risk_score > 0.7:
            reasoning.append(f"Elevated risk: {risk_score:.2f}")
        elif risk_score < 0.3:
            reasoning.append(f"Low risk: {risk_score:.2f}")
        
        return reasoning
    
    def _calculate_overall_confidence(self, technical_signals: Dict[str, Any],
                                    sentiment_signals: Dict[str, Any],
                                    risk_assessment: Dict[str, Any]) -> float:
        """Calculate overall confidence score for the recommendation set."""
        
        # Technical confidence (40% weight)
        technical_confidence = 0.0
        if technical_signals.get("trend_strength"):
            technical_confidence += technical_signals["trend_strength"] * 0.3
        if technical_signals.get("momentum"):
            technical_confidence += abs(technical_signals["momentum"]) * 0.1
        
        # Sentiment confidence (30% weight)
        sentiment_confidence = 0.0
        sentiment_strength = abs(sentiment_signals.get("overall_sentiment", 0)) / 100
        sentiment_confidence += sentiment_strength * 0.3
        
        # Risk confidence (30% weight)
        risk_confidence = 0.0
        risk_score = risk_assessment.get("risk_score", 0.5)
        # Lower risk score means higher confidence
        risk_confidence += (1.0 - risk_score) * 0.3
        
        total_confidence = technical_confidence + sentiment_confidence + risk_confidence
        
        return min(total_confidence, 1.0)
    
    def _assess_data_quality(self, technical_data: Dict[str, Any],
                           sentiment_data: Dict[str, Any],
                           market_data: Dict[str, Any]) -> str:
        """Assess the quality of input data."""
        
        quality_score = 0.0
        
        # Technical data quality
        if technical_data and len(technical_data) > 5:
            quality_score += 0.4
        if technical_data.get("indicators"):
            quality_score += 0.2
        
        # Sentiment data quality
        if sentiment_data and len(sentiment_data) > 2:
            quality_score += 0.3
        if sentiment_data.get("overall_sentiment") is not None:
            quality_score += 0.1
        
        # Market data quality
        if market_data and market_data.get("current_price"):
            quality_score += 0.2
        if market_data.get("volume"):
            quality_score += 0.1
        
        # Determine quality level
        if quality_score >= 0.8:
            return "excellent"
        elif quality_score >= 0.6:
            return "good"
        elif quality_score >= 0.4:
            return "fair"
        else:
            return "poor"
    
    def _build_market_context(self, market_data: Dict[str, Any],
                            technical_data: Dict[str, Any],
                            sentiment_data: Dict[str, Any]) -> str:
        """Build market context description."""
        
        context_parts = []
        
        # Market conditions
        if market_data.get("current_price"):
            context_parts.append(f"Current price: ${market_data['current_price']:.2f}")
        
        if technical_data.get("trend_direction"):
            context_parts.append(f"Trend: {technical_data['trend_direction']}")
        
        if sentiment_data.get("overall_sentiment") is not None:
            sentiment = sentiment_data["overall_sentiment"]
            if sentiment > 20:
                context_parts.append("Market sentiment: Bullish")
            elif sentiment < -20:
                context_parts.append("Market sentiment: Bearish")
            else:
                context_parts.append("Market sentiment: Neutral")
        
        if technical_data.get("volatility"):
            volatility = technical_data["volatility"]
            if volatility > 0.7:
                context_parts.append("High volatility environment")
            elif volatility < 0.3:
                context_parts.append("Low volatility environment")
        
        return " | ".join(context_parts) if context_parts else "Limited market context available"


# Placeholder classes for dependencies
class RiskCalculator:
    """Placeholder for risk calculation functionality."""
    def assess_risk(self, symbol: str, technical_data: Dict[str, Any],
                    sentiment_data: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        return {"risk_score": 0.5, "risk_factors": {}}


class TechnicalSignalAnalyzer:
    """Placeholder for technical signal analysis."""
    def analyze_signals(self, technical_data: Dict[str, Any]) -> Dict[str, Any]:
        return {"trend_direction": "sideways", "trend_strength": 0.5, "momentum": 0.0}


class SentimentSignalAnalyzer:
    """Placeholder for sentiment signal analysis."""
    def analyze_signals(self, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        return {"overall_sentiment": 0.0}


class PositionSizer:
    """Placeholder for position sizing calculations."""
    def calculate_position_size(self, risk_assessment: Dict[str, Any],
                              entry_price: float, stop_loss: float) -> str:
        return "Standard position size" 