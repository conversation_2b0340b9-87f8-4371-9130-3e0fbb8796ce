"""
Shared base model configuration for Pydantic models.
Consolidates duplicate Config classes with common settings.
"""

from datetime import datetime
from typing import Any, Dict


class BaseModelConfig:
    """Base configuration for Pydantic models with common settings."""
    
    json_encoders = {
        datetime: lambda v: v.isoformat()
    }
    
    # Common validation settings
    validate_assignment = True
    arbitrary_types_allowed = True
    
    # JSON serialization settings
    json_schema_extra = {
        "examples": []
    }

class FrozenModelConfig(BaseModelConfig):
    """Configuration for immutable Pydantic models."""
    
    frozen = True

class StrictModelConfig(BaseModelConfig):
    """Configuration for strict validation Pydantic models."""
    
    extra = "forbid"  # Reject extra fields
    validate_assignment = True
    arbitrary_types_allowed = False

# Convenience functions for common configurations
def get_base_config() -> Dict[str, Any]:
    """Get base model configuration."""
    return {
        "json_encoders": BaseModelConfig.json_encoders,
        "validate_assignment": BaseModelConfig.validate_assignment,
        "arbitrary_types_allowed": BaseModelConfig.arbitrary_types_allowed
    }

def get_frozen_config() -> Dict[str, Any]:
    """Get frozen model configuration."""
    return {
        **get_base_config(),
        "frozen": True
    }

def get_strict_config() -> Dict[str, Any]:
    """Get strict model configuration."""
    return {
        **get_base_config(),
        "extra": "forbid",
        "arbitrary_types_allowed": False
    } 