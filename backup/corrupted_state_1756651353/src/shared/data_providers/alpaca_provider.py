"""
Alpaca Market Data Provider

Provides high-quality real-time and historical market data from Alpaca.
Excellent for US stocks, options, crypto, and forex data.
Updated to use the unified base class for consistency.
"""

import asyncio
from datetime import datetime, timezone, timedelta
import logging
import os
from typing import Dict, List, Any, Optional

import httpx
from unified_base import UnifiedDataProvider, ProviderType, MarketDataResponse, HistoricalData, ProviderError, ProviderTimeoutError

        await asyncio.sleep(1.0)
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about this provider."""
        return {
            "name": "Alpaca",
            "type": "market_data",
            "quality": "high",
            "rate_limit": "1000/month (free tier)",
            "coverage": ["US stocks", "options", "crypto", "forex"],
            "configured": self.is_configured,
            "base_url": self.base_url
        } 