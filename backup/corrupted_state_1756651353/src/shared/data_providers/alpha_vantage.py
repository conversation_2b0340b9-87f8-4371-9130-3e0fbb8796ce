"""
Alpha Vantage provider for market data.
Primary provider with rate limiting and error handling.
Uses centralized configuration for API keys and settings.
Enhanced with comprehensive error handling and retry mechanisms.
Updated to use unified base class for consistency.
"""

import asyncio
from datetime import datetime, timezone
import logging
import time
from typing import Dict, Any, Optional, List

from cache_utils import cache_get
from cache_utils import cache_set
import httpx
from unified_base import UnifiedDataProvider, ProviderType, MarketDataResponse, HistoricalData, ProviderError

from src.core.config_manager import config
from src.shared.error_handling import retry_with_backoff, with_error_logging
        cache_set(self._cache, key, data)

    async def _rate_limit(self):
        """Simple rate limiting - wait between requests"""
        await asyncio.sleep(self.rate_limit_delay)

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for Alpha Vantage"""
        return symbol.upper().strip()
    
    def is_configured(self) -> bool:
        """Check if Alpha Vantage is properly configured with API key"""
        return bool(self.api_key)
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        Get current ticker data from Alpha Vantage.
        
        Args:
            symbol: Stock symbol to fetch data for
            
        Returns:
            Dictionary with ticker data or error information
        """
        if not self.is_configured():
            return {"error": "Alpha Vantage provider not configured: missing API key"}
        
        try:
            cache_key = f"av_{symbol}"
            cached = self._cache_get(cache_key)
            if cached is not None:
                return cached
            
            await self._rate_limit()
            
            normalized_symbol = self._normalize_symbol(symbol)
            
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": normalized_symbol,
                "apikey": self.api_key
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.base_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # Check for API errors
                if "Error Message" in data:
                    error_msg = data.get("Error Message", "Unknown Alpha Vantage error")
                    return {"error": f"Alpha Vantage API error: {error_msg}"}
                
                if "Note" in data:  # Rate limit message
                    return {"error": "Alpha Vantage rate limit exceeded"}
                
                quote_data = data.get("Global Quote", {})
                if not quote_data:
                    return {"error": f"No data available for {symbol} from Alpha Vantage"}
                
                # Extract price data
                price_str = quote_data.get("05. price")
                if not price_str:
                    return {"error": f"No price data for {symbol} from Alpha Vantage"}
                
                try:
                    price = float(price_str)
                except (ValueError, TypeError):
                    return {"error": f"Invalid price data for {symbol} from Alpha Vantage"}
                
                result = {
                    "symbol": symbol,
                    "current_price": price,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "alpha_vantage",
                    "volume": self._parse_float(quote_data.get("06. volume")),
                    "open": self._parse_float(quote_data.get("02. open")),
                    "high": self._parse_float(quote_data.get("03. high")),
                    "low": self._parse_float(quote_data.get("04. low")),
                    "close": self._parse_float(quote_data.get("05. price")),
                    "change_percent": self._parse_float(quote_data.get("10. change percent"))
                }
                
                self._cache_set(cache_key, result)
                return result
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage HTTP error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error fetching from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage provider error: {str(e)}"}
    
    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d") -> Dict[str, Any]:
        """
        Get historical data from Alpha Vantage.
        
        Args:
            symbol: Stock symbol to fetch data for
            period: Time period (e.g., "1mo", "3mo", "1y")
            interval: Data interval (e.g., "1d", "60min", "30min")
            
        Returns:
            Dictionary with historical data or error information
        """
        if not self.is_configured():
            return {"error": "Alpha Vantage provider not configured: missing API key"}
        
        try:
            cache_key = f"av_history_{symbol}_{period}_{interval}"
            cached = self._cache_get(cache_key)
            if cached is not None:
                return cached
            
            await self._rate_limit()
            
            normalized_symbol = self._normalize_symbol(symbol)
            
            # Map period to Alpha Vantage function
            function_map = {
                "1d": "TIME_SERIES_INTRADAY",
                "1mo": "TIME_SERIES_DAILY",
                "3mo": "TIME_SERIES_DAILY",
                "1y": "TIME_SERIES_DAILY",
                "max": "TIME_SERIES_DAILY_ADJUSTED"
            }
            
            function = function_map.get(period, "TIME_SERIES_DAILY")
            
            params = {
                "function": function,
                "symbol": normalized_symbol,
                "apikey": self.api_key
            }
            
            if function == "TIME_SERIES_INTRADAY":
                params["interval"] = interval
                params["outputsize"] = "compact"
            else:
                params["outputsize"] = "full" if period == "max" else "compact"
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.base_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # Check for API errors
                if "Error Message" in data:
                    error_msg = data.get("Error Message", "Unknown Alpha Vantage error")
                    return {"error": f"Alpha Vantage API error: {error_msg}"}
                
                if "Note" in data:  # Rate limit message
                    return {"error": "Alpha Vantage rate limit exceeded"}
                
                # Extract time series data based on function
                time_series_key = None
                if function == "TIME_SERIES_INTRADAY":
                    time_series_key = f"Time Series ({interval})"
                elif function == "TIME_SERIES_DAILY":
                    time_series_key = "Time Series (Daily)"
                elif function == "TIME_SERIES_DAILY_ADJUSTED":
                    time_series_key = "Time Series (Daily)"
                
                time_series = data.get(time_series_key, {})
                if not time_series:
                    return {"error": f"No historical data available for {symbol} from Alpha Vantage"}
                
                # Format historical data
                historical_data = []
                for timestamp_str, values in time_series.items():
                    try:
                        historical_data.append({
                            "timestamp": timestamp_str,
                            "open": self._parse_float(values.get("1. open")),
                            "high": self._parse_float(values.get("2. high")),
                            "low": self._parse_float(values.get("3. low")),
                            "close": self._parse_float(values.get("4. close")),
                            "volume": self._parse_float(values.get("5. volume")),
                            "adjusted_close": self._parse_float(values.get("5. adjusted close"))
                        })
                    except (ValueError, TypeError):
                        continue
                
                result = {
                    "symbol": symbol,
                    "data": historical_data,
                    "period": period,
                    "interval": interval,
                    "count": len(historical_data),
                    "source": "alpha_vantage"
                }
                
                self._cache_set(cache_key, result)
                return result
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching history from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage HTTP error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error fetching history from Alpha Vantage for {symbol}: {e}")
            return {"error": f"Alpha Vantage provider error: {str(e)}"}
    
    async def get_multiple_tickers(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Get current ticker data for multiple symbols in batch with rate limiting."""
        try:
            results = []
            
            for i, symbol in enumerate(symbols):
                try:
                    ticker_data = await self.get_ticker(symbol)
                    if ticker_data and not ticker_data.get('error'):
                        results.append(ticker_data)
                    else:
                        # Add error entry for failed symbols
                        results.append({
                            "symbol": symbol,
                            "error": ticker_data.get('error', 'Unknown error') if ticker_data else 'No data'
                        })
                        
                except Exception as e:
                    logger.debug(f"Failed to fetch {symbol}: {e}")
                    results.append({
                        "symbol": symbol,
                        "error": str(e)
                    })
                
                # Rate limiting between requests (Alpha Vantage has strict limits)
                if i < len(symbols) - 1:  # Don't sleep after the last request
                    await asyncio.sleep(self.rate_limit_delay)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch ticker fetch: {e}")
            return []
    
    async def get_tickers_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Alternative method name for batch ticker fetching."""
        return await self.get_multiple_tickers(symbols)
    
    def _parse_float(self, value: Any) -> Optional[float]:
        """Safely parse float values from API response"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

# Create singleton instance for easy access
alpha_vantage_provider = AlphaVantageProvider()