import asyncio
from datetime import datetime, timedelta, timezone
import logging
import time
from typing import List, Dict, Any, Optional

from cache_utils import cache_get
from cache_utils import cache_set
import pandas as pd
from unified_base import UnifiedDataProvider, ProviderType, MarketDataResponse, HistoricalData, ProviderError
import yfinance as yf

from src.data.providers.base import MarketDataResponse
            
            return MarketDataResponse(
                symbol=request.symbol,
                price=ticker_data['current_price'],
                volume=ticker_data['volume'],
                timestamp=datetime.now(),
                change=ticker_data['change'],
                change_percent=ticker_data['change_percent'],
                open=ticker_data.get('previous_close'),
                high=ticker_data['current_price'],  # Use current price as fallback
                low=ticker_data['current_price'],   # Use current price as fallback
                close=ticker_data['current_price'],
                market_cap=ticker_data.get('market_cap'),
                pe_ratio=ticker_data.get('pe_ratio')
            )
        except Exception as e:
            logger.error(f"Error getting market data for {request.symbol}: {e}")
            raise ProviderError(str(e), "yfinance") 

    async def get_multiple_tickers(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Get current ticker data for multiple symbols in batch."""
        try:
            results = []
            
            for symbol in symbols:
                try:
                    ticker_data = await self.get_ticker(symbol)
                    if ticker_data and not ticker_data.get('error'):
                        results.append(ticker_data)
                    else:
                        # Add error entry for failed symbols
                        results.append({
                            "symbol": symbol,
                            "error": ticker_data.get('error', 'Unknown error') if ticker_data else 'No data'
                        })
                        
                except Exception as e:
                    logger.debug(f"Failed to fetch {symbol}: {e}")
                    results.append({
                        "symbol": symbol,
                        "error": str(e)
                    })
                
                # Small delay between requests to be respectful
                await asyncio.sleep(0.1)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch ticker fetch: {e}")
            return []
    
    async def get_tickers_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Alternative method name for batch ticker fetching."""
        return await self.get_multiple_tickers(symbols) 