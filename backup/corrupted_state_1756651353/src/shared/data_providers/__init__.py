"""
Data providers package for shared market data access.
Provides abstract base classes and concrete implementations for various market data sources.
Updated to use unified base classes for consistency.
"""

from aggregator import DataProviderAggregator
from alpha_vantage import AlphaVantageProvider
from unified_base import UnifiedDataProvider, HistoricalData
from yfinance_provider import YFinanceProvider


__all__ = [
    'UnifiedDataProvider',
    'HistoricalData',
    'AlphaVantageProvider',
    'YFinanceProvider',
    'DataProviderAggregator'
]