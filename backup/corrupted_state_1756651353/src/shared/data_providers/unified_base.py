"""
Unified Data Provider Base Class

This module consolidates all the best features from the existing base classes:
- DataProviderBase (shared)
- BaseMarketDataProvider (API)
- DataProvider (data source manager)

Features:
- Consistent error handling
- Performance metrics tracking
- Rate limiting support
- Metadata and attribution
- Async/await support
- Fallback mechanisms
"""

from abc import ABC, abstractmethod
import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import logging
import time
from typing import Any, Dict, List, Optional, Union

from typing_extensions import Protocol

from src.core.exceptions import DataProviderError, DataProviderTimeoutError, DataProviderRateLimitError, DataQualityError, ProviderError, ProviderTimeoutError, ProviderRateLimitError, ProviderUnavailableError
    DataProviderError, DataProviderTimeoutError, DataProviderRateLimitError,
    DataQualityError, ProviderError, ProviderTimeoutError, ProviderRateLimitError,
    ProviderUnavailableError
)

logger = logging.getLogger(__name__)


class ProviderType(Enum):
    """Types of data providers."""
    STOCK_DATA = "stock_data"
    CRYPTO_DATA = "crypto_data"
    FOREX_DATA = "forex_data"
    ECONOMIC_DATA = "economic_data"
    NEWS_DATA = "news_data"


class ProviderStatus(Enum):
    """Provider availability status."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    DEGRADED = "degraded"
    RATE_LIMITED = "rate_limited"


@dataclass
class ProviderMetrics:
    """Performance metrics for a provider."""
    success_rate: float = 1.0
    avg_response_time: float = 0.0
    last_success: Optional[datetime] = None
    error_count: int = 0
    total_requests: int = 0
    rate_limit_remaining: int = 0
    rate_limit_reset: Optional[datetime] = None


@dataclass
class ProviderMetadata:
    """Metadata for provider responses."""
    provider_name: str
    provider_type: ProviderType
    fetched_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    data_window_start: Optional[datetime] = None
    data_window_end: Optional[datetime] = None
    is_fallback: bool = False
    fallback_reason: Optional[str] = None
    response_time_ms: Optional[float] = None
    data_quality_score: Optional[float] = None
    attribution: Optional[str] = None


@dataclass
class MarketDataResponse:
    """Standardized market data response."""
    symbol: str
    price: float
    timestamp: datetime
    metadata: ProviderMetadata
    volume: Optional[int] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    open: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    previous_close: Optional[float] = None
    market_cap: Optional[float] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class HistoricalData:
    """Standardized historical data format."""
    symbol: str
    dates: List[datetime]
    opens: List[float]
    closes: List[float]
    highs: List[float]
    lows: List[float]
    volumes: List[int]
    metadata: ProviderMetadata
    additional_fields: Optional[Dict[str, List[Any]]] = None


class RateLimiter:
    """Rate limiting for API providers."""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.requests: List[float] = []
        self.last_reset = time.time()
    
    async def acquire(self) -> bool:
        """Acquire permission to make a request."""
        now = time.time()
        
        # Reset counter if a minute has passed
        if now - self.last_reset >= 60:
            self.requests.clear()
            self.last_reset = now
        
        # Remove old requests (older than 1 minute)
        self.requests = [req_time for req_time in self.requests if now - req_time < 60]
        
        if len(self.requests) < self.requests_per_minute:
            self.requests.append(now)
            return True
        
        return False
    
    def get_wait_time(self) -> float:
        """Get time to wait before next request."""
        if not self.requests:
            return 0.0
        
        oldest_request = min(self.requests)
        return max(0.0, 60.0 - (time.time() - oldest_request))
    
    def get_remaining_requests(self) -> int:
        """Get remaining requests in current window."""
        now = time.time()
        self.requests = [req_time for req_time in self.requests if now - req_time < 60]
        return max(0, self.requests_per_minute - len(self.requests))


class UnifiedDataProvider(ABC):
    """
    Unified base class for all market data providers.
    
    This class combines the best features from all existing base classes:
    - Performance metrics tracking
    - Rate limiting
    - Error handling
    - Metadata and attribution
    - Async/await support
    - Fallback mechanisms
    """
    
    def __init__(
        self,
        provider_name: str,
        provider_type: ProviderType,
        config: Optional[Dict[str, Any]] = None
    ):
        self.provider_name = provider_name
        self.provider_type = provider_type
        self.config = config or {}
        
        # Status and availability
        self.status = ProviderStatus.AVAILABLE
        self.is_available = True
        self.last_error: Optional[str] = None
        
        # Performance metrics
        self.metrics = ProviderMetrics()
        
        # Rate limiting
        rate_limit = self.config.get('rate_limit', 60)
        self.rate_limiter = RateLimiter(rate_limit)
        
        # Configuration
        self.timeout = self.config.get('timeout', 30.0)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_backoff = self.config.get('retry_backoff', 2.0)
        
        logger.info(f"Initialized {provider_name} provider ({provider_type.value})")
    
    @abstractmethod
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol."""
        pass
    
    @abstractmethod
    async def get_historical_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> HistoricalData:
        """Get historical data for a symbol."""
        pass
    
    async def get_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive stock data (compatibility method)."""
        try:
            current_price = await self.get_current_price(symbol)
            return {
                'symbol': symbol,
                'current_price': current_price.price,
                'timestamp': current_price.timestamp.isoformat(),
                'volume': current_price.volume,
                'change': current_price.change,
                'change_percent': current_price.change_percent,
                'provider': self.provider_name,
                'metadata': current_price.metadata
            }
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            raise
    
    async def get_market_data(self) -> Dict[str, Any]:
        """Get general market data (compatibility method)."""
        raise NotImplementedError("Market data not implemented for this provider")
    
    async def _measure_response_time(self, func, *args, **kwargs):
        """Measure response time for provider operations."""
        start_time = asyncio.get_event_loop().time()
        success = False
        
        try:
            # Check rate limit
            if not await self.rate_limiter.acquire():
                wait_time = self.rate_limiter.get_wait_time()
                raise ProviderError(
                    f"Rate limit exceeded. Wait {wait_time:.1f} seconds",
                    self.provider_name
                )
            
            result = await func(*args, **kwargs)
            success = True
            
            # Update metrics
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000  # Convert to ms
            self._update_metrics(success, response_time)
            
            # Add response time to metadata if result has metadata
            if hasattr(result, 'metadata') and result.metadata:
                result.metadata.response_time_ms = response_time
            elif isinstance(result, list) and result and hasattr(result[0], 'metadata'):
                for item in result:
                    if hasattr(item, 'metadata') and item.metadata:
                        item.metadata.response_time_ms = response_time
            
            return result
            
        except Exception as e:
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            self._update_metrics(success, response_time)
            self.last_error = str(e)
            self.status = ProviderStatus.DEGRADED
            raise
    
    def _update_metrics(self, success: bool, response_time: float):
        """Update provider performance metrics."""
        self.metrics.total_requests += 1
        
        if success:
            self.metrics.last_success = datetime.now(timezone.utc)
            self.metrics.success_rate = (
                (self.metrics.success_rate * (self.metrics.total_requests - 1) + 1.0) / 
                self.metrics.total_requests
            )
            self.status = ProviderStatus.AVAILABLE
        else:
            self.metrics.error_count += 1
            self.metrics.success_rate = (
                (self.metrics.success_rate * (self.metrics.total_requests - 1)) / 
                self.metrics.total_requests
            )
        
        # Update average response time
        self.metrics.avg_response_time = (
            (self.metrics.avg_response_time * (self.metrics.total_requests - 1) + response_time) / 
            self.metrics.total_requests
        )
        
        # Update rate limit info
        self.metrics.rate_limit_remaining = self.rate_limiter.get_remaining_requests()
    
    def _create_metadata(
        self,
        is_fallback: bool = False,
        fallback_reason: Optional[str] = None,
        response_time_ms: Optional[float] = None,
        data_window_start: Optional[datetime] = None,
        data_window_end: Optional[datetime] = None
    ) -> ProviderMetadata:
        """Create provider metadata for responses."""
        return ProviderMetadata(
            provider_name=self.provider_name,
            provider_type=self.provider_type,
            fetched_at=datetime.now(timezone.utc),
            data_window_start=data_window_start,
            data_window_end=data_window_end,
            is_fallback=is_fallback,
            fallback_reason=fallback_reason,
            response_time_ms=response_time_ms
        )
    
    def _create_response(
        self,
        symbol: str,
        price: float,
        timestamp: datetime,
        **kwargs
    ) -> MarketDataResponse:
        """Create a market data response with metadata."""
        metadata = kwargs.pop('metadata', None)
        if not metadata:
            metadata = self._create_metadata()
        
        return MarketDataResponse(
            symbol=symbol,
            price=price,
            timestamp=timestamp,
            metadata=metadata,
            **kwargs
        )
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get provider health status."""
        return {
            'provider_name': self.provider_name,
            'provider_type': self.provider_type.value,
            'status': self.status.value,
            'is_available': self.is_available,
            'last_error': self.last_error,
            'metrics': {
                'success_rate': self.metrics.success_rate,
                'avg_response_time': self.metrics.avg_response_time,
                'total_requests': self.metrics.total_requests,
                'error_count': self.metrics.error_count,
                'rate_limit_remaining': self.metrics.rate_limit_remaining
            }
        } 