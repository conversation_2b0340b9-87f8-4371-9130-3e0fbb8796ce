from collections import Counter
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, List

from logger import get_logger
import numpy as np

                most_common = Counter(values).most_common(1)
                if most_common:
                    consensus_data[field] = most_common[0][0]
        
        # Calculate overall agreement percentage
        total_comparisons = 0
        agreements = 0
        
        for field, field_providers in provider_values.items():
            if len(field_providers) > 1:
                consensus_value = consensus_data.get(field)
                for provider, value in field_providers.items():
                    total_comparisons += 1
                    if field in numeric_fields:
                        # For numeric fields, consider within 5% as agreement
                        if consensus_value and abs(value - consensus_value) / consensus_value <= 0.05:
                            agreements += 1
                    else:
                        # For string fields, exact match
                        if str(value).upper() == str(consensus_value).upper():
                            agreements += 1
        
        agreement_percentage = (agreements / total_comparisons * 100) if total_comparisons > 0 else 100
        
        # Calculate confidence based on number of providers and agreement
        num_providers = len(provider_data)
        confidence = min(100, (num_providers * 20) + (agreement_percentage * 0.8))
        
        return ConsensusResult(
            consensus_value=consensus_data,
            confidence=confidence,
            provider_values=provider_values,
            outliers=outliers,
            agreement_percentage=agreement_percentage
        )
    
    @staticmethod
    def validate_with_consensus(provider_data: Dict[str, Dict[str, Any]]) -> ValidationResult:
        """
        Validate data using consensus from multiple providers
        
        Args:
            provider_data: Dict mapping provider names to their data
            
        Returns:
            Enhanced ValidationResult with consensus data and quality scoring
        """
        if not provider_data:
            return ValidationResult(
                is_valid=False,
                quality_score=0.0,
                errors={"providers": "No provider data available"}
            )
        
        # Create consensus
        consensus_result = FinancialDataValidator.create_consensus(provider_data)
        
        # Validate consensus data
        validation_result = FinancialDataValidator.validate_stock_data(consensus_result.consensus_value)
        
        # Calculate provider-specific quality scores
        provider_scores = {}
        for provider, data in provider_data.items():
            provider_scores[provider] = FinancialDataValidator.calculate_quality_score(data, provider)
        
        # Calculate overall quality score weighted by provider reliability
        if provider_scores:
            weighted_score = sum(provider_scores.values()) / len(provider_scores)
            # Boost score based on consensus confidence
            consensus_boost = consensus_result.confidence * 0.1
            overall_quality = min(100, weighted_score + consensus_boost)
        else:
            overall_quality = 0.0
        
        # Add consensus-specific warnings
        warnings = validation_result.warnings or {}
        if consensus_result.outliers:
            warnings['consensus'] = f"Data conflicts detected: {', '.join(consensus_result.outliers)}"
        
        if consensus_result.agreement_percentage < 80:
            warnings['agreement'] = f"Low provider agreement: {consensus_result.agreement_percentage:.1f}%"
        
        return ValidationResult(
            is_valid=validation_result.is_valid and consensus_result.confidence > 50,
            quality_score=overall_quality,
            errors=validation_result.errors,
            warnings=warnings if warnings else None,
            consensus_data=consensus_result.consensus_value,
            provider_scores=provider_scores
        ) 