"""
Core Initialization Module

Centralizes core system imports and configuration.
"""

from typing import Dict, Any

from .config_manager import config, get_config, ConfigurationError
from .exceptions import TradingBotBaseException
from .monitoring import SystemMonitor
from .pipeline_engine import Pipeline
from .pipeline_engine import <PERSON><PERSON>ine, PipelineStage, PipelineBuilder


# Expose key configuration and core system components
__all__ = [
    'config',
    'get_config',
    'ConfigurationError',
    'PipelineEngine',
    'Pipeline',
    'PipelineStage',
    'PipelineBuilder',
    'SystemMonitor',
    'TradingBotBaseException'
]

# Optional: Add a global configuration summary function
def get_config_summary() -> Dict[str, Any]:
    """
    Retrieve a summary of the current system configuration.
    
    Returns:
        Dictionary containing key configuration details
    """
    return config.as_dict()