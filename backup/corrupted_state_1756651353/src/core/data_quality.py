"""
Data Quality Scoring System

Provides comprehensive data quality assessment and scoring for market data.
Integrates with gap detection, provider attribution, and monitoring systems.
"""

from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from enum import Enum
import logging
from typing import Dict, List, Optional, Any, Union

from src.api.data.metrics import cache_metrics
from src.shared.data_validation import assess_data_quality, detect_data_gaps

logger = logging.getLogger(__name__)


class QualityLevel(Enum):
    """Data quality levels based on scoring."""
    EXCELLENT = "excellent"      # 90-100
    GOOD = "good"               # 80-89
    FAIR = "fair"               # 70-79
    POOR = "poor"               # 50-69
    VERY_POOR = "very_poor"     # 0-49


@dataclass
class QualityScore:
    """Comprehensive data quality score with breakdown."""
    symbol: str
    overall_score: float
    completeness_score: float
    freshness_score: float
    consistency_score: float
    provider_reliability_score: float
    gap_penalty: float
    quality_level: QualityLevel
    timestamp: datetime
    data_window_start: Optional[datetime] = None
    data_window_end: Optional[datetime] = None
    interval_type: str = "1d"
    
    def __post_init__(self):
        """Ensure timestamp is timezone-aware."""
        if self.timestamp and self.timestamp.tzinfo is None:
            self.timestamp = self.timestamp.replace(tzinfo=timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            'symbol': self.symbol,
            'overall_score': round(self.overall_score, 2),
            'completeness_score': round(self.completeness_score, 2),
            'freshness_score': round(self.freshness_score, 2),
            'consistency_score': round(self.consistency_score, 2),
            'provider_reliability_score': round(self.provider_reliability_score, 2),
            'gap_penalty': round(self.gap_penalty, 2),
            'quality_level': self.quality_level.value,
            'timestamp': self.timestamp.isoformat(),
            'data_window_start': self.data_window_start.isoformat() if self.data_window_start else None,
            'data_window_end': self.data_window_end.isoformat() if self.data_window_end else None,
            'interval_type': self.interval_type
        }


class DataQualityScorer:
    """
    Comprehensive data quality scoring system.
    """
    
    def __init__(self):
        self.quality_thresholds = {
            QualityLevel.EXCELLENT: 90,
            QualityLevel.GOOD: 80,
            QualityLevel.FAIR: 70,
            QualityLevel.POOR: 50,
            QualityLevel.VERY_POOR: 0
        }
        
        # Weights for different quality factors
        self.quality_weights = {
            'completeness': 0.35,      # 35% - data completeness
            'freshness': 0.25,          # 25% - how recent the data is
            'consistency': 0.20,        # 20% - data consistency/validity
            'provider_reliability': 0.20  # 20% - provider performance
        }
    
    async def score_data_quality(
        self, 
        symbol: str, 
        data: Any, 
        interval_type: str = "1d",
        provider_metadata: Optional[Dict] = None
    ) -> QualityScore:
        """
        Generate comprehensive quality score for market data.
        
        Args:
            symbol: Stock symbol
            data: Market data (DataFrame, list, or dict)
            interval_type: Data interval type
            provider_metadata: Provider information and performance metrics
            
        Returns:
            QualityScore object with detailed breakdown
        """
        try:
            # Assess data completeness and gaps
            completeness_assessment = await self._assess_completeness(data, symbol, interval_type)
            
            # Assess data freshness
            freshness_score = await self._assess_freshness(provider_metadata)
            
            # Assess data consistency
            consistency_score = await self._assess_consistency(data, symbol)
            
            # Assess provider reliability
            provider_reliability_score = await self._assess_provider_reliability(provider_metadata)
            
            # Calculate gap penalty
            gap_penalty = self._calculate_gap_penalty(completeness_assessment.get('gaps', []))
            
            # Calculate weighted overall score
            overall_score = self._calculate_weighted_score(
                completeness_assessment.get('completeness', 0),
                freshness_score,
                consistency_score,
                provider_reliability_score,
                gap_penalty
            )
            
            # Determine quality level
            quality_level = self._determine_quality_level(overall_score)
            
            # Create quality score object
            quality_score = QualityScore(
                symbol=symbol,
                overall_score=overall_score,
                completeness_score=completeness_assessment.get('completeness', 0),
                freshness_score=freshness_score,
                consistency_score=consistency_score,
                provider_reliability_score=provider_reliability_score,
                gap_penalty=gap_penalty,
                quality_level=quality_level,
                timestamp=datetime.now(timezone.utc),
                data_window_start=completeness_assessment.get('data_window_start'),
                data_window_end=completeness_assessment.get('data_window_end'),
                interval_type=interval_type
            )
            
            # Record quality metrics
            await self._record_quality_metrics(quality_score)
            
            logger.info(
                f"Quality score for {symbol}: {overall_score:.1f}/100 "
                f"({quality_level.value}) - Completeness: {completeness_assessment.get('completeness', 0):.1f}%, "
                f"Freshness: {freshness_score:.1f}%, Gaps: {len(completeness_assessment.get('gaps', []))}"
            )
            
            return quality_score
            
        except Exception as e:
            logger.error(f"Error scoring data quality for {symbol}: {e}")
            # Return minimum quality score on error
            return QualityScore(
                symbol=symbol,
                overall_score=0.0,
                completeness_score=0.0,
                freshness_score=0.0,
                consistency_score=0.0,
                provider_reliability_score=0.0,
                gap_penalty=100.0,
                quality_level=QualityLevel.VERY_POOR,
                timestamp=datetime.now(timezone.utc),
                interval_type=interval_type
            )
    
    async def _assess_completeness(
        self, 
        data: Any, 
        symbol: str, 
        interval_type: str
    ) -> Dict[str, Any]:
        """Assess data completeness and detect gaps."""
        try:
            if hasattr(data, 'empty') and data.empty:
                return {
                    'completeness': 0.0,
                    'gaps': [],
                    'data_window_start': None,
                    'data_window_end': None
                }
            
            # Use the gap detector to assess completeness
            if hasattr(data, 'index') and hasattr(data.index, 'min'):
                # DataFrame with datetime index
                assessment = assess_data_quality(data, symbol, interval_type)
                return {
                    'completeness': assessment.get('completeness', 0.0),
                    'gaps': assessment.get('gaps', []),
                    'data_window_start': data.index.min() if not data.empty else None,
                    'data_window_end': data.index.max() if not data.empty else None
                }
            else:
                # List or other data structure
                return {
                    'completeness': 100.0 if data else 0.0,
                    'gaps': [],
                    'data_window_start': None,
                    'data_window_end': None
                }
                
        except Exception as e:
            logger.error(f"Error assessing completeness for {symbol}: {e}")
            return {
                'completeness': 0.0,
                'gaps': [],
                'data_window_start': None,
                'data_window_end': None
            }
    
    async def _assess_freshness(self, provider_metadata: Optional[Dict]) -> float:
        """Assess data freshness based on provider metadata."""
        try:
            if not provider_metadata:
                return 50.0  # Default score for unknown freshness
            
            fetched_at = provider_metadata.get('fetched_at')
            if not fetched_at:
                return 50.0
            
            # Parse timestamp
            if isinstance(fetched_at, str):
                try:
                    fetched_time = datetime.fromisoformat(fetched_at.replace('Z', '+00:00'))
                except ValueError:
                    return 50.0
            else:
                fetched_time = fetched_at
            
            # Ensure timezone-aware
            if fetched_time.tzinfo is None:
                fetched_time = fetched_time.replace(tzinfo=timezone.utc)
            
            # Calculate age
            now = datetime.now(timezone.utc)
            age_minutes = int((now - fetched_time).total_seconds() / 60)
            
            # Score based on age
            if age_minutes < 1:
                return 100.0  # Just updated
            elif age_minutes < 5:
                return 95.0   # Very recent
            elif age_minutes < 15:
                return 90.0   # Recent
            elif age_minutes < 60:
                return 80.0   # Moderate
            elif age_minutes < 240:  # 4 hours
                return 60.0   # Somewhat stale
            else:
                return max(20.0, 100.0 - (age_minutes / 10))  # Very stale
                
        except Exception as e:
            logger.error(f"Error assessing freshness: {e}")
            return 50.0
    
    async def _assess_consistency(self, data: Any, symbol: str) -> float:
        """Assess data consistency and validity."""
        try:
            if not data:
                return 0.0
            
            consistency_score = 100.0
            
            # Check for basic data structure
            if hasattr(data, 'columns') and hasattr(data, 'index'):
                # DataFrame - check for required columns
                required_columns = ['open', 'high', 'low', 'close', 'volume']
                missing_columns = [col for col in required_columns if col not in data.columns]
                if missing_columns:
                    consistency_score -= len(missing_columns) * 10
                
                # Check for reasonable price ranges
                if 'close' in data.columns:
                    close_prices = data['close']
                    if (close_prices <= 0).any():
                        consistency_score -= 20  # Invalid prices
                    
                    # Check for extreme price movements (>100% in one day)
                    if 'open' in data.columns:
                        price_changes = abs((close_prices - data['open']) / data['open'])
                        if (price_changes > 1.0).any():
                            consistency_score -= 15  # Extreme movements
                
                # Check for reasonable volume
                if 'volume' in data.columns:
                    if (data['volume'] < 0).any():
                        consistency_score -= 10  # Negative volume
            
            return max(0.0, consistency_score)
            
        except Exception as e:
            logger.error(f"Error assessing consistency for {symbol}: {e}")
            return 50.0
    
    async def _assess_provider_reliability(self, provider_metadata: Optional[Dict]) -> float:
        """Assess provider reliability based on metadata."""
        try:
            if not provider_metadata:
                return 70.0  # Default score for unknown provider
            
            reliability_score = 100.0
            
            # Check if fallback was used
            if provider_metadata.get('is_fallback', False):
                reliability_score -= 20  # Penalty for using fallback
            
            # Check response time
            response_time = provider_metadata.get('response_time_ms')
            if response_time:
                if response_time > 5000:  # >5 seconds
                    reliability_score -= 30
                elif response_time > 2000:  # >2 seconds
                    reliability_score -= 20
                elif response_time > 1000:  # >1 second
                    reliability_score -= 10
            
            # Check cache status
            if provider_metadata.get('cache_hit', False):
                reliability_score += 5  # Bonus for cache hits
            
            return max(0.0, min(100.0, reliability_score))
            
        except Exception as e:
            logger.error(f"Error assessing provider reliability: {e}")
            return 70.0
    
    def _calculate_gap_penalty(self, gaps: List[Dict]) -> float:
        """Calculate penalty for data gaps."""
        if not gaps:
            return 0.0
        
        total_penalty = 0.0
        
        for gap in gaps:
            severity = gap.get('severity', 'unknown')
            duration_minutes = gap.get('duration_seconds', 0) / 60
            
            # Penalty based on severity
            if severity == 'critical':
                total_penalty += min(30.0, duration_minutes / 2)
            elif severity == 'major':
                total_penalty += min(20.0, duration_minutes / 3)
            elif severity == 'moderate':
                total_penalty += min(15.0, duration_minutes / 4)
            elif severity == 'minor':
                total_penalty += min(10.0, duration_minutes / 6)
        
        return min(100.0, total_penalty)
    
    def _calculate_weighted_score(
        self,
        completeness: float,
        freshness: float,
        consistency: float,
        provider_reliability: float,
        gap_penalty: float
    ) -> float:
        """Calculate weighted overall quality score."""
        weighted_score = (
            completeness * self.quality_weights['completeness'] +
            freshness * self.quality_weights['freshness'] +
            consistency * self.quality_weights['consistency'] +
            provider_reliability * self.quality_weights['provider_reliability']
        )
        
        # Apply gap penalty
        final_score = weighted_score - gap_penalty
        
        return max(0.0, min(100.0, final_score))
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level based on score."""
        if score >= 90:
            return QualityLevel.EXCELLENT
        elif score >= 80:
            return QualityLevel.GOOD
        elif score >= 70:
            return QualityLevel.FAIR
        elif score >= 50:
            return QualityLevel.POOR
        else:
            return QualityLevel.VERY_POOR
    
    async def _record_quality_metrics(self, quality_score: QualityScore):
        """Record quality metrics for monitoring."""
        try:
            # Record overall quality score
            cache_metrics.record_data_quality(
                quality_score.symbol,
                quality_score.interval_type,
                quality_score.overall_score,
                quality_score.completeness_score
            )
            
        except Exception as e:
            logger.error(f"Error recording quality metrics: {e}")


# Global quality scorer instance
quality_scorer = DataQualityScorer()


async def score_data_quality(
    symbol: str, 
    data: Any, 
    interval_type: str = "1d",
    provider_metadata: Optional[Dict] = None
) -> QualityScore:
    """Convenience function to score data quality."""
    return await quality_scorer.score_data_quality(symbol, data, interval_type, provider_metadata)


def get_quality_level_description(level: QualityLevel) -> str:
    """Get human-readable description of quality level."""
    descriptions = {
        QualityLevel.EXCELLENT: "Data quality is excellent - high confidence in analysis",
        QualityLevel.GOOD: "Data quality is good - reliable for most analysis",
        QualityLevel.FAIR: "Data quality is fair - use with caution",
        QualityLevel.POOR: "Data quality is poor - significant limitations",
        QualityLevel.VERY_POOR: "Data quality is very poor - not recommended for analysis"
    }
    return descriptions.get(level, "Unknown quality level") 