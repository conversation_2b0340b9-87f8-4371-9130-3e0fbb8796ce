"""
Outlier Detection System for Market Data

Detects price and volume anomalies using statistical methods and provides
confidence adjustments for analysis based on outlier severity.
"""

from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
import logging
from typing import Dict, List, Optional, Any, Union, Tuple

import numpy as np
import pandas as pd

from src.api.data.metrics import cache_metrics

logger = logging.getLogger(__name__)


class OutlierType(Enum):
    """Types of outliers that can be detected."""
    PRICE_SPIKE = "price_spike"           # Sudden price increase
    PRICE_DROP = "price_drop"             # Sudden price decrease
    VOLUME_SPIKE = "volume_spike"         # Unusual volume increase
    VOLUME_DROP = "volume_drop"           # Unusual volume decrease
    PRICE_GAP = "price_gap"               # Large gap between open/close
    VOLATILITY_SPIKE = "volatility_spike" # Unusual price volatility
    MULTIPLE_OUTLIERS = "multiple_outliers" # Multiple types detected


class OutlierSeverity(Enum):
    """Severity levels for detected outliers."""
    MINOR = "minor"       # Slight anomaly, minimal impact
    MODERATE = "moderate" # Noticeable anomaly, some impact
    MAJOR = "major"       # Significant anomaly, high impact
    CRITICAL = "critical" # Extreme anomaly, critical impact


@dataclass
class Outlier:
    """Represents a detected outlier with metadata."""
    symbol: str
    outlier_type: OutlierType
    severity: OutlierSeverity
    timestamp: datetime
    value: float
    expected_range: Tuple[float, float]
    deviation_score: float
    confidence_impact: float
    description: str
    recommendations: List[str]
    
    def __post_init__(self):
        """Ensure timestamp is timezone-aware."""
        if self.timestamp and self.timestamp.tzinfo is None:
            self.timestamp = self.timestamp.replace(tzinfo=timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            'symbol': self.symbol,
            'outlier_type': self.outlier_type.value,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'value': round(self.value, 4),
            'expected_range': [round(self.expected_range[0], 4), round(self.expected_range[1], 4)],
            'deviation_score': round(self.deviation_score, 2),
            'confidence_impact': round(self.confidence_impact, 2),
            'description': self.description,
            'recommendations': self.recommendations
        }


class OutlierDetector:
    """
    Detects outliers in market data using statistical methods.
    """
    
    def __init__(self):
        # Statistical thresholds for outlier detection
        self.price_thresholds = {
            'z_score_threshold': 3.0,      # Standard deviations for price outliers
            'percent_change_threshold': 0.15,  # 15% change threshold
            'gap_threshold': 0.10,         # 10% gap threshold
            'volatility_multiplier': 2.5   # Volatility spike multiplier
        }
        
        self.volume_thresholds = {
            'z_score_threshold': 2.5,      # Standard deviations for volume outliers
            'volume_ratio_threshold': 3.0, # Volume spike ratio threshold
            'min_volume_threshold': 1000   # Minimum volume to consider
        }
        
        # Confidence impact factors (percentage reduction)
        self.confidence_impacts = {
            OutlierSeverity.MINOR: 2.0,      # 2% confidence reduction
            OutlierSeverity.MODERATE: 8.0,   # 8% confidence reduction
            OutlierSeverity.MAJOR: 20.0,     # 20% confidence reduction
            OutlierSeverity.CRITICAL: 40.0   # 40% confidence reduction
        }
    
    def detect_outliers(
        self, 
        data: pd.DataFrame, 
        symbol: str,
        lookback_periods: int = 20
    ) -> List[Outlier]:
        """
        Detect outliers in market data.
        
        Args:
            data: DataFrame with OHLCV data
            symbol: Stock symbol
            lookback_periods: Number of periods to use for baseline calculation
            
        Returns:
            List of detected outliers
        """
        try:
            outliers = []
            
            if data.empty or len(data) < lookback_periods:
                logger.warning(f"Insufficient data for outlier detection: {len(data)} rows")
                return outliers
            
            # Ensure required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in data.columns for col in required_columns):
                logger.warning(f"Missing required columns for outlier detection: {data.columns}")
                return outliers
            
            # Sort by timestamp/index
            data = data.sort_index()
            
            # Detect price outliers
            price_outliers = self._detect_price_outliers(data, symbol, lookback_periods)
            outliers.extend(price_outliers)
            
            # Detect volume outliers
            volume_outliers = self._detect_volume_outliers(data, symbol, lookback_periods)
            outliers.extend(volume_outliers)
            
            # Detect price gaps
            gap_outliers = self._detect_price_gaps(data, symbol, lookback_periods)
            outliers.extend(gap_outliers)
            
            # Detect volatility spikes
            volatility_outliers = self._detect_volatility_spikes(data, symbol, lookback_periods)
            outliers.extend(volatility_outliers)
            
            # Record outlier metrics
            if outliers:
                self._record_outlier_metrics(outliers, symbol)
            
            logger.info(f"Detected {len(outliers)} outliers for {symbol}")
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting outliers for {symbol}: {e}")
            return []
    
    def _detect_price_outliers(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        lookback_periods: int
    ) -> List[Outlier]:
        """Detect price outliers using statistical methods."""
        outliers = []
        
        try:
            # Calculate price changes
            data = data.copy()
            data['price_change'] = data['close'].pct_change()
            data['price_change_abs'] = data['price_change'].abs()
            
            # Calculate rolling statistics
            rolling_mean = data['price_change_abs'].rolling(window=lookback_periods).mean()
            rolling_std = data['price_change_abs'].rolling(window=lookback_periods).std()
            
            # Detect outliers using z-score method
            for i in range(lookback_periods, len(data)):
                if pd.isna(rolling_std.iloc[i]) or rolling_std.iloc[i] == 0:
                    continue
                
                price_change = data['price_change'].iloc[i]
                price_change_abs = data['price_change_abs'].iloc[i]
                expected_mean = rolling_mean.iloc[i]
                expected_std = rolling_std.iloc[i]
                
                # Calculate z-score
                z_score = abs((price_change_abs - expected_mean) / expected_std)
                
                if z_score > self.price_thresholds['z_score_threshold']:
                    # Determine outlier type and severity
                    outlier_type = OutlierType.PRICE_SPIKE if price_change > 0 else OutlierType.PRICE_DROP
                    severity = self._determine_price_severity(z_score, price_change_abs)
                    
                    # Calculate confidence impact
                    confidence_impact = self.confidence_impacts[severity]
                    
                    # Generate description and recommendations
                    description = self._generate_price_outlier_description(
                        outlier_type, severity, price_change, z_score
                    )
                    recommendations = self._generate_price_recommendations(severity, outlier_type)
                    
                    outlier = Outlier(
                        symbol=symbol,
                        outlier_type=outlier_type,
                        severity=severity,
                        timestamp=data.index[i],
                        value=price_change,
                        expected_range=(expected_mean - expected_std, expected_mean + expected_std),
                        deviation_score=z_score,
                        confidence_impact=confidence_impact,
                        description=description,
                        recommendations=recommendations
                    )
                    
                    outliers.append(outlier)
            
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting price outliers: {e}")
            return []
    
    def _detect_volume_outliers(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        lookback_periods: int
    ) -> List[Outlier]:
        """Detect volume outliers using statistical methods."""
        outliers = []
        
        try:
            # Calculate volume ratios
            data = data.copy()
            rolling_volume_mean = data['volume'].rolling(window=lookback_periods).mean()
            data['volume_ratio'] = data['volume'] / rolling_volume_mean
            
            # Detect volume spikes
            for i in range(lookback_periods, len(data)):
                volume_ratio = data['volume_ratio'].iloc[i]
                volume = data['volume'].iloc[i]
                
                if pd.isna(volume_ratio) or volume < self.volume_thresholds['min_volume_threshold']:
                    continue
                
                if volume_ratio > self.volume_thresholds['volume_ratio_threshold']:
                    # Determine severity based on volume ratio
                    severity = self._determine_volume_severity(volume_ratio)
                    
                    # Calculate confidence impact
                    confidence_impact = self.confidence_impacts[severity]
                    
                    # Generate description and recommendations
                    description = self._generate_volume_outlier_description(severity, volume_ratio, volume)
                    recommendations = self._generate_volume_recommendations(severity)
                    
                    outlier = Outlier(
                        symbol=symbol,
                        outlier_type=OutlierType.VOLUME_SPIKE,
                        severity=severity,
                        timestamp=data.index[i],
                        value=volume_ratio,
                        expected_range=(0.5, self.volume_thresholds['volume_ratio_threshold']),
                        deviation_score=volume_ratio,
                        confidence_impact=confidence_impact,
                        description=description,
                        recommendations=recommendations
                    )
                    
                    outliers.append(outlier)
                
                elif volume_ratio < 0.3:  # Volume drop
                    severity = OutlierSeverity.MODERATE
                    confidence_impact = self.confidence_impacts[severity]
                    
                    description = f"Unusually low volume: {volume_ratio:.2f}x average"
                    recommendations = ["Verify data completeness", "Check for trading halts"]
                    
                    outlier = Outlier(
                        symbol=symbol,
                        outlier_type=OutlierType.VOLUME_DROP,
                        severity=severity,
                        timestamp=data.index[i],
                        value=volume_ratio,
                        expected_range=(0.3, 2.0),
                        deviation_score=1/volume_ratio,
                        confidence_impact=confidence_impact,
                        description=description,
                        recommendations=recommendations
                    )
                    
                    outliers.append(outlier)
            
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting volume outliers: {e}")
            return []
    
    def _detect_price_gaps(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        lookback_periods: int
    ) -> List[Outlier]:
        """Detect price gaps between consecutive periods."""
        outliers = []
        
        try:
            # Calculate price gaps
            data = data.copy()
            data['price_gap'] = (data['open'] - data['close'].shift(1)) / data['close'].shift(1)
            data['gap_abs'] = data['price_gap'].abs()
            
            # Calculate rolling gap statistics
            rolling_gap_mean = data['gap_abs'].rolling(window=lookback_periods).mean()
            rolling_gap_std = data['gap_abs'].rolling(window=lookback_periods).std()
            
            for i in range(lookback_periods, len(data)):
                if pd.isna(rolling_gap_std.iloc[i]) or rolling_gap_std.iloc[i] == 0:
                    continue
                
                gap = data['price_gap'].iloc[i]
                gap_abs = data['gap_abs'].iloc[i]
                expected_gap = rolling_gap_mean.iloc[i]
                expected_std = rolling_gap_std.iloc[i]
                
                # Check if gap exceeds threshold
                if gap_abs > self.price_thresholds['gap_threshold']:
                    # Calculate z-score for gap
                    z_score = abs((gap_abs - expected_gap) / expected_std)
                    
                    # Determine severity
                    severity = self._determine_gap_severity(gap_abs, z_score)
                    confidence_impact = self.confidence_impacts[severity]
                    
                    # Generate description and recommendations
                    description = f"Large price gap: {gap:.2%} (expected: {expected_gap:.2%} ± {expected_std:.2%})"
                    recommendations = self._generate_gap_recommendations(severity, gap)
                    
                    outlier = Outlier(
                        symbol=symbol,
                        outlier_type=OutlierType.PRICE_GAP,
                        severity=severity,
                        timestamp=data.index[i],
                        value=gap,
                        expected_range=(expected_gap - expected_std, expected_gap + expected_std),
                        deviation_score=z_score,
                        confidence_impact=confidence_impact,
                        description=description,
                        recommendations=recommendations
                    )
                    
                    outliers.append(outlier)
            
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting price gaps: {e}")
            return []
    
    def _detect_volatility_spikes(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        lookback_periods: int
    ) -> List[Outlier]:
        """Detect volatility spikes using rolling standard deviation."""
        outliers = []
        
        try:
            # Calculate rolling volatility
            data = data.copy()
            data['returns'] = data['close'].pct_change()
            rolling_vol = data['returns'].rolling(window=lookback_periods).std()
            
            # Calculate volatility ratio
            vol_mean = rolling_vol.rolling(window=lookback_periods).mean()
            data['volatility_ratio'] = rolling_vol / vol_mean
            
            for i in range(lookback_periods * 2, len(data)):
                vol_ratio = data['volatility_ratio'].iloc[i]
                
                if pd.isna(vol_ratio):
                    continue
                
                if vol_ratio > self.price_thresholds['volatility_multiplier']:
                    severity = self._determine_volatility_severity(vol_ratio)
                    confidence_impact = self.confidence_impacts[severity]
                    
                    description = f"Volatility spike: {vol_ratio:.2f}x normal levels"
                    recommendations = self._generate_volatility_recommendations(severity)
                    
                    outlier = Outlier(
                        symbol=symbol,
                        outlier_type=OutlierType.VOLATILITY_SPIKE,
                        severity=severity,
                        timestamp=data.index[i],
                        value=vol_ratio,
                        expected_range=(0.5, self.price_thresholds['volatility_multiplier']),
                        deviation_score=vol_ratio,
                        confidence_impact=confidence_impact,
                        description=description,
                        recommendations=recommendations
                    )
                    
                    outliers.append(outlier)
            
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting volatility spikes: {e}")
            return []
    
    def _determine_price_severity(self, z_score: float, price_change: float) -> OutlierSeverity:
        """Determine severity of price outlier."""
        if z_score > 5.0 or abs(price_change) > 0.25:
            return OutlierSeverity.CRITICAL
        elif z_score > 4.0 or abs(price_change) > 0.20:
            return OutlierSeverity.MAJOR
        elif z_score > 3.5 or abs(price_change) > 0.15:
            return OutlierSeverity.MODERATE
        else:
            return OutlierSeverity.MINOR
    
    def _determine_volume_severity(self, volume_ratio: float) -> OutlierSeverity:
        """Determine severity of volume outlier."""
        if volume_ratio > 10.0:
            return OutlierSeverity.CRITICAL
        elif volume_ratio > 5.0:
            return OutlierSeverity.MAJOR
        elif volume_ratio > 3.0:
            return OutlierSeverity.MODERATE
        else:
            return OutlierSeverity.MINOR
    
    def _determine_gap_severity(self, gap_abs: float, z_score: float) -> OutlierSeverity:
        """Determine severity of price gap."""
        if gap_abs > 0.20 or z_score > 5.0:
            return OutlierSeverity.CRITICAL
        elif gap_abs > 0.15 or z_score > 4.0:
            return OutlierSeverity.MAJOR
        elif gap_abs > 0.10 or z_score > 3.0:
            return OutlierSeverity.MODERATE
        else:
            return OutlierSeverity.MINOR
    
    def _determine_volatility_severity(self, vol_ratio: float) -> OutlierSeverity:
        """Determine severity of volatility spike."""
        if vol_ratio > 5.0:
            return OutlierSeverity.CRITICAL
        elif vol_ratio > 3.5:
            return OutlierSeverity.MAJOR
        elif vol_ratio > 2.5:
            return OutlierSeverity.MODERATE
        else:
            return OutlierSeverity.MINOR
    
    def _generate_price_outlier_description(
        self, 
        outlier_type: OutlierType, 
        severity: OutlierSeverity, 
        price_change: float, 
        z_score: float
    ) -> str:
        """Generate description for price outlier."""
        direction = "increase" if outlier_type == OutlierType.PRICE_SPIKE else "decrease"
        return f"Unusual price {direction}: {price_change:.2%} (z-score: {z_score:.1f})"
    
    def _generate_price_recommendations(self, severity: OutlierSeverity, outlier_type: OutlierType) -> List[str]:
        """Generate recommendations for price outliers."""
        recommendations = []
        
        if severity in [OutlierSeverity.MAJOR, OutlierSeverity.CRITICAL]:
            recommendations.append("Verify data accuracy with multiple sources")
            recommendations.append("Check for news events or market announcements")
        
        if outlier_type == OutlierType.PRICE_SPIKE:
            recommendations.append("Consider profit-taking opportunities")
        else:
            recommendations.append("Look for potential buying opportunities")
        
        recommendations.append("Monitor for follow-through price action")
        
        return recommendations
    
    def _generate_volume_recommendations(self, severity: OutlierSeverity) -> List[str]:
        """Generate recommendations for volume outliers."""
        recommendations = []
        
        if severity in [OutlierSeverity.MAJOR, OutlierSeverity.CRITICAL]:
            recommendations.append("Verify volume data completeness")
            recommendations.append("Check for corporate actions or news")
        
        recommendations.append("Monitor price action for confirmation")
        recommendations.append("Consider position sizing adjustments")
        
        return recommendations
    
    def _generate_gap_recommendations(self, severity: OutlierSeverity, gap: float) -> List[str]:
        """Generate recommendations for price gaps."""
        recommendations = []
        
        if severity in [OutlierSeverity.MAJOR, OutlierSeverity.CRITICAL]:
            recommendations.append("Verify gap is not due to data error")
            recommendations.append("Check for after-hours news or events")
        
        if gap > 0:
            recommendations.append("Monitor for gap fill or continuation")
        else:
            recommendations.append("Watch for potential reversal signals")
        
        return recommendations
    
    def _generate_volatility_recommendations(self, severity: OutlierSeverity) -> List[str]:
        """Generate recommendations for volatility spikes."""
        recommendations = []
        
        if severity in [OutlierSeverity.MAJOR, OutlierSeverity.CRITICAL]:
            recommendations.append("Reduce position sizes due to high volatility")
            recommendations.append("Use wider stop-loss levels")
        
        recommendations.append("Monitor for volatility contraction")
        recommendations.append("Consider volatility-based strategies")
        
        return recommendations
    
    def _record_outlier_metrics(self, outliers: List[Outlier], symbol: str):
        """Record outlier metrics for monitoring."""
        try:
            # This would integrate with your metrics system
            # For now, just log the metrics
            total_confidence_impact = sum(outlier.confidence_impact for outlier in outliers)
            severity_counts = {}
            
            for outlier in outliers:
                severity = outlier.severity.value
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            logger.debug(
                f"Outlier metrics for {symbol}: {len(outliers)} outliers, "
                f"total confidence impact: {total_confidence_impact:.1f}%, "
                f"severity distribution: {severity_counts}"
            )
            
        except Exception as e:
            logger.error(f"Error recording outlier metrics: {e}")
    
    def adjust_confidence_for_outliers(
        self, 
        base_confidence: float, 
        outliers: List[Outlier]
    ) -> float:
        """
        Adjust confidence score based on detected outliers.
        
        Args:
            base_confidence: Original confidence score (0-100)
            outliers: List of detected outliers
            
        Returns:
            Adjusted confidence score
        """
        try:
            if not outliers:
                return base_confidence
            
            # Calculate total confidence impact
            total_impact = sum(outlier.confidence_impact for outlier in outliers)
            
            # Apply confidence reduction
            adjusted_confidence = base_confidence - total_impact
            
            # Ensure confidence stays within bounds
            adjusted_confidence = max(0.0, min(100.0, adjusted_confidence))
            
            logger.info(
                f"Confidence adjusted for outliers: "
                f"{base_confidence:.1f} → {adjusted_confidence:.1f} "
                f"({total_impact:.1f}% reduction due to {len(outliers)} outliers)"
            )
            
            return adjusted_confidence
            
        except Exception as e:
            logger.error(f"Error adjusting confidence for outliers: {e}")
            return base_confidence


# Global outlier detector instance
outlier_detector = OutlierDetector()


def detect_outliers(
    data: pd.DataFrame, 
    symbol: str,
    lookback_periods: int = 20
) -> List[Outlier]:
    """Convenience function to detect outliers."""
    return outlier_detector.detect_outliers(data, symbol, lookback_periods)


def adjust_confidence_for_outliers(
    base_confidence: float, 
    outliers: List[Outlier]
) -> float:
    """Convenience function to adjust confidence for outliers."""
    return outlier_detector.adjust_confidence_for_outliers(base_confidence, outliers) 