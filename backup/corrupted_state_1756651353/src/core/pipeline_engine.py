"""
Pipeline Engine for Trading Bot

Provides a robust, configurable pipeline system for executing
complex trading operations with error handling and performance tracking.
"""

import asyncio
import time
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Union

from .config_manager import config
from .exceptions import PipelineError, StageExecutionError, PipelineTimeoutError
from .logger import get_logger
from .monitoring import PerformanceTracker
import uuid

logger = get_logger(__name__)
performance_tracker = PerformanceTracker()

@dataclass
class PipelineContext:
    """
    Comprehensive context for pipeline execution
    Tracks metadata, state, and performance of pipeline processing
    """
    pipeline_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    stages_executed: List[str] = field(default_factory=list)
    data: Dict[str, Any] = field(default_factory=dict)
    errors: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_stage(self, stage_name: str):
        """Record a stage's execution"""
        self.stages_executed.append(stage_name)
    
    def add_error(self, stage_name: str, error: Exception):
        """Record an error during pipeline execution"""
        self.errors.append({
            'stage': stage_name,
            'error': str(error),
            'timestamp': datetime.now()
        })


class PipelineStage:
    """
    Represents a single stage in a pipeline with execution and error handling
    """
    
    def __init__(
        self, 
        name: str, 
        processor: Callable, 
        is_async: bool = False,
        timeout: Optional[float] = None
    ):
        """
        Initialize a pipeline stage
        
        Args:
            name: Unique stage name
            processor: Function to process this stage
            is_async: Whether the processor is an async function
            timeout: Maximum execution time for this stage
        """
        self.name = name
        self.processor = processor
        self.is_async = is_async
        self.timeout = timeout or config.get('pipeline', 'stage_timeout', 30.0)
    
    async def execute(self, context: PipelineContext, input_data: Any) -> Any:
        """
        Execute the stage with performance tracking and error handling
        
        Args:
            context: Pipeline execution context
            input_data: Data passed from previous stage
        
        Returns:
            Processed data for next stage
        """
        start_time = time.time()
        try:
            # Wrap execution with performance tracking
            if self.is_async:
                result = await asyncio.wait_for(
                    self.processor(input_data, context), 
                    timeout=self.timeout
                )
            else:
                result = self.processor(input_data, context)
            
            # Record stage execution
            context.add_stage(self.name)
            
            # Log performance
            execution_time = time.time() - start_time
            logger.info(
                f"Pipeline Stage '{self.name}' completed", 
                extra={
                    'stage_name': self.name,
                    'execution_time': execution_time,
                    'pipeline_id': context.pipeline_id
                }
            )
            
            return result
        
        except asyncio.TimeoutError:
            error = PipelineTimeoutError(
                pipeline_id=context.pipeline_id, 
                timeout=self.timeout
            )
            context.add_error(self.name, error)
            raise
        
        except Exception as e:
            error = StageExecutionError(
                stage_name=self.name, 
                message=str(e)
            )
            context.add_error(self.name, error)
            raise


class Pipeline:
    """
    Configurable pipeline with dynamic stage execution
    Supports both synchronous and asynchronous processing
    """
    
    def __init__(
        self, 
        name: str, 
        stages: Optional[List[PipelineStage]] = None,
        parallel_execution: Optional[bool] = None
    ):
        """
        Initialize a pipeline
        
        Args:
            name: Unique pipeline name
            stages: List of pipeline stages
            parallel_execution: Whether to execute stages in parallel
        """
        self.name = name
        self.stages = stages or []
        
        # Use configuration or default to sequential
        self.parallel_execution = (
            parallel_execution if parallel_execution is not None 
            else config.get('pipeline', 'parallel_execution', False)
        )
    
    def add_stage(self, stage: PipelineStage):
        """
        Add a stage to the pipeline
        
        Args:
            stage: PipelineStage to add
        """
        self.stages.append(stage)
    
    @performance_tracker.track_performance
    async def execute(self, initial_data: Any = None) -> Dict[str, Any]:
        """
        Execute the entire pipeline
        
        Args:
            initial_data: Initial data to start pipeline processing
        
        Returns:
            Final processed data and execution context
        """
        context = PipelineContext()
        
        try:
            # Sequential execution
            if not self.parallel_execution:
                current_data = initial_data
                for stage in self.stages:
                    current_data = await stage.execute(context, current_data)
                
                return {
                    'data': current_data,
                    'context': context
                }
            
            # Parallel execution
            else:
                # Use asyncio.gather for parallel stage execution
                tasks = [
                    stage.execute(context, initial_data) 
                    for stage in self.stages
                ]
                results = await asyncio.gather(*tasks)
                
                return {
                    'data': results[-1],  # Return last stage result
                    'context': context
                }
        
        except Exception as e:
            logger.error(
                f"Pipeline '{self.name}' execution failed", 
                extra={
                    'pipeline_name': self.name,
                    'error': str(e),
                    'pipeline_id': context.pipeline_id
                }
            )
            raise PipelineError(f"Pipeline execution failed: {e}")


class PipelineBuilder:
    """
    Fluent interface for building pipelines
    """
    
    def __init__(self, name: str):
        """
        Initialize pipeline builder
        
        Args:
            name: Name of the pipeline
        """
        self.pipeline = Pipeline(name)
    
    def add_stage(
        self, 
        name: str, 
        processor: Callable, 
        is_async: bool = False,
        timeout: Optional[float] = None
    ):
        """
        Add a stage to the pipeline
        
        Args:
            name: Stage name
            processor: Processing function
            is_async: Whether processor is async
            timeout: Stage execution timeout
        
        Returns:
            PipelineBuilder for method chaining
        """
        stage = PipelineStage(name, processor, is_async, timeout)
        self.pipeline.add_stage(stage)
        return self
    
    def build(self) -> Pipeline:
        """
        Build and return the configured pipeline
        
        Returns:
            Configured Pipeline instance
        """
        return self.pipeline