import os
from typing import Op<PERSON>, Dict, Any

import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class SecretsManager:
    """
    Secure secrets management utility with encryption and key derivation.
    
    Provides methods for:
    - Encrypting sensitive data
    - Decrypting sensitive data
    - Secure key generation
    - Environment variable management
    """
    
    def __init__(self, master_key: Optional[str] = None):
        """
        Initialize SecretsManager with optional master key.
        
        Args:
            master_key (str, optional): Master encryption key
        """
        self._master_key = master_key or self._generate_master_key()
        self._fernet = self._create_fernet_instance()
    
    @staticmethod
    def _generate_master_key(length: int = 32) -> str:
        """
        Generate a secure random master key.
        
        Args:
            length (int): Length of the key in bytes
        
        Returns:
            str: Base64 encoded master key
        """
        return base64.urlsafe_b64encode(os.urandom(length)).decode()
    
    def _create_fernet_instance(self) -> Ferne<PERSON>:
        """
        Create a Fernet encryption instance from the master key.
        
        Returns:
            Fernet: Encryption/decryption instance
        """
        key = base64.urlsafe_b64decode(self._master_key)
        return Fernet(base64.urlsafe_b64encode(key))
    
    def encrypt(self, data: str) -> str:
        """
        Encrypt a string value.
        
        Args:
            data (str): Data to encrypt
        
        Returns:
            str: Encrypted data
        """
        return self._fernet.encrypt(data.encode()).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        Decrypt an encrypted string.
        
        Args:
            encrypted_data (str): Encrypted data to decrypt
        
        Returns:
            str: Decrypted data
        """
        return self._fernet.decrypt(encrypted_data.encode()).decode()
    
    @classmethod
    def from_environment(cls, env_var: str = 'MASTER_SECRET_KEY'):
        """
        Create SecretsManager from an environment variable.
        
        Args:
            env_var (str): Environment variable name
        
        Returns:
            SecretsManager: Initialized secrets manager
        """
        master_key = os.getenv(env_var)
        return cls(master_key)
    
    def load_secrets(self, secrets_file: str = '.secrets') -> Dict[str, Any]:
        """
        Load encrypted secrets from a file.
        
        Args:
            secrets_file (str): Path to secrets file
        
        Returns:
            dict: Decrypted secrets
        """
        try:
            with open(secrets_file, 'r') as f:
                encrypted_secrets = f.read().strip().split('\n')
                return {
                    line.split('=')[0]: self.decrypt(line.split('=')[1])
                    for line in encrypted_secrets if line
                }
        except FileNotFoundError:
            return {}
    
    def save_secrets(self, secrets: Dict[str, str], secrets_file: str = '.secrets'):
        """
        Save secrets to an encrypted file.
        
        Args:
            secrets (dict): Secrets to save
            secrets_file (str): Path to save secrets
        """
        encrypted_lines = [
            f"{key}={self.encrypt(value)}"
            for key, value in secrets.items()
        ]
        
        with open(secrets_file, 'w') as f:
            f.write('\n'.join(encrypted_lines))
        
        # Set restrictive permissions
        os.chmod(secrets_file, 0o600)
    
    @staticmethod
    def generate_secret_key(length: int = 32) -> str:
        """
        Generate a cryptographically secure secret key.
        
        Args:
            length (int): Length of the key
        
        Returns:
            str: Secure random key
        """
        return base64.urlsafe_b64encode(os.urandom(length)).decode()

# Example usage
def manage_environment_secrets():
    """
    Demonstrate secrets management for environment variables.
    """
    secrets_manager = SecretsManager.from_environment()
    
    # Example: Storing and retrieving database credentials
    database_secrets = {
        'DB_USERNAME': '[CONFIGURE_FROM_ENVIRONMENT]',
        'DB_PASSWORD': '[CONFIGURE_FROM_ENVIRONMENT]',
        'API_KEY': '[CONFIGURE_FROM_ENVIRONMENT]'
    }
    
    # Encrypt and save secrets
    secrets_manager.save_secrets(database_secrets)
    
    # Load and decrypt secrets
    loaded_secrets = secrets_manager.load_secrets()
    print("Loaded Secrets:", loaded_secrets)

# Optional: Generate a new master key for environment
def generate_master_key():
    """
    Generate and print a new master key for environment setup.
    """
    new_master_key = SecretsManager._generate_master_key()
    print(f"New Master Secret Key: {new_master_key}")
    print("Set this in your environment as MASTER_SECRET_KEY") 