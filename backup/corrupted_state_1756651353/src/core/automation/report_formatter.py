"""
Report Formatter

Converts generated reports to different output formats including Discord,
markdown, and email. Provides consistent formatting across all output types.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import json
import json
import logging
from typing import Dict, List, Any, Optional

from report_engine import GeneratedReport, ReportType, AIInsight, MarketData, SectorPerformance, MarketHealth

        error_data = {
            "error": True,
            "message": f"Unable to format {report.report_type.value} report",
            "error_details": str(error),
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(error_data, indent=2)


# Global formatter instance
report_formatter = ReportFormatter()


def format_report_for_discord(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for Discord."""
    return report_formatter.format_for_discord(report)


def format_report_for_markdown(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for markdown."""
    return report_formatter.format_for_markdown(report)


def format_report_for_email(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for email."""
    return report_formatter.format_for_email(report)


def format_report_for_json(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for JSON."""
    return report_formatter.format_for_json(report) 