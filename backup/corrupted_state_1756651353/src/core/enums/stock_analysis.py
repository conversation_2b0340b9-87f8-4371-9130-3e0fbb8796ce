from enum import Enum, auto
from typing import List


class StockRecommendation(Enum):
    """Standardized stock recommendations"""
    STRONG_BUY = "Strong Buy"
    BUY = "Buy"
    HOLD = "Hold"
    SELL = "Sell"
    STRONG_SELL = "Strong Sell"

class AnalysisCategory(Enum):
    """Categories of stock analysis"""
    TECHNICAL = "Technical Analysis"
    FUNDAMENTAL = "Fundamental Analysis"
    SENTIMENT = "Market Sentiment"
    RISK = "Risk Assessment"
    COMPARATIVE = "Comparative Analysis"

class DataConfidenceLevel(Enum):
    """Confidence levels for data and analysis"""
    HIGH = "High Confidence"
    MEDIUM = "Medium Confidence"
    LOW = "Low Confidence"
    SPECULATIVE = "Speculative"

class MarketTrend(Enum):
    """Market trend classifications"""
    BULLISH = "Bullish"
    BEARISH = "Bearish"
    NEUTRAL = "Neutral"
    VOLATILE = "Volatile"

def get_recommendation_emoji(recommendation: StockRecommendation) -> str:
    """
    Get an emoji representation of the stock recommendation
    
    Args:
        recommendation (StockRecommendation): The stock recommendation
    
    Returns:
        str: Corresponding emoji
    """
    emoji_map = {
        StockRecommendation.STRONG_BUY: "🟢🚀",
        StockRecommendation.BUY: "🟢",
        StockRecommendation.HOLD: "🟡",
        StockRecommendation.SELL: "🔴",
        StockRecommendation.STRONG_SELL: "🔴🚨"
    }
    return emoji_map.get(recommendation, "❓")

def get_confidence_color(confidence: DataConfidenceLevel) -> str:
    """
    Get a color representation of confidence level
    
    Args:
        confidence (DataConfidenceLevel): The confidence level
    
    Returns:
        str: Corresponding color hex code
    """
    color_map = {
        DataConfidenceLevel.HIGH: "#2ecc71",      # Green
        DataConfidenceLevel.MEDIUM: "#f39c12",    # Orange
        DataConfidenceLevel.LOW: "#e74c3c",       # Red
        DataConfidenceLevel.SPECULATIVE: "#9b59b6"  # Purple
    }
    return color_map.get(confidence, "#34495e")  # Default to dark gray 