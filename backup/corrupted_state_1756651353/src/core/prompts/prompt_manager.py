"""
Central prompt management system for trading analysis.
"""

from dataclasses import dataclass
import json
from pathlib import Path
import re
import re
from typing import Dict, List, Optional, Any

from models import IntentType, ToolType, IntentClassification, AnalysisPrompt, ComplianceTemplate, PromptResult

        timeframe_patterns = {
            r'\b(daily|1d|today)\b': "intraday",
            r'\b(weekly|1w|week)\b': "short_term",
            r'\b(monthly|1m|month)\b': "medium_term",
            r'\b(yearly|1y|year)\b': "long_term"
        }
        
        for pattern, timeframe in timeframe_patterns.items():
            if re.search(pattern, text_lower):
                return timeframe
        
        # Default based on intent with fallback
        defaults = {
            IntentType.PRICE_CHECK: "intraday",
            IntentType.TECHNICAL_ANALYSIS: "short_term",
            IntentType.FUNDAMENTAL_ANALYSIS: "long_term",
            IntentType.OPTIONS_STRATEGY: "short_term",
            IntentType.MARKET_OVERVIEW: "medium_term",
            IntentType.RISK_MANAGEMENT: "long_term",
            IntentType.EDUCATIONAL: "long_term",
            IntentType.GENERAL_CHAT: "medium_term"
        }
        
        return defaults.get(intent, "medium_term")
    
    def determine_risk_level(self, intent: IntentType, symbols: List[str]) -> str:
        """Determine risk level for the analysis."""
        base_risk = {
            IntentType.PRICE_CHECK: "low",
            IntentType.TECHNICAL_ANALYSIS: "medium",
            IntentType.FUNDAMENTAL_ANALYSIS: "medium",
            IntentType.OPTIONS_STRATEGY: "high",
            IntentType.MARKET_OVERVIEW: "medium",
            IntentType.RISK_MANAGEMENT: "low",
            IntentType.EDUCATIONAL: "low",
            IntentType.GENERAL_CHAT: "low"
        }
        
        risk = base_risk.get(intent, "medium")
        
        # Boost risk for volatile symbols
        volatile_symbols = ["GME", "AMC", "TSLA", "NVDA", "PLTR", "RIOT"]
        if any(symbol in volatile_symbols for symbol in symbols):
            risk_levels = ["low", "medium", "high", "very_high"]
            current_idx = risk_levels.index(risk)
            if current_idx < len(risk_levels) - 1:
                risk = risk_levels[current_idx + 1]
        
        return risk
    
    def build_analysis_prompt(self, user_query: str, classification: IntentClassification) -> AnalysisPrompt:
        """Build complete analysis prompt for AI processing."""
        disclaimer = self.compliance_templates.get_for_intent(
            classification.intent, 
            classification.risk_level
        )
        
        return AnalysisPrompt(
            system_prompt=self.system_prompt,
            user_prompt=user_query,
            intent=classification.intent,
            symbols=classification.symbols,
            tools_required=classification.tools_required,
            timeframe=classification.timeframe,
            risk_level=classification.risk_level,
            compliance_template=disclaimer
        )
    
    def classify_query(self, user_query: str) -> IntentClassification:
        """Main classification method - processes user query into structured result."""
        # Extract components
        symbols = self.extract_symbols(user_query)
        intent = self.classify_intent(user_query)
        tools_required = self.determine_tools_required(intent, symbols)
        confidence = self.calculate_confidence(intent, symbols, user_query)
        timeframe = self.determine_timeframe(intent, user_query)
        risk_level = self.determine_risk_level(intent, symbols)
        
        # Generate initial response (this will be enhanced by AI service)
        disclaimer = self.compliance_templates.get_for_intent(intent, risk_level)
        
        return IntentClassification(
            intent=intent,
            symbols=symbols,
            tools_required=tools_required,
            confidence=confidence,
            timeframe=timeframe,
            risk_level=risk_level,
            raw_response=f"Query classified as {intent.value}. {disclaimer}"
        )


# Usage example and testing
if __name__ == "__main__":
    prompt_mgr = PromptManager()
    
    # Test cases
    test_queries = [
        "What is the price of $GME and all available indicator values?",
        "$AAPL current price",
        "What are some good options strategies for high volatility?",
        "How do I calculate position size for risk management?",
        "Explain RSI indicator"
    ]
    
    for query in test_queries:
        result = prompt_mgr.classify_query(query)
        print(f"\nQuery: {query}")
        print(f"Classification: {result.to_dict()}")