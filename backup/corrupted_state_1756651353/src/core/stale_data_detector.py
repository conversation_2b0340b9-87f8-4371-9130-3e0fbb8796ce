"""
Stale Data Detection and Warning System

Detects stale data and provides automatic confidence adjustments and warnings.
Integrates with data quality scoring and provider attribution systems.
"""

from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from enum import Enum
import logging
from typing import Dict, List, Optional, Any, Union

from src.api.data.metrics import cache_metrics
from src.core.data_quality import QualityScore, QualityLevel
from src.core.market_calendar import get_market_context, is_market_open, MarketStatus

logger = logging.getLogger(__name__)


class StaleDataSeverity(Enum):
    """Severity levels for stale data warnings."""
    NONE = "none"           # Data is fresh
    MILD = "mild"           # Slightly stale (5-15 min)
    MODERATE = "moderate"   # Moderately stale (15-60 min)
    SEVERE = "severe"       # Severely stale (1-4 hours)
    CRITICAL = "critical"   # Critically stale (>4 hours)


@dataclass
class StaleDataWarning:
    """Warning about stale data with recommendations."""
    symbol: str
    severity: StaleDataSeverity
    data_age_minutes: int
    data_age_hours: float
    last_update: datetime
    warning_message: str
    confidence_adjustment: float
    recommendations: List[str]
    timestamp: datetime
    
    def __post_init__(self):
        """Ensure timestamps are timezone-aware."""
        if self.last_update and self.last_update.tzinfo is None:
            self.last_update = self.last_update.replace(tzinfo=timezone.utc)
        if self.timestamp and self.timestamp.tzinfo is None:
            self.timestamp = self.timestamp.replace(tzinfo=timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            'symbol': self.symbol,
            'severity': self.severity.value,
            'data_age_minutes': self.data_age_minutes,
            'data_age_hours': round(self.data_age_hours, 2),
            'last_update': self.last_update.isoformat(),
            'warning_message': self.warning_message,
            'confidence_adjustment': round(self.confidence_adjustment, 2),
            'recommendations': self.recommendations,
            'timestamp': self.timestamp.isoformat()
        }


class StaleDataDetector:
    """
    Detects stale data and provides warnings and confidence adjustments.
    """
    
    def __init__(self):
        # Stale data thresholds in minutes
        self.stale_thresholds = {
            StaleDataSeverity.NONE: 5,      # <5 min = fresh
            StaleDataSeverity.MILD: 15,     # 5-15 min = mild
            StaleDataSeverity.MODERATE: 60, # 15-60 min = moderate
            StaleDataSeverity.SEVERE: 240,  # 1-4 hours = severe
            StaleDataSeverity.CRITICAL: 1440  # >4 hours = critical
        }
        
        # Confidence adjustment factors (percentage reduction)
        self.confidence_adjustments = {
            StaleDataSeverity.NONE: 0.0,      # No adjustment
            StaleDataSeverity.MILD: 5.0,      # 5% reduction
            StaleDataSeverity.MODERATE: 15.0, # 15% reduction
            StaleDataSeverity.SEVERE: 30.0,   # 30% reduction
            StaleDataSeverity.CRITICAL: 50.0  # 50% reduction
        }
        
        # Warning message templates
        self.warning_templates = {
            StaleDataSeverity.NONE: "Data is fresh and reliable",
            StaleDataSeverity.MILD: "Data is slightly stale - consider recent market changes",
            StaleDataSeverity.MODERATE: "Data is moderately stale - verify current prices",
            StaleDataSeverity.SEVERE: "Data is significantly stale - use with extreme caution",
            StaleDataSeverity.CRITICAL: "Data is critically stale - not recommended for trading decisions"
        }
    
    def detect_stale_data(
        self, 
        symbol: str, 
        last_update: datetime,
        current_time: Optional[datetime] = None
    ) -> StaleDataWarning:
        """
        Detect if data is stale and generate appropriate warnings.
        
        Args:
            symbol: Stock symbol
            last_update: When the data was last updated
            current_time: Current time (defaults to now)
            
        Returns:
            StaleDataWarning with severity, adjustments, and recommendations
        """
        try:
            if current_time is None:
                current_time = datetime.now(timezone.utc)
            
            # Ensure timezone-aware
            if last_update.tzinfo is None:
                last_update = last_update.replace(tzinfo=timezone.utc)
            if current_time.tzinfo is None:
                current_time = current_time.replace(tzinfo=timezone.utc)
            
            # Calculate data age
            age_delta = current_time - last_update
            age_minutes = int(age_delta.total_seconds() / 60)
            age_hours = age_delta.total_seconds() / 3600
            
            # Get market context for intelligent severity assessment
            market_context = get_market_context(current_time)
            market_status = market_context.get('status', 'unknown')
            is_market_currently_open = market_context.get('is_open', False)
            
            # Determine severity with market awareness
            severity = self._determine_intelligent_severity(
                age_minutes, 
                market_status, 
                is_market_currently_open,
                market_context
            )
            
            # Get confidence adjustment
            confidence_adjustment = self.confidence_adjustments[severity]
            
            # Generate intelligent warning message
            warning_message = self._generate_intelligent_warning_message(
                severity, 
                age_minutes, 
                market_status, 
                market_context
            )
            
            # Generate contextual recommendations
            recommendations = self._generate_intelligent_recommendations(
                severity, 
                age_minutes, 
                market_context
            )
            
            # Create warning object
            warning = StaleDataWarning(
                symbol=symbol,
                severity=severity,
                data_age_minutes=age_minutes,
                data_age_hours=age_hours,
                last_update=last_update,
                warning_message=warning_message,
                confidence_adjustment=confidence_adjustment,
                recommendations=recommendations,
                timestamp=current_time
            )
            
            # Record stale data metrics with market context
            self._record_stale_data_metrics(warning, market_context)
            
            logger.info(
                f"Stale data detected for {symbol}: {severity.value} "
                f"({age_minutes} min old, {confidence_adjustment:.1f}% confidence reduction, "
                f"market status: {market_status})"
            )
            
            return warning
            
        except Exception as e:
            logger.error(f"Error detecting stale data for {symbol}: {e}")
            # Return critical warning on error
            return StaleDataWarning(
                symbol=symbol,
                severity=StaleDataSeverity.CRITICAL,
                data_age_minutes=9999,
                data_age_hours=9999.0,
                last_update=last_update,
                warning_message="Error detecting data freshness - assume stale",
                confidence_adjustment=50.0,
                recommendations=["Verify data source manually", "Check system status"],
                timestamp=datetime.now(timezone.utc)
            )
    
    def _determine_severity(self, age_minutes: int) -> StaleDataSeverity:
        """Determine severity based on data age."""
        if age_minutes < self.stale_thresholds[StaleDataSeverity.MILD]:
            return StaleDataSeverity.NONE
        elif age_minutes < self.stale_thresholds[StaleDataSeverity.MODERATE]:
            return StaleDataSeverity.MILD
        elif age_minutes < self.stale_thresholds[StaleDataSeverity.SEVERE]:
            return StaleDataSeverity.MODERATE
        elif age_minutes < self.stale_thresholds[StaleDataSeverity.CRITICAL]:
            return StaleDataSeverity.SEVERE
        else:
            return StaleDataSeverity.CRITICAL
    
    def _generate_recommendations(
        self, 
        severity: StaleDataSeverity, 
        age_minutes: int, 
        symbol: str
    ) -> List[str]:
        """Generate recommendations based on stale data severity."""
        recommendations = []
        
        if severity == StaleDataSeverity.NONE:
            recommendations.append("Data is current - proceed with confidence")
            return recommendations
        
        # General recommendations for stale data
        if severity in [StaleDataSeverity.MILD, StaleDataSeverity.MODERATE]:
            recommendations.append("Verify current prices from live sources")
            recommendations.append("Consider market conditions may have changed")
        
        if severity in [StaleDataSeverity.SEVERE, StaleDataSeverity.CRITICAL]:
            recommendations.append("Do not use for real-time trading decisions")
            recommendations.append("Check for system or provider issues")
            recommendations.append("Consider alternative data sources")
        
        # Specific recommendations based on age
        if age_minutes > 60:  # >1 hour
            recommendations.append("Data is from previous trading session")
            if age_minutes > 1440:  # >1 day
                recommendations.append("Data is from previous trading day")
        
        # Market hours considerations
        current_hour = datetime.now(timezone.utc).hour
        if 13 <= current_hour <= 21:  # US market hours (1 PM - 9 PM UTC)
            if age_minutes > 15:
                recommendations.append("Market is open - use live data sources")
        else:
            if age_minutes < 60:
                recommendations.append("Market is closed - data age is acceptable")
        
        return recommendations
    
    def _determine_intelligent_severity(
        self, 
        age_minutes: int, 
        market_status: str, 
        is_market_open: bool,
        market_context: Dict[str, Any]
    ) -> StaleDataSeverity:
        """
        Determine severity with market awareness and intelligent adjustments.
        """
        # Base severity from age
        base_severity = self._determine_severity(age_minutes)
        
        # Apply market-aware adjustments
        if market_status == MarketStatus.HOLIDAY.value:
            # During holidays, downgrade severity (data is expected to be stale)
            if base_severity == StaleDataSeverity.CRITICAL:
                return StaleDataSeverity.MODERATE
            elif base_severity == StaleDataSeverity.SEVERE:
                return StaleDataSeverity.MILD
            elif base_severity == StaleDataSeverity.MAJOR:
                return StaleDataSeverity.MINOR
            else:
                return StaleDataSeverity.NONE
        
        elif market_status == MarketStatus.WEEKEND.value:
            # During weekends, downgrade severity
            if base_severity == StaleDataSeverity.CRITICAL:
                return StaleDataSeverity.MAJOR
            elif base_severity == StaleDataSeverity.SEVERE:
                return StaleDataSeverity.MODERATE
            elif base_severity == StaleDataSeverity.MAJOR:
                return StaleDataSeverity.MILD
            else:
                return StaleDataSeverity.NONE
        
        elif market_status == MarketStatus.AFTER_HOURS.value:
            # During after-hours, slightly downgrade severity
            if base_severity == StaleDataSeverity.CRITICAL:
                return StaleDataSeverity.SEVERE
            elif base_severity == StaleDataSeverity.SEVERE:
                return StaleDataSeverity.MODERATE
            else:
                return base_severity
        
        elif not is_market_open:
            # Market is closed for other reasons, apply moderate downgrade
            if base_severity == StaleDataSeverity.CRITICAL:
                return StaleDataSeverity.SEVERE
            elif base_severity == StaleDataSeverity.SEVERE:
                return StaleDataSeverity.MODERATE
            else:
                return base_severity
        
        # Market is open, use base severity
        return base_severity
    
    def _generate_intelligent_warning_message(
        self, 
        severity: StaleDataSeverity, 
        age_minutes: int, 
        market_status: str, 
        market_context: Dict[str, Any]
    ) -> str:
        """
        Generate intelligent warning message with market context.
        """
        # Base message
        if severity == StaleDataSeverity.NONE:
            return "Data is fresh and reliable"
        
        # Add market context to warning
        if market_status == MarketStatus.HOLIDAY.value:
            holiday_name = market_context.get('holiday', {}).get('name', 'holiday')
            return f"Data is {severity.value} during {holiday_name} (markets closed) - expected staleness"
        
        elif market_status == MarketStatus.WEEKEND.value:
            day_of_week = market_context.get('weekend_info', {}).get('day_of_week', 'weekend')
            return f"Data is {severity.value} during {day_of_week} (markets closed) - expected staleness"
        
        elif market_status == MarketStatus.AFTER_HOURS.value:
            return f"Data is {severity.value} during after-hours trading - limited updates expected"
        
        elif market_status == MarketStatus.PRE_MARKET.value:
            return f"Data is {severity.value} during pre-market trading - limited updates expected"
        
        else:
            # Market is open, use standard severity message
            return self.warning_templates[severity]
    
    def _generate_intelligent_recommendations(
        self, 
        severity: StaleDataSeverity, 
        age_minutes: int, 
        market_context: Dict[str, Any]
    ) -> List[str]:
        """
        Generate contextual recommendations based on market status.
        """
        recommendations = []
        market_status = market_context.get('status', 'unknown')
        
        # Market-specific recommendations
        if market_status == MarketStatus.HOLIDAY.value:
            holiday_name = market_context.get('holiday', {}).get('name', 'holiday')
            recommendations.append(f"Markets closed today for {holiday_name} - data staleness is expected")
            recommendations.append("Next trading session will provide fresh data")
            
            next_open = market_context.get('next_market_open')
            if next_open:
                recommendations.append(f"Markets reopen: {next_open}")
        
        elif market_status == MarketStatus.WEEKEND.value:
            day_of_week = market_context.get('weekend_info', {}).get('day_of_week', 'weekend')
            recommendations.append(f"Markets closed on {day_of_week} - data staleness is expected")
            
            next_trading_day = market_context.get('weekend_info', {}).get('next_trading_day')
            if next_trading_day:
                recommendations.append(f"Next trading day: {next_trading_day}")
        
        elif market_status == MarketStatus.AFTER_HOURS.value:
            recommendations.append("Limited data updates during after-hours trading")
            recommendations.append("Regular market hours provide most reliable data")
        
        elif market_status == MarketStatus.PRE_MARKET.value:
            recommendations.append("Limited data updates during pre-market trading")
            recommendations.append("Regular market hours provide most reliable data")
        
        # Add severity-based recommendations
        if severity in [StaleDataSeverity.SEVERE, StaleDataSeverity.CRITICAL]:
            if market_status not in [MarketStatus.HOLIDAY.value, MarketStatus.WEEKEND.value]:
                # Only add critical recommendations if market should be open
                recommendations.append("Verify data source and connectivity")
                recommendations.append("Check for system or provider issues")
        
        # Add general recommendations
        if severity != StaleDataSeverity.NONE:
            recommendations.append("Consider data age when making trading decisions")
            recommendations.append("Verify current prices from live sources if needed")
        
        return recommendations
    
    def _record_stale_data_metrics(self, warning: StaleDataWarning, market_context: Dict[str, Any]):
        """Record stale data metrics for monitoring."""
        try:
            # This would integrate with your metrics system
            # For now, just log the metrics
            logger.debug(
                f"Stale data metrics: {warning.symbol} - "
                f"{warning.severity.value} - {warning.data_age_minutes} min - "
                f"market_status: {market_context.get('status', 'unknown')}"
            )
            
        except Exception as e:
            logger.error(f"Error recording stale data metrics: {e}")
    
    def adjust_confidence_for_staleness(
        self, 
        base_confidence: float, 
        warning: StaleDataWarning
    ) -> float:
        """
        Adjust confidence score based on stale data warning.
        
        Args:
            base_confidence: Original confidence score (0-100)
            warning: StaleDataWarning object
            
        Returns:
            Adjusted confidence score
        """
        try:
            # Apply confidence reduction
            adjusted_confidence = base_confidence - warning.confidence_adjustment
            
            # Ensure confidence stays within bounds
            adjusted_confidence = max(0.0, min(100.0, adjusted_confidence))
            
            logger.info(
                f"Confidence adjusted for {warning.symbol}: "
                f"{base_confidence:.1f} → {adjusted_confidence:.1f} "
                f"({warning.confidence_adjustment:.1f}% reduction due to {warning.severity.value} staleness)"
            )
            
            return adjusted_confidence
            
        except Exception as e:
            logger.error(f"Error adjusting confidence for staleness: {e}")
            return base_confidence
    
    def get_stale_data_summary(self, warnings: List[StaleDataWarning]) -> Dict[str, Any]:
        """Generate summary of stale data warnings."""
        if not warnings:
            return {
                'total_warnings': 0,
                'severity_distribution': {},
                'average_age_minutes': 0,
                'total_confidence_reduction': 0.0
            }
        
        severity_counts = {}
        total_age = 0
        total_confidence_reduction = 0.0
        
        for warning in warnings:
            # Count by severity
            severity = warning.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Sum ages and confidence reductions
            total_age += warning.data_age_minutes
            total_confidence_reduction += warning.confidence_adjustment
        
        return {
            'total_warnings': len(warnings),
            'severity_distribution': severity_counts,
            'average_age_minutes': total_age / len(warnings),
            'total_confidence_reduction': total_confidence_reduction
        }


# Global stale data detector instance
stale_data_detector = StaleDataDetector()


def detect_stale_data(
    symbol: str, 
    last_update: datetime,
    current_time: Optional[datetime] = None
) -> StaleDataWarning:
    """Convenience function to detect stale data."""
    return stale_data_detector.detect_stale_data(symbol, last_update, current_time)


def adjust_confidence_for_staleness(
    base_confidence: float, 
    warning: StaleDataWarning
) -> float:
    """Convenience function to adjust confidence for staleness."""
    return stale_data_detector.adjust_confidence_for_staleness(base_confidence, warning)


def get_stale_data_summary(warnings: List[StaleDataWarning]) -> Dict[str, Any]:
    """Convenience function to get stale data summary."""
    return stale_data_detector.get_stale_data_summary(warnings) 