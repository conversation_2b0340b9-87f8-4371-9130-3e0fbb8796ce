"""
Unified configuration system for the entire application.
All configuration should be managed through this module.
"""

import os
from enum import Enum
from pathlib import Path
from typing import Any, Dict, Optional, Union

from pydantic import BaseModel, Field, validator

class ConfigSource(Enum):
    """Configuration source types."""
    ENV = "env"
    FILE = "file"
    DEFAULT = "default"

class BaseConfig(BaseModel):
    """Base configuration model with common functionality."""
    
    class Config:
        """Pydantic model configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        validate_all = True

class DatabaseConfig(BaseConfig):
    """Database configuration settings."""
    
    host: str = Field(default_factory=lambda: os.getenv("POSTGRES_HOST", "localhost"))
    port: int = Field(default_factory=lambda: int(os.getenv("POSTGRES_PORT", "5432")))
    database: str = Field(default_factory=lambda: os.getenv("POSTGRES_DB", "tradingview"))
    user: str = Field(default_factory=lambda: os.getenv("POSTGRES_USER", "tradingview"))
    password: str = Field(default_factory=lambda: os.getenv("POSTGRES_PASSWORD"))
    
    @validator("password", pre=True)
    def validate_password(cls, v: Optional[str]) -> str:
        """Ensure password is provided."""
        if not v:
            raise ValueError("Database password must be set in environment")
        return v

class RedisConfig(BaseConfig):
    """Redis configuration settings."""
    
    host: str = Field(default_factory=lambda: os.getenv("REDIS_HOST", "localhost"))
    port: int = Field(default_factory=lambda: int(os.getenv("REDIS_PORT", "6379")))
    db: int = Field(default_factory=lambda: int(os.getenv("REDIS_DB", "0")))
    password: Optional[str] = Field(default_factory=lambda: os.getenv("REDIS_PASSWORD"))

class APIConfig(BaseConfig):
    """API configuration settings."""
    
    host: str = Field(default_factory=lambda: os.getenv("API_HOST", "0.0.0.0"))
    port: int = Field(default_factory=lambda: int(os.getenv("API_PORT", "8000")))
    debug: bool = Field(default_factory=lambda: os.getenv("API_DEBUG", "false").lower() == "true")
    cors_origins: list[str] = Field(
        default_factory=lambda: os.getenv("CORS_ORIGINS", "*").split(",")
    )

class DataProviderConfig(BaseConfig):
    """Data provider configuration settings."""
    
    polygon_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("POLYGON_API_KEY"))
    finnhub_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("FINNHUB_API_KEY"))
    alpha_vantage_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("ALPHA_VANTAGE_API_KEY"))
    
    # Provider settings
    yahoo_finance_enabled: bool = Field(default=True)
    polygon_enabled: bool = Field(default=False)
    finnhub_enabled: bool = Field(default=False)
    alpha_vantage_enabled: bool = Field(default=False)
    
    # Rate limits (requests per minute)
    yahoo_finance_rate_limit: int = Field(default=5)
    polygon_rate_limit: int = Field(default=5)
    finnhub_rate_limit: int = Field(default=30)
    alpha_vantage_rate_limit: int = Field(default=5)
    
    # Timeouts (seconds)
    yahoo_finance_timeout: float = Field(default=10.0)
    polygon_timeout: float = Field(default=8.0)
    finnhub_timeout: float = Field(default=5.0)
    alpha_vantage_timeout: float = Field(default=10.0)

class TechnicalAnalysisConfig(BaseConfig):
    """Technical analysis configuration settings."""
    
    # Indicators
    rsi_period: int = Field(default=14)
    macd_fast_period: int = Field(default=12)
    macd_slow_period: int = Field(default=26)
    macd_signal_period: int = Field(default=9)
    
    # Moving averages
    sma_periods: list[int] = Field(default=[20, 50, 200])
    ema_periods: list[int] = Field(default=[9, 21])
    
    # Volatility
    atr_period: int = Field(default=14)
    bollinger_period: int = Field(default=20)
    bollinger_std: float = Field(default=2.0)

class TradingStrategyConfig(BaseConfig):
    """Trading strategy configuration settings."""
    
    # Risk management
    max_position_size: float = Field(default=0.02)  # 2% of account
    max_risk_per_trade: float = Field(default=0.01)  # 1% of account
    position_sizing_atr_multiplier: float = Field(default=1.5)
    
    # Entry/Exit
    profit_target_atr_multiplier: float = Field(default=2.0)
    stop_loss_atr_multiplier: float = Field(default=1.0)
    trailing_stop_activation: float = Field(default=1.5)  # ATR multiplier
    
    # Filters
    min_daily_volume: int = Field(default=500000)
    min_price: float = Field(default=5.0)
    max_spread_percent: float = Field(default=0.02)  # 2%

class AIConfig(BaseConfig):
    """AI and machine learning configuration settings."""
    
    openai_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    model_name: str = Field(default="gpt-4")
    temperature: float = Field(default=0.7)
    max_tokens: int = Field(default=2000)
    
    # Caching
    cache_enabled: bool = Field(default=True)
    cache_ttl: int = Field(default=300)  # 5 minutes
    
    # Rate limiting
    rate_limit_enabled: bool = Field(default=True)
    rate_limit_requests: int = Field(default=50)
    rate_limit_window: int = Field(default=60)  # 1 minute

class AppConfig(BaseConfig):
    """Main application configuration that combines all sub-configs."""
    
    # Environment
    env: str = Field(default_factory=lambda: os.getenv("ENVIRONMENT", "development"))
    debug: bool = Field(default_factory=lambda: os.getenv("DEBUG", "false").lower() == "true")
    log_level: str = Field(default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"))
    
    # Sub-configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    data_providers: DataProviderConfig = Field(default_factory=DataProviderConfig)
    technical_analysis: TechnicalAnalysisConfig = Field(default_factory=TechnicalAnalysisConfig)
    trading_strategy: TradingStrategyConfig = Field(default_factory=TradingStrategyConfig)
    ai: AIConfig = Field(default_factory=AIConfig)
    
    class Config:
        """Pydantic model configuration."""
        env_prefix = ""

class ConfigManager:
    """
    Configuration manager that handles loading and accessing all configuration.
    Implements the singleton pattern.
    """
    
    _instance = None
    _config: Optional[AppConfig] = None
    
    def __new__(cls) -> "ConfigManager":
        """Ensure only one instance exists."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize configuration if not already loaded."""
        if self._config is None:
            self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from environment and files."""
        try:
            self._config = AppConfig()
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {str(e)}")
    
    @property
    def config(self) -> AppConfig:
        """Get the current configuration."""
        if self._config is None:
            self.load_config()
        return self._config
    
    def reload(self) -> None:
        """Reload configuration from sources."""
        self.load_config()
    
    def get_section(self, section: str) -> Any:
        """
        Get a specific configuration section.
        
        Args:
            section: Name of the configuration section
            
        Returns:
            Configuration section
        """
        if not hasattr(self.config, section):
            raise ConfigurationError(f"Configuration section '{section}' not found")
        return getattr(self.config, section)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.config.dict()

class ConfigurationError(Exception):
    """Raised when there is a configuration error."""
    pass

# Global configuration instance
config_manager = ConfigManager() 