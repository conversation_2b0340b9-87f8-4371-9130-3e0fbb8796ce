"""
Watchlist Manager - Foundation for Automated Analysis

Manages user watchlists with priority-based scheduling, automated refresh,
and integration with the analysis pipeline.
"""

import asyncio
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import json
import logging
from typing import Dict, List, Optional, Any


logger = logging.getLogger(__name__)


class WatchlistPriority(Enum):
    """Watchlist priority levels for automated analysis."""
    HIGH = "high"      # Refresh every 15 minutes
    MEDIUM = "medium"  # Refresh every 1 hour
    LOW = "low"        # Refresh every 4 hours


class AnalysisDepth(Enum):
    """Analysis depth levels for automated processing."""
    QUICK = "quick"        # 2-3 minutes, basic indicators
    STANDARD = "standard"  # 5-10 minutes, comprehensive analysis
    DEEP_DIVE = "deep_dive"  # 15-20 minutes, professional analysis


@dataclass
class WatchlistEntry:
    """Individual symbol entry in a watchlist."""
    symbol: str
    added_at: datetime
    notes: Optional[str] = None
    tags: List[str] = None
    priority: WatchlistPriority = WatchlistPriority.MEDIUM
    analysis_depth: AnalysisDepth = AnalysisDepth.STANDARD
    last_analysis: Optional[datetime] = None
    next_analysis: Optional[datetime] = None
    analysis_count: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.next_analysis is None:
            self.next_analysis = self._calculate_next_analysis()
    
    def _calculate_next_analysis(self) -> datetime:
        """Calculate when next analysis should occur based on priority."""
        now = datetime.now()
        if self.priority == WatchlistPriority.HIGH:
            return now + timedelta(minutes=15)
        elif self.priority == WatchlistPriority.MEDIUM:
            return now + timedelta(hours=1)
        else:  # LOW
            return now + timedelta(hours=4)
    
    def needs_analysis(self) -> bool:
        """Check if symbol needs analysis."""
        return datetime.now() >= self.next_analysis
    
    def mark_analysis_complete(self):
        """Mark analysis as complete and schedule next one."""
        self.last_analysis = datetime.now()
        self.next_analysis = self._calculate_next_analysis()
        self.analysis_count += 1


@dataclass
class Watchlist:
    """User watchlist with metadata and configuration."""
    user_id: str
    name: str
    symbols: List[WatchlistEntry]
    created_at: datetime
    last_updated: datetime
    priority: WatchlistPriority = WatchlistPriority.MEDIUM
    analysis_depth: AnalysisDepth = AnalysisDepth.STANDARD
    refresh_frequency: str = "1h"  # 15m, 1h, 4h, 1d
    auto_refresh: bool = True
    max_symbols: int = 50
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = []
    
    def add_symbol(self, symbol: str, notes: str = None, tags: List[str] = None, 
                   priority: WatchlistPriority = None, analysis_depth: AnalysisDepth = None) -> bool:
        """Add a symbol to the watchlist."""
        if len(self.symbols) >= self.max_symbols:
            logger.warning(f"Watchlist {self.name} for user {self.user_id} is full ({self.max_symbols} symbols)")
            return False
        
        # Check if symbol already exists
        if any(entry.symbol == symbol.upper() for entry in self.symbols):
            logger.info(f"Symbol {symbol} already exists in watchlist {self.name}")
            return False
        
        # Use watchlist defaults if not specified
        if priority is None:
            priority = self.priority
        if analysis_depth is None:
            analysis_depth = self.analysis_depth
        
        entry = WatchlistEntry(
            symbol=symbol.upper(),
            added_at=datetime.now(),
            notes=notes,
            tags=tags or [],
            priority=priority,
            analysis_depth=analysis_depth
        )
        
        self.symbols.append(entry)
        self.last_updated = datetime.now()
        logger.info(f"Added {symbol} to watchlist {self.name} for user {self.user_id}")
        return True
    
    def remove_symbol(self, symbol: str) -> bool:
        """Remove a symbol from the watchlist."""
        symbol = symbol.upper()
        for i, entry in enumerate(self.symbols):
            if entry.symbol == symbol:
                removed_entry = self.symbols.pop(i)
                self.last_updated = datetime.now()
                logger.info(f"Removed {symbol} from watchlist {self.name} for user {self.user_id}")
                return True
        return False
    
    def get_symbols_needing_analysis(self) -> List[WatchlistEntry]:
        """Get symbols that need analysis based on their schedule."""
        now = datetime.now()
        return [entry for entry in self.symbols if entry.needs_analysis()]
    
    def get_high_priority_symbols(self) -> List[WatchlistEntry]:
        """Get high priority symbols for immediate analysis."""
        return [entry for entry in self.symbols if entry.priority == WatchlistPriority.HIGH]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert watchlist to dictionary for storage."""
        return {
            'user_id': self.user_id,
            'name': self.name,
            'symbols': [asdict(entry) for entry in self.symbols],
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'priority': self.priority.value,
            'analysis_depth': self.analysis_depth.value,
            'refresh_frequency': self.refresh_frequency,
            'auto_refresh': self.auto_refresh,
            'max_symbols': self.max_symbols
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Watchlist':
        """Create watchlist from dictionary."""
        # Convert string dates back to datetime objects
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        
        # Convert priority and analysis_depth back to enums
        data['priority'] = WatchlistPriority(data['priority'])
        data['analysis_depth'] = AnalysisDepth(data['analysis_depth'])
        
        # Convert symbol entries back to WatchlistEntry objects
        symbols = []
        for symbol_data in data['symbols']:
            # Convert string dates back to datetime objects
            symbol_data['added_at'] = datetime.fromisoformat(symbol_data['added_at'])
            if symbol_data.get('last_analysis'):
                symbol_data['last_analysis'] = datetime.fromisoformat(symbol_data['last_analysis'])
            if symbol_data.get('next_analysis'):
                symbol_data['next_analysis'] = datetime.fromisoformat(symbol_data['next_analysis'])
            
            # Convert priority and analysis_depth back to enums
            symbol_data['priority'] = WatchlistPriority(symbol_data['priority'])
            symbol_data['analysis_depth'] = AnalysisDepth(symbol_data['analysis_depth'])
            
            symbols.append(WatchlistEntry(**symbol_data))
        
        data['symbols'] = symbols
        return cls(**data)


class WatchlistManager:
    """Manages user watchlists with automated analysis scheduling."""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.watchlists: Dict[str, Watchlist] = {}
        self._load_watchlists()
    
    def _load_watchlists(self):
        """Load watchlists from storage (placeholder for Redis/Postgres integration)."""
        # TODO: Implement Redis/Postgres integration
        # For now, create a default watchlist
        default_watchlist = Watchlist(
            user_id=self.user_id,
            name="default",
            symbols=[],
            created_at=datetime.now(),
            last_updated=datetime.now()
        )
        self.watchlists["default"] = default_watchlist
    
    def create_watchlist(self, name: str, priority: WatchlistPriority = WatchlistPriority.MEDIUM,
                        analysis_depth: AnalysisDepth = AnalysisDepth.STANDARD) -> bool:
        """Create a new watchlist."""
        if name in self.watchlists:
            logger.warning(f"Watchlist {name} already exists for user {self.user_id}")
            return False
        
        watchlist = Watchlist(
            user_id=self.user_id,
            name=name,
            symbols=[],
            created_at=datetime.now(),
            last_updated=datetime.now(),
            priority=priority,
            analysis_depth=analysis_depth
        )
        
        self.watchlists[name] = watchlist
        logger.info(f"Created watchlist {name} for user {self.user_id}")
        return True
    
    def get_watchlist(self, name: str = "default") -> Optional[Watchlist]:
        """Get a watchlist by name."""
        return self.watchlists.get(name)
    
    def list_watchlists(self) -> List[str]:
        """List all watchlist names for the user."""
        return list(self.watchlists.keys())
    
    def add_symbol(self, symbol: str, watchlist_name: str = "default", notes: str = None,
                   tags: List[str] = None, priority: WatchlistPriority = None,
                   analysis_depth: AnalysisDepth = None) -> bool:
        """Add a symbol to a watchlist."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            logger.error(f"Watchlist {watchlist_name} not found for user {self.user_id}")
            return False
        
        return watchlist.add_symbol(symbol, notes, tags, priority, analysis_depth)
    
    def remove_symbol(self, symbol: str, watchlist_name: str = "default") -> bool:
        """Remove a symbol from a watchlist."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            logger.error(f"Watchlist {watchlist_name} not found for user {self.user_id}")
            return False
        
        return watchlist.remove_symbol(symbol)
    
    def get_symbols_needing_analysis(self, watchlist_name: str = "default") -> List[WatchlistEntry]:
        """Get symbols that need analysis from a specific watchlist."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            return []
        
        return watchlist.get_symbols_needing_analysis()
    
    def get_all_symbols_needing_analysis(self) -> List[WatchlistEntry]:
        """Get all symbols across all watchlists that need analysis."""
        symbols_needing_analysis = []
        for watchlist in self.watchlists.values():
            symbols_needing_analysis.extend(watchlist.get_symbols_needing_analysis())
        
        # Sort by priority (high first) and then by next_analysis time
        symbols_needing_analysis.sort(
            key=lambda x: (x.priority.value != WatchlistPriority.HIGH.value, x.next_analysis)
        )
        
        return symbols_needing_analysis
    
    def get_high_priority_symbols(self) -> List[WatchlistEntry]:
        """Get all high priority symbols across all watchlists."""
        high_priority_symbols = []
        for watchlist in self.watchlists.values():
            high_priority_symbols.extend(watchlist.get_high_priority_symbols())
        
        return high_priority_symbols
    
    def update_symbol_priority(self, symbol: str, new_priority: WatchlistPriority,
                              watchlist_name: str = "default") -> bool:
        """Update the priority of a symbol in a watchlist."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            return False
        
        for entry in watchlist.symbols:
            if entry.symbol == symbol.upper():
                entry.priority = new_priority
                entry.next_analysis = entry._calculate_next_analysis()
                watchlist.last_updated = datetime.now()
                logger.info(f"Updated priority for {symbol} to {new_priority.value} in watchlist {watchlist_name}")
                return True
        
        return False
    
    def update_symbol_analysis_depth(self, symbol: str, new_depth: AnalysisDepth,
                                    watchlist_name: str = "default") -> bool:
        """Update the analysis depth of a symbol in a watchlist."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            return False
        
        for entry in watchlist.symbols:
            if entry.symbol == symbol.upper():
                entry.analysis_depth = new_depth
                watchlist.last_updated = datetime.now()
                logger.info(f"Updated analysis depth for {symbol} to {new_depth.value} in watchlist {watchlist_name}")
                return True
        
        return False
    
    def mark_analysis_complete(self, symbol: str, watchlist_name: str = "default") -> bool:
        """Mark analysis as complete for a symbol."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            return False
        
        for entry in watchlist.symbols:
            if entry.symbol == symbol.upper():
                entry.mark_analysis_complete()
                watchlist.last_updated = datetime.now()
                logger.info(f"Marked analysis complete for {symbol} in watchlist {watchlist_name}")
                return True
        
        return False
    
    def get_watchlist_summary(self, watchlist_name: str = "default") -> Dict[str, Any]:
        """Get a summary of a watchlist."""
        watchlist = self.get_watchlist(watchlist_name)
        if not watchlist:
            return {}
        
        symbols_needing_analysis = watchlist.get_symbols_needing_analysis()
        high_priority_count = len([s for s in watchlist.symbols if s.priority == WatchlistPriority.HIGH])
        
        return {
            'name': watchlist.name,
            'total_symbols': len(watchlist.symbols),
            'symbols_needing_analysis': len(symbols_needing_analysis),
            'high_priority_symbols': high_priority_count,
            'last_updated': watchlist.last_updated.isoformat(),
            'priority': watchlist.priority.value,
            'analysis_depth': watchlist.analysis_depth.value,
            'auto_refresh': watchlist.auto_refresh
        }
    
    def save_watchlists(self):
        """Save watchlists to storage (placeholder for Redis/Postgres integration)."""
        # TODO: Implement Redis/Postgres integration
        logger.info(f"Saving {len(self.watchlists)} watchlists for user {self.user_id}")
        for name, watchlist in self.watchlists.items():
            watchlist_data = watchlist.to_dict()
            # TODO: Save to Redis/Postgres
            logger.debug(f"Saved watchlist {name}: {len(watchlist.symbols)} symbols")
    
    def load_watchlists(self):
        """Load watchlists from storage (placeholder for Redis/Postgres integration)."""
        # TODO: Implement Redis/Postgres integration
        logger.info(f"Loading watchlists for user {self.user_id}")
        self._load_watchlists()


# Convenience function for creating watchlist manager
def create_watchlist_manager(user_id: str) -> WatchlistManager:
    """Create a watchlist manager for a user."""
    return WatchlistManager(user_id) 