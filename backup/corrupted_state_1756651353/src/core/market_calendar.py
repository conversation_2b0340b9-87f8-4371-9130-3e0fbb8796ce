"""
Market Calendar Service

Provides trading schedule information, holiday detection, and market hours awareness.
Integrates with stale data detection and AI responses for context-aware data freshness assessment.
"""

from dataclasses import dataclass
from datetime import datetime, time, timedelta, date
from enum import Enum
import json
import logging
from typing import Dict, List, Optional, Any, Union

import pytz


logger = logging.getLogger(__name__)


class MarketStatus(Enum):
    """Market status enumeration."""
    OPEN = "open"
    CLOSED = "closed"
    PRE_MARKET = "pre_market"
    AFTER_HOURS = "after_hours"
    HALF_DAY = "half_day"
    HOLIDAY = "holiday"
    WEEKEND = "weekend"


class Exchange(Enum):
    """Supported exchanges."""
    NYSE = "nyse"
    NASDAQ = "nasdaq"
    LSE = "lse"  # London Stock Exchange
    HKEX = "hkex"  # Hong Kong Exchange
    TSE = "tse"  # Tokyo Stock Exchange


@dataclass
class MarketHours:
    """Market hours configuration for an exchange."""
    exchange: Exchange
    timezone: str
    regular_open: time
    regular_close: time
    pre_market_open: time
    after_hours_close: time
    half_day_close: Optional[time] = None
    
    def __post_init__(self):
        """Validate market hours."""
        if self.half_day_close and self.half_day_close <= self.regular_open:
            raise ValueError("Half-day close must be after regular open")


@dataclass
class Holiday:
    """Holiday information."""
    name: str
    date: date
    exchange: Exchange
    is_half_day: bool = False
    half_day_close: Optional[time] = None
    description: Optional[str] = None
    
    def __post_init__(self):
        """Validate holiday data."""
        if self.is_half_day and not self.half_day_close:
            raise ValueError("Half-day holidays must specify half-day close time")


class MarketCalendar:
    """
    Comprehensive market calendar service with holiday detection and trading schedule awareness.
    """
    
    def __init__(self):
        # Default market hours (US markets)
        self.market_hours = {
            Exchange.NYSE: MarketHours(
                exchange=Exchange.NYSE,
                timezone="America/New_York",
                regular_open=time(9, 30),      # 9:30 AM ET
                regular_close=time(16, 0),     # 4:00 PM ET
                pre_market_open=time(4, 0),    # 4:00 AM ET
                after_hours_close=time(20, 0)  # 8:00 PM ET
            ),
            Exchange.NASDAQ: MarketHours(
                exchange=Exchange.NASDAQ,
                timezone="America/New_York",
                regular_open=time(9, 30),      # 9:30 AM ET
                regular_close=time(16, 0),     # 4:00 PM ET
                pre_market_open=time(4, 0),    # 4:00 AM ET
                after_hours_close=time(20, 0)  # 8:00 PM ET
            )
        }
        
        # US Federal Holidays (2024-2025)
        self.holidays = self._load_us_holidays()
        
        # Market status cache
        self._status_cache = {}
        self._cache_ttl = 300  # 5 minutes
    
    def _load_us_holidays(self) -> List[Holiday]:
        """Load US Federal holidays for 2024-2025."""
        holidays = [
            # 2024
            Holiday("New Year's Day", date(2024, 1, 1), Exchange.NYSE),
            Holiday("Martin Luther King Jr. Day", date(2024, 1, 15), Exchange.NYSE),
            Holiday("Presidents' Day", date(2024, 2, 19), Exchange.NYSE),
            Holiday("Good Friday", date(2024, 3, 29), Exchange.NYSE),
            Holiday("Memorial Day", date(2024, 5, 27), Exchange.NYSE),
            Holiday("Juneteenth", date(2024, 6, 19), Exchange.NYSE),
            Holiday("Independence Day", date(2024, 7, 4), Exchange.NYSE),
            Holiday("Labor Day", date(2024, 9, 2), Exchange.NYSE),
            Holiday("Thanksgiving", date(2024, 11, 28), Exchange.NYSE),
            Holiday("Christmas Day", date(2024, 12, 25), Exchange.NYSE),
            
            # 2025
            Holiday("New Year's Day", date(2025, 1, 1), Exchange.NYSE),
            Holiday("Martin Luther King Jr. Day", date(2025, 1, 20), Exchange.NYSE),
            Holiday("Presidents' Day", date(2025, 2, 17), Exchange.NYSE),
            Holiday("Good Friday", date(2025, 4, 18), Exchange.NYSE),
            Holiday("Memorial Day", date(2025, 5, 26), Exchange.NYSE),
            Holiday("Juneteenth", date(2025, 6, 19), Exchange.NYSE),
            Holiday("Independence Day", date(2025, 7, 4), Exchange.NYSE),
            Holiday("Labor Day", date(2025, 9, 1), Exchange.NYSE),
            Holiday("Thanksgiving", date(2025, 11, 27), Exchange.NYSE),
            Holiday("Christmas Day", date(2025, 12, 25), Exchange.NYSE),
            
            # Add NASDAQ holidays (same as NYSE)
            Holiday("New Year's Day", date(2024, 1, 1), Exchange.NASDAQ),
            Holiday("Martin Luther King Jr. Day", date(2024, 1, 15), Exchange.NASDAQ),
            Holiday("Presidents' Day", date(2024, 2, 19), Exchange.NASDAQ),
            Holiday("Good Friday", date(2024, 3, 29), Exchange.NASDAQ),
            Holiday("Memorial Day", date(2024, 5, 27), Exchange.NASDAQ),
            Holiday("Juneteenth", date(2024, 6, 19), Exchange.NASDAQ),
            Holiday("Independence Day", date(2024, 7, 4), Exchange.NASDAQ),
            Holiday("Labor Day", date(2024, 9, 2), Exchange.NASDAQ),
            Holiday("Thanksgiving", date(2024, 11, 28), Exchange.NASDAQ),
            Holiday("Christmas Day", date(2024, 12, 25), Exchange.NASDAQ),
            
            # 2025 NASDAQ
            Holiday("New Year's Day", date(2025, 1, 1), Exchange.NASDAQ),
            Holiday("Martin Luther King Jr. Day", date(2025, 1, 20), Exchange.NASDAQ),
            Holiday("Presidents' Day", date(2025, 2, 17), Exchange.NASDAQ),
            Holiday("Good Friday", date(2025, 4, 18), Exchange.NASDAQ),
            Holiday("Memorial Day", date(2025, 5, 26), Exchange.NASDAQ),
            Holiday("Juneteenth", date(2025, 6, 19), Exchange.NASDAQ),
            Holiday("Independence Day", date(2025, 7, 4), Exchange.NASDAQ),
            Holiday("Labor Day", date(2025, 9, 1), Exchange.NASDAQ),
            Holiday("Thanksgiving", date(2025, 11, 27), Exchange.NASDAQ),
            Holiday("Christmas Day", date(2025, 12, 25), Exchange.NASDAQ),
        ]
        
        return holidays
    
    def is_market_open(
        self, 
        dt: datetime, 
        exchange: Exchange = Exchange.NYSE,
        include_pre_after_hours: bool = False
    ) -> bool:
        """
        Check if market is open at the given datetime.
        
        Args:
            dt: Datetime to check
            exchange: Exchange to check
            include_pre_after_hours: Include pre-market and after-hours trading
            
        Returns:
            True if market is open, False otherwise
        """
        try:
            # Convert to exchange timezone
            exchange_tz = pytz.timezone(self.market_hours[exchange].timezone)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=pytz.UTC)
            
            local_dt = dt.astimezone(exchange_tz)
            local_date = local_dt.date()
            local_time = local_dt.time()
            
            # Check if it's a weekend
            if local_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return False
            
            # Check if it's a holiday
            if self._is_holiday(local_date, exchange):
                return False
            
            # Check regular market hours
            market_hours = self.market_hours[exchange]
            
            if include_pre_after_hours:
                # Include pre-market and after-hours
                return (market_hours.pre_market_open <= local_time <= market_hours.after_hours_close)
            else:
                # Regular market hours only
                return (market_hours.regular_open <= local_time <= market_hours.regular_close)
                
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            return False
    
    def get_market_status(
        self, 
        dt: datetime, 
        exchange: Exchange = Exchange.NYSE
    ) -> MarketStatus:
        """
        Get detailed market status for the given datetime.
        
        Args:
            dt: Datetime to check
            exchange: Exchange to check
            
        Returns:
            MarketStatus enum value
        """
        try:
            # Convert to exchange timezone
            exchange_tz = pytz.timezone(self.market_hours[exchange].timezone)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=pytz.UTC)
            
            local_dt = dt.astimezone(exchange_tz)
            local_date = local_dt.date()
            local_time = local_dt.time()
            
            # Check if it's a weekend
            if local_date.weekday() >= 5:
                return MarketStatus.WEEKEND
            
            # Check if it's a holiday
            if self._is_holiday(local_date, exchange):
                return MarketStatus.HOLIDAY
            
            # Check market hours
            market_hours = self.market_hours[exchange]
            
            if local_time < market_hours.regular_open:
                return MarketStatus.PRE_MARKET
            elif local_time > market_hours.regular_close:
                return MarketStatus.AFTER_HOURS
            else:
                return MarketStatus.OPEN
                
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return MarketStatus.CLOSED
    
    def next_market_open(
        self, 
        dt: datetime, 
        exchange: Exchange = Exchange.NYSE
    ) -> Optional[datetime]:
        """
        Get the next market open time from the given datetime.
        
        Args:
            dt: Starting datetime
            exchange: Exchange to check
            
        Returns:
            Next market open datetime or None if error
        """
        try:
            # Convert to exchange timezone
            exchange_tz = pytz.timezone(self.market_hours[exchange].timezone)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=pytz.UTC)
            
            local_dt = dt.astimezone(exchange_tz)
            market_hours = self.market_hours[exchange]
            
            # Start with next day
            next_date = local_dt.date() + timedelta(days=1)
            
            # Find next trading day
            for _ in range(10):  # Look ahead up to 10 days
                if next_date.weekday() < 5 and not self._is_holiday(next_date, exchange):
                    # Found next trading day
                    next_open = datetime.combine(next_date, market_hours.regular_open)
                    next_open = exchange_tz.localize(next_open)
                    return next_open.astimezone(pytz.UTC)
                
                next_date += timedelta(days=1)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting next market open: {e}")
            return None
    
    def last_market_close(
        self, 
        dt: datetime, 
        exchange: Exchange = Exchange.NYSE
    ) -> Optional[datetime]:
        """
        Get the last market close time from the given datetime.
        
        Args:
            dt: Starting datetime
            exchange: Exchange to check
            
        Returns:
            Last market close datetime or None if error
        """
        try:
            # Convert to exchange timezone
            exchange_tz = pytz.timezone(self.market_hours[exchange].timezone)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=pytz.UTC)
            
            local_dt = dt.astimezone(exchange_tz)
            market_hours = self.market_hours[exchange]
            
            # Start with previous day
            prev_date = local_dt.date() - timedelta(days=1)
            
            # Find last trading day
            for _ in range(10):  # Look back up to 10 days
                if prev_date.weekday() < 5 and not self._is_holiday(prev_date, exchange):
                    # Found last trading day
                    last_close = datetime.combine(prev_date, market_hours.regular_close)
                    last_close = exchange_tz.localize(last_close)
                    return last_close.astimezone(pytz.UTC)
                
                prev_date -= timedelta(days=1)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting last market close: {e}")
            return None
    
    def get_market_context(
        self, 
        dt: datetime, 
        exchange: Exchange = Exchange.NYSE
    ) -> Dict[str, Any]:
        """
        Get comprehensive market context for the given datetime.
        
        Args:
            dt: Datetime to check
            exchange: Exchange to check
            
        Returns:
            Dictionary with market context information
        """
        try:
            status = self.get_market_status(dt, exchange)
            is_open = self.is_market_open(dt, exchange)
            
            context = {
                'status': status.value,
                'is_open': is_open,
                'exchange': exchange.value,
                'timezone': self.market_hours[exchange].timezone,
                'current_time': dt.isoformat(),
                'market_hours': {
                    'regular_open': self.market_hours[exchange].regular_open.isoformat(),
                    'regular_close': self.market_hours[exchange].regular_close.isoformat(),
                    'pre_market_open': self.market_hours[exchange].pre_market_open.isoformat(),
                    'after_hours_close': self.market_hours[exchange].after_hours_close.isoformat()
                }
            }
            
            # Add next/previous market times
            if not is_open:
                next_open = self.next_market_open(dt, exchange)
                if next_open:
                    context['next_market_open'] = next_open.isoformat()
                
                last_close = self.last_market_close(dt, exchange)
                if last_close:
                    context['last_market_close'] = last_close.isoformat()
            
            # Add holiday information if applicable
            if status == MarketStatus.HOLIDAY:
                holiday = self._get_holiday(dt.date(), exchange)
                if holiday:
                    context['holiday'] = {
                        'name': holiday.name,
                        'description': holiday.description,
                        'is_half_day': holiday.is_half_day
                    }
            
            # Add weekend information if applicable
            if status == MarketStatus.WEEKEND:
                context['weekend_info'] = {
                    'day_of_week': dt.strftime('%A'),
                    'next_trading_day': self.next_market_open(dt, exchange).isoformat() if self.next_market_open(dt, exchange) else None
                }
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting market context: {e}")
            return {
                'status': 'error',
                'is_open': False,
                'error': str(e)
            }
    
    def _is_holiday(self, check_date: date, exchange: Exchange) -> bool:
        """Check if a date is a holiday for the given exchange."""
        return any(
            holiday.date == check_date and holiday.exchange == exchange
            for holiday in self.holidays
        )
    
    def _get_holiday(self, check_date: date, exchange: Exchange) -> Optional[Holiday]:
        """Get holiday information for a date if it's a holiday."""
        for holiday in self.holidays:
            if holiday.date == check_date and holiday.exchange == exchange:
                return holiday
        return None
    
    def add_holiday(
        self, 
        name: str, 
        holiday_date: date, 
        exchange: Exchange,
        is_half_day: bool = False,
        half_day_close: Optional[time] = None,
        description: Optional[str] = None
    ):
        """Add a custom holiday to the calendar."""
        holiday = Holiday(
            name=name,
            date=holiday_date,
            exchange=exchange,
            is_half_day=is_half_day,
            half_day_close=half_day_close,
            description=description
        )
        self.holidays.append(holiday)
        logger.info(f"Added holiday: {name} on {holiday_date} for {exchange.value}")
    
    def get_trading_days(
        self, 
        start_date: date, 
        end_date: date, 
        exchange: Exchange = Exchange.NYSE
    ) -> List[date]:
        """
        Get list of trading days between start and end dates.
        
        Args:
            start_date: Start date
            end_date: End date
            exchange: Exchange to check
            
        Returns:
            List of trading dates
        """
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            if (current_date.weekday() < 5 and 
                not self._is_holiday(current_date, exchange)):
                trading_days.append(current_date)
            current_date += timedelta(days=1)
        
        return trading_days
    
    def get_market_hours_summary(self, exchange: Exchange = Exchange.NYSE) -> Dict[str, Any]:
        """Get summary of market hours for an exchange."""
        hours = self.market_hours[exchange]
        return {
            'exchange': exchange.value,
            'timezone': hours.timezone,
            'regular_hours': f"{hours.regular_open.strftime('%H:%M')} - {hours.regular_close.strftime('%H:%M')}",
            'pre_market': f"{hours.pre_market_open.strftime('%H:%M')} - {hours.regular_open.strftime('%H:%M')}",
            'after_hours': f"{hours.regular_close.strftime('%H:%M')} - {hours.after_hours_close.strftime('%H:%M')}",
            'total_trading_hours': 6.5,  # 6 hours 30 minutes
            'extended_hours': 8.0  # 8 hours total including pre/after
        }


# Global market calendar instance
market_calendar = MarketCalendar()


def is_market_open(
    dt: datetime, 
    exchange: Exchange = Exchange.NYSE,
    include_pre_after_hours: bool = False
) -> bool:
    """Convenience function to check if market is open."""
    return market_calendar.is_market_open(dt, exchange, include_pre_after_hours)


def get_market_status(
    dt: datetime, 
    exchange: Exchange = Exchange.NYSE
) -> MarketStatus:
    """Convenience function to get market status."""
    return market_calendar.get_market_status(dt, exchange)


def get_market_context(
    dt: datetime, 
    exchange: Exchange = Exchange.NYSE
) -> Dict[str, Any]:
    """Convenience function to get market context."""
    return market_calendar.get_market_context(dt, exchange)


def next_market_open(
    dt: datetime, 
    exchange: Exchange = Exchange.NYSE
) -> Optional[datetime]:
    """Convenience function to get next market open."""
    return market_calendar.next_market_open(dt, exchange)


def last_market_close(
    dt: datetime, 
    exchange: Exchange = Exchange.NYSE
) -> Optional[datetime]:
    """Convenience function to get last market close."""
    return market_calendar.last_market_close(dt, exchange) 