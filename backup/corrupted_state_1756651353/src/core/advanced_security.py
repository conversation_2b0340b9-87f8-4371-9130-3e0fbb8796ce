from datetime import datetime, timed<PERSON>ta
from enum import Enum, auto
import logging
from typing import Dict, Any, List, Optional

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request
import jwt
from pydantic import BaseModel
import pyotp


class UserRole(Enum):
    """Enumeration of user roles with hierarchical access"""
    ADMIN = "admin"
    TRADER = "trader"
    VIEWER = "viewer"

class RoleBasedAccessControl:
    """Comprehensive Role-Based Access Control System"""
    
    ROLE_HIERARCHY = {
        UserRole.ADMIN: 3,
        UserRole.TRADER: 2,
        UserRole.VIEWER: 1
    }
    
    @classmethod
    def has_permission(cls, current_role: UserRole, required_role: UserRole) -> bool:
        """
        Check if current role has sufficient permissions
        
        Args:
            current_role (UserRole): Current user's role
            required_role (UserRole): Required role for the action
        
        Returns:
            bool: Whether access is permitted
        """
        return cls.ROLE_HIERARCHY.get(current_role, 0) >= cls.ROLE_HIERARCHY.get(required_role, 0)

class MultiFactorAuthenticator:
    """Advanced Multi-Factor Authentication Manager"""
    
    def __init__(self, user):
        """
        Initialize MFA for a specific user
        
        Args:
            user: User object with MFA-related attributes
        """
        self.user = user
        self.mfa_methods = {
            'totp': self._validate_totp,
            'email': self._send_email_code,
            'sms': self._send_sms_code
        }
        self._verification_codes: Dict[str, Dict] = {} # In-memory cache for verification codes
    
    def generate_mfa_challenge(self, method: str = 'totp') -> Dict[str, str]:
        """
        Generate a multi-factor authentication challenge
        
        Args:
            method (str): MFA method (default: TOTP)
        
        Returns:
            Dict[str, str]: Challenge details
        """
        if method not in self.mfa_methods:
            raise ValueError(f"Unsupported MFA method: {method}")
        
        return self.mfa_methods[method]()
    
    def _validate_totp(self) -> Dict[str, str]:
        """
        Generate TOTP (Time-based One-Time Password) challenge
        
        Returns:
            Dict[str, str]: TOTP challenge details
        """
        # Generate a secret for the user if not exists
        if not hasattr(self.user, 'mfa_secret') or not self.user.mfa_secret:
            self.user.mfa_secret = pyotp.random_base32()
        
        totp = pyotp.TOTP(self.user.mfa_secret)
        return {
            'challenge_type': 'totp',
            'qr_code': totp.provisioning_uri(
                name=self.user.email, 
                issuer_name='TradingBot'
            )
        }
    
    def _send_email_code(self) -> Dict[str, str]:
        """
        Generate and send email verification code
        
        Returns:
            Dict[str, str]: Email verification challenge
        """
        try:
            # Generate a secure verification code
            verification_code = pyotp.random_base32()[:6]
            
            # Store the code securely with expiration
            self._store_verification_code('email', verification_code)
            
            # TODO: Implement actual email sending service integration
            # For now, log the code for development/testing
            logging.info(f"Email verification code generated for user {self.user.email}: {verification_code}")
            
            return {
                'challenge_type': 'email',
                'message': 'Verification code sent to email',
                'status': 'success'
            }
        except Exception as e:
            logging.error(f"Failed to send email verification code: {e}")
            return {
                'challenge_type': 'email',
                'message': 'Failed to send verification code',
                'status': 'error',
                'error': str(e)
            }
    
    def _send_sms_code(self) -> Dict[str, str]:
        """
        Generate and send SMS verification code
        
        Returns:
            Dict[str, str]: SMS verification challenge
        """
        try:
            # Generate a secure verification code
            verification_code = pyotp.random_base32()[:6]
            
            # Store the code securely with expiration
            self._store_verification_code('sms', verification_code)
            
            # TODO: Implement actual SMS service integration (Twilio, etc.)
            # For now, log the code for development/testing
            logging.info(f"SMS verification code generated for user {self.user.phone}: {verification_code}")
            
            return {
                'challenge_type': 'sms',
                'message': 'Verification code sent via SMS',
                'status': 'success'
            }
        except Exception as e:
            logging.error(f"Failed to send SMS verification code: {e}")
            return {
                'challenge_type': 'sms',
                'message': 'Failed to send verification code',
                'status': 'error',
                'error': str(e)
            }
    
    def _store_verification_code(self, method: str, code: str) -> None:
        """
        Store verification code securely with expiration
        
        Args:
            method (str): Verification method (email/sms)
            code (str): Verification code to store
        """
        try:
            # Store in secure cache with 10-minute expiration
            cache_key = f"verification_{method}_{self.user.id}"
            # TODO: Implement secure cache storage (Redis with encryption)
            # For now, use in-memory storage with expiration
            self._verification_codes[cache_key] = {
                'code': code,
                'expires_at': datetime.now() + timedelta(minutes=10),
                'attempts': 0
            }
        except Exception as e:
            logging.error(f"Failed to store verification code: {e}")
            raise
    
    def verify_mfa_token(self, method: str, token: str) -> bool:
        """
        Verify multi-factor authentication token
        
        Args:
            method (str): MFA method
            token (str): Token to verify
        
        Returns:
            bool: Whether the token is valid
        """
        if method == 'totp':
            totp = pyotp.TOTP(self.user.mfa_secret)
            return totp.verify(token)
        
        # Add other verification methods
        return False

class AdaptiveRateLimiter:
    """
    Intelligent, adaptive rate limiting system
    
    Dynamically adjusts rate limits based on user behavior
    """
    
    def __init__(self, base_limit: int = 100, learning_rate: float = 0.1):
        """
        Initialize Adaptive Rate Limiter
        
        Args:
            base_limit (int): Initial request limit
            learning_rate (float): Rate of limit adjustment
        """
        self.request_history: Dict[str, List[Dict]] = {}
        self.base_limit = base_limit
        self.learning_rate = learning_rate
    
    def is_allowed(self, user_id: str, request: Request) -> bool:
        """
        Determine if a request is allowed based on adaptive rate limiting
        
        Args:
            user_id (str): Unique user identifier
            request (Request): Incoming request object
        
        Returns:
            bool: Whether the request is allowed
        """
        current_time = datetime.now()
        user_requests = self.request_history.get(user_id, [])
        
        # Remove expired requests (last minute)
        user_requests = [
            req for req in user_requests 
            if current_time - req['timestamp'] < timedelta(minutes=1)
        ]
        
        # Calculate dynamic limit based on recent behavior
        dynamic_limit = self._calculate_dynamic_limit(user_requests, user_id)
        
        # If requests exceed dynamic limit, block
        if len(user_requests) >= dynamic_limit:
            return False
        
        # Record this request
        user_requests.append({
            'timestamp': current_time,
            'endpoint': str(request.url.path)
        })
        self.request_history[user_id] = user_requests
        
        return True
    
    def _calculate_dynamic_limit(self, requests: List[Dict], user_id: str) -> int:
        """
        Calculate dynamic rate limit based on request patterns
        
        Args:
            requests (List[Dict]): Recent request history
            user_id (str): Unique user identifier
        
        Returns:
            int: Dynamically adjusted rate limit
        """
        # If requests exceed base limit, start reducing
        if len(requests) >= self.base_limit:
            # Reduce the number of stored requests
            reduced_requests = requests[:int(self.base_limit * 0.5)]
            self.request_history[user_id] = reduced_requests
            return int(self.base_limit * 0.5)
        
        return self.base_limit

def role_required(required_role: Optional[UserRole] = None):
    """
    Flexible security decorator that allows configurable access control
    
    Args:
        required_role (Optional[UserRole]): Minimum role required to access the endpoint
    
    Returns:
        Callable: Decorator with intelligent access control
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Log the access attempt
            logging.info(f"Accessing {func.__name__} with role requirement: {required_role}")
            
            # If no specific role is required, allow access
            if required_role is None:
                return await func(*args, **kwargs)
            
            # Retrieve current user's role (placeholder for now)
            current_user_role = UserRole.VIEWER
            
            # Intelligent access control
            if RoleBasedAccessControl.has_permission(current_user_role, required_role):
                return await func(*args, **kwargs)
            else:
                # Log security event
                logging.warning(f"Access denied to {func.__name__}. Required: {required_role}, Current: {current_user_role}")
                
                # Provide a more informative error
                raise HTTPException(
                    status_code=403, 
                    detail=f"Insufficient permissions. Required role: {required_role}"
                )
        
        return wrapper
    return decorator 