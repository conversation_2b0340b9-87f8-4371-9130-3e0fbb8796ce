"""
Bot Health Monitoring Configuration
Handles the integration between bot and monitoring system
"""

from typing import Optional

from src.api.routes.bot_health import bot_health_monitor
from src.api.routes.bot_health import detailed_health_check
from src.bot.client import TradingBot
        
        try:
            health_data = await detailed_health_check()
            return {
                "status": health_data["overall_status"],
                "bot_connected": health_data["details"]["bot"]["bot"]["connected"],
                "uptime": health_data["details"]["bot"]["uptime"]["process"],
                "timestamp": health_data["timestamp"]
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": str(datetime.utcnow())
            }

# Global monitoring instance
bot_monitor = BotMonitorConfig()