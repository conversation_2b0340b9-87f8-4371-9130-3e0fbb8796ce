"""
Unified Exception Hierarchy for Trading Automation System

Consolidates all exception classes into a single, comprehensive hierarchy
that provides consistent error handling across all components.
"""

from datetime import datetime
from enum import Enum
import sys
import traceback
from typing import Optional, Dict, Any, List


class ErrorCategory(Enum):
    """Categories of errors that can occur in the system."""
    GENERAL = "general"
    CONFIGURATION = "configuration"
    VALIDATION = "validation"
    DATA_PROVIDER = "data_provider"
    PIPELINE = "pipeline"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NETWORK = "network"
    DATABASE = "database"
    EXTERNAL_API = "external_api"


class ErrorSeverity(Enum):
    """Severity levels for errors."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TradingBotBaseException(Exception):
    """
    Base exception class for all trading bot errors.
    
    Provides consistent error handling with context, severity, and categorization.
    """
    
    def __init__(
        self, 
        message: str, 
        category: ErrorCategory = ErrorCategory.GENERAL,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[str] = None,
        original_error: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details
        self.original_error = original_error
        self.context = context or {}
        self.timestamp = self._get_timestamp()
        self.traceback = self._get_traceback()
        
        # Store any additional kwargs as context
        for key, value in kwargs.items():
            if key not in ['message', 'category', 'severity', 'details', 'original_error', 'context']:
                self.context[key] = value
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        return datetime.utcnow().isoformat()
    
    def _get_traceback(self) -> str:
        """Get current traceback for debugging"""
        return ''.join(traceback.format_tb(sys.exc_info()[2])) if sys.exc_info()[2] else ''
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization"""
        return {
            'message': self.message,
            'category': self.category.value,
            'severity': self.severity.value,
            'details': self.details,
            'original_error': str(self.original_error) if self.original_error else None,
            'context': self.context,
            'timestamp': self.timestamp,
            'traceback': self.traceback,
            'exception_type': self.__class__.__name__
        }
    
    def __str__(self) -> str:
        """String representation with context"""
        base = f"{self.__class__.__name__}: {self.message}"
        if self.context:
            context_str = ', '.join(f"{k}={v}" for k, v in self.context.items())
            base += f" (Context: {context_str})"
        return base


# Configuration and Validation Errors
class ConfigurationError(TradingBotBaseException):
    """Configuration-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.CONFIGURATION, **kwargs)


class ValidationError(TradingBotBaseException):
    """Data validation errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.VALIDATION, **kwargs)


class ConfigValidationError(ValidationError):
    """Configuration validation errors"""
    pass


# Pipeline Errors
class PipelineError(TradingBotBaseException):
    """Base class for pipeline-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.PIPELINE, **kwargs)


class StageExecutionError(PipelineError):
    """Errors during pipeline stage execution"""
    def __init__(self, stage_name: str, message: str, **kwargs):
        super().__init__(message, context={'stage_name': stage_name}, **kwargs)


class PipelineTimeoutError(PipelineError):
    """Pipeline execution timeout"""
    def __init__(self, timeout_seconds: int, **kwargs):
        super().__init__(
            f"Pipeline execution timed out after {timeout_seconds} seconds",
            severity=ErrorSeverity.HIGH,
            context={'timeout_seconds': timeout_seconds},
            **kwargs
        )


# Data Provider Errors
class DataProviderError(TradingBotBaseException):
    """Base class for data provider errors"""
    def __init__(self, message: str, provider_name: str = None, **kwargs):
        context = kwargs.get('context', {})
        if provider_name:
            context['provider_name'] = provider_name
        super().__init__(message, ErrorCategory.DATA_PROVIDER, context=context, **kwargs)


class ProviderError(DataProviderError):
    """Generic provider error (alias for DataProviderError)"""
    pass


class DataProviderTimeoutError(DataProviderError):
    """Data provider timeout errors"""
    def __init__(self, provider_name: str, timeout_seconds: float, **kwargs):
        super().__init__(
            f"Data provider {provider_name} timed out after {timeout_seconds} seconds",
            provider_name=provider_name,
            severity=ErrorSeverity.MEDIUM,
            context={'timeout_seconds': timeout_seconds},
            **kwargs
        )


class ProviderTimeoutError(DataProviderTimeoutError):
    """Provider timeout error (alias for DataProviderTimeoutError)"""
    pass


class DataProviderRateLimitError(DataProviderError):
    """Data provider rate limit errors"""
    def __init__(self, provider_name: str, retry_after: int = None, **kwargs):
        context = {'retry_after': retry_after} if retry_after else {}
        super().__init__(
            f"Data provider {provider_name} rate limit exceeded",
            provider_name=provider_name,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )


# Authentication and Authorization Errors
class AuthenticationError(TradingBotBaseException):
    """Authentication-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.AUTHENTICATION, **kwargs)


class AuthorizationError(TradingBotBaseException):
    """Authorization-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.AUTHORIZATION, **kwargs)


# Network and External API Errors
class NetworkError(TradingBotBaseException):
    """Network-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.NETWORK, **kwargs)


class ExternalAPIError(TradingBotBaseException):
    """External API errors"""
    def __init__(self, message: str, api_name: str = None, **kwargs):
        context = kwargs.get('context', {})
        if api_name:
            context['api_name'] = api_name
        super().__init__(message, ErrorCategory.EXTERNAL_API, context=context, **kwargs)


# Database Errors
class DatabaseError(TradingBotBaseException):
    """Database-related errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DATABASE, **kwargs)


class DatabaseConnectionError(DatabaseError):
    """Database connection errors"""
    pass


class DatabaseQueryError(DatabaseError):
    """Database query errors"""
    pass


# General System Errors
class SystemError(TradingBotBaseException):
    """General system errors"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.GENERAL, **kwargs)


class ResourceNotFoundError(TradingBotBaseException):
    """Resource not found errors"""
    def __init__(self, resource_type: str, resource_id: str, **kwargs):
        super().__init__(
            f"{resource_type} with ID '{resource_id}' not found",
            context={'resource_type': resource_type, 'resource_id': resource_id},
            **kwargs
        )


class TimeoutError(TradingBotBaseException):
    """General timeout errors"""
    def __init__(self, operation: str, timeout_seconds: float, **kwargs):
        super().__init__(
            f"Operation '{operation}' timed out after {timeout_seconds} seconds",
            context={'operation': operation, 'timeout_seconds': timeout_seconds},
            **kwargs
        )


# Utility functions for error handling
def create_error_context(**kwargs) -> Dict[str, Any]:
    """Create a standardized error context dictionary."""
    return kwargs


def format_error_message(error: Exception, include_traceback: bool = False) -> str:
    """Format an error message with optional traceback."""
    if isinstance(error, TradingBotBaseException):
        message = f"{error.__class__.__name__}: {error.message}"
        if error.context:
            context_str = ', '.join(f"{k}={v}" for k, v in error.context.items())
            message += f" (Context: {context_str})"
        if include_traceback and error.traceback:
            message += f"\nTraceback:\n{error.traceback}"
        return message
    else:
        return f"{error.__class__.__name__}: {str(error)}"


def is_critical_error(error: Exception) -> bool:
    """Check if an error is critical based on severity."""
    if isinstance(error, TradingBotBaseException):
        return error.severity == ErrorSeverity.CRITICAL
    return False


def should_retry_error(error: Exception) -> bool:
    """Determine if an error should be retried."""
    if isinstance(error, TradingBotBaseException):
        # Don't retry critical errors or configuration errors
        if error.severity == ErrorSeverity.CRITICAL:
            return False
        if error.category == ErrorCategory.CONFIGURATION:
            return False
        # Retry network and timeout errors
        if error.category in [ErrorCategory.NETWORK, ErrorCategory.DATA_PROVIDER]:
            return True
    return False