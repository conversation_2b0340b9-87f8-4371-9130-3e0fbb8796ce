"""
Configuration validation for critical trading parameters.
Prevents invalid fallback values from being used in production.
"""

from dataclasses import fields
import logging
import os
from typing import Dict, Any, <PERSON><PERSON>, Optional

from config_manager import TradingStrategyConfig, TradingBotConfig

        
        # Create config instance
        config = TradingBotConfig()
        trading_config = config.get('trading_strategy')
        
        # Validate trading strategy
        config_valid, config_errors = ConfigValidator.validate_trading_strategy(trading_config)
        if not config_valid:
            logger.error(f"Configuration validation errors: {config_errors}")
            return False
        
        logger.info("Configuration validation completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False 