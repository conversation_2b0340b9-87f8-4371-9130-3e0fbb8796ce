from collections import defaultdict
from datetime import datetime, timedelta
import json
import os
import re
from typing import Dict, Any, List, Optional, Tuple

import uuid


class ResponseFeedbackCollector:
    """
    Enhanced feedback collector with detailed analytics including sentiment analysis,
    confidence tracking, response length metrics, and comprehensive trend analysis
    """
    def __init__(self, feedback_file: str = 'response_feedback.json'):
        self.feedback_file = feedback_file
        self.feedback_data = self._load_feedback()
        
        # Simple sentiment analysis word lists
        self.positive_words = {
            'good', 'great', 'excellent', 'awesome', 'fantastic', 'perfect',
            'amazing', 'wonderful', 'brilliant', 'superb', 'outstanding',
            'helpful', 'useful', 'accurate', 'precise', 'insightful', 'valuable'
        }
        
        self.negative_words = {
            'bad', 'poor', 'terrible', 'awful', 'horrible', 'useless',
            'wrong', 'inaccurate', 'misleading', 'confusing', 'vague',
            'unhelpful', 'disappointing', 'frustrating', 'incorrect'
        }
    
    def _load_feedback(self) -> Dict[str, Any]:
        """Load existing feedback data with enhanced structure"""
        if os.path.exists(self.feedback_file):
            try:
                with open(self.feedback_file, 'r') as f:
                    data = json.load(f)
                    # Ensure backward compatibility with new fields
                    return self._migrate_legacy_data(data)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        return self._get_default_feedback_structure()
    
    def _get_default_feedback_structure(self) -> Dict[str, Any]:
        """Return the default enhanced feedback data structure"""
        return {
            'total_feedback_entries': 0,
            'feedback_by_type': {},
            'average_ratings': {},
            'feedback_trends': {
                'daily': {},
                'weekly': {},
                'monthly': {}
            },
            'sentiment_analysis': {
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'average_sentiment_score': 0.0,
                'sentiment_by_type': {}
            },
            'confidence_metrics': {
                'total_confidence': 0.0,
                'confidence_count': 0,
                'average_confidence': 0.0,
                'confidence_by_rating': defaultdict(lambda: {'total': 0.0, 'count': 0}),
                'confidence_by_type': {}
            },
            'response_length_metrics': {
                'total_length': 0,
                'length_count': 0,
                'average_length': 0.0,
                'length_by_rating': defaultdict(lambda: {'total': 0, 'count': 0}),
                'length_by_type': {}
            }
        }
    
    def _migrate_legacy_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Migrate legacy feedback data to new structure"""
        default_structure = self._get_default_feedback_structure()
        
        # Migrate basic fields
        for key in ['total_feedback_entries', 'feedback_by_type', 'average_ratings']:
            if key in data:
                default_structure[key] = data[key]
        
        # Migrate trends
        if 'feedback_trends' in data:
            if isinstance(data['feedback_trends'], dict):
                default_structure['feedback_trends']['daily'] = data['feedback_trends']
        
        return default_structure
    
    def _save_feedback(self):
        """Save feedback data to file"""
        # Ensure directory exists
        os.makedirs(os.path.dirname(self.feedback_file), exist_ok=True)
        
        with open(self.feedback_file, 'w') as f:
            # Convert defaultdict to regular dict for JSON serialization
            serializable_data = json.loads(json.dumps(self.feedback_data, default=self._serialize_defaultdict))
            json.dump(serializable_data, f, indent=2)
    
    def _serialize_defaultdict(self, obj):
        """Helper to serialize defaultdict for JSON"""
        if isinstance(obj, defaultdict):
            return dict(obj)
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def record_feedback(
        self, 
        response: Dict[str, Any], 
        rating: int, 
        user_comment: str = '', 
        feedback_type: str = 'general'
    ) -> str:
        """
        Record user feedback for a specific response with enhanced analytics
        
        Args:
            response (dict): The original AI-generated response
            rating (int): User rating (1-5)
            user_comment (str, optional): Additional user feedback
            feedback_type (str, optional): Type of feedback
        
        Returns:
            str: Unique feedback ID
        """
        # Validate rating
        if rating < 1 or rating > 5:
            raise ValueError("Rating must be between 1 and 5")
        
        # Generate unique feedback ID
        feedback_id = str(uuid.uuid4())
        timestamp = datetime.now()
        
        # Extract response metadata
        response_type = response.get('response_type', 'unknown')
        confidence = response.get('metadata', {}).get('confidence', 0.0)
        response_text = response.get('response', '')
        response_length = len(response_text)
        
        # Analyze sentiment
        sentiment_score, sentiment_label = self._analyze_sentiment(user_comment)
        
        # Update feedback data
        self.feedback_data['total_feedback_entries'] += 1
        
        # Track feedback by response type
        if response_type not in self.feedback_data['feedback_by_type']:
            self.feedback_data['feedback_by_type'][response_type] = {
                'total_entries': 0,
                'total_rating': 0,
                'average_rating': 0,
                'sentiment_scores': [],
                'confidence_scores': [],
                'response_lengths': []
            }
        
        type_feedback = self.feedback_data['feedback_by_type'][response_type]
        type_feedback['total_entries'] += 1
        type_feedback['total_rating'] += rating
        type_feedback['average_rating'] = (
            type_feedback['total_rating'] / type_feedback['total_entries']
        )
        type_feedback['sentiment_scores'].append(sentiment_score)
        type_feedback['confidence_scores'].append(confidence)
        type_feedback['response_lengths'].append(response_length)
        
        # Update overall average ratings
        self.feedback_data['average_ratings'][response_type] = type_feedback['average_rating']
        
        # Track feedback trends (daily, weekly, monthly)
        self._update_trends(timestamp, rating, response_type, sentiment_score, confidence, response_length)
        
        # Update sentiment analysis
        self._update_sentiment_analysis(sentiment_score, sentiment_label, response_type)
        
        # Update confidence metrics
        self._update_confidence_metrics(confidence, rating, response_type)
        
        # Update response length metrics
        self._update_length_metrics(response_length, rating, response_type)
        
        # Save feedback
        self._save_feedback()
        
        return feedback_id
    
    def _analyze_sentiment(self, comment: str) -> Tuple[float, str]:
        """
        Analyze sentiment of user comment using simple word matching
        
        Returns:
            tuple: (sentiment_score, sentiment_label) where score is between -1 and 1,
                   and label is 'positive', 'negative', or 'neutral'
        """
        if not comment:
            return 0.0, 'neutral'
        
        # Simple word-based sentiment analysis
        words = re.findall(r'\w+', comment.lower())
        positive_count = sum(1 for word in words if word in self.positive_words)
        negative_count = sum(1 for word in words if word in self.negative_words)
        
        total_relevant = positive_count + negative_count
        if total_relevant == 0:
            return 0.0, 'neutral'
        
        sentiment_score = (positive_count - negative_count) / total_relevant
        
        if sentiment_score > 0:
            sentiment_label = 'positive'
        elif sentiment_score < 0:
            sentiment_label = 'negative'
        else:
            sentiment_label = 'neutral'
        
        return sentiment_score, sentiment_label
    
    def _update_trends(self, timestamp: datetime, rating: int, response_type: str, 
                      sentiment_score: float, confidence: float, response_length: int):
        """Update daily, weekly, and monthly trend data"""
        # Daily trends
        day_key = timestamp.strftime('%Y-%m-%d')
        if day_key not in self.feedback_data['feedback_trends']['daily']:
            self.feedback_data['feedback_trends']['daily'][day_key] = {
                'total_entries': 0,
                'average_rating': 0,
                'total_rating': 0,
                'ratings_distribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
                'average_sentiment': 0.0,
                'total_sentiment': 0.0,
                'average_confidence': 0.0,
                'total_confidence': 0.0,
                'average_length': 0.0,
                'total_length': 0,
                'response_types': defaultdict(int)
            }
        
        daily_data = self.feedback_data['feedback_trends']['daily'][day_key]
        daily_data['total_entries'] += 1
        daily_data['total_rating'] += rating
        daily_data['average_rating'] = daily_data['total_rating'] / daily_data['total_entries']
        daily_data['ratings_distribution'][rating] += 1
        daily_data['total_sentiment'] += sentiment_score
        daily_data['average_sentiment'] = daily_data['total_sentiment'] / daily_data['total_entries']
        daily_data['total_confidence'] += confidence
        daily_data['average_confidence'] = daily_data['total_confidence'] / daily_data['total_entries']
        daily_data['total_length'] += response_length
        daily_data['average_length'] = daily_data['total_length'] / daily_data['total_entries']
        daily_data['response_types'][response_type] += 1
        
        # Weekly trends (ISO week)
        week_key = f"{timestamp.isocalendar()[0]}-W{timestamp.isocalendar()[1]:02d}"
        if week_key not in self.feedback_data['feedback_trends']['weekly']:
            self.feedback_data['feedback_trends']['weekly'][week_key] = {
                'total_entries': 0,
                'average_rating': 0,
                'total_rating': 0,
                'ratings_distribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
                'response_types': defaultdict(int)
            }
        
        weekly_data = self.feedback_data['feedback_trends']['weekly'][week_key]
        weekly_data['total_entries'] += 1
        weekly_data['total_rating'] += rating
        weekly_data['average_rating'] = weekly_data['total_rating'] / weekly_data['total_entries']
        weekly_data['ratings_distribution'][rating] += 1
        weekly_data['response_types'][response_type] += 1
        
        # Monthly trends
        month_key = timestamp.strftime('%Y-%m')
        if month_key not in self.feedback_data['feedback_trends']['monthly']:
            self.feedback_data['feedback_trends']['monthly'][month_key] = {
                'total_entries': 0,
                'average_rating': 0,
                'total_rating': 0,
                'ratings_distribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
                'response_types': defaultdict(int)
            }
        
        monthly_data = self.feedback_data['feedback_trends']['monthly'][month_key]
        monthly_data['total_entries'] += 1
        monthly_data['total_rating'] += rating
        monthly_data['average_rating'] = monthly_data['total_rating'] / monthly_data['total_entries']
        monthly_data['ratings_distribution'][rating] += 1
        monthly_data['response_types'][response_type] += 1
    
    def _update_sentiment_analysis(self, sentiment_score: float, sentiment_label: str, response_type: str):
        """Update sentiment analysis metrics"""
        sentiment_data = self.feedback_data['sentiment_analysis']
        
        # Update overall sentiment counts
        if sentiment_label == 'positive':
            sentiment_data['positive_count'] += 1
        elif sentiment_label == 'negative':
            sentiment_data['negative_count'] += 1
        else:
            sentiment_data['neutral_count'] += 1
        
        # Update average sentiment score
        total_sentiment = (sentiment_data.get('positive_count', 0) * 1.0 + 
                          sentiment_data.get('negative_count', 0) * -1.0)
        total_entries = (sentiment_data.get('positive_count', 0) + 
                        sentiment_data.get('negative_count', 0) + 
                        sentiment_data.get('neutral_count', 0))
        
        if total_entries > 0:
            sentiment_data['average_sentiment_score'] = total_sentiment / total_entries
        
        # Update sentiment by response type
        if response_type not in sentiment_data['sentiment_by_type']:
            sentiment_data['sentiment_by_type'][response_type] = {
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'average_sentiment': 0.0
            }
        
        type_sentiment = sentiment_data['sentiment_by_type'][response_type]
        if sentiment_label == 'positive':
            type_sentiment['positive_count'] += 1
        elif sentiment_label == 'negative':
            type_sentiment['negative_count'] += 1
        else:
            type_sentiment['neutral_count'] += 1
        
        total_type_sentiment = (type_sentiment['positive_count'] * 1.0 + 
                               type_sentiment['negative_count'] * -1.0)
        total_type_entries = (type_sentiment['positive_count'] + 
                             type_sentiment['negative_count'] + 
                             type_sentiment['neutral_count'])
        
        if total_type_entries > 0:
            type_sentiment['average_sentiment'] = total_type_sentiment / total_type_entries
    
    def _update_confidence_metrics(self, confidence: float, rating: int, response_type: str):
        """Update confidence-related metrics"""
        conf_data = self.feedback_data['confidence_metrics']
        
        # Update overall confidence metrics
        conf_data['total_confidence'] += confidence
        conf_data['confidence_count'] += 1
        if conf_data['confidence_count'] > 0:
            conf_data['average_confidence'] = conf_data['total_confidence'] / conf_data['confidence_count']
        
        # Update confidence by rating
        rating_conf = conf_data['confidence_by_rating'][rating]
        rating_conf['total'] += confidence
        rating_conf['count'] += 1
        
        # Update confidence by response type
        if response_type not in conf_data['confidence_by_type']:
            conf_data['confidence_by_type'][response_type] = {
                'total_confidence': 0.0,
                'count': 0,
                'average_confidence': 0.0
            }
        
        type_conf = conf_data['confidence_by_type'][response_type]
        type_conf['total_confidence'] += confidence
        type_conf['count'] += 1
        if type_conf['count'] > 0:
            type_conf['average_confidence'] = type_conf['total_confidence'] / type_conf['count']
    
    def _update_length_metrics(self, length: int, rating: int, response_type: str):
        """Update response length metrics"""
        length_data = self.feedback_data['response_length_metrics']
        
        # Update overall length metrics
        length_data['total_length'] += length
        length_data['length_count'] += 1
        if length_data['length_count'] > 0:
            length_data['average_length'] = length_data['total_length'] / length_data['length_count']
        
        # Update length by rating
        rating_length = length_data['length_by_rating'][rating]
        rating_length['total'] += length
        rating_length['count'] += 1
        
        # Update length by response type
        if response_type not in length_data['length_by_type']:
            length_data['length_by_type'][response_type] = {
                'total_length': 0,
                'count': 0,
                'average_length': 0.0
            }
        
        type_length = length_data['length_by_type'][response_type]
        type_length['total_length'] += length
        type_length['count'] += 1
        if type_length['count'] > 0:
            type_length['average_length'] = type_length['total_length'] / type_length['count']
    
    def get_feedback_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive feedback report with enhanced analytics
        
        Returns:
            dict: Detailed feedback report including sentiment, confidence, and length metrics
        """
        report = {
            'total_feedback_entries': self.feedback_data['total_feedback_entries'],
            'feedback_by_type': self.feedback_data['feedback_by_type'],
            'average_ratings': self.feedback_data['average_ratings'],
            'feedback_trends': self.feedback_data['feedback_trends'],
            'sentiment_analysis': self.feedback_data['sentiment_analysis'],
            'confidence_metrics': dict(self.feedback_data['confidence_metrics']),
            'response_length_metrics': dict(self.feedback_data['response_length_metrics']),
            'timestamp': datetime.now().isoformat()
        }
        
        # Add derived insights
        report['insights'] = self._generate_insights()
        
        return report
    
    def _generate_insights(self) -> Dict[str, Any]:
        """Generate actionable insights from the feedback data"""
        insights = {}
        
        # Confidence vs Rating correlation
        conf_data = self.feedback_data['confidence_metrics']
        if conf_data['confidence_by_rating']:
            rating_corr = {}
            for rating, data in conf_data['confidence_by_rating'].items():
                if data['count'] > 0:
                    rating_corr[rating] = data['total'] / data['count']
            insights['confidence_by_rating'] = rating_corr
        
        # Length vs Rating correlation
        length_data = self.feedback_data['response_length_metrics']
        if length_data['length_by_rating']:
            length_corr = {}
            for rating, data in length_data['length_by_rating'].items():
                if data['count'] > 0:
                    length_corr[rating] = data['total'] / data['count']
            insights['length_by_rating'] = length_corr
        
        # Sentiment by response type
        sentiment_data = self.feedback_data['sentiment_analysis']
        if sentiment_data['sentiment_by_type']:
            insights['sentiment_by_type'] = sentiment_data['sentiment_by_type']
        
        return insights
    
    def get_detailed_analysis(self, days: int = 30) -> Dict[str, Any]:
        """
        Get detailed analysis for a specific time period
        
        Args:
            days (int): Number of days to analyze (default: 30)
        
        Returns:
            dict: Detailed analysis including trends, correlations, and recommendations
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        analysis = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'summary': {
                'total_feedback': 0,
                'average_rating': 0.0,
                'average_sentiment': 0.0,
                'average_confidence': 0.0
            },
            'trends': {},
            'correlations': {},
            'recommendations': []
        }
        
        # Analyze daily trends for the period
        daily_trends = {}
        current_date = start_date
        while current_date <= end_date:
            day_key = current_date.strftime('%Y-%m-%d')
            if day_key in self.feedback_data['feedback_trends']['daily']:
                daily_trends[day_key] = self.feedback_data['feedback_trends']['daily'][day_key]
                analysis['summary']['total_feedback'] += daily_trends[day_key]['total_entries']
                analysis['summary']['average_rating'] += daily_trends[day_key]['total_rating']
                analysis['summary']['average_sentiment'] += daily_trends[day_key]['total_sentiment']
                analysis['summary']['average_confidence'] += daily_trends[day_key]['total_confidence']
            current_date += timedelta(days=1)
        
        # Calculate averages
        if analysis['summary']['total_feedback'] > 0:
            analysis['summary']['average_rating'] /= analysis['summary']['total_feedback']
            analysis['summary']['average_sentiment'] /= analysis['summary']['total_feedback']
            analysis['summary']['average_confidence'] /= analysis['summary']['total_feedback']
        
        analysis['trends']['daily'] = daily_trends
        
        # Generate recommendations based on analysis
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on feedback analysis"""
        recommendations = []
        
        # Example recommendations based on data
        if analysis['summary']['average_rating'] < 3.0:
            recommendations.append("Consider improving response quality as average rating is low")
        
        if analysis['summary']['average_confidence'] < 0.6:
            recommendations.append("Work on increasing confidence scores for better user trust")
        
        if analysis['summary']['average_sentiment'] < 0:
            recommendations.append("Address negative sentiment in user feedback")
        
        return recommendations
    
    def reset_feedback(self):
        """Reset all collected feedback data"""
        self.feedback_data = self._get_default_feedback_structure()
        self._save_feedback()

# Global feedback collector
response_feedback_collector = ResponseFeedbackCollector()