"""
Text Parser for TradingView Pine Script Alerts
Parses human-readable alert messages into structured data
"""

from dataclasses import dataclass
from datetime import datetime
import re
from typing import Optional, Dict, Any



@dataclass
class ParsedAlert:
    """Parsed TradingView alert data"""
    symbol: str
    alert_type: str
    signal: str
    timestamp: int
    timeframe: str
    entry_price: Optional[float]
    tp1_price: Optional[float]
    tp2_price: Optional[float]
    tp3_price: Optional[float]
    sl_price: Optional[float]
    raw_text: str


class PineScriptAlertParser:
    """Parser for TradingView Pine Script alerts with uniform format"""
    
    def __init__(self):
        # The new format is: TYPE|SIGNAL|TIME|SYMBOL|TIMEFRAME|ENTRY|TP1|TP2|TP3|SL
        pass
    
    def parse_alert(self, text: str) -> ParsedAlert:
        """Parse TradingView alert text into structured data"""
        if not text or not text.strip():
            raise ValueError("Empty alert text")
        
        # Clean the text
        clean_text = text.strip()
        
        # Check if this is the new uniform format (pipe-separated)
        if '|' in clean_text and clean_text.count('|') >= 9:
            return self._parse_uniform_format(clean_text)
        else:
            # Fallback to old format parsing
            return self._parse_legacy_format(clean_text)
    
    def _parse_uniform_format(self, text: str) -> ParsedAlert:
        """Parse the new uniform pipe-separated format"""
        try:
            parts = text.split('|')
            if len(parts) < 10:
                raise ValueError(f"Expected 10 parts, got {len(parts)}")
            
            alert_type = parts[0].strip()
            signal = parts[1].strip()
            timestamp = int(parts[2].strip())
            symbol = parts[3].strip()
            timeframe = parts[4].strip()
            
            # Parse prices (handle potential empty values)
            entry_price = self._safe_float(parts[5])
            tp1_price = self._safe_float(parts[6])
            tp2_price = self._safe_float(parts[7])
            tp3_price = self._safe_float(parts[8])
            sl_price = self._safe_float(parts[9])
            
            # Create parsed alert
            parsed_alert = ParsedAlert(
                symbol=symbol,
                alert_type=alert_type,
                signal=signal,
                timestamp=timestamp,
                timeframe=timeframe,
                entry_price=entry_price,
                tp1_price=tp1_price,
                tp2_price=tp2_price,
                tp3_price=tp3_price,
                sl_price=sl_price,
                raw_text=text
            )
            
            return parsed_alert
            
        except Exception as e:
            raise ValueError(f"Failed to parse uniform format: {str(e)}")
    
    def _parse_legacy_format(self, text: str) -> ParsedAlert:
        """Parse legacy emoji-based format as fallback"""
        # Legacy patterns for extracting data from alert text
        patterns = {
            'symbol': r'\$([A-Z]+)',
            'timeframe': r'\(([^)]+)\)',
            'entry_price': r'Entry:\s*([\d.]+)',
            'tp1_price': r'TP1:\s*([\d.]+)',
            'tp2_price': r'TP2:\s*([\d.]+)',
            'tp3_price': r'TP3:\s*([\d.]+)',
            'sl_price': r'SL:\s*([\d.]+)',
        }
        
        # Extract basic information
        symbol = self._extract_with_pattern(text, patterns['symbol'], 'UNKNOWN')
        timeframe = self._extract_with_pattern(text, patterns['timeframe'], 'UNKNOWN')
        alert_type = self._determine_legacy_alert_type(text)
        signal = self._extract_legacy_signal(text)
        
        # Extract prices
        entry_price = self._extract_price_with_pattern(text, patterns['entry_price'])
        tp1_price = self._extract_price_with_pattern(text, patterns['tp1_price'])
        tp2_price = self._extract_price_with_pattern(text, patterns['tp2_price'])
        tp3_price = self._extract_price_with_pattern(text, patterns['tp3_price'])
        sl_price = self._extract_price_with_pattern(text, patterns['sl_price'])
        
        # Create parsed alert with current timestamp
        parsed_alert = ParsedAlert(
            symbol=symbol,
            alert_type=alert_type,
            signal=signal,
            timestamp=int(datetime.now().timestamp()),
            timeframe=timeframe,
            entry_price=entry_price,
            tp1_price=tp1_price,
            tp2_price=tp2_price,
            tp3_price=tp3_price,
            sl_price=sl_price,
            raw_text=text
        )
        
        return parsed_alert
    
    def _safe_float(self, value: str) -> Optional[float]:
        """Safely convert string to float"""
        try:
            return float(value.strip()) if value.strip() else None
        except (ValueError, TypeError):
            return None
    
    def _extract_with_pattern(self, text: str, pattern: str, default: str) -> str:
        """Extract value using regex pattern with default fallback"""
        match = re.search(pattern, text)
        return match.group(1) if match else default
    
    def _extract_price_with_pattern(self, text: str, pattern: str) -> Optional[float]:
        """Extract price value using regex pattern"""
        match = re.search(pattern, text)
        if match:
            try:
                return float(match.group(1))
            except (ValueError, TypeError):
                return None
        return None
    
    def _determine_legacy_alert_type(self, text: str) -> str:
        """Determine alert type from legacy format"""
        text_upper = text.upper()
        
        if 'LONG TRADE ALERT' in text_upper:
            return 'ENTRY'
        elif 'SHORT TRADE ALERT' in text_upper:
            return 'ENTRY'
        elif 'TP1 HIT' in text_upper:
            return 'TP_HIT'
        elif 'TP2 HIT' in text_upper:
            return 'TP_HIT'
        elif 'TP3 HIT' in text_upper:
            return 'TP_HIT'
        elif 'SL HIT' in text_upper:
            return 'SL_HIT'
        else:
            return 'UNKNOWN'
    
    def _extract_legacy_signal(self, text: str) -> str:
        """Extract signal from legacy format"""
        text_upper = text.upper()
        
        if 'LONG TRADE ALERT' in text_upper or '🟢' in text:
            return 'LONG'
        elif 'SHORT TRADE ALERT' in text_upper or '🔴' in text:
            return 'SHORT'
        elif 'TP1 HIT' in text_upper:
            return 'LONG_TP1' if '🟢' in text else 'SHORT_TP1'
        elif 'TP2 HIT' in text_upper:
            return 'LONG_TP2' if '🟢' in text else 'SHORT_TP2'
        elif 'TP3 HIT' in text_upper:
            return 'LONG_TP3' if '🟢' in text else 'SHORT_TP3'
        elif 'SL HIT' in text_upper:
            return 'LONG_SL' if '🟢' in text else 'SHORT_SL'
        else:
            return 'UNKNOWN'
    
    def parse_multiple_alerts(self, text: str) -> list[ParsedAlert]:
        """Parse multiple alerts from a single text (if multiple tickers per webhook)"""
        # Split by common alert separators
        alert_separators = ['\n\n', '---', '===']
        
        for separator in alert_separators:
            if separator in text:
                alert_parts = text.split(separator)
                alerts = []
                for part in alert_parts:
                    if part.strip():
                        try:
                            alert = self.parse_alert(part.strip())
                            alerts.append(alert)
                        except ValueError:
                            continue  # Skip invalid alerts
                return alerts
        
        # If no separators found, try to parse as single alert
        try:
            return [self.parse_alert(text)]
        except ValueError:
            return [] 