"""
Watchlist Manager for TradingView Automation
Manages user watchlists, symbols, and alerts
"""

import asyncio
from dataclasses import dataclass
import logging
import time
from typing import Dict, List, Any, Optional

from prometheus_client import Counter, Histogram


logger = logging.getLogger(__name__)

# Prometheus metrics
watchlist_operations_total = Counter('watchlist_operations_total', 'Total watchlist operations', ['operation', 'status'])
watchlist_operation_duration = Histogram('watchlist_operation_duration_seconds', 'Watchlist operation duration')

@dataclass
class WatchlistSymbol:
    """Represents a symbol in a user's watchlist"""
    id: int
    symbol: str
    notes: Optional[str]
    alert_threshold: Optional[float]
    alert_type: str
    is_active: bool
    added_at: float

@dataclass
class UserWatchlist:
    """Represents a user's watchlist"""
    id: int
    discord_user_id: str
    watchlist_name: str
    is_active: bool
    created_at: float
    updated_at: float
    symbols: List[WatchlistSymbol]

class WatchlistManager:
    """Manages user watchlists and symbols"""
    
    def __init__(self, db_pool):
        self.db_pool = db_pool
    
    async def create_watchlist(self, discord_user_id: str, watchlist_name: str = "Default") -> Optional[int]:
        """Create a new watchlist for a user"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    INSERT INTO user_watchlists (discord_user_id, watchlist_name)
                    VALUES ($1, $2)
                    RETURNING id
                """, discord_user_id, watchlist_name)
                
                if result:
                    watchlist_id = result['id']
                    watchlist_operations_total.labels(operation='create', status='success').inc()
                    logger.info(f"Created watchlist {watchlist_name} for user {discord_user_id}", 
                               watchlist_id=watchlist_id)
                    return watchlist_id
                else:
                    watchlist_operations_total.labels(operation='create', status='error').inc()
                    logger.error(f"Failed to create watchlist for user {discord_user_id}")
                    return None
                    
        except Exception as e:
            watchlist_operations_total.labels(operation='create', status='error').inc()
            logger.error(f"Failed to create watchlist", error=str(e), user_id=discord_user_id)
            return None
        finally:
            duration = time.time() - start_time
            watchlist_operation_duration.observe(duration)
    
    async def get_user_watchlists(self, discord_user_id: str) -> List[UserWatchlist]:
        """Get all watchlists for a user"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                # Get watchlists
                watchlist_rows = await conn.fetch("""
                    SELECT id, watchlist_name, is_active, created_at, updated_at
                    FROM user_watchlists
                    WHERE discord_user_id = $1 AND is_active = TRUE
                    ORDER BY created_at DESC
                """, discord_user_id)
                
                watchlists = []
                for row in watchlist_rows:
                    # Get symbols for this watchlist
                    symbol_rows = await conn.fetch("""
                        SELECT id, symbol, notes, alert_threshold, alert_type, is_active, added_at
                        FROM watchlist_symbols
                        WHERE watchlist_id = $1 AND is_active = TRUE
                        ORDER BY added_at ASC
                    """, row['id'])
                    
                    symbols = [
                        WatchlistSymbol(
                            id=s['id'],
                            symbol=s['symbol'],
                            notes=s['notes'],
                            alert_threshold=s['alert_threshold'],
                            alert_type=s['alert_type'],
                            is_active=s['is_active'],
                            added_at=s['added_at'].timestamp() if s['added_at'] else time.time()
                        )
                        for s in symbol_rows
                    ]
                    
                    watchlist = UserWatchlist(
                        id=row['id'],
                        discord_user_id=discord_user_id,
                        watchlist_name=row['watchlist_name'],
                        is_active=row['is_active'],
                        created_at=row['created_at'].timestamp() if row['created_at'] else time.time(),
                        updated_at=row['updated_at'].timestamp() if row['updated_at'] else time.time(),
                        symbols=symbols
                    )
                    watchlists.append(watchlist)
                
                watchlist_operations_total.labels(operation='get', status='success').inc()
                logger.info(f"Retrieved {len(watchlists)} watchlists for user {discord_user_id}")
                return watchlists
                
        except Exception as e:
            watchlist_operations_total.labels(operation='get', status='error').inc()
            logger.error(f"Failed to get watchlists", error=str(e), user_id=discord_user_id)
            return []
        finally:
            duration = time.time() - start_time
            watchlist_operation_duration.observe(duration)
    
    async def add_symbol_to_watchlist(self, watchlist_id: int, symbol: str, notes: str = None, 
                                     alert_threshold: float = None, alert_type: str = "price_change") -> bool:
        """Add a symbol to a watchlist"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO watchlist_symbols (watchlist_id, symbol, notes, alert_threshold, alert_type)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (watchlist_id, symbol) DO UPDATE SET
                        notes = EXCLUDED.notes,
                        alert_threshold = EXCLUDED.alert_threshold,
                        alert_type = EXCLUDED.alert_type,
                        is_active = TRUE
                """, watchlist_id, symbol, notes, alert_threshold, alert_type)
                
                watchlist_operations_total.labels(operation='add_symbol', status='success').inc()
                logger.info(f"Added symbol {symbol} to watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            watchlist_operations_total.labels(operation='add_symbol', status='error').inc()
            logger.error(f"Failed to add symbol {symbol} to watchlist {watchlist_id}", error=str(e))
            return False
        finally:
            duration = time.time() - start_time
            watchlist_operation_duration.observe(duration)
    
    async def remove_symbol_from_watchlist(self, watchlist_id: int, symbol: str) -> bool:
        """Remove a symbol from a watchlist"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE watchlist_symbols 
                    SET is_active = FALSE 
                    WHERE watchlist_id = $1 AND symbol = $2
                """, watchlist_id, symbol)
                
                watchlist_operations_total.labels(operation='remove_symbol', status='success').inc()
                logger.info(f"Removed symbol {symbol} from watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            watchlist_operations_total.labels(operation='remove_symbol', status='error').inc()
            logger.error(f"Failed to remove symbol {symbol} from watchlist {watchlist_id}", error=str(e))
            return False
        finally:
            duration = time.time() - start_time
            watchlist_operation_duration.observe(duration)
    
    async def delete_watchlist(self, watchlist_id: int) -> bool:
        """Delete a watchlist (soft delete)"""
        start_time = time.time()
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE user_watchlists 
                    SET is_active = FALSE 
                    WHERE id = $1
                """, watchlist_id)
                
                watchlist_operations_total.labels(operation='delete', status='success').inc()
                logger.info(f"Deleted watchlist {watchlist_id}")
                return True
                
        except Exception as e:
            watchlist_operations_total.labels(operation='delete', status='error').inc()
            logger.error(f"Failed to delete watchlist {watchlist_id}", error=str(e))
            return False
        finally:
            duration = time.time() - start_time
            watchlist_operation_duration.observe(duration)
    
    async def get_watchlist_summary(self, discord_user_id: str) -> Dict[str, Any]:
        """Get a summary of user's watchlists"""
        try:
            watchlists = await self.get_user_watchlists(discord_user_id)
            
            total_symbols = sum(len(w.symbols) for w in watchlists)
            active_watchlists = len(watchlists)
            
            # Get most watched symbols
            symbol_counts = {}
            for watchlist in watchlists:
                for symbol in watchlist.symbols:
                    symbol_counts[symbol.symbol] = symbol_counts.get(symbol.symbol, 0) + 1
            
            top_symbols = sorted(symbol_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                'total_watchlists': active_watchlists,
                'total_symbols': total_symbols,
                'top_symbols': [{'symbol': s[0], 'count': s[1]} for s in top_symbols],
                'watchlists': [
                    {
                        'name': w.watchlist_name,
                        'symbol_count': len(w.symbols),
                        'created': w.created_at
                    }
                    for w in watchlists
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get watchlist summary", error=str(e), user_id=discord_user_id)
            return {} 