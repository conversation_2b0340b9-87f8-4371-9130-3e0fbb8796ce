"""
Discord Bot Permission System
Simple permission levels for Discord bot commands
"""

from enum import Enum
from typing import Optional, Dict, Any

import discord
from discord.ext import commands


class PermissionLevel(Enum):
    """Simple permission levels for Discord bot commands"""
    PUBLIC = "public"      # Everyone can use
    PAID = "paid"          # Paid tier users
    ADMIN = "admin"        # Server admins only
    OWNER = "owner"        # Bot owner only

class DiscordPermissionChecker:
    """Check Discord user permissions for bot commands"""
    
    def __init__(self, bot_owner_id: Optional[int] = None):
        self.bot_owner_id = bot_owner_id
        self.paid_role_names = ["paid", "premium", "vip", "pro"]  # Configurable paid role names
        self.admin_role_names = ["admin", "moderator", "mod"]     # Configurable admin role names
    
    def has_permission(self, member: discord.Member, required_level: PermissionLevel) -> bool:
        """
        Check if a Discord member has the required permission level
        
        Args:
            member: Discord member object
            required_level: Required permission level
            
        Returns:
            bool: True if member has permission
        """
        if required_level == PermissionLevel.PUBLIC:
            return True
            
        if required_level == PermissionLevel.OWNER:
            return member.id == self.bot_owner_id
            
        if required_level == PermissionLevel.ADMIN:
            return self._is_admin(member)
            
        if required_level == PermissionLevel.PAID:
            return self._is_paid(member) or self._is_admin(member)
            
        return False
    
    def _is_admin(self, member: discord.Member) -> bool:
        """Check if member has admin permissions"""
        # Check Discord permissions
        if member.guild_permissions.administrator:
            return True
            
        # Check role names
        member_roles = [role.name.lower() for role in member.roles]
        return any(admin_role in member_roles for admin_role in self.admin_role_names)
    
    def _is_paid(self, member: discord.Member) -> bool:
        """Check if member has paid tier access"""
        member_roles = [role.name.lower() for role in member.roles]
        return any(paid_role in member_roles for paid_role in self.paid_role_names)
    
    def get_user_tier(self, member: discord.Member) -> str:
        """Get user's current tier level"""
        if self._is_admin(member):
            return "admin"
        elif self._is_paid(member):
            return "paid"
        else:
            return "public"

def require_permission(permission_level: PermissionLevel):
    """
    Decorator to require specific permission level for bot commands
    
    Usage:
        @require_permission(PermissionLevel.PAID)
        async def premium_command(ctx):
            # Only paid users can access this
    """
    def decorator(func):
        async def wrapper(self, ctx, *args, **kwargs):
            # Get permission checker from bot instance
            permission_checker = getattr(self, 'permission_checker', None)
            if not permission_checker:
                await ctx.send("❌ Permission system not configured")
                return
                
            if not permission_checker.has_permission(ctx.author, permission_level):
                tier_names = {
                    PermissionLevel.PAID: "paid tier",
                    PermissionLevel.ADMIN: "admin",
                    PermissionLevel.OWNER: "bot owner"
                }
                required_tier = tier_names.get(permission_level, "higher permission")
                await ctx.send(f"❌ This command requires {required_tier} access")
                return
                
            return await func(self, ctx, *args, **kwargs)
        return wrapper
    return decorator 