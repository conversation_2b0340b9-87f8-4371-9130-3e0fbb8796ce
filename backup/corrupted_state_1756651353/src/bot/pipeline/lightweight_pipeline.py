"""
Lightweight Pipeline System for Simple Operations

This module provides fast-path routing for simple operations that don't need
the full pipeline complexity. It bypasses the heavy pipeline stages for
basic queries like price checks, simple data fetches, etc.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from src.core.logger import get_logger
from src.shared.data_providers.cache_utils import cache_get, cache_set

logger = get_logger(__name__)

class OperationType(Enum):
    """Types of operations to determine routing"""
    SIMPLE_PRICE_CHECK = "simple_price_check"
    SIMPLE_DATA_FETCH = "simple_data_fetch"
    BASIC_ANALYSIS = "basic_analysis"
    COMPLEX_ANALYSIS = "complex_analysis"
    FULL_PIPELINE = "full_pipeline"

@dataclass
class OperationRequest:
    """Request for pipeline operation"""
    operation_type: OperationType
    symbol: str
    data_types: List[str]
    parameters: Dict[str, Any]
    user_id: Optional[str] = None
    session_id: Optional[str] = None

@dataclass
class OperationResponse:
    """Response from pipeline operation"""
    success: bool
    data: Any
    operation_type: OperationType
    execution_time_ms: float
    cache_hit: bool
    error: Optional[str] = None

class LightweightPipeline:
    """
    Fast-path pipeline for simple operations.
    
    This bypasses the heavy pipeline stages for basic operations,
    providing significant performance improvements.
    """
    
    def __init__(self):
        self.logger = logger
        self.cache = {}
        self.cache_expiry = 300  # 5 minutes
        
    async def route_operation(self, request: OperationRequest) -> OperationResponse:
        """
        Route operation to appropriate handler based on complexity.
        
        Args:
            request: Operation request with type and parameters
            
        Returns:
            OperationResponse with results and metadata
        """
        start_time = datetime.now()
        
        try:
            # Check cache first for simple operations
            cache_key = self._generate_cache_key(request)
            cached_result = cache_get(self.cache, cache_key, self.cache_expiry)
            
            if cached_result:
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                return OperationResponse(
                    success=True,
                    data=cached_result,
                    operation_type=request.operation_type,
                    execution_time_ms=execution_time,
                    cache_hit=True
                )
            
            # Route to appropriate handler
            if request.operation_type == OperationType.SIMPLE_PRICE_CHECK:
                result = await self._handle_simple_price_check(request)
            elif request.operation_type == OperationType.SIMPLE_DATA_FETCH:
                result = await self._handle_simple_data_fetch(request)
            elif request.operation_type == OperationType.BASIC_ANALYSIS:
                result = await self._handle_basic_analysis(request)
            else:
                # Complex operations should use full pipeline
                raise ValueError(f"Operation type {request.operation_type} requires full pipeline")
            
            # Cache the result
            cache_set(self.cache, cache_key, result)
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            return OperationResponse(
                success=True,
                data=result,
                operation_type=request.operation_type,
                execution_time_ms=execution_time,
                cache_hit=False
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            self.logger.error(f"Error in lightweight pipeline: {e}")
            return OperationResponse(
                success=False,
                data=None,
                operation_type=request.operation_type,
                execution_time_ms=execution_time,
                cache_hit=False,
                error=str(e)
            )
    
    async def _handle_simple_price_check(self, request: OperationRequest) -> Dict[str, Any]:
        """Handle simple price check operations"""
        try:
            # Import here to avoid circular imports
            from src.api.data.providers.data_source_manager import DataSourceManager
            
            manager = DataSourceManager()
            
            # Fetch basic price data
            data = await manager.fetch_historical_data(
                request.symbol, 
                days=1,  # Just today's data
                data_types=["price"]
            )
            
            if data and len(data) > 0:
                latest = data[-1]  # Most recent data point
                return {
                    "symbol": request.symbol,
                    "price": latest.get("close", latest.get("price")),
                    "timestamp": latest.get("timestamp"),
                    "change": latest.get("change", 0),
                    "volume": latest.get("volume", 0)
                }
            else:
                return {
                    "symbol": request.symbol,
                    "error": "No data available"
                }
                
        except Exception as e:
            self.logger.error(f"Error in simple price check: {e}")
            raise
    
    async def _handle_simple_data_fetch(self, request: OperationRequest) -> Dict[str, Any]:
        """Handle simple data fetch operations"""
        try:
            from src.api.data.providers.data_source_manager import DataSourceManager
            
            manager = DataSourceManager()
            
            # Fetch requested data types
            data = await manager.fetch_historical_data(
                request.symbol,
                days=request.parameters.get("days", 7),
                data_types=request.data_types
            )
            
            return {
                "symbol": request.symbol,
                "data": data,
                "data_types": request.data_types,
                "count": len(data) if data else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error in simple data fetch: {e}")
            raise
    
    async def _handle_basic_analysis(self, request: OperationRequest) -> Dict[str, Any]:
        """Handle basic analysis operations"""
        try:
            from src.api.data.providers.data_source_manager import DataSourceManager
            from src.shared.technical_analysis.calculator import TechnicalCalculator
            
            manager = DataSourceManager()
            calculator = TechnicalCalculator()
            
            # Fetch data for analysis
            data = await manager.fetch_historical_data(
                request.symbol,
                days=request.parameters.get("days", 30),
                data_types=["price", "volume"]
            )
            
            if not data:
                return {"error": "No data available for analysis"}
            
            # Extract price and volume data
            prices = [d.get("close", d.get("price", 0)) for d in data]
            volumes = [d.get("volume", 0) for d in data]
            
            # Calculate basic indicators
            sma_20 = calculator.calculate_sma(prices, 20) if len(prices) >= 20 else None
            rsi = calculator.calculate_rsi(prices, 14) if len(prices) >= 14 else None
            
            return {
                "symbol": request.symbol,
                "analysis": {
                    "sma_20": sma_20,
                    "rsi": rsi,
                    "current_price": prices[-1] if prices else None,
                    "price_change": prices[-1] - prices[0] if len(prices) > 1 else 0,
                    "volume_avg": sum(volumes) / len(volumes) if volumes else 0
                },
                "data_points": len(data)
            }
            
        except Exception as e:
            self.logger.error(f"Error in basic analysis: {e}")
            raise
    
    def _generate_cache_key(self, request: OperationRequest) -> str:
        """Generate cache key for operation request"""
        key_parts = [
            request.operation_type.value,
            request.symbol,
            ",".join(sorted(request.data_types)),
            str(hash(frozenset(request.parameters.items())))
        ]
        return ":".join(key_parts)
    
    def get_operation_complexity(self, request: OperationRequest) -> str:
        """Determine operation complexity for routing decisions"""
        if request.operation_type in [OperationType.SIMPLE_PRICE_CHECK, OperationType.SIMPLE_DATA_FETCH]:
            return "simple"
        elif request.operation_type == OperationType.BASIC_ANALYSIS:
            return "basic"
        else:
            return "complex"
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the lightweight pipeline"""
        return {
            "cache_size": len(self.cache),
            "cache_hit_rate": 0.75,  # Placeholder - would track actual hits
            "avg_execution_time_ms": 50,  # Placeholder - would track actual times
            "operations_handled": 100,  # Placeholder - would track actual count
            "cache_expiry_seconds": self.cache_expiry
        }

# Global lightweight pipeline instance
lightweight_pipeline = LightweightPipeline()

# Convenience functions for easy access
async def quick_price_check(symbol: str, user_id: str = None) -> Dict[str, Any]:
    """Quick price check using lightweight pipeline"""
    request = OperationRequest(
        operation_type=OperationType.SIMPLE_PRICE_CHECK,
        symbol=symbol,
        data_types=["price"],
        parameters={},
        user_id=user_id
    )
    
    response = await lightweight_pipeline.route_operation(request)
    return response.data if response.success else {"error": response.error}

async def quick_data_fetch(symbol: str, data_types: List[str], days: int = 7) -> Dict[str, Any]:
    """Quick data fetch using lightweight pipeline"""
    request = OperationRequest(
        operation_type=OperationType.SIMPLE_DATA_FETCH,
        symbol=symbol,
        data_types=data_types,
        parameters={"days": days}
    )
    
    response = await lightweight_pipeline.route_operation(request)
    return response.data if response.success else {"error": response.error}

async def quick_analysis(symbol: str, analysis_type: str = "basic") -> Dict[str, Any]:
    """Quick analysis using lightweight pipeline"""
    request = OperationRequest(
        operation_type=OperationType.BASIC_ANALYSIS,
        symbol=symbol,
        data_types=["price", "volume"],
        parameters={"days": 30, "analysis_type": analysis_type}
    )
    
    response = await lightweight_pipeline.route_operation(request)
    return response.data if response.success else {"error": response.error} 