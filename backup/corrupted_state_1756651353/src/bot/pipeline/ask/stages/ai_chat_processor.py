import asyncio
import json
import logging
import os
import re
import re
import re
import re
import time
import traceback
import traceback
import traceback
from typing import Dict, Any, List, Optional

from ai_cache import get_cached_ai_response, cache_ai_response
from config import PipelineConfig
from models import AIAskResult
from openai import OpenAI
from openai import OpenAI
from postprocessor.response_generator import response_generator
from prompts import SYSTEM_PROMPT, FALLBACK_RESPONSES
from templates.analysis_response import ResponseTemplateEngine, ResponseStyle
from utils.cache_integration import cache_integration

from core.enhanced_ai_client import enhanced_ai_client
from core.market_context_processor import market_context_processor
from core.technical_analysis_processor import technical_analysis_processor
from src.api.data.market_data_service import MarketDataService
from src.core.config_manager import config
from src.data.cache.manager import CacheManager
                time.sleep(2 ** attempt)  # 2, 4, 8 seconds between retries

        # If all attempts fail
        logger.error(f"❌ [{self.pipeline_id}] AI call failed after {max_attempts} attempts. Last error: {last_error}")
        return AIAskResult(
            intent="general_question",
            symbols=[],
            needs_data=False,
            response=FALLBACK_RESPONSES["ai_error"]
        ).dict()

    def _legacy_generate_final_response(self, initial_response: str, data: Dict) -> str:
        if not data:
            return initial_response
        
        # If we have data, use the template engine to format a proper response
        try:
            template_engine = ResponseTemplateEngine()
            
            # Determine the appropriate template type based on the intent and data
            # For now, use 'stock_analysis' as default for single symbol, 'market_overview' for multiple
            template_type = "stock_analysis"
            style = ResponseStyle.DETAILED
            
            if len(data) > 1:
                template_type = "market_overview"
            
            # For single symbol, extract the first symbol's data
            if len(data) == 1:
                symbol = list(data.keys())[0]
                stock_data = data[symbol]
                
                # Enhance the data with additional fields for the template
                enhanced_data = {
                    'symbol': symbol,
                    'price': stock_data.get('current_price', stock_data.get('price', 0)),
                    'change': stock_data.get('change_percent', stock_data.get('change', 0)),
                    'volume': stock_data.get('volume', stock_data.get('volume_traded', 0)),
                    'timestamp': stock_data.get('timestamp', ''),
                    'market_cap': stock_data.get('market_cap', 0),
                    'support': stock_data.get('support', 0),
                    'resistance': stock_data.get('resistance', 0)
                }
                
                # Generate the response using the template engine
                formatted_response = template_engine.generate_response(
                    template_type,
                    style,
                    enhanced_data,
                    {}  # query_analysis is empty for now
                )
                
                return formatted_response
            else:
                # For multiple symbols, create a market overview
                indices_summary = []
                for symbol, stock_data in data.items():
                    price = stock_data.get('current_price', stock_data.get('price', 0))
                    change = stock_data.get('change_percent', stock_data.get('change', 0))
                    change_sign = "+" if change >= 0 else "-"
                    indices_summary.append(f"• **{symbol}**: ${price} ({change_sign}{abs(change)}%)")
                
                enhanced_data = {
                    'indices_summary': "\n".join(indices_summary),
                    'overall_sentiment': 'Neutral',  # Placeholder, could be enhanced
                    'market_trend': 'Mixed',         # Placeholder
                    'timestamp': 'Current',
                    'data_quality': 80
                }
                
                formatted_response = template_engine.generate_response(
                    "market_overview",
                    ResponseStyle.SIMPLE,
                    enhanced_data,
                    {}
                )
                
                return formatted_response
                
        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] Error generating template response: {e}")
            # Fallback to basic response if template engine fails
            data_str = "\n".join([f"{symbol}: {info}" for symbol, info in data.items()])
            return f"{initial_response}\n\nHere's the data I found:\n{data_str}"

    def _calculate_technical_indicators_quality(self, indicators: Dict[str, Any]) -> int:
        """Calculate quality score for technical indicators (legacy method)."""
        try:
            # Basic quality calculation based on data completeness
            quality_score = 0
            
            # Check for required fields
            required_fields = ['rsi', 'macd', 'bollinger_bands', 'volume']
            for field in required_fields:
                if field in indicators and indicators[field] is not None:
                    quality_score += 20
            
            # Check for data freshness (if timestamp available)
            if 'timestamp' in indicators:
                quality_score += 10
            
            # Check for signal strength
            if 'signal_strength' in indicators:
                quality_score += 10
            
            # Ensure score is within bounds
            return min(max(quality_score, 0), 100)
            
        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] Error calculating technical indicators quality: {e}")
            return 50  # Default fallback score

    async def _get_comprehensive_market_context(self, primary_symbol: str) -> Dict[str, Any]:
        """Get comprehensive market context for a symbol (legacy method)."""
        try:
            # Basic market context gathering
            market_context = {
                'symbol': primary_symbol,
                'market_sentiment': 'Neutral',
                'sector_trend': 'Mixed',
                'market_volatility': 'Medium',
                'key_events': [],
                'timestamp': 'current'
            }
            
            # Try to get additional market data if available
            try:
                # This would normally fetch from market data providers
                # For now, return basic context
                pass
            except Exception as e:
                logger.warning(f"⚠️ [{self.pipeline_id}] Could not fetch additional market data: {e}")
            
            return market_context
            
        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] Error getting market context: {e}")
            return {
                'symbol': primary_symbol,
                'market_sentiment': 'Unknown',
                'sector_trend': 'Unknown',
                'market_volatility': 'Unknown',
                'key_events': [],
                'timestamp': 'unknown'
            }

    def _format_enhanced_market_context(self, market_context: Dict[str, Any]) -> str:
        """Format market context for enhanced response (legacy method)."""
        try:
            if not market_context:
                return "Market context unavailable."
            
            # Basic formatting
            formatted = f"Market Context for {market_context.get('symbol', 'Unknown')}:\n"
            formatted += f"• Sentiment: {market_context.get('market_sentiment', 'Unknown')}\n"
            formatted += f"• Sector Trend: {market_context.get('sector_trend', 'Unknown')}\n"
            formatted += f"• Volatility: {market_context.get('market_volatility', 'Unknown')}\n"
            
            # Add key events if available
            key_events = market_context.get('key_events', [])
            if key_events:
                formatted += "• Key Events:\n"
                for event in key_events[:3]:  # Limit to 3 events
                    formatted += f"  - {event}\n"
            
            return formatted
            
        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] Error formatting market context: {e}")
            return "Market context formatting failed."

async def processor(context: Any, results: Dict[str, Any]) -> Dict[str, Any]:
    """Process AI chat requests with proper pipeline interface"""
    # Extract query from context or results
    query = getattr(context, 'original_query', None)
    if not query:
        # Try to get query from results if not in context
        query = results.get('query', '')
    
    if not query:
        return {
            'error': 'No query provided',
            'response': 'Please provide a question to analyze.',
            'intent': 'general_question',
            'symbols': [],
            'needs_data': False
        }
    
    # Ensure context has a pipeline_id
    if not hasattr(context, 'pipeline_id'):
        context.pipeline_id = 'unknown'
    
    processor = AIChatProcessor(context=context)
    result = await processor.process(query)
    
    # Debug logging to see exactly what we're returning
    logger.info(f"🔍 AI Chat Processor returning result with keys: {list(result.keys())}")
    logger.debug(f"🔍 AI Chat Processor raw result: {result}")
    
    # Ensure we have a response key
    if 'response' not in result or not result['response']:
        logger.warning("⚠️ No response generated, creating fallback...")
        result['response'] = "I couldn't generate a response for your query. Please try asking about stocks or trading topics."
    
    logger.info(f"✅ Final result has response: {len(str(result.get('response', '')))} chars")
    return result
