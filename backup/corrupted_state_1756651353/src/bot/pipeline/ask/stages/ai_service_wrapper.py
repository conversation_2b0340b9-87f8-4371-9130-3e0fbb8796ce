def _format_enhanced_response(self, symbol: str, technical_analysis: Dict[str, Any], signals: List[Dict[str, Any]]) -> str:
    """Format the enhanced response with comprehensive analysis."""
    
    # Check if we have valid data
    if technical_analysis.get('status') == 'data_unavailable':
        return f"**❌ {symbol} Analysis Unavailable**\n\n⚠️ **No valid market data available**\n\nUnable to provide technical analysis due to data unavailability. Please try again later or verify the symbol is correct."
    
    if technical_analysis.get('status') == 'error':
        return f"**❌ {symbol} Analysis Error**\n\n⚠️ **Error occurred during analysis**\n\n{technical_analysis.get('error', 'Unknown error')}"
    
    response_parts = [f"**📊 {symbol} Comprehensive Technical Analysis**"]
    
    # Add current price and basic info
    if 'current_price' in technical_analysis.get('indicators', {}):
        price = technical_analysis['indicators']['current_price']
        response_parts.append(f"\n💰 **Current Price**: ${price:.2f}")
    else:
        response_parts.append(f"\n💰 **Current Price**: Data unavailable")
    
    # Add market context
    if 'market_context' in technical_analysis and technical_analysis['market_context']:
        context = technical_analysis['market_context']
        sector = context.get('sector', 'N/A')
        industry = context.get('industry', 'N/A')
        response_parts.append(f"\n🏢 **Market Context**: {sector} ({industry})")
        
        if 'sector_performance' in context:
            perf = context['sector_performance']
            response_parts.append(f"• Sector Performance: 1D: {perf.get('1d', 0):.1f}% | 1W: {perf.get('1w', 0):.1f}% | 1M: {perf.get('1m', 0):.1f}%")
        
        if 'fundamentals' in context:
            fund = context['fundamentals']
            response_parts.append(f"• Fundamentals: P/E {fund.get('pe_ratio', 0):.1f} | EPS ${fund.get('eps', 0):.2f} | Rev Growth {fund.get('revenue_growth', 0):.1f}%")
        
        if 'risk_metrics' in context:
            risk = context['risk_metrics']
            response_parts.append(f"• Risk: Beta {risk.get('beta', 0):.2f} | Volatility {risk.get('volatility', 0):.2f} | Rating: {risk.get('risk_rating', 'N/A')}")
    else:
        response_parts.append(f"\n🏢 **Market Context**: Data unavailable")
    
    # Add technical indicators
    indicators = technical_analysis.get('indicators', {})
    if indicators:
        response_parts.append("\n📈 **Technical Indicators**:")
        
        # RSI
        if 'rsi' in indicators:
            rsi = indicators['rsi']
            if rsi > 70:
                rsi_status = "🔴 Overbought"
            elif rsi < 30:
                rsi_status = "🟢 Oversold"
            else:
                rsi_status = "🟡 Neutral"
            response_parts.append(f"• RSI ({rsi:.1f}): {rsi_status}")
        
        # MACD
        if 'macd' in indicators and 'macd_signal' in indicators:
            macd = indicators['macd']
            signal = indicators['macd_signal']
            if macd > signal:
                macd_status = "🟢 Bullish"
            else:
                macd_status = "🔴 Bearish"
            response_parts.append(f"• MACD: {macd:.3f} vs Signal {signal:.3f} ({macd_status})")
        
        # Moving averages
        ma_indicators = ['sma_20', 'sma_50', 'ema_20', 'ema_50']
        available_mas = [(k, v) for k, v in indicators.items() if k in ma_indicators]
        if available_mas:
            response_parts.append("• Moving Averages:")
            for ma_name, ma_value in available_mas:
                response_parts.append(f"  - {ma_name}: ${ma_value:.2f}")
        
        # Bollinger Bands
        if 'bb_upper' in indicators and 'bb_lower' in indicators:
            bb_upper = indicators['bb_upper']
            bb_lower = indicators['bb_lower']
            current_price = indicators.get('current_price', 0)
            if current_price > bb_upper:
                bb_status = "🔴 Above Upper Band"
            elif current_price < bb_lower:
                bb_status = "🟢 Below Lower Band"
            else:
                bb_status = "🟡 Within Bands"
            response_parts.append(f"• Bollinger Bands: ${bb_lower:.2f} - ${bb_upper:.2f} ({bb_status})")
        
        # ATR
        if 'atr' in indicators:
            atr = indicators['atr']
            response_parts.append(f"• ATR (Volatility): ${atr:.2f}")
        
        # Volume
        if 'volume_ma' in indicators and 'volume_spike' in indicators:
            vol_ma = indicators['volume_ma']
            vol_spike = indicators['volume_spike']
            spike_status = "🔴 High" if vol_spike > 1.5 else "🟢 Normal" if vol_spike < 0.5 else "🟡 Elevated"
            response_parts.append(f"• Volume: MA {vol_ma:,.0f} | Spike: {vol_spike:.1f}x ({spike_status})")
    else:
        response_parts.append("\n📈 **Technical Indicators**: Data unavailable")
    
    # Add enhanced supply/demand zones
    zones = technical_analysis.get('zones', {}).get('zones', [])
    if zones:
        response_parts.append("\n🎯 **Supply & Demand Zones**:")
        for i, zone in enumerate(zones[:3], 1):
            zone_type = zone.get('zone_type', 'unknown')
            center_price = zone.get('center_price', 0)
            strength = zone.get('strength', 0)
            priority = zone.get('priority', 'medium')
            method = zone.get('method', 'unknown')
            
            # Add zone priority indicator
            priority_indicator = '🔴' if priority == 'high' else '🟡' if priority == 'medium' else '🟢'
            response_parts.append(f"{i}. {priority_indicator} {zone_type.title()} Zone: ${center_price:.2f} (Strength: {strength:.0f}/100, Method: {method})")
    else:
        response_parts.append("\n🎯 **Supply & Demand Zones**: Data unavailable")
    
    # Add zone analysis insights
    zone_analysis = technical_analysis.get('zone_analysis', {})
    if zone_analysis:
        response_parts.append("\n📊 **Zone Analysis Insights**:")
        if 'market_structure' in zone_analysis:
            structure = zone_analysis['market_structure']
            structure_desc = {
                'resistance_dominated': '🔴 Resistance dominated market',
                'support_dominated': '🟢 Support dominated market',
                'balanced': '⚖️ Balanced zone structure',
                'weak_structure': '⚠️ Weak zone structure'
            }.get(structure, '❓ Undefined structure')
            response_parts.append(f"• Market Structure: {structure_desc}")
        
        if 'nearest_demand' in zone_analysis and zone_analysis['nearest_demand']:
            nearest_demand = zone_analysis['nearest_demand']
            distance = nearest_demand.get('price_distance_to_current', 0)
            response_parts.append(f"• Nearest Support: ${nearest_demand.get('center_price', 0):.2f} (Distance: ${distance:.2f})")
        
        if 'nearest_supply' in zone_analysis and zone_analysis['nearest_supply']:
            nearest_supply = zone_analysis['nearest_supply']
            distance = nearest_supply.get('price_distance_to_current', 0)
            response_parts.append(f"• Nearest Resistance: ${nearest_supply.get('center_price', 0):.2f} (Distance: ${distance:.2f})")
    
    # Add zone recommendations
    recommendations = technical_analysis.get('zone_recommendations', [])
    if recommendations:
        response_parts.append("\n💡 **Zone-Based Recommendations**:")
        for rec in recommendations[:3]:
            if rec.get('recommendation_type') == 'entry':
                action = rec.get('action', 'unknown')
                zone_price = rec.get('zone_center', 0)
                response_parts.append(f"• {action.title()}: Target ${zone_price:.2f} zone")
            elif rec.get('recommendation_type') == 'structure':
                action = rec.get('action', 'unknown')
                structure = rec.get('market_structure', 'unknown')
                response_parts.append(f"• Market Outlook: {action} ({structure})")
    
    # Add trading signals
    if signals:
        response_parts.append("\n🚨 **Trading Signals**:")
        for signal in signals[:3]:
            signal_type = signal.get('signal_type', 'unknown')
            direction = signal.get('direction', 'neutral')
            confidence = signal.get('confidence_score', 0)
            response_parts.append(f"• {signal_type}: {direction.title()} (Confidence: {confidence:.0f}%)")
    else:
        response_parts.append("\n🚨 **Trading Signals**: No signals generated")
    
    # Add data quality and timestamp
    data_quality = technical_analysis.get('data_quality', 0)
    timestamp = technical_analysis.get('analysis_timestamp', 'unknown')
    response_parts.append(f"\n📊 **Data Quality**: {data_quality:.1f}% • {timestamp}")
    
    # Add risk disclaimer
    response_parts.append("\n⚠️ **Disclaimer**: This analysis is for educational purposes only. Past performance doesn't guarantee future results. Always do your own research and consult with a financial advisor before making trading decisions.")
    
    return "\n".join(response_parts)