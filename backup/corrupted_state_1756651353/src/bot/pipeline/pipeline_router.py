"""
Pipeline Router for Intelligent Operation Routing

This module intelligently routes operations between the lightweight pipeline
for simple operations and the full pipeline for complex analysis.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass

from src.core.logger import get_logger
from .lightweight_pipeline import (
    LightweightPipeline, 
    OperationRequest, 
    OperationResponse,
    OperationType,
    quick_price_check,
    quick_data_fetch,
    quick_analysis
)

logger = get_logger(__name__)

@dataclass
class RoutingDecision:
    """Decision about which pipeline to use"""
    use_lightweight: bool
    reason: str
    estimated_complexity: str
    expected_time_ms: float

class PipelineRouter:
    """
    Intelligent router that decides between lightweight and full pipeline.
    
    Analyzes operation complexity and routes accordingly for optimal performance.
    """
    
    def __init__(self):
        self.logger = logger
        self.lightweight_pipeline = LightweightPipeline()
        self.routing_stats = {
            "lightweight_routes": 0,
            "full_pipeline_routes": 0,
            "total_operations": 0,
            "avg_decision_time_ms": 0.0
        }
    
    async def route_operation(self, 
                            symbol: str,
                            operation: str,
                            data_types: List[str] = None,
                            parameters: Dict[str, Any] = None,
                            user_id: str = None,
                            session_id: str = None) -> Dict[str, Any]:
        """
        Route operation to appropriate pipeline based on complexity analysis.
        
        Args:
            symbol: Stock symbol to analyze
            operation: Type of operation (price_check, data_fetch, analysis, etc.)
            data_types: Types of data needed
            parameters: Additional operation parameters
            user_id: User ID for tracking
            session_id: Session ID for tracking
            
        Returns:
            Operation results with metadata about which pipeline was used
        """
        start_time = datetime.now()
        
        try:
            # Analyze operation complexity
            routing_decision = self._analyze_complexity(
                operation, data_types, parameters
            )
            
            # Update routing statistics
            self._update_routing_stats(routing_decision)
            
            # Route to appropriate pipeline
            if routing_decision.use_lightweight:
                self.logger.info(f"Routing {operation} for {symbol} to lightweight pipeline")
                result = await self._execute_lightweight(
                    symbol, operation, data_types, parameters, user_id
                )
                result["pipeline_used"] = "lightweight"
                result["routing_reason"] = routing_decision.reason
            else:
                self.logger.info(f"Routing {operation} for {symbol} to full pipeline")
                result = await self._execute_full_pipeline(
                    symbol, operation, data_types, parameters, user_id, session_id
                )
                result["pipeline_used"] = "full"
                result["routing_reason"] = routing_decision.reason
            
            # Add routing metadata
            result["routing_decision"] = {
                "use_lightweight": routing_decision.use_lightweight,
                "reason": routing_decision.reason,
                "estimated_complexity": routing_decision.estimated_complexity,
                "expected_time_ms": routing_decision.expected_time_ms,
                "actual_decision_time_ms": (datetime.now() - start_time).total_seconds() * 1000
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in pipeline routing: {e}")
            return {
                "error": str(e),
                "pipeline_used": "error",
                "routing_reason": "error_occurred"
            }
    
    def _analyze_complexity(self, 
                           operation: str,
                           data_types: List[str] = None,
                           parameters: Dict[str, Any] = None) -> RoutingDecision:
        """
        Analyze operation complexity to determine routing.
        
        Returns:
            RoutingDecision with pipeline choice and reasoning
        """
        data_types = data_types or []
        parameters = parameters or {}
        
        # Simple operations that can use lightweight pipeline
        simple_operations = {
            "price_check": {
                "complexity": "simple",
                "expected_time_ms": 50,
                "reason": "Basic price check - lightweight pipeline sufficient"
            },
            "data_fetch": {
                "complexity": "simple" if len(data_types) <= 2 else "basic",
                "expected_time_ms": 100 if len(data_types) <= 2 else 200,
                "reason": f"Data fetch for {len(data_types)} data types"
            },
            "basic_analysis": {
                "complexity": "basic",
                "expected_time_ms": 300,
                "reason": "Basic technical analysis - lightweight pipeline can handle"
            }
        }
        
        # Complex operations that need full pipeline
        complex_operations = {
            "comprehensive_analysis": {
                "complexity": "complex",
                "expected_time_ms": 2000,
                "reason": "Comprehensive analysis requires full pipeline features"
            },
            "ai_analysis": {
                "complexity": "complex",
                "expected_time_ms": 5000,
                "reason": "AI analysis requires full pipeline with AI services"
            },
            "multi_symbol_analysis": {
                "complexity": "complex",
                "expected_time_ms": 3000,
                "reason": "Multi-symbol analysis requires full pipeline orchestration"
            }
        }
        
        # Check if operation is simple
        if operation in simple_operations:
            op_info = simple_operations[operation]
            
            # Additional complexity checks for data fetch
            if operation == "data_fetch":
                if len(data_types) > 3 or parameters.get("days", 0) > 30:
                    op_info["complexity"] = "complex"
                    op_info["expected_time_ms"] = 1500
                    op_info["reason"] = "Large data fetch - requires full pipeline"
            
            # Additional complexity checks for basic analysis
            elif operation == "basic_analysis":
                if parameters.get("indicators", []):
                    indicator_count = len(parameters["indicators"])
                    if indicator_count > 5:
                        op_info["complexity"] = "complex"
                        op_info["expected_time_ms"] = 2000
                        op_info["reason"] = f"Analysis with {indicator_count} indicators - requires full pipeline"
            
            return RoutingDecision(
                use_lightweight=True,
                reason=op_info["reason"],
                estimated_complexity=op_info["complexity"],
                expected_time_ms=op_info["expected_time_ms"]
            )
        
        # Check if operation is complex
        elif operation in complex_operations:
            op_info = complex_operations[operation]
            return RoutingDecision(
                use_lightweight=False,
                reason=op_info["reason"],
                estimated_complexity=op_info["complexity"],
                expected_time_ms=op_info["expected_time_ms"]
            )
        
        # Default to full pipeline for unknown operations
        else:
            return RoutingDecision(
                use_lightweight=False,
                reason=f"Unknown operation '{operation}' - using full pipeline for safety",
                estimated_complexity="unknown",
                expected_time_ms=1000
            )
    
    async def _execute_lightweight(self,
                                 symbol: str,
                                 operation: str,
                                 data_types: List[str],
                                 parameters: Dict[str, Any],
                                 user_id: str) -> Dict[str, Any]:
        """Execute operation using lightweight pipeline"""
        try:
            if operation == "price_check":
                return await quick_price_check(symbol, user_id)
            elif operation == "data_fetch":
                days = parameters.get("days", 7)
                return await quick_data_fetch(symbol, data_types, days)
            elif operation == "basic_analysis":
                analysis_type = parameters.get("analysis_type", "basic")
                return await quick_analysis(symbol, analysis_type)
            else:
                raise ValueError(f"Unsupported operation for lightweight pipeline: {operation}")
                
        except Exception as e:
            self.logger.error(f"Error in lightweight pipeline execution: {e}")
            raise
    
    async def _execute_full_pipeline(self,
                                   symbol: str,
                                   operation: str,
                                   data_types: List[str],
                                   parameters: Dict[str, Any],
                                   user_id: str,
                                   session_id: str) -> Dict[str, Any]:
        """Execute operation using full pipeline"""
        try:
            # Import here to avoid circular imports
            from .pipeline_engine import PipelineEngine
            
            # Create pipeline request
            pipeline_request = {
                "symbol": symbol,
                "operation": operation,
                "data_types": data_types,
                "parameters": parameters,
                "user_id": user_id,
                "session_id": session_id
            }
            
            # Execute full pipeline
            pipeline_engine = PipelineEngine()
            result = await pipeline_engine.execute(pipeline_request)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in full pipeline execution: {e}")
            raise
    
    def _update_routing_stats(self, decision: RoutingDecision):
        """Update routing statistics"""
        self.routing_stats["total_operations"] += 1
        
        if decision.use_lightweight:
            self.routing_stats["lightweight_routes"] += 1
        else:
            self.routing_stats["full_pipeline_routes"] += 1
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics and performance metrics"""
        total = self.routing_stats["total_operations"]
        
        if total > 0:
            lightweight_percentage = (self.routing_stats["lightweight_routes"] / total) * 100
            full_pipeline_percentage = (self.routing_stats["full_pipeline_routes"] / total) * 100
        else:
            lightweight_percentage = 0
            full_pipeline_percentage = 0
        
        return {
            **self.routing_stats,
            "lightweight_percentage": round(lightweight_percentage, 2),
            "full_pipeline_percentage": round(full_pipeline_percentage, 2),
            "efficiency_score": round(lightweight_percentage, 2)  # Higher is better
        }
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        lightweight_metrics = await self.lightweight_pipeline.get_performance_metrics()
        routing_stats = self.get_routing_stats()
        
        return {
            "routing": routing_stats,
            "lightweight_pipeline": lightweight_metrics,
            "overall_efficiency": routing_stats["efficiency_score"],
            "recommendations": self._generate_recommendations(routing_stats)
        }
    
    def _generate_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """Generate performance recommendations based on routing statistics"""
        recommendations = []
        
        if stats["lightweight_percentage"] < 50:
            recommendations.append("Consider optimizing more operations for lightweight pipeline")
        
        if stats["lightweight_percentage"] > 90:
            recommendations.append("High lightweight usage - consider if some operations need full pipeline features")
        
        if stats["total_operations"] < 10:
            recommendations.append("Limited data - routing decisions may not be optimal yet")
        
        return recommendations

# Global pipeline router instance
pipeline_router = PipelineRouter()

# Convenience functions for easy access
async def route_operation(symbol: str,
                         operation: str,
                         data_types: List[str] = None,
                         parameters: Dict[str, Any] = None,
                         user_id: str = None,
                         session_id: str = None) -> Dict[str, Any]:
    """Route operation using the intelligent pipeline router"""
    return await pipeline_router.route_operation(
        symbol, operation, data_types, parameters, user_id, session_id
    )

async def get_routing_stats() -> Dict[str, Any]:
    """Get current routing statistics"""
    return pipeline_router.get_routing_stats()

async def get_performance_metrics() -> Dict[str, Any]:
    """Get comprehensive performance metrics"""
    return await pipeline_router.get_performance_metrics() 