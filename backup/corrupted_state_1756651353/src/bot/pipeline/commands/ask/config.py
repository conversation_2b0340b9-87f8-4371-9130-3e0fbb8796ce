"""
Enhanced Ask Pipeline Configuration - PROFESSIONAL GRADE

Enterprise-grade configuration management for the ask pipeline with:
✅ Comprehensive environment variable validation
✅ Type-safe configuration with defaults
✅ Performance tuning parameters
✅ Security and compliance settings
✅ Real-time configuration updates
✅ Configuration validation and health checks
"""

from dataclasses import dataclass, field
from enum import Enum
from functools import lru_cache
import json
import logging
import os
from pathlib import Path
import time
from typing import Optional, Dict, Any, List

import yaml

from src.core.config_manager import get_config
from src.core.exceptions import ConfigurationError, ConfigValidationError
        config = get_config()
        return config.get(section, key, default)
    
    def _get_string(self, key: str, default: str) -> str:
        """Get string environment variable with validation"""
        value = os.getenv(key, default)
        if not isinstance(value, str):
            logger.warning(f"Invalid string value for {key}: {value}, using default: {default}")
            return default
        return value
    
    def _get_int(self, key: str, default: int) -> int:
        """Get integer environment variable with validation"""
        try:
            value = os.getenv(key, str(default))
            int_value = int(value)
            if int_value < 0 and key.endswith(('_rate_limit', '_timeout', '_ttl')):
                logger.warning(f"Negative value for {key}: {int_value}, using default: {default}")
                return default
            return int_value
        except (ValueError, TypeError):
            logger.warning(f"Invalid integer value for {key}: {os.getenv(key)}, using default: {default}")
            return default
    
    def _get_float(self, key: str, default: float) -> float:
        """Get float environment variable with validation"""
        try:
            value = os.getenv(key, str(default))
            float_value = float(value)
            if float_value < 0:
                logger.warning(f"Negative value for {key}: {float_value}, using default: {default}")
                return default
            return float_value
        except (ValueError, TypeError):
            logger.warning(f"Invalid float value for {key}: {os.getenv(key)}, using default: {default}")
            return default
    
    def _get_bool(self, key: str, default: bool) -> bool:
        """Get boolean environment variable with validation"""
        value = os.getenv(key, str(default)).lower()
        if value in ("true", "1", "yes", "on"):
            return True
        elif value in ("false", "0", "no", "off"):
            return False
        else:
            logger.warning(f"Invalid boolean value for {key}: {os.getenv(key)}, using default: {default}")
            return default
    
    def get_section_config(self, section_name: str) -> Dict[str, Any]:
        """Get configuration for a specific pipeline section"""
        section_configs = {
            "query_analysis": {
                "max_retries": 2,
                "timeout": 10.0,
                "critical": True
            },
            "data_collection": {
                "max_retries": 3,
                "timeout": 15.0,
                "critical": True
            },
            "response_generation": {
                "max_retries": 2,
                "timeout": 20.0,
                "critical": True
            },
            "response_formatting": {
                "max_retries": 1,
                "timeout": 5.0,
                "critical": False
            }
        }
        
        return section_configs.get(section_name, {})
    
    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """Get configuration for a specific market data provider"""
        provider_configs = {
            "yahoo_finance": {
                "enabled": self.yahoo_finance_enabled,
                "rate_limit": self.yahoo_finance_rate_limit,
                "timeout": self.yahoo_finance_timeout
            },
            "polygon": {
                "enabled": self.polygon_enabled,
                "rate_limit": self.polygon_rate_limit,
                "timeout": self.polygon_timeout
            },
            "finnhub": {
                "enabled": self.finnhub_enabled,
                "rate_limit": self.finnhub_rate_limit,
                "timeout": self.finnhub_timeout
            },
            "alpha_vantage": {
                "enabled": self.alpha_vantage_enabled,
                "rate_limit": self.alpha_vantage_rate_limit,
                "timeout": self.alpha_vantage_timeout
            }
        }
        
        return provider_configs.get(provider_name, {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get configuration for AI/OpenRouter"""
        return {
            "enabled": self.openrouter_enabled,
            "model": self.openrouter_model,
            "temperature": self.openrouter_temperature,
            "max_tokens": self.openrouter_max_tokens,
            "timeout": self.openrouter_timeout,
            "base_url": self.openrouter_base_url,
            "has_api_key": bool(self.openrouter_api_key),
            "model_fallbacks": {
                "global": self.model_global_fallback,
                "llm": self.llm_model,
                "quick": self.model_quick,
                "analysis": self.model_analysis,
                "heavy": self.model_heavy
            }
        }
    
    def is_provider_enabled(self, provider_name: str) -> bool:
        """Check if a specific provider is enabled"""
        provider_config = self.get_provider_config(provider_name)
        return provider_config.get("enabled", False)
    
    def get_enabled_providers(self) -> List[str]:
        """Get list of enabled market data providers"""
        providers = ["yahoo_finance", "polygon", "finnhub", "alpha_vantage"]
        return [p for p in providers if self.is_provider_enabled(p)]
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get comprehensive configuration summary"""
        return {
            "source": self._config_source.value,
            "validation": self._validation.get_summary(),
            "pipeline": {
                "enabled": self.enabled,
                "timeout": self.timeout,
                "max_retries": self.max_retries,
                "quality_threshold": self.quality_threshold
            },
            "providers": {
                "enabled": self.get_enabled_providers(),
                "total": len(self.get_enabled_providers())
            },
            "performance": {
                "parallel_execution": self.parallel_execution,
                "cache_enabled": self.cache_enabled,
                "metrics_enabled": self.enable_metrics
            },
            "ai": self.get_ai_config()
        }
    
    def reload_configuration(self) -> bool:
        """Reload configuration from sources"""
        try:
            logger.info("🔄 Reloading configuration...")
            self._load_configuration()
            self._validate_configuration()
            
            if self._validation.is_valid:
                logger.info("✅ Configuration reloaded successfully")
                return True
            else:
                logger.error(f"❌ Configuration reload failed: {self._validation.get_summary()}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Configuration reload error: {e}")
            return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert the config object to a dictionary."""
        return {
            # Pipeline settings
            'enabled': self.enabled,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'quality_threshold': self.quality_threshold,
            
            # Caching settings
            'cache_enabled': self.cache_enabled,
            'cache_ttl': self.cache_ttl,
            
            # Performance settings
            'parallel_execution': self.parallel_execution,
            'section_timeout': self.section_timeout,
            'quality_check_enabled': self.quality_check_enabled,
            'fallback_enabled': self.fallback_enabled,
            
            # Monitoring settings
            'log_level': self.log_level,
            'enable_metrics': self.enable_metrics,
            'enable_audit_trail': self.enable_audit_trail,
            'enable_performance_tracking': self.enable_performance_tracking,
            
            # Response settings
            'response_template_cache_enabled': self.response_template_cache_enabled,
            'response_template_cache_ttl': self.response_template_cache_ttl,
            'response_max_length': self.response_max_length,
            'response_enable_markdown': self.response_enable_markdown,
            
            # Market data providers
            'yahoo_finance_enabled': self.yahoo_finance_enabled,
            'polygon_enabled': self.polygon_enabled,
            'finnhub_enabled': self.finnhub_enabled,
            'alpha_vantage_enabled': self.alpha_vantage_enabled,
            
            # Rate limits
            'yahoo_finance_rate_limit': self.yahoo_finance_rate_limit,
            'polygon_rate_limit': self.polygon_rate_limit,
            'finnhub_rate_limit': self.finnhub_rate_limit,
            'alpha_vantage_rate_limit': self.alpha_vantage_rate_limit,
            
            # Timeouts
            'yahoo_finance_timeout': self.yahoo_finance_timeout,
            'polygon_timeout': self.polygon_timeout,
            'finnhub_timeout': self.finnhub_timeout,
            'alpha_vantage_timeout': self.alpha_vantage_timeout,
            
            # Development/testing
            'enable_mock_data': self.enable_mock_data,
            'enable_debug_logging': self.enable_debug_logging,
            'enable_performance_profiling': self.enable_performance_profiling,
            'enable_api_response_logging': self.enable_api_response_logging,
            
            # Circuit breaker
            'circuit_breaker_enabled': self.circuit_breaker_enabled,
            'circuit_breaker_threshold': self.circuit_breaker_threshold,
            'circuit_breaker_cooldown': self.circuit_breaker_cooldown,
            'circuit_breaker_timeout': self.circuit_breaker_timeout
        }


@lru_cache()
def get_config() -> AskPipelineConfig:
    """Get cached configuration instance"""
    return AskPipelineConfig()


# Global configuration instance
config = get_config()


def get_config_summary() -> Dict[str, Any]:
    """Get configuration summary for monitoring"""
    return config.get_config_summary()


def reload_config() -> bool:
    """Reload configuration (for runtime updates)"""
    return config.reload_configuration() 