"""
Symbol Validator - Dollar Sign Required

Only extracts ticker symbols that are prefixed with $ sign.
Clean, simple implementation with proper error handling.
"""

from dataclasses import dataclass
import logging
import os
import re
from typing import List, Optional

        
        # Remove basic dangerous patterns
        dangerous = [
            r'<script.*?>', r'javascript:', r'<iframe.*?>', r'<object.*?>',
            r'<embed.*?>', r'<form.*?>', r'<input.*?>', r'<textarea.*?>'
        ]
        
        sanitized = query
        for pattern in dangerous:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # Basic length limit
        if len(sanitized) > 2000:
            sanitized = sanitized[:2000]
        
        return sanitized.strip()

    def _analyze_query_context(self, query: str) -> dict:
        """Simple context analysis of the query."""
        return {
            "contains_symbols": '$' in query,
            "contains_numbers": any(c.isdigit() for c in query),
            "estimated_complexity": "simple" if len(query) < 100 else "moderate" if len(query) < 300 else "complex"
        }

    def is_enhanced_mode(self) -> bool:
        """Check if enhanced features are available."""
        return hasattr(self, 'enhanced_validation')