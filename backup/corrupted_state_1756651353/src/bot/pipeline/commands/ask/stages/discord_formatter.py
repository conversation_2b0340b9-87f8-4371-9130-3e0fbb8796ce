"""
Discord Formatter Utility

Enhanced Discord formatting with rich embeds, color coding, and structured displays
for trading recommendations and market analysis.
"""

from datetime import datetime
import logging
from typing import Dict, Any, List, Optional

import discord


logger = logging.getLogger(__name__)

class DiscordFormatter:
    """Enhanced Discord formatting for trading bot responses"""
    
    # Color constants for different recommendation types
    COLORS = {
        'BUY': discord.Color.green(),
        'SELL': discord.Color.red(),
        'HOLD': discord.Color.blue(),
        'WAIT': discord.Color.orange(),
        'NEUTRAL': discord.Color.light_grey(),
        'ERROR': discord.Color.dark_red(),
        'WARNING': discord.Color.gold(),
        'SUCCESS': discord.Color.green()
    }
    
    # Emoji constants for different statuses
    EMOJIS = {
        'BUY': '🟢',
        'SELL': '🔴',
        'HOLD': '🟡',
        'WAIT': '🟠',
        'SUCCESS': '✅',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'INFO': 'ℹ️',
        'MONEY': '💰',
        'CHART': '📊',
        'TREND': '📈',
        'VOLUME': '📊',
        'TIME': '⏰',
        'SOURCE': '🔗'
    }
    
    @staticmethod
    def create_stock_analysis_embed(symbol: str, data: Dict[str, Any], 
                                   confidence: float, recommendation: str) -> discord.Embed:
        """Create a rich Discord embed for stock analysis"""
        try:
            # Determine color based on recommendation
            action = recommendation.split()[0] if recommendation else "HOLD"
            color = DiscordFormatter.COLORS.get(action, DiscordFormatter.COLORS['NEUTRAL'])
            
            # Create embed
            embed = discord.Embed(
                title=f"{DiscordFormatter.EMOJIS['CHART']} {symbol.upper()} Analysis",
                description=f"**{DiscordFormatter.EMOJIS['MONEY']} Market Analysis Report**",
                color=color,
                timestamp=datetime.now()
            )
            
            # Add price information
            current_price = data.get('current_price', 0.0)
            change_percent = data.get('change_percent', 0.0)
            change_amount = data.get('change', 0.0)
            volume = data.get('volume', 0)
            
            # Price field with color coding
            change_sign = "+" if change_percent >= 0 else ""
            price_text = f"${current_price:.2f}"
            change_text = f"{change_sign}{change_percent:.2f}% ({change_sign}${abs(change_amount):.2f})"
            
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['MONEY']} Current Price",
                value=price_text,
                inline=True
            )
            
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['TREND']} Daily Change",
                value=change_text,
                inline=True
            )
            
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['VOLUME']} Volume",
                value=f"{volume:,}",
                inline=True
            )
            
            # Recommendation field with confidence
            confidence_emoji = DiscordFormatter._get_confidence_emoji(confidence)
            recommendation_text = f"{DiscordFormatter.EMOJIS.get(action, '')} **{recommendation}**"
            confidence_text = f"{confidence_emoji} Confidence: **{confidence:.0f}%**"
            
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['CHART']} Trading Recommendation",
                value=f"{recommendation_text}\n{confidence_text}",
                inline=False
            )
            
            # Data quality indicator
            data_quality = data.get('data_quality', 0.0)
            quality_emoji = DiscordFormatter._get_quality_emoji(data_quality)
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['INFO']} Data Quality",
                value=f"{quality_emoji} {data_quality:.0f}%",
                inline=True
            )
            
            # Risk level
            risk_level = data.get('risk_level', 'Medium')
            risk_emoji = DiscordFormatter._get_risk_emoji(risk_level)
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['WARNING']} Risk Level",
                value=f"{risk_emoji} {risk_level}",
                inline=True
            )
            
            # Add technical patterns if available
            patterns = data.get('technical_patterns', [])
            if patterns:
                pattern_text = "\n".join([
                    f"• {pattern.get('pattern_type', 'Unknown')} ({pattern.get('direction', 'neutral')})"
                    for pattern in patterns[:3]  # Show first 3 patterns
                ])
                embed.add_field(
                    name=f"{DiscordFormatter.EMOJIS['CHART']} Technical Patterns",
                    value=pattern_text,
                    inline=False
                )
            
            # Add sentiment if available
            sentiment = data.get('sentiment', {})
            if sentiment and 'sentiment_label' in sentiment:
                sentiment_emoji = DiscordFormatter._get_sentiment_emoji(sentiment['overall_sentiment'])
                embed.add_field(
                    name=f"{DiscordFormatter.EMOJIS['TREND']} Market Sentiment",
                    value=f"{sentiment_emoji} {sentiment['sentiment_label']}",
                    inline=True
                )
            
            # Add warning if using fallback data
            if data.get('status') == 'fallback' or data.get('status') == 'fallback_enhanced':
                embed.add_field(
                    name=f"{DiscordFormatter.EMOJIS['WARNING']} Data Source",
                    value="⚠️ Using fallback data - real-time data unavailable",
                    inline=False
                )
            
            # Footer with timestamp and source
            source = data.get('source', 'unknown')
            embed.set_footer(
                text=f"Analysis generated at {datetime.now().strftime('%H:%M:%S')} • Source: {source.title()}"
            )
            
            return embed
            
        except Exception as e:
            logger.error(f"Error creating stock analysis embed: {e}")
            return DiscordFormatter._create_error_embed(f"Failed to create analysis embed: {str(e)}")
    
    @staticmethod
    def create_market_overview_embed(market_data: Dict[str, Any]) -> discord.Embed:
        """Create a rich Discord embed for market overview"""
        try:
            embed = discord.Embed(
                title=f"{DiscordFormatter.EMOJIS['CHART']} Market Overview",
                description=f"**{DiscordFormatter.EMOJIS['TREND']} Major Market Indices**",
                color=DiscordFormatter.COLORS['NEUTRAL'],
                timestamp=datetime.now()
            )
            
            # Add market indices
            indices = market_data.get('market_indices', {})
            for index_name, index_data in indices.items():
                if isinstance(index_data, dict):
                    price = index_data.get('current_price', 0.0)
                    change = index_data.get('change_percent', 0.0)
                    
                    change_sign = "+" if change >= 0 else ""
                    change_text = f"{change_sign}{change:.2f}%"
                    
                    # Color code based on change
                    if change > 0:
                        change_text = f"🟢 {change_text}"
                    elif change < 0:
                        change_text = f"🔴 {change_text}"
                    else:
                        change_text = f"🟡 {change_text}"
                    
                    embed.add_field(
                        name=f"{DiscordFormatter.EMOJIS['CHART']} {index_name}",
                        value=f"${price:.2f} {change_text}",
                        inline=True
                    )
            
            # Add overall market sentiment
            overall_sentiment = market_data.get('overall_sentiment', 'Neutral')
            sentiment_emoji = DiscordFormatter._get_sentiment_emoji(0.0)  # Default neutral
            embed.add_field(
                name=f"{DiscordFormatter.EMOJIS['TREND']} Overall Sentiment",
                value=f"{sentiment_emoji} {overall_sentiment}",
                inline=False
            )
            
            # Footer
            embed.set_footer(text=f"Market data updated at {datetime.now().strftime('%H:%M:%S')}")
            
            return embed
            
        except Exception as e:
            logger.error(f"Error creating market overview embed: {e}")
            return DiscordFormatter._create_error_embed(f"Failed to create market overview: {str(e)}")
    
    @staticmethod
    def create_error_embed(error_message: str, symbol: str = None) -> discord.Embed:
        """Create an error embed with helpful information"""
        embed = discord.Embed(
            title=f"{DiscordFormatter.EMOJIS['ERROR']} Analysis Error",
            description=f"**{DiscordFormatter.EMOJIS['WARNING']} Unable to complete analysis**",
            color=DiscordFormatter.COLORS['ERROR'],
            timestamp=datetime.now()
        )
        
        embed.add_field(
            name="Error Details",
            value=f"```{error_message}```",
            inline=False
        )
        
        if symbol:
            embed.add_field(
                name="Symbol",
                value=f"`{symbol.upper()}`",
                inline=True
            )
        
        embed.add_field(
            name="Recommendations",
            value="• Check if the symbol is valid\n• Try again in a few moments\n• Use fallback mode if available",
            inline=False
        )
        
        embed.set_footer(text="Error occurred during analysis")
        
        return embed
    
    @staticmethod
    def create_fallback_embed(symbol: str, fallback_data: Dict[str, Any]) -> discord.Embed:
        """Create an embed for fallback data"""
        embed = discord.Embed(
            title=f"{DiscordFormatter.EMOJIS['WARNING']} {symbol.upper()} - Fallback Analysis",
            description=f"**{DiscordFormatter.EMOJIS['INFO']} Using fallback data system**",
            color=DiscordFormatter.COLORS['WARNING'],
            timestamp=datetime.now()
        )
        
        # Add fallback data
        current_price = fallback_data.get('current_price', 0.0)
        change_percent = fallback_data.get('change_percent', 0.0)
        confidence = fallback_data.get('confidence', 0.0)
        recommendation = fallback_data.get('recommendation', 'HOLD')
        
        embed.add_field(
            name=f"{DiscordFormatter.EMOJIS['MONEY']} Estimated Price",
            value=f"${current_price:.2f}",
            inline=True
        )
        
        change_sign = "+" if change_percent >= 0 else ""
        embed.add_field(
            name=f"{DiscordFormatter.EMOJIS['TREND']} Estimated Change",
            value=f"{change_sign}{change_percent:.2f}%",
            inline=True
        )
        
        confidence_emoji = DiscordFormatter._get_confidence_emoji(confidence)
        embed.add_field(
            name=f"{DiscordFormatter.EMOJIS['INFO']} Confidence",
            value=f"{confidence_emoji} {confidence:.0f}%",
            inline=True
        )
        
        embed.add_field(
            name=f"{DiscordFormatter.EMOJIS['CHART']} Recommendation",
            value=f"{DiscordFormatter.EMOJIS.get(recommendation.split()[0], '')} {recommendation}",
            inline=False
        )
        
        embed.add_field(
            name=f"{DiscordFormatter.EMOJIS['WARNING']} Note",
            value="This analysis uses fallback data. Real-time data may differ.",
            inline=False
        )
        
        embed.set_footer(text="Fallback analysis generated")
        
        return embed
    
    @staticmethod
    def _get_confidence_emoji(confidence: float) -> str:
        """Get emoji for confidence level"""
        if confidence >= 80:
            return "🟢"
        elif confidence >= 60:
            return "🟡"
        elif confidence >= 40:
            return "🟠"
        else:
            return "🔴"
    
    @staticmethod
    def _get_quality_emoji(quality: float) -> str:
        """Get emoji for data quality"""
        if quality >= 80:
            return "🟢"
        elif quality >= 60:
            return "🟡"
        elif quality >= 40:
            return "🟠"
        else:
            return "🔴"
    
    @staticmethod
    def _get_risk_emoji(risk_level: str) -> str:
        """Get emoji for risk level"""
        risk_emojis = {
            'Low': '🟢',
            'Medium': '🟡',
            'High': '🔴'
        }
        return risk_emojis.get(risk_level, '🟡')
    
    @staticmethod
    def _get_sentiment_emoji(sentiment: float) -> str:
        """Get emoji for sentiment"""
        if sentiment > 0.3:
            return "🟢"
        elif sentiment > -0.3:
            return "🟡"
        else:
            return "🔴"
    
    @staticmethod
    def create_progress_bar(percentage: float, width: int = 10) -> str:
        """Create a visual progress bar for confidence/quality scores"""
        filled = int(width * percentage / 100)
        empty = width - filled
        
        bar = "█" * filled + "░" * empty
        return f"{bar} {percentage:.0f}%"
    
    @staticmethod
    def format_currency(amount: float) -> str:
        """Format currency amounts with proper formatting"""
        if amount >= 1_000_000_000:
            return f"${amount/1_000_000_000:.2f}B"
        elif amount >= 1_000_000:
            return f"${amount/1_000_000:.2f}M"
        elif amount >= 1_000:
            return f"${amount/1_000:.2f}K"
        else:
            return f"${amount:.2f}"
    
    @staticmethod
    def format_percentage(percentage: float) -> str:
        """Format percentage with proper sign and precision"""
        sign = "+" if percentage >= 0 else ""
        return f"{sign}{percentage:.2f}%" 