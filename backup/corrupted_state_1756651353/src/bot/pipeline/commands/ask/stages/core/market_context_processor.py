"""
Enhanced Market Context Processor for the /ask pipeline.

This module extracts the market context logic from ai_chat_processor.py
including comprehensive market context gathering, formatting, and enhancement.
"""

from datetime import datetime
import logging
import time
from typing import Dict, Any, List, Optional, Union

from config import get_config
from market_context_service import is_market_open
from market_context_service import is_market_open

                market_open = is_market_open()
                market_context['market_status'] = {
                    'markets_open': market_open,
                    'current_time_et': datetime.now().strftime('%H:%M:%S'),
                    'status_description': 'Open' if market_open else 'Closed'
                }
            except ImportError:
                market_context['market_status'] = {
                    'markets_open': False,
                    'current_time_et': datetime.now().strftime('%H:%M:%S'),
                    'status_description': 'Unknown'
                }
            
            return market_context
            
        except Exception as e:
            logger.error(f"❌ [{pipeline_id}] Legacy market context generation failed: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'generation_mode': 'legacy',
                'error': str(e)
            }
    
    def format_enhanced_market_context(self, market_context: Dict[str, Any], 
                                     pipeline_id: str = None) -> str:
        """
        Format enhanced market context for display.
        
        This replaces the monolithic _format_enhanced_market_context method from ai_chat_processor.py
        
        Args:
            market_context: Market context data dictionary
            pipeline_id: Optional pipeline ID for logging
            
        Returns:
            Formatted market context string
        """
        if not self._enhancements_enabled:
            return self._legacy_format_market_context(market_context, pipeline_id)
        
        try:
            start_time = time.time()
            
            formatted_context = self._enhanced_context_formatting(market_context)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self._update_performance_metrics(processing_time, True)
            
            logger.debug(f"✅ [{pipeline_id}] Enhanced market context formatting successful")
            return formatted_context
            
        except Exception as e:
            logger.error(f"❌ [{pipeline_id}] Enhanced market context formatting failed: {e}")
            self._update_performance_metrics(0, False)
            # Fall back to legacy method
            return self._legacy_format_market_context(market_context, pipeline_id)
    
    def _enhanced_context_formatting(self, market_context: Dict[str, Any]) -> str:
        """Enhanced market context formatting with rich information."""
        if not market_context:
            return ""
        
        formatted_parts = []
        
        # Market status
        if 'market_status' in market_context:
            status = market_context['market_status']
            status_emoji = "🟢" if status.get('markets_open', False) else "🔴"
            formatted_parts.append(
                f"{status_emoji} **Market Status**: {status.get('status_description', 'Unknown')} "
                f"({status.get('current_time_et', 'N/A')})"
            )
        
        # Sector context
        if 'sector_context' in market_context:
            sector = market_context['sector_context']
            if 'sector' in sector and 'sector_performance' in sector:
                perf = sector['sector_performance']
                daily_sign = "+" if perf.get('daily_change', 0) >= 0 else ""
                formatted_parts.append(
                    f"🏭 **Sector**: {sector['sector']} ({daily_sign}{perf.get('daily_change', 0):.1f}% today)"
                )
        
        # Global market indicators
        if 'global_market_indicators' in market_context:
            indicators = market_context['global_market_indicators']
            if 'sp500_change' in indicators:
                sp500_sign = "+" if indicators['sp500_change'] >= 0 else ""
                formatted_parts.append(
                    f"📊 **S&P 500**: {sp500_sign}{indicators['sp500_change']:.1f}%"
                )
        
        # Economic context
        if 'economic_context' in market_context:
            econ = market_context['economic_context']
            if 'fed_funds_rate' in econ:
                formatted_parts.append(
                    f"🏦 **Fed Rate**: {econ['fed_funds_rate']:.2f}%"
                )
        
        # Market sentiment
        if 'market_sentiment' in market_context:
            sentiment = market_context['market_sentiment']
            if 'fear_greed_index' in sentiment:
                index = sentiment['fear_greed_index']
                sentiment_level = "Fear" if index < 30 else "Greed" if index > 70 else "Neutral"
                formatted_parts.append(
                    f"😨 **Fear & Greed**: {index} ({sentiment_level})"
                )
        
        # Combine all parts
        if formatted_parts:
            return "\n".join(formatted_parts)
        else:
            return ""
    
    def _legacy_format_market_context(self, market_context: Dict[str, Any], 
                                    pipeline_id: str) -> str:
        """Legacy market context formatting method."""
        if not market_context:
            return ""
        
        formatted_parts = []
        
        # Basic market status
        if 'market_status' in market_context:
            status = market_context['market_status']
            if status.get('markets_open', False):
                formatted_parts.append("🟢 Markets are currently OPEN")
            else:
                formatted_parts.append("🔴 Markets are currently CLOSED")
        
        # Basic timestamp
        if 'timestamp' in market_context:
            try:
                timestamp = datetime.fromisoformat(market_context['timestamp'])
                formatted_parts.append(f"📅 Data as of: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            except:
                pass
        
        # Combine parts
        if formatted_parts:
            return "\n".join(formatted_parts)
        else:
            return ""
    
    def _update_performance_metrics(self, processing_time: float, success: bool) -> None:
        """Update performance tracking metrics."""
        if success:
            self.successful_context_requests += 1
            # Update average processing time
            if self.successful_context_requests == 1:
                self.avg_processing_time = processing_time
            else:
                self.avg_processing_time = (
                    (self.avg_processing_time * (self.successful_context_requests - 1) + processing_time) 
                    / self.successful_context_requests
                )
        else:
            self.failed_context_requests += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        total_requests = self.total_context_requests
        success_rate = (self.successful_context_requests / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_context_requests': total_requests,
            'successful_context_requests': self.successful_context_requests,
            'failed_context_requests': self.failed_context_requests,
            'success_rate': success_rate,
            'avg_processing_time': self.avg_processing_time,
            'enhancements_enabled': self._enhancements_enabled,
            'context_cache_size': len(self.context_cache),
            'context_cache_ttl': self.context_cache_ttl
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Market context processor enhancements {'enabled' if enable else 'disabled'}")
    
    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.total_context_requests = 0
        self.successful_context_requests = 0
        self.failed_context_requests = 0
        self.avg_processing_time = 0.0
        logger.info("Market context processor metrics reset")
    
    def clear_cache(self) -> None:
        """Clear the market context cache."""
        cache_size = len(self.context_cache)
        self.context_cache.clear()
        logger.info(f"Market context cache cleared ({cache_size} entries)")
    
    def update_cache_ttl(self, new_ttl: int) -> None:
        """Update the cache TTL."""
        self.context_cache_ttl = new_ttl
        logger.info(f"Market context cache TTL updated to {new_ttl}s")


# Global instance for easy access
market_context_processor = MarketContextProcessor() 