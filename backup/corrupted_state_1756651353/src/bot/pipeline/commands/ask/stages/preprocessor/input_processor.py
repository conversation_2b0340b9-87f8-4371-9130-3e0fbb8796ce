"""
Input Processor for the /ask pipeline.

This module integrates our enhanced SymbolValidator with the main pipeline
to replace the monolithic validation logic.
"""

import logging
import re
import re
from typing import Tuple, List, Dict, Any

from config import get_config
from symbol_validator import SymbolValidator

        # Look for $ followed by uppercase letters
        symbol_pattern = r'\$([A-Z]{1,5})'
        matches = re.findall(symbol_pattern, text)
        
        # Remove duplicates and validate
        valid_symbols = []
        for symbol in set(matches):
            if re.match(r'^[A-Z]{1,5}$', symbol):
                valid_symbols.append(symbol)
        
        return valid_symbols
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get statistics about validation performance."""
        return {
            "enhancements_enabled": self._enhancements_enabled,
            "symbol_validator_enhanced": self.symbol_validator.is_enhanced_mode(),
            "config": {
                "enable_symbol_validator_enhancements": self.config.enable_symbol_validator_enhancements
            }
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Input processor enhancements {'enabled' if enable else 'disabled'}")


# Global instance for easy access
input_processor = InputProcessor() 