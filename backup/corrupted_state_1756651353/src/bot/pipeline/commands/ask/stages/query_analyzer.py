"""
Query Analysis Module

Intelligent query analysis and intent classification.
"""

from dataclasses import dataclass
from enum import Enum
import logging
from typing import List, Dict, Any, Optional

from symbol_validator import SymbolValidator


logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    """AI-determined query intent"""
    STOCK_ANALYSIS = "stock_analysis"
    MARKET_OVERVIEW = "market_overview"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    COMPARISON = "comparison"
    GENERAL_QUESTION = "general_question"
    PORTFOLIO_ADVICE = "portfolio_advice"
    RISK_ASSESSMENT = "risk_assessment"

class ProcessingRoute(Enum):
    """AI-determined processing route"""
    DATA_DRIVEN = "data_driven"
    KNOWLEDGE_BASED = "knowledge_based"
    HYBRID = "hybrid"
    QUICK_RESPONSE = "quick_response"

@dataclass
class SymbolContext:
    """Context about a potential symbol - AI decides what to do with it"""
    text: str
    confidence: float
    context: str
    is_likely_ticker: bool
    suggestions: List[str]
    should_analyze: bool = False  # AI decides this

@dataclass
class QueryAnalysis:
    """AI-driven query analysis with flexible symbol handling"""
    intent: QueryIntent
    confidence: float
    symbols: List[SymbolContext]  # AI gets context, decides what to do
    processing_route: ProcessingRoute
    data_requirements: Dict[str, Any]
    context_clues: List[str]
    response_style: str
    urgency: str
    complexity: int  # 1-10 scale
    requires_market_data: bool
    requires_ai_reasoning: bool

class AIQueryAnalyzer:
    """Pure AI-driven query analysis - flexible and intelligent"""
    
    def __init__(self):
        self.symbol_validator: Optional[SymbolValidator] = None  # Properly typed with import
        self._initialize_route_specifications()
    
    def _initialize_route_specifications(self) -> Dict[ProcessingRoute, Dict[str, Any]]:
        """Define processing routes - AI chooses based on context"""
        return {
            ProcessingRoute.DATA_DRIVEN: {
                "description": "Heavy data collection and analysis",
                "use_cases": ["stock_analysis", "technical_analysis", "fundamental_analysis"],
                "data_sources": ["market_data", "technical_indicators", "fundamentals"],
                "response_type": "comprehensive_analysis"
            },
            ProcessingRoute.KNOWLEDGE_BASED: {
                "description": "AI reasoning and knowledge synthesis",
                "use_cases": ["general_question", "portfolio_advice", "risk_assessment"],
                "data_sources": ["knowledge_base", "ai_reasoning"],
                "response_type": "expert_advice"
            },
            ProcessingRoute.HYBRID: {
                "description": "Combined data and knowledge approach",
                "use_cases": ["comparison", "market_overview"],
                "data_sources": ["market_data", "ai_reasoning"],
                "response_type": "balanced_insight"
            },
            ProcessingRoute.QUICK_RESPONSE: {
                "description": "Fast, lightweight response",
                "use_cases": ["simple_questions", "quick_checks"],
                "data_sources": ["basic_data"],
                "response_type": "concise_answer"
            }
        }
    
    async def analyze_query(self, query: str, context: Any) -> QueryAnalysis:
        """
        AI-driven query analysis - flexible and intelligent
        
        The AI gets context about potential symbols but makes its own decisions
        about what to analyze and how to process the query.
        """
        
        query_lower = query.lower().strip()
        
        # Get symbol suggestions (not rigid validation)
        symbol_suggestions = self._get_symbol_suggestions(query)
        
        # AI-style intent classification (flexible)
        intent, confidence = self._classify_intent(query_lower, symbol_suggestions)
        
        # AI decides which symbols to actually analyze
        symbols_to_analyze = self._ai_symbol_selection(symbol_suggestions, query_lower, intent)
        
        # AI-style route selection (flexible)
        processing_route = self._select_processing_route(intent, symbols_to_analyze, query_lower)
        
        # AI-style data requirements analysis
        data_requirements = self._analyze_data_requirements(intent, symbols_to_analyze, processing_route)
        
        # AI-style context analysis
        context_clues = self._extract_context_clues(query_lower, symbol_suggestions)
        
        # AI-style response planning
        response_style = self._determine_response_style(intent, context_clues, symbols_to_analyze)
        urgency = self._assess_urgency(query_lower, intent)
        complexity = self._assess_complexity(query_lower, symbols_to_analyze, intent)
        
        # AI decides if market data is needed
        requires_market_data = self._ai_decides_market_data_needed(symbols_to_analyze, intent)
        requires_ai_reasoning = self._ai_decides_reasoning_needed(intent, complexity)
        
        logger.info(f"AI Query Analysis - Intent: {intent.value}, Route: {processing_route.value}, "
                   f"Symbols to analyze: {[s.text for s in symbols_to_analyze]}, Confidence: {confidence}")
        
        return QueryAnalysis(
            intent=intent,
            confidence=confidence,
            symbols=symbols_to_analyze,
            processing_route=processing_route,
            data_requirements=data_requirements,
            context_clues=context_clues,
            response_style=response_style,
            urgency=urgency,
            complexity=complexity,
            requires_market_data=requires_market_data,
            requires_ai_reasoning=requires_ai_reasoning
        )
    
    def _get_symbol_suggestions(self, query: str) -> List[SymbolContext]:
        """Get symbol suggestions without rigid filtering"""
        if not hasattr(self, 'symbol_validator') or not self.symbol_validator:
            # Fallback if no validator available
            return []
        
        try:
            suggestions = self.symbol_validator.suggest_symbols(query)
            # Convert to our SymbolContext format
            return [
                SymbolContext(
                    text=s.text,
                    confidence=s.confidence,
                    context=s.context,
                    is_likely_ticker=s.is_likely_ticker,
                    suggestions=s.suggestions
                ) for s in suggestions
            ]
        except Exception as e:
            logger.warning(f"Error getting symbol suggestions: {e}")
            return []
    
    def _ai_symbol_selection(self, suggestions: List[SymbolContext], query: str, intent: QueryIntent) -> List[SymbolContext]:
        """
        AI decides which symbols to actually analyze
        
        This is where the intelligence happens - the AI gets context but makes its own decisions
        about what to analyze and how to process the query.
        """
        if not suggestions:
            return []
        
        selected_symbols = []
        
        for suggestion in suggestions:
            # AI decision logic - flexible and context-aware
            
            # High confidence symbols are likely worth analyzing
            if suggestion.confidence > 0.8:
                suggestion.should_analyze = True
                selected_symbols.append(suggestion)
                continue
            
            # Medium confidence - AI decides based on context
            if suggestion.confidence > 0.5:
                # Check if the query context suggests this should be analyzed
                if self._ai_context_suggests_analysis(suggestion, query, intent):
                    suggestion.should_analyze = True
                    selected_symbols.append(suggestion)
                else:
                    suggestion.should_analyze = False
                continue
            
            # Low confidence - AI might still analyze if context is strong
            if suggestion.confidence > 0.2:
                if self._ai_context_strongly_suggests_analysis(suggestion, query, intent):
                    suggestion.should_analyze = True
                    selected_symbols.append(suggestion)
                else:
                    suggestion.should_analyze = False
                continue
        
        return selected_symbols
    
    def _ai_context_suggests_analysis(self, suggestion: SymbolContext, query: str, intent: QueryIntent) -> bool:
        """AI decides if context suggests analysis should happen"""
        
        # Strong stock-related context
        stock_indicators = ['stock', 'price', 'trading', 'market', 'quote', 'symbol', 'ticker', 'vs', 'compare', 'analysis']
        has_stock_context = any(indicator in query.lower() for indicator in stock_indicators)
        
        # Strong intent for analysis
        analysis_intents = [QueryIntent.STOCK_ANALYSIS, QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.FUNDAMENTAL_ANALYSIS]
        strong_analysis_intent = intent in analysis_intents
        
        # Symbol is in ticker database
        is_verified_ticker = suggestion.is_likely_ticker
        
        # AI decision: analyze if any strong indicators present
        return has_stock_context or strong_analysis_intent or is_verified_ticker
    
    def _ai_context_strongly_suggests_analysis(self, suggestion: SymbolContext, query: str, intent: QueryIntent) -> bool:
        """AI decides if context strongly suggests analysis"""
        
        # Skip analysis if query is clearly not about stocks
        general_terms = ['what', 'why', 'how', 'explain', 'tell me']
        if any(term in query.lower() for term in general_terms) and intent == QueryIntent.GENERAL_QUESTION:
            return False
        
        # Only proceed with analysis if:
        # 1. Explicit stock/ticker mention AND high confidence
        # OR 
        # 2. Strong technical/fundamental analysis intent
        stock_terms = ['stock', 'ticker', 'symbol', '$']
        has_explicit_stock_ref = any(term in query.lower() for term in stock_terms)
        
        analysis_intents = [QueryIntent.STOCK_ANALYSIS, QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.FUNDAMENTAL_ANALYSIS]
        
        return (has_explicit_stock_ref and suggestion.confidence > 0.9) or \
               (intent in analysis_intents and suggestion.confidence > 0.7)
    
    def _classify_intent(self, query: str, symbols: List[SymbolContext]) -> tuple[QueryIntent, float]:
        """Improved intent classification that avoids false stock matches"""
        query_lower = query.lower()
        
        # Check for general questions first
        general_terms = ['what', 'why', 'how', 'explain', 'tell me', '?']
        if any(term in query_lower for term in general_terms):
            # Only classify as stock analysis if explicitly mentioned
            stock_terms = ['stock', 'ticker', 'symbol', '$']
            if not any(term in query_lower for term in stock_terms):
                return QueryIntent.GENERAL_QUESTION, 0.9
        
        # Multiple intent indicators increase confidence
        indicators = 0
        
        # Stock analysis indicators
        if any(word in query_lower for word in ['stock', 'price', 'trading', 'market', 'quote']):
            indicators += 1
        if any(word in query_lower for word in ['analyze', 'analysis', 'chart', 'technical']):
            indicators += 1
        if any(word in query_lower for word in ['earnings', 'fundamental', 'financial']):
            indicators += 1
        
        # Comparison indicators
        if any(word in query_lower for word in ['vs', 'versus', 'compare', 'better']):
            indicators += 1
        
        # General question indicators
        if any(word in query_lower for word in ['what', 'how', 'why', 'when', 'where']):
            indicators += 1
        
        # Adjust confidence based on indicators
        confidence = 0.5  # Default confidence
        if indicators >= 3:
            confidence = 0.9
        elif indicators >= 2:
            confidence = 0.7
        elif indicators >= 1:
            confidence = 0.6
        
        # Determine intent based on strongest signals
        if any(word in query_lower for word in ['technical', 'chart', 'indicator']):
            intent = QueryIntent.TECHNICAL_ANALYSIS
        elif any(word in query_lower for word in ['earnings', 'fundamental', 'financial']):
            intent = QueryIntent.FUNDAMENTAL_ANALYSIS
        elif any(word in query_lower for word in ['vs', 'versus', 'compare']):
            intent = QueryIntent.COMPARISON
        elif any(word in query_lower for word in ['stock', 'price', 'trading']):
            intent = QueryIntent.STOCK_ANALYSIS
        elif any(word in query_lower for word in ['portfolio', 'investment', 'advice']):
            intent = QueryIntent.PORTFOLIO_ADVICE
        elif any(word in query_lower for word in ['risk', 'volatility', 'safe']):
            intent = QueryIntent.RISK_ASSESSMENT
        else:
            intent = QueryIntent.GENERAL_QUESTION
        
        return intent, confidence
    
    def _select_processing_route(self, intent: QueryIntent, symbols: List[SymbolContext], query: str) -> ProcessingRoute:
        """AI decides processing route based on context"""
        
        # High confidence symbols suggest data-driven approach
        high_confidence_symbols = [s for s in symbols if s.confidence > 0.7]
        
        # Strong analysis intents suggest comprehensive processing
        analysis_intents = [QueryIntent.STOCK_ANALYSIS, QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.FUNDAMENTAL_ANALYSIS]
        
        if intent in analysis_intents and high_confidence_symbols:
            return ProcessingRoute.DATA_DRIVEN
        elif intent in analysis_intents:
            return ProcessingRoute.HYBRID
        elif intent == QueryIntent.COMPARISON and len(symbols) >= 2:
            return ProcessingRoute.HYBRID
        elif intent in [QueryIntent.PORTFOLIO_ADVICE, QueryIntent.RISK_ASSESSMENT]:
            return ProcessingRoute.KNOWLEDGE_BASED
        elif intent == QueryIntent.GENERAL_QUESTION:
            return ProcessingRoute.QUICK_RESPONSE
        else:
            return ProcessingRoute.HYBRID
    
    def _analyze_data_requirements(self, intent: QueryIntent, symbols: List[SymbolContext], route: ProcessingRoute) -> Dict[str, Any]:
        """AI determines what data is needed"""
        
        requirements = {
            "market_data": False,
            "technical_indicators": False,
            "fundamentals": False,
            "news_sentiment": False,
            "ai_reasoning": True  # Always need AI reasoning
        }
        
        # Data-driven routes need market data
        if route in [ProcessingRoute.DATA_DRIVEN, ProcessingRoute.HYBRID]:
            if symbols:
                requirements["market_data"] = True
                if intent in [QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.STOCK_ANALYSIS]:
                    requirements["technical_indicators"] = True
                if intent in [QueryIntent.FUNDAMENTAL_ANALYSIS, QueryIntent.STOCK_ANALYSIS]:
                    requirements["fundamentals"] = True
        
        return requirements
    
    def _extract_context_clues(self, query: str, symbols: List[SymbolContext]) -> List[str]:
        """Extract context clues for AI reasoning"""
        
        clues = []
        query_lower = query.lower()
        
        # Time context
        if any(word in query_lower for word in ['today', 'now', 'current', 'latest']):
            clues.append("time_urgency")
        if any(word in query_lower for word in ['tomorrow', 'next', 'future', 'prediction']):
            clues.append("future_focus")
        
        # Market context
        if any(word in query_lower for word in ['bull', 'bear', 'trend', 'momentum']):
            clues.append("market_sentiment")
        if any(word in query_lower for word in ['volatility', 'risk', 'safe']):
            clues.append("risk_focus")
        
        # Analysis depth
        if any(word in query_lower for word in ['detailed', 'comprehensive', 'deep']):
            clues.append("deep_analysis")
        if any(word in query_lower for word in ['quick', 'simple', 'brief']):
            clues.append("quick_analysis")
        
        # Symbol confidence context
        high_confidence = [s for s in symbols if s.confidence > 0.7]
        if high_confidence:
            clues.append("high_confidence_symbols")
        elif symbols:
            clues.append("low_confidence_symbols")
        
        return clues
    
    def _determine_response_style(self, intent: QueryIntent, context_clues: List[str], symbols: List[SymbolContext]) -> str:
        """AI determines response style"""
        
        if "deep_analysis" in context_clues:
            return "comprehensive"
        elif "quick_analysis" in context_clues:
            return "concise"
        elif intent in [QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.FUNDAMENTAL_ANALYSIS]:
            return "detailed"
        elif intent == QueryIntent.COMPARISON:
            return "comparative"
        else:
            return "balanced"
    
    def _assess_urgency(self, query: str, intent: QueryIntent) -> str:
        """AI assesses urgency"""
        
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['now', 'urgent', 'quick', 'immediate']):
            return "high"
        elif any(word in query_lower for word in ['today', 'current', 'latest']):
            return "medium"
        else:
            return "low"
    
    def _assess_complexity(self, query: str, symbols: List[SymbolContext], intent: QueryIntent) -> int:
        """AI assesses query complexity (1-10 scale)"""
        
        complexity = 1  # Base complexity
        
        # Add complexity for multiple symbols
        if len(symbols) > 1:
            complexity += 2
        
        # Add complexity for analysis intents
        if intent in [QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.FUNDAMENTAL_ANALYSIS]:
            complexity += 3
        
        # Add complexity for comparison
        if intent == QueryIntent.COMPARISON:
            complexity += 2
        
        # Add complexity for portfolio/risk advice
        if intent in [QueryIntent.PORTFOLIO_ADVICE, QueryIntent.RISK_ASSESSMENT]:
            complexity += 2
        
        # Cap at 10
        return min(complexity, 10)
    
    def _ai_decides_market_data_needed(self, symbols: List[SymbolContext], intent: QueryIntent) -> bool:
        """AI decides if market data is needed"""
        
        # High confidence symbols suggest market data is useful
        high_confidence = any(s.confidence > 0.7 for s in symbols)
        
        # Analysis intents benefit from market data
        analysis_intents = [QueryIntent.STOCK_ANALYSIS, QueryIntent.TECHNICAL_ANALYSIS, QueryIntent.FUNDAMENTAL_ANALYSIS]
        analysis_intent = intent in analysis_intents
        
        # AI decision: market data if symbols are likely real or analysis is needed
        return high_confidence or analysis_intent
    
    def _ai_decides_reasoning_needed(self, intent: QueryIntent, complexity: int) -> bool:
        """AI decides if reasoning is needed"""
        
        # Always need reasoning for complex queries
        if complexity > 5:
            return True
        
        # Always need reasoning for advice intents
        advice_intents = [QueryIntent.PORTFOLIO_ADVICE, QueryIntent.RISK_ASSESSMENT, QueryIntent.GENERAL_QUESTION]
        if intent in advice_intents:
            return True
        
        # AI decision: reasoning is always valuable
        return True

    # ===== ENHANCEMENT METHODS (NEW) =====
    # These methods add context building capabilities while preserving existing functionality

    async def enhanced_query_analysis(self, query: str, context: Any, enable_enhancements: bool = True) -> dict:
        """
        Enhanced query analysis with optional context building features.
        
        Args:
            query: User input query
            context: User context
            enable_enhancements: Whether to enable enhanced features
            
        Returns:
            Dictionary with analysis results and enhancements
        """
        # Use existing analysis logic
        base_analysis = await self.analyze_query(query, context)
        
        result = {
            "success": True,
            "analysis": base_analysis,
            "enhancements": [],
            "metadata": {}
        }
        
        # Add enhancements if enabled
        if enable_enhancements:
            # Enhanced context building
            enhanced_context = await self._build_enhanced_context(query, base_analysis, context)
            result["enhancements"].append("enhanced_context")
            result["enhanced_context"] = enhanced_context
            
            # Processing recommendations
            recommendations = self._generate_processing_recommendations(base_analysis)
            result["enhancements"].append("processing_recommendations")
            result["recommendations"] = recommendations
            
            # Enhanced metadata
            result["metadata"] = {
                "processing_mode": "enhanced",
                "enhancement_level": "full"
            }
        else:
            result["metadata"] = {
                "processing_mode": "legacy"
            }
        
        return result

    async def _build_enhanced_context(self, query: str, analysis: QueryAnalysis, context: Any) -> dict:
        """Build enhanced context for the query analysis."""
        enhanced_context = {
            "query_metadata": {
                "length": len(query),
                "word_count": len(query.split()),
                "complexity_score": analysis.complexity
            },
            "symbol_analysis": {
                "total_symbols": len(analysis.symbols),
                "high_confidence_count": len([s for s in analysis.symbols if s.confidence > 0.7]),
                "symbol_types": self._categorize_symbols(analysis.symbols)
            },
            "processing_optimization": {
                "suggested_route": analysis.processing_route.value,
                "data_requirements": analysis.data_requirements,
                "estimated_processing_time": self._estimate_processing_time(analysis)
            }
        }
        
        return enhanced_context

    def _categorize_symbols(self, symbols: List[SymbolContext]) -> dict:
        """Categorize symbols for better context understanding."""
        categories = {
            "high_confidence": [],
            "medium_confidence": [],
            "low_confidence": []
        }
        
        for symbol in symbols:
            if symbol.confidence > 0.7:
                categories["high_confidence"].append(symbol.text)
            elif symbol.confidence > 0.4:
                categories["medium_confidence"].append(symbol.text)
            else:
                categories["low_confidence"].append(symbol.text)
        
        return categories

    def _generate_processing_recommendations(self, analysis: QueryAnalysis) -> dict:
        """Generate recommendations for optimal processing."""
        recommendations = {
            "priority": "normal",
            "suggested_approaches": [],
            "performance_tips": []
        }
        
        # Priority based on complexity and urgency
        if analysis.complexity > 7:
            recommendations["priority"] = "high"
            recommendations["suggested_approaches"].append("batch_processing")
        
        if analysis.requires_market_data:
            recommendations["suggested_approaches"].append("parallel_data_fetch")
            recommendations["performance_tips"].append("Use caching for market data")
        
        if analysis.requires_ai_reasoning:
            recommendations["suggested_approaches"].append("ai_optimization")
            recommendations["performance_tips"].append("Optimize AI model selection")
        
        return recommendations

    def _estimate_processing_time(self, analysis: QueryAnalysis) -> str:
        """Estimate processing time based on analysis complexity."""
        base_time = 2  # seconds
        
        if analysis.complexity > 7:
            base_time += 3
        elif analysis.complexity > 4:
            base_time += 1
        
        if analysis.requires_market_data:
            base_time += 2
        
        if analysis.requires_ai_reasoning:
            base_time += 3
        
        return f"{base_time}s"

    def is_enhanced_mode(self) -> bool:
        """Check if enhanced features are available."""
        return hasattr(self, 'enhanced_query_analysis') 