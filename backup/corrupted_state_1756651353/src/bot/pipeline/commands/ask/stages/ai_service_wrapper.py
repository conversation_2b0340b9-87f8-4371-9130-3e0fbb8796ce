"""
AI Service Wrapper with Correlation ID Logging

Provides correlation ID logging for all AI service calls to enable request tracing.
Enhanced with comprehensive technical analysis integration.
"""

import asyncio
import asyncio
from datetime import datetime, timezone
import logging
import re
import time
from typing import Any, Optional, Dict, List

import numpy as np
import pandas as pd
from unittest.mock import Mock

from src.api.data.metrics import cache_metrics
from src.api.data.providers.finnhub import FinnhubProvider
from src.api.data.providers.polygon import PolygonProvider
from src.api.data.providers.polygon import PolygonProvider
from src.core.logger import get_logger
from src.core.market_calendar import get_market_context, MarketStatus
from src.core.outlier_detector import detect_outliers, adjust_confidence_for_outliers
from src.core.risk_management.atr_calculator import atr_stop_loss_calculator, StopLossConfig
from src.core.risk_management.atr_calculator import atr_stop_loss_calculator, TradeDirection
from src.core.stale_data_detector import detect_stale_data, adjust_confidence_for_staleness
from src.shared.data_providers.yfinance_provider import YFinanceProvider
from src.shared.data_validation import gap_detector, assess_data_quality
from src.shared.market_analysis.signal_analyzer import SignalAnalyzer
from src.shared.technical_analysis.calculator import technical_analysis_calculator
from src.shared.technical_analysis.zones import supply_demand_detector, enhanced_zone_analyzer
            
            # If no symbol provided, return empty context
            if not symbol:
                return {}
            
            # Try to get real market context from providers
            providers = [FinnhubProvider(), PolygonProvider()]
            
            for provider in providers:
                try:
                    if hasattr(provider, 'get_company_profile'):
                        context = await provider.get_company_profile(symbol)
                        if context and not context.get('error'):
                            return context
                    elif hasattr(provider, 'get_fundamentals'):
                        fundamentals = await provider.get_fundamentals(symbol)
                        if fundamentals and not fundamentals.get('error'):
                            return fundamentals
                except Exception as e:
                    logger.warning(f"Provider {provider.__class__.__name__} failed for market context: {e}")
                    continue
            
            # Return empty context if all providers fail
            logger.warning(f"All providers failed for market context of {symbol}")
            return {}
            
        except Exception as e:
            logger.error(f"Error getting market context for {symbol}: {e}")
            return {}
    
    async def _generate_trading_signals(self, symbol: str, market_data: Optional[pd.DataFrame] = None) -> List[Dict[str, Any]]:
        """Generate trading signals using the signal analyzer."""
        try:
            # Use provided market data or return empty if none available
            if market_data is None or market_data.empty:
                logger.warning(f"No market data available for signal generation for {symbol}")
                return []
            
            # Analyze for signals across different timeframes
            signals = []
            timeframes = ['1d', '4h', '1h']
            
            for timeframe in timeframes:
                try:
                    timeframe_signals = self.signal_analyzer.analyze_market_data(
                        symbol, timeframe, market_data
                    )
                    signals.extend(timeframe_signals)
                except Exception as e:
                    logger.warning(f"Signal analysis failed for {timeframe}: {e}")
                    continue
            
            return [signal.to_dict() for signal in signals]
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []
    
    async def _assess_data_quality_with_gaps(
        self, 
        market_data: pd.DataFrame, 
        symbol: str
    ) -> Dict[str, Any]:
        """
        Assess data quality and detect gaps, recording metrics for monitoring.
        """
        try:
            if market_data.empty:
                return {
                    'quality_score': 0.0,
                    'completeness': 0.0,
                    'gap_count': 0,
                    'gaps': [],
                    'status': 'no_data',
                    'recommendations': ['No data available for analysis']
                }
            
            # Determine interval type based on data frequency
            interval_type = self._determine_interval_type(market_data)
            
            # Assess data quality using gap detector
            quality_assessment = assess_data_quality(
                market_data, symbol, interval_type
            )
            
            # Record metrics for monitoring
            if quality_assessment.get('gaps'):
                cache_metrics.record_data_gaps_batch(
                    quality_assessment['gaps'], symbol, interval_type
                )
            
            cache_metrics.record_data_quality(
                symbol, 
                interval_type,
                quality_assessment.get('quality_score', 0.0),
                quality_assessment.get('completeness', 0.0)
            )
            
            logger.info(
                f"Data quality assessment for {symbol}: "
                f"Score {quality_assessment.get('quality_score', 0):.1f}, "
                f"Completeness {quality_assessment.get('completeness', 0):.1f}%, "
                f"Gaps: {quality_assessment.get('gap_count', 0)}"
            )
            
            return quality_assessment
            
        except Exception as e:
            logger.error(f"Error assessing data quality for {symbol}: {e}")
            return {
                'quality_score': 0.0,
                'completeness': 0.0,
                'gap_count': 0,
                'gaps': [],
                'status': 'error',
                'recommendations': [f'Error assessing quality: {str(e)}']
            }
    
    def _determine_interval_type(self, market_data: pd.DataFrame) -> str:
        """Determine the interval type based on data frequency."""
        if market_data.empty or len(market_data) < 2:
            return '1d'
        
        try:
            # Calculate time differences between consecutive timestamps
            timestamps = market_data.index.sort_values()
            time_diffs = timestamps[1:] - timestamps[:-1]
            
            if len(time_diffs) == 0:
                return '1d'
            
            # Get median time difference
            median_diff = time_diffs.median().total_seconds()
            
            # Map to interval types
            if median_diff <= 60:  # ≤1 minute
                return '1m'
            elif median_diff <= 300:  # ≤5 minutes
                return '5m'
            elif median_diff <= 900:  # ≤15 minutes
                return '15m'
            elif median_diff <= 3600:  # ≤1 hour
                return '1h'
            else:
                return '1d'
                
        except Exception as e:
            logger.warning(f"Could not determine interval type: {e}, defaulting to 1d")
            return '1d'
    
    async def _generate_ai_chat_response(self, symbol: str, technical_analysis: Dict[str, Any], signals: List[Dict[str, Any]]) -> str:
        """Generate a conversational AI response that interprets technical data and provides insights."""
        try:
            # Check if we have valid data
            if technical_analysis.get('status') == 'data_unavailable':
                return f"I'm sorry, but I'm unable to provide analysis for {symbol} at the moment. The market data appears to be unavailable. This could be due to:\n\n• Market hours (some data is only available during trading hours)\n• Symbol not found or delisted\n• Temporary data provider issues\n\nPlease try again later or verify the symbol is correct."
            
            if technical_analysis.get('status') == 'error':
                return f"I encountered an error while analyzing {symbol}. The technical analysis system is experiencing issues. Please try again in a few minutes."
            
            # Start building the conversational response
            response_parts = []
            
            # Opening analysis
            current_price = technical_analysis.get('indicators', {}).get('current_price', 0)
            if current_price > 0:
                response_parts.append(f"Looking at {symbol}, I can see the current price is ${current_price:.2f}. ")
            
            # Market context analysis
            market_context = technical_analysis.get('market_context', {})
            if market_context:
                sector = market_context.get('sector', '')
                industry = market_context.get('industry', '')
                if sector and industry:
                    response_parts.append(f"This is a {sector} sector stock, specifically in the {industry} industry. ")
                    
                    # Add sector performance context
                    if 'sector_performance' in market_context:
                        perf = market_context['sector_performance']
                        if '1d' in perf and '1w' in perf:
                            daily_change = perf['1d']
                            weekly_change = perf['1w']
                            
                            if daily_change > 0:
                                response_parts.append(f"The {sector} sector is up {daily_change:.1f}% today and {weekly_change:.1f}% this week, which is generally positive for {symbol}. ")
                            else:
                                response_parts.append(f"The {sector} sector is down {abs(daily_change):.1f}% today and {weekly_change:.1f}% this week, which may be putting pressure on {symbol}. ")
            
            # Technical indicators analysis
            indicators = technical_analysis.get('indicators', {})
            if indicators:
                response_parts.append("\n\nFrom a technical perspective, here's what I'm seeing:")
                
                # RSI analysis
                if 'rsi' in indicators:
                    rsi = indicators['rsi']
                    if rsi > 70:
                        response_parts.append(f"• The RSI is at {rsi:.1f}, which indicates {symbol} is overbought. This suggests the stock might be due for a pullback or consolidation. ")
                    elif rsi < 30:
                        response_parts.append(f"• The RSI is at {rsi:.1f}, which indicates {symbol} is oversold. This could present a potential buying opportunity as the stock may be undervalued. ")
                    else:
                        response_parts.append(f"• The RSI is at {rsi:.1f}, which is in neutral territory. This suggests the stock isn't showing extreme momentum in either direction. ")
                
                # MACD analysis
                if ('macd' in indicators and 'macd_signal' in indicators and 
                    indicators['macd'] is not None and indicators['macd_signal'] is not None):
                    try:
                        macd = float(indicators['macd'])
                        signal = float(indicators['macd_signal'])
                        if macd > signal:
                            response_parts.append(f"• The MACD is above its signal line ({macd:.3f} vs {signal:.3f}), indicating bullish momentum is building. ")
                        else:
                            response_parts.append(f"• The MACD is below its signal line ({macd:.3f} vs {signal:.3f}), suggesting bearish momentum may be taking over. ")
                    except (ValueError, TypeError):
                        pass  # Skip this comparison if values are invalid
                
                # Moving averages analysis
                if ('sma_20' in indicators and 'sma_50' in indicators and 
                    indicators['sma_20'] is not None and indicators['sma_50'] is not None):
                    try:
                        sma_20 = float(indicators['sma_20'])
                        sma_50 = float(indicators['sma_50'])
                        if sma_20 > sma_50:
                            response_parts.append(f"• The 20-day moving average (${sma_20:.2f}) is above the 50-day (${sma_50:.2f}), which is a bullish trend signal. ")
                        else:
                            response_parts.append(f"• The 20-day moving average (${sma_20:.2f}) is below the 50-day (${sma_50:.2f}), indicating a bearish trend. ")
                    except (ValueError, TypeError):
                        pass  # Skip this comparison if values are invalid
                
                # Bollinger Bands analysis
                if ('bb_upper' in indicators and 'bb_lower' in indicators and 'current_price' in indicators and
                    indicators['bb_upper'] is not None and indicators['bb_lower'] is not None and indicators['current_price'] is not None):
                    try:
                        bb_upper = float(indicators['bb_upper'])
                        bb_lower = float(indicators['bb_lower'])
                        current_price = float(indicators['current_price'])
                        
                        if current_price > bb_upper:
                            response_parts.append(f"• The price is above the upper Bollinger Band (${bb_upper:.2f}), suggesting {symbol} may be overextended and due for a reversal. ")
                        elif current_price < bb_lower:
                            response_parts.append(f"• The price is below the lower Bollinger Band (${bb_lower:.2f}), indicating {symbol} may be oversold and could bounce back. ")
                        else:
                            response_parts.append(f"• The price is within the Bollinger Bands (${bb_lower:.2f} - ${bb_upper:.2f}), suggesting normal volatility levels. ")
                    except (ValueError, TypeError):
                        pass  # Skip this comparison if values are invalid
            
            # Supply and demand zones analysis
            zones = technical_analysis.get('zones', {}).get('zones', [])
            if zones:
                response_parts.append("\n\nLooking at the supply and demand zones:")
                
                # Find nearest support and resistance
                support_zones = [z for z in zones if z.get('zone_type') == 'demand']
                resistance_zones = [z for z in zones if z.get('zone_type') == 'supply']
                
                if support_zones:
                    nearest_support = min(support_zones, key=lambda x: abs(x.get('center_price', 0) - current_price))
                    support_price = nearest_support.get('center_price', 0)
                    support_strength = nearest_support.get('strength', 0)
                    response_parts.append(f"• The nearest support level is around ${support_price:.2f} with a strength rating of {support_strength:.0f}/100. ")
                
                if resistance_zones:
                    nearest_resistance = min(resistance_zones, key=lambda x: abs(x.get('center_price', 0) - current_price))
                    resistance_price = nearest_resistance.get('center_price', 0)
                    resistance_strength = nearest_resistance.get('strength', 0)
                    response_parts.append(f"• The nearest resistance level is around ${resistance_price:.2f} with a strength rating of {resistance_strength:.0f}/100. ")
            
            # Trading signals analysis
            if signals:
                response_parts.append("\n\nBased on my analysis, here are the key trading signals:")
                
                for signal in signals[:3]:
                    signal_type = signal.get('signal_type', 'unknown')
                    direction = signal.get('direction', 'neutral')
                    confidence = signal.get('confidence_score', 0)
                    
                    if direction == 'bullish':
                        response_parts.append(f"• {signal_type.title()} signal: Bullish with {confidence:.0f}% confidence. This suggests {symbol} may move higher. ")
                    elif direction == 'bearish':
                        response_parts.append(f"• {signal_type.title()} signal: Bearish with {confidence:.0f}% confidence. This suggests {symbol} may move lower. ")
                    else:
                        response_parts.append(f"• {signal_type.title()} signal: Neutral with {confidence:.0f}% confidence. ")
            
            # Overall assessment and recommendation
            response_parts.append("\n\n**Overall Assessment:** ")
            
            # Determine overall sentiment based on indicators
            bullish_signals = 0
            bearish_signals = 0
            
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                if rsi < 30:
                    bullish_signals += 1
                elif rsi > 70:
                    bearish_signals += 1
            
            if ('macd' in indicators and 'macd_signal' in indicators and 
                indicators['macd'] is not None and indicators['macd_signal'] is not None):
                try:
                    macd = float(indicators['macd'])
                    signal = float(indicators['macd_signal'])
                    if macd > signal:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                except (ValueError, TypeError):
                    pass  # Skip this comparison if values are invalid
            
            if ('sma_20' in indicators and 'sma_50' in indicators and 
                indicators['sma_20'] is not None and indicators['sma_50'] is not None):
                try:
                    sma_20 = float(indicators['sma_20'])
                    sma_50 = float(indicators['sma_50'])
                    if sma_20 > sma_50:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                except (ValueError, TypeError):
                    pass  # Skip this comparison if values are invalid
            
            # Generate recommendation
            if bullish_signals > bearish_signals:
                response_parts.append(f"Based on the technical indicators, {symbol} appears to have a bullish bias. The momentum indicators and trend analysis suggest upward potential. ")
            elif bearish_signals > bullish_signals:
                response_parts.append(f"Based on the technical indicators, {symbol} appears to have a bearish bias. The momentum indicators and trend analysis suggest downward pressure. ")
            else:
                response_parts.append(f"Based on the technical indicators, {symbol} appears to be in a neutral state with mixed signals. The stock may consolidate or move sideways. ")
            
            # Risk assessment
            data_quality = technical_analysis.get('data_quality', {})
            if data_quality:
                quality_score = data_quality.get('quality_score', 0)
                completeness = data_quality.get('completeness', 0)
                gap_count = data_quality.get('gap_count', 0)
                
                # Add data quality information
                response_parts.append(f"\n\n**📊 Data Quality Assessment:**")
                response_parts.append(f"• Quality Score: {quality_score:.1f}/100")
                response_parts.append(f"• Data Completeness: {completeness:.1f}%")
                
                if gap_count > 0:
                    response_parts.append(f"• ⚠️ **Data Gaps Detected**: {gap_count} missing intervals")
                    
                    # Add specific gap information
                    gaps = data_quality.get('gaps', [])
                    if gaps:
                        response_parts.append("  **Gap Details:**")
                        for i, gap in enumerate(gaps[:3], 1):  # Show first 3 gaps
                            severity = gap.get('severity', 'unknown')
                            start_time = gap.get('start_time', 'unknown')
                            end_time = gap.get('end_time', 'unknown')
                            duration_min = gap.get('duration_seconds', 0) / 60
                            
                            severity_emoji = {
                                'minor': '🟡',
                                'moderate': '🟠', 
                                'major': '🔴',
                                'critical': '🚨'
                            }.get(severity, '❓')
                            
                            response_parts.append(
                                f"  {i}. {severity_emoji} {severity.title()}: "
                                f"{start_time} to {end_time} ({duration_min:.1f} min)"
                            )
                        
                        if len(gaps) > 3:
                            response_parts.append(f"  ... and {len(gaps) - 3} more gaps")
                    
                    # Add recommendations
                    recommendations = data_quality.get('recommendations', [])
                    if recommendations:
                        response_parts.append("  **Recommendations:**")
                        for rec in recommendations[:2]:  # Show first 2 recommendations
                            response_parts.append(f"  • {rec}")
                
                # Add quality-based confidence adjustment
                if quality_score < 50:
                    response_parts.append(f"\n\n**⚠️ Important Note:** The data quality for this analysis is {quality_score:.1f}%, which indicates significant data issues. Please verify current prices independently before making any trading decisions.")
                elif quality_score < 80:
                    response_parts.append(f"\n\n**⚠️ Note:** The data quality for this analysis is {quality_score:.1f}%, which is below optimal levels. Consider this when interpreting the results.")
                else:
                    response_parts.append(f"\n\n**✅ Data Quality:** The data quality for this analysis is {quality_score:.1f}%, which provides confidence in the analysis results.")
            
            # Add stale data warnings and confidence adjustments
            stale_data_warnings = self._check_for_stale_data(technical_analysis)
            if stale_data_warnings:
                response_parts.append(f"\n\n**🕐 Data Freshness Warnings:**")
                for warning in stale_data_warnings:
                    response_parts.append(f"• {warning.warning_message}")
                    if warning.recommendations:
                        response_parts.append(f"  **Recommendations:**")
                        for rec in warning.recommendations[:2]:  # Show first 2 recommendations
                            response_parts.append(f"  - {rec}")
                
                # Apply confidence adjustments
                if stale_data_warnings:
                    base_confidence = 82  # Default confidence
                    adjusted_confidence = base_confidence
                    for warning in stale_data_warnings:
                        adjusted_confidence = adjust_confidence_for_staleness(adjusted_confidence, warning)
                    
                    if adjusted_confidence < base_confidence:
                        response_parts.append(f"\n\n**📉 Confidence Adjustment:** Analysis confidence adjusted from {base_confidence}% to {adjusted_confidence:.1f}% due to data staleness.")
            
            # Add market context information
            market_context = await self._get_market_context()
            if market_context:
                response_parts.append(f"\n\n**📅 Market Status:**")
                
                market_status = market_context.get('status', 'unknown')
                if market_status == MarketStatus.HOLIDAY.value:
                    holiday_name = market_context.get('holiday', {}).get('name', 'holiday')
                    response_parts.append(f"• 🎉 Markets closed today for {holiday_name}")
                    
                    next_open = market_context.get('next_market_open')
                    if next_open:
                        response_parts.append(f"• Next trading session: {next_open}")
                
                elif market_status == MarketStatus.WEEKEND.value:
                    day_of_week = market_context.get('weekend_info', {}).get('day_of_week', 'weekend')
                    response_parts.append(f"• 🏖️ Markets closed on {day_of_week}")
                    
                    next_trading_day = market_context.get('weekend_info', {}).get('next_trading_day')
                    if next_trading_day:
                        response_parts.append(f"• Next trading day: {next_trading_day}")
                
                elif market_status == MarketStatus.AFTER_HOURS.value:
                    response_parts.append(f"• 🌙 After-hours trading session")
                    response_parts.append(f"• Limited data updates during extended hours")
                
                elif market_status == MarketStatus.PRE_MARKET.value:
                    response_parts.append(f"• 🌅 Pre-market trading session")
                    response_parts.append(f"• Limited data updates before regular hours")
                
                elif market_status == MarketStatus.OPEN.value:
                    response_parts.append(f"• 🟢 Markets are currently open")
                    response_parts.append(f"• Regular trading hours with full data updates")
                
                # Add market hours information
                market_hours = market_context.get('market_hours', {})
                if market_hours:
                    response_parts.append(f"• Regular hours: {market_hours.get('regular_open', 'N/A')} - {market_hours.get('regular_close', 'N/A')}")
                    response_parts.append(f"• Extended hours: {market_hours.get('pre_market_open', 'N/A')} - {market_hours.get('after_hours_close', 'N/A')}")
            
            # Add outlier warnings and confidence adjustments
            outlier_warnings = self._check_for_outliers(technical_analysis)
            if outlier_warnings:
                response_parts.append(f"\n\n**⚠️ Outlier Warnings:**")
                for warning in outlier_warnings:
                    response_parts.append(f"• {warning.warning_message}")
                    if warning.recommendations:
                        response_parts.append(f"  **Recommendations:**")
                        for rec in warning.recommendations[:2]:  # Show first 2 recommendations
                            response_parts.append(f"  - {rec}")
                
                # Apply confidence adjustments
                if outlier_warnings:
                    base_confidence = 82  # Default confidence
                    adjusted_confidence = base_confidence
                    for warning in outlier_warnings:
                        adjusted_confidence = adjust_confidence_for_outliers(adjusted_confidence, warning)
                    
                    if adjusted_confidence < base_confidence:
                        response_parts.append(f"\n\n**📉 Confidence Adjustment:** Analysis confidence adjusted from {base_confidence}% to {adjusted_confidence:.1f}% due to outlier data.")
            
            # Closing thoughts
            response_parts.append(f"\n\n**Remember:** This analysis is based on technical indicators and historical patterns. Market conditions can change rapidly, and past performance doesn't guarantee future results. Always consider your risk tolerance and investment goals, and consider consulting with a financial advisor for personalized advice.")
            
            # Add provider attribution footer
            attribution_footer = self._generate_attribution_footer(technical_analysis)
            if attribution_footer:
                response_parts.append(f"\n\n{attribution_footer}")
            
            return "".join(response_parts)
            
        except Exception as e:
            logger.error(f"Error generating AI chat response for {symbol}: {e}")
            logger.error(f"Error type: {type(e)}")
            logger.error(f"Error traceback:", exc_info=True)
            return f"I apologize, but I encountered an error while generating the analysis for {symbol}. Please try again or contact support if the issue persists."
    
    def _format_indicators(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Format indicators for display in the response."""
        formatted = {}
        
        # Price data
        if 'price' in indicators:
            formatted['current_price'] = indicators['price']
        
        # Trend indicators
        trend_indicators = ['sma_20', 'sma_50', 'ema_20', 'ema_50']
        for indicator in trend_indicators:
            if indicator in indicators:
                formatted[indicator] = indicators[indicator]
        
        # Momentum indicators
        momentum_indicators = ['rsi', 'macd', 'macd_signal', 'macd_histogram']
        for indicator in momentum_indicators:
            if indicator in indicators:
                formatted[indicator] = indicators[indicator]
        
        # Volatility indicators
        volatility_indicators = ['bb_upper', 'bb_middle', 'bb_lower', 'atr']
        for indicator in volatility_indicators:
            if indicator in indicators:
                formatted[indicator] = indicators[indicator]
        
        # Volume indicators
        volume_indicators = ['volume_ma', 'volume_ratio', 'volume_spike']
        for indicator in volume_indicators:
            if indicator in indicators:
                formatted[indicator] = indicators[indicator]
        
        # Supply/demand zones
        if 'supply_demand_zones' in indicators:
            formatted['zones'] = indicators['supply_demand_zones']
        
        # Zone metrics
        if 'zone_metrics' in indicators:
            formatted['zone_metrics'] = indicators['zone_metrics']
        
        return formatted
    
    def _format_enhanced_response(self, symbol: str, technical_analysis: Dict[str, Any], signals: List[Dict[str, Any]]) -> str:
        """Format the enhanced response with comprehensive analysis."""
        
        response_parts = [f"**📊 {symbol} Comprehensive Technical Analysis**"]
        
        # Add current price and basic info
        if 'current_price' in technical_analysis.get('indicators', {}):
            price = technical_analysis['indicators']['current_price']
            response_parts.append(f"\n💰 **Current Price**: ${price:.2f}")
        
        # Add market context
        if 'market_context' in technical_analysis:
            context = technical_analysis['market_context']
            response_parts.append(f"\n🏢 **Market Context**: {context.get('sector', 'N/A')} ({context.get('industry', 'N/A')})")
            
            if 'sector_performance' in context:
                perf = context['sector_performance']
                response_parts.append(f"• Sector Performance: 1D: {perf.get('1d', 0):.1f}% | 1W: {perf.get('1w', 0):.1f}% | 1M: {perf.get('1m', 0):.1f}%")
            
            if 'fundamentals' in context:
                fund = context['fundamentals']
                response_parts.append(f"• Fundamentals: P/E {fund.get('pe_ratio', 0):.1f} | EPS ${fund.get('eps', 0):.2f} | Rev Growth {fund.get('revenue_growth', 0):.1f}%")
            
            if 'risk_metrics' in context:
                risk = context['risk_metrics']
                response_parts.append(f"• Risk: Beta {risk.get('beta', 0):.2f} | Volatility {risk.get('volatility', 0):.2f} | Rating: {risk.get('risk_rating', 'N/A')}")
        
        # Add technical indicators
        indicators = technical_analysis.get('indicators', {})
        if indicators:
            response_parts.append("\n📈 **Technical Indicators**:")
            
            # RSI
            if 'rsi' in indicators and indicators['rsi'] is not None:
                rsi = indicators['rsi']
                try:
                    rsi = float(rsi)
                    if rsi > 70:
                        rsi_status = "🔴 Overbought"
                    elif rsi < 30:
                        rsi_status = "🟢 Oversold"
                    else:
                        rsi_status = "🟡 Neutral"
                    response_parts.append(f"• RSI ({rsi:.1f}): {rsi_status}")
                except (ValueError, TypeError):
                    response_parts.append(f"• RSI: {rsi} (Invalid value)")
            
            # MACD
            if ('macd' in indicators and 'macd_signal' in indicators and 
                indicators['macd'] is not None and indicators['macd_signal'] is not None):
                try:
                    macd = float(indicators['macd'])
                    signal = float(indicators['macd_signal'])
                    if macd > signal:
                        macd_status = "🟢 Bullish"
                    else:
                        macd_status = "🔴 Bearish"
                    response_parts.append(f"• MACD: {macd:.3f} vs Signal {signal:.3f} ({macd_status})")
                except (ValueError, TypeError):
                    response_parts.append(f"• MACD: Invalid values")
            
            # Moving averages
            ma_indicators = ['sma_20', 'sma_50', 'ema_20', 'ema_50']
            available_mas = [(k, v) for k, v in indicators.items() if k in ma_indicators]
            if available_mas:
                response_parts.append("• Moving Averages:")
                for ma_name, ma_value in available_mas:
                    response_parts.append(f"  - {ma_name}: ${ma_value:.2f}")
            
            # Bollinger Bands
            if ('bb_upper' in indicators and 'bb_lower' in indicators and 
                indicators['bb_upper'] is not None and indicators['bb_lower'] is not None):
                try:
                    bb_upper = float(indicators['bb_upper'])
                    bb_lower = float(indicators['bb_lower'])
                    current_price = indicators.get('current_price', price)
                    if current_price is not None:
                        current_price = float(current_price)
                        if current_price > bb_upper:
                            bb_status = "🔴 Above Upper Band"
                        elif current_price < bb_lower:
                            bb_status = "🟢 Below Lower Band"
                        else:
                            bb_status = "🟡 Within Bands"
                        response_parts.append(f"• Bollinger Bands: ${bb_lower:.2f} - ${bb_upper:.2f} ({bb_status})")
                    else:
                        response_parts.append(f"• Bollinger Bands: ${bb_lower:.2f} - ${bb_upper:.2f}")
                except (ValueError, TypeError):
                    response_parts.append(f"• Bollinger Bands: Invalid values")
            
            # ATR
            if 'atr' in indicators:
                atr = indicators['atr']
                response_parts.append(f"• ATR (Volatility): ${atr:.2f}")
            
            # Volume
            if ('volume_ma' in indicators and 'volume_spike' in indicators and 
                indicators['volume_ma'] is not None and indicators['volume_spike'] is not None):
                try:
                    vol_ma = float(indicators['volume_ma'])
                    vol_spike = float(indicators['volume_spike'])
                    spike_status = "🔴 High" if vol_spike > 1.5 else "🟢 Normal" if vol_spike < 0.5 else "🟡 Elevated"
                    response_parts.append(f"• Volume: MA {vol_ma:,.0f} | Spike: {vol_spike:.1f}x ({spike_status})")
                except (ValueError, TypeError):
                    response_parts.append(f"• Volume: Invalid values")
        
        # Add enhanced supply/demand zones
        zones = technical_analysis.get('zones', {}).get('zones', [])
        if zones:
            response_parts.append("\n🎯 **Supply & Demand Zones**:")
            for i, zone in enumerate(zones[:3], 1):
                zone_type = zone.get('zone_type', 'unknown')
                center_price = zone.get('center_price', 0)
                strength = zone.get('strength', 0)
                priority = zone.get('priority', 'medium')
                method = zone.get('method', 'unknown')
                
                # Add zone priority indicator
                priority_indicator = '🔴' if priority == 'high' else '🟡' if priority == 'medium' else '🟢'
                response_parts.append(f"{i}. {priority_indicator} {zone_type.title()} Zone: ${center_price:.2f} (Strength: {strength:.0f}/100, Method: {method})")
        
        # Add zone analysis insights
        zone_analysis = technical_analysis.get('zone_analysis', {})
        if zone_analysis:
            response_parts.append("\n📊 **Zone Analysis Insights**:")
            if 'market_structure' in zone_analysis:
                structure = zone_analysis['market_structure']
                structure_desc = {
                    'resistance_dominated': '🔴 Resistance dominated market',
                    'support_dominated': '🟢 Support dominated market',
                    'balanced': '⚖️ Balanced zone structure',
                    'weak_structure': '⚠️ Weak zone structure'
                }.get(structure, '❓ Undefined structure')
                response_parts.append(f"• Market Structure: {structure_desc}")
            
            if 'nearest_demand' in zone_analysis and zone_analysis['nearest_demand']:
                nearest_demand = zone_analysis['nearest_demand']
                distance = nearest_demand.get('price_distance_to_current', 0)
                response_parts.append(f"• Nearest Support: ${nearest_demand.get('center_price', 0):.2f} (Distance: ${distance:.2f})")
            
            if 'nearest_supply' in zone_analysis and zone_analysis['nearest_supply']:
                nearest_supply = zone_analysis['nearest_supply']
                distance = nearest_supply.get('price_distance_to_current', 0)
                response_parts.append(f"• Nearest Resistance: ${nearest_supply.get('center_price', 0):.2f} (Distance: ${distance:.2f})")
        
        # Add zone recommendations
        recommendations = technical_analysis.get('zone_recommendations', [])
        if recommendations:
            response_parts.append("\n💡 **Zone-Based Recommendations**:")
            for rec in recommendations[:3]:
                if rec.get('recommendation_type') == 'entry':
                    action = rec.get('action', 'unknown')
                    zone_price = rec.get('zone_center', 0)
                    response_parts.append(f"• {action.title()}: Target ${zone_price:.2f} zone")
                elif rec.get('recommendation_type') == 'structure':
                    action = rec.get('action', 'unknown')
                    structure = rec.get('market_structure', 'unknown')
                    response_parts.append(f"• Market Outlook: {action} ({structure})")
        
        # Add trading signals
        if signals:
            response_parts.append("\n🚨 **Trading Signals**:")
            for signal in signals[:3]:
                signal_type = signal.get('signal_type', 'unknown')
                direction = signal.get('direction', 'neutral')
                confidence = signal.get('confidence_score', 0)
                response_parts.append(f"• {signal_type}: {direction.title()} (Confidence: {confidence:.0f}%)")
        
        # Add risk disclaimer
        response_parts.append("\n⚠️ **Disclaimer**: This analysis is for educational purposes only. Past performance doesn't guarantee future results. Always do your own research and consult with a financial advisor before making trading decisions.")
        
        return "\n".join(response_parts)

    def _generate_attribution_footer(self, technical_analysis: Dict[str, Any]) -> str:
        """
        Generate provider attribution footer with transparency information.
        """
        try:
            # Extract provider information from the analysis
            market_data = technical_analysis.get('market_data', {})
            if not market_data:
                return ""
            
            # Get the primary data source
            primary_provider = None
            fallback_providers = []
            
            # Look for provider information in the data
            if hasattr(market_data, 'metadata') and market_data.metadata:
                primary_provider = market_data.metadata
            elif isinstance(market_data, list) and market_data:
                # Check first item for metadata
                first_item = market_data[0]
                if hasattr(first_item, 'metadata') and first_item.metadata:
                    primary_provider = first_item.metadata
            
            if not primary_provider:
                return ""
            
            # Build attribution text
            attribution_parts = []
            
            # Primary provider info
            if primary_provider.get('provider_name'):
                provider_name = primary_provider['provider_name']
                provider_type = primary_provider.get('provider_type', 'unknown')
                
                # Get freshness info
                fetched_at = primary_provider.get('fetched_at')
                if fetched_at:
                    try:
                        if isinstance(fetched_at, str):
                            fetched_time = datetime.fromisoformat(fetched_at.replace('Z', '+00:00'))
                        else:
                            fetched_time = fetched_at
                        
                        if fetched_time.tzinfo is None:
                            fetched_time = fetched_time.replace(tzinfo=timezone.utc)
                        
                        now = datetime.now(timezone.utc)
                        age_minutes = int((now - fetched_time).total_seconds() / 60)
                        
                        if age_minutes < 1:
                            freshness = "just updated"
                        elif age_minutes < 60:
                            freshness = f"updated {age_minutes} min ago"
                        else:
                            hours = age_minutes // 60
                            mins = age_minutes % 60
                            freshness = f"updated {hours}h {mins}m ago"
                        
                        attribution_parts.append(f"📊 **Data Source**: {provider_name} ({freshness})")
                        
                        # Record freshness metrics
                        cache_metrics.record_data_freshness(
                            provider_name, 
                            technical_analysis.get('symbol', 'unknown'),
                            'analysis',
                            age_minutes
                        )
                        
                    except Exception as e:
                        logger.warning(f"Could not parse fetched_at time: {e}")
                        attribution_parts.append(f"📊 **Data Source**: {provider_name}")
                else:
                    attribution_parts.append(f"📊 **Data Source**: {provider_name}")
            
            # Add fallback information if used
            if primary_provider.get('is_fallback'):
                fallback_reason = primary_provider.get('fallback_reason', 'primary provider unavailable')
                attribution_parts.append(f"⚠️ **Fallback Used**: {fallback_reason}")
                
                # Record fallback metrics
                primary_name = primary_provider.get('fallback_reason', 'unknown')
                cache_metrics.record_provider_fallback(
                    primary_name,
                    primary_provider.get('provider_name', 'unknown'),
                    fallback_reason
                )
            
            # Add data window information if available
            data_window_start = primary_provider.get('data_window_start')
            data_window_end = primary_provider.get('data_window_end')
            if data_window_start and data_window_end:
                try:
                    if isinstance(data_window_start, str):
                        start_time = datetime.fromisoformat(data_window_start.replace('Z', '+00:00'))
                    else:
                        start_time = data_window_start
                    
                    if isinstance(data_window_end, str):
                        end_time = datetime.fromisoformat(data_window_end.replace('Z', '+00:00'))
                    else:
                        end_time = data_window_end
                    
                    start_str = start_time.strftime("%Y-%m-%d %H:%M")
                    end_str = end_time.strftime("%Y-%m-%d %H:%M")
                    attribution_parts.append(f"📅 **Data Coverage**: {start_str} → {end_str}")
                    
                except Exception as e:
                    logger.warning(f"Could not parse data window times: {e}")
            
            # Add cache information if applicable
            if primary_provider.get('cache_hit'):
                ttl_remaining = primary_provider.get('cache_ttl_remaining')
                if ttl_remaining:
                    attribution_parts.append(f"💾 **Cache Status**: Served from cache (TTL: {ttl_remaining}s remaining)")
                else:
                    attribution_parts.append("💾 **Cache Status**: Served from cache")
            
            # Add response time if available
            response_time = primary_provider.get('response_time_ms')
            if response_time:
                attribution_parts.append(f"⚡ **Response Time**: {response_time:.1f}ms")
            
            if attribution_parts:
                return "\n".join(attribution_parts)
            
            return ""
            
        except Exception as e:
            logger.error(f"Error generating attribution footer: {e}")
            return ""

    def _check_for_stale_data(self, technical_analysis: Dict[str, Any]) -> List:
        """
        Check for stale data in the technical analysis and return warnings.
        """
        try:
            warnings = []
            
            # Check market data freshness
            market_data = technical_analysis.get('market_data')
            if market_data:
                # Get the most recent timestamp from market data
                if hasattr(market_data, 'index') and not market_data.empty:
                    # DataFrame with datetime index
                    latest_timestamp = market_data.index.max()
                    if hasattr(latest_timestamp, 'timestamp'):
                        warning = detect_stale_data(
                            symbol=technical_analysis.get('symbol', 'unknown'),
                            last_update=latest_timestamp
                        )
                        if warning.severity.value != 'none':
                            warnings.append(warning)
                elif isinstance(market_data, list) and market_data:
                    # List of MarketDataResponse objects
                    for item in market_data:
                        if hasattr(item, 'metadata') and item.metadata:
                            fetched_at = item.metadata.get('fetched_at')
                            if fetched_at:
                                try:
                                    if isinstance(fetched_at, str):
                                        fetched_time = datetime.fromisoformat(fetched_at.replace('Z', '+00:00'))
                                    else:
                                        fetched_time = fetched_at
                                    
                                    warning = detect_stale_data(
                                        symbol=technical_analysis.get('symbol', 'unknown'),
                                        last_update=fetched_time
                                    )
                                    if warning.severity.value != 'none':
                                        warnings.append(warning)
                                        break  # Only need one warning per symbol
                                except Exception as e:
                                    logger.warning(f"Could not parse fetched_at time: {e}")
                                    continue
            
            # Check analysis timestamp freshness
            analysis_timestamp = technical_analysis.get('analysis_timestamp')
            if analysis_timestamp:
                try:
                    if isinstance(analysis_timestamp, str):
                        analysis_time = datetime.fromisoformat(analysis_timestamp.replace('Z', '+00:00'))
                    else:
                        analysis_time = analysis_timestamp
                    
                    warning = detect_stale_data(
                        symbol=technical_analysis.get('symbol', 'unknown'),
                        last_update=analysis_time
                    )
                    if warning.severity.value != 'none':
                        warnings.append(warning)
                except Exception as e:
                    logger.warning(f"Could not parse analysis timestamp: {e}")
            
            return warnings
            
        except Exception as e:
            logger.error(f"Error checking for stale data: {e}")
            return []

    def _check_for_outliers(self, technical_analysis: Dict[str, Any]) -> List:
        """
        Check for outliers in the technical analysis and return warnings.
        """
        try:
            warnings = []
            
            # Get market data for outlier detection
            market_data = technical_analysis.get('market_data')
            if market_data and hasattr(market_data, 'index') and not market_data.empty:
                # Use the outlier detector to find anomalies
                outliers = detect_outliers(
                    data=market_data,
                    symbol=technical_analysis.get('symbol', 'unknown'),
                    lookback_periods=20
                )
                
                # Convert outliers to warnings format
                for outlier in outliers:
                    warning = Mock()
                    warning.warning_message = outlier.description
                    warning.recommendations = outlier.recommendations
                    warning.confidence_impact = outlier.confidence_impact
                    warnings.append(warning)
            
            # Check for extreme indicator values
            indicators = technical_analysis.get('indicators', {})
            if indicators:
                # RSI extreme values
                if 'rsi' in indicators:
                    rsi_value = indicators['rsi']
                    if rsi_value > 100 or rsi_value < 0:
                        warning = Mock()
                        warning.warning_message = f"Extreme RSI value: {rsi_value:.1f} (normal range: 0-100)"
                        warning.recommendations = ["Verify RSI calculation", "Check for data anomalies"]
                        warning.confidence_impact = 15.0
                        warnings.append(warning)
                
                # Bollinger Bands validation
                if 'bb_upper' in indicators and 'bb_lower' in indicators:
                    bb_upper = indicators['bb_upper']
                    bb_lower = indicators['bb_lower']
                    if bb_upper <= bb_lower or bb_lower <= 0:
                        warning = Mock()
                        warning.warning_message = f"Invalid Bollinger Bands: Upper={bb_upper:.2f}, Lower={bb_lower:.2f}"
                        warning.recommendations = ["Verify Bollinger Bands calculation", "Check price data quality"]
                        warning.confidence_impact = 20.0
                        warnings.append(warning)
                
                # ATR validation
                if 'atr' in indicators:
                    atr_value = indicators['atr']
                    if atr_value <= 0:
                        warning = Mock()
                        warning.warning_message = f"Invalid ATR value: {atr_value:.2f} (should be positive)"
                        warning.recommendations = ["Verify ATR calculation", "Check for data gaps"]
                        warning.confidence_impact = 15.0
                        warnings.append(warning)
            
            return warnings
            
        except Exception as e:
            logger.error(f"Error checking for outliers: {e}")
            return []


async def call_ai_with_correlation(
    prompt: str,
    model: str = "default",
    correlation_id: Optional[str] = None,
    operation_type: str = "ai_query"
) -> Dict[str, Any]:
    """
    Make an AI service call with correlation ID logging.
    
    Args:
        prompt: Input prompt for AI
        model: AI model to use
        correlation_id: Correlation ID for request tracing
        operation_type: Type of AI operation
        
    Returns:
        AI response
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.info(
        "AI service call started",
        extra={
            "operation_type": operation_type,
            "model": model,
            "prompt_length": len(prompt),
            "correlation_id": correlation_id
        }
    )
    
    try:
        # Create a minimal config for the AI processor
        config = {
            'pipeline': {
                'model': model,
                'temperature': 0.7,
                'max_tokens': 2000,
                'timeout': 30.0
            },
            'technical': {
                'rsi_period': 14,
                'sma_short': 20,
                'sma_long': 50,
                'ema_short': 12,
                'ema_long': 26,
                'lookback_days': 90,
                'recent_prices_days': 20,
                'volume_sma_period': 20,
                'bollinger_period': 20,
                'bollinger_std': 2,
                'decimal_places': 2
            }
        }
        ai_processor = AIChatProcessor(config)
        
        # Make the AI call
        response = await ai_processor.process(prompt)
        
        logger.info(
            "AI service call completed successfully",
            extra={
                "operation_type": operation_type,
                "model": model,
                "response_length": len(str(response)),
                "correlation_id": correlation_id
            }
        )
        return response
        
    except Exception as e:
        logger.error(
            "AI service call failed",
            extra={
                "operation_type": operation_type,
                "model": model,
                "error": str(e),
                "error_type": type(e).__name__,
                "correlation_id": correlation_id
            },
            exc_info=True
        )
        raise


async def batch_ai_calls_with_correlation(
    prompts: List[str],
    model: str = "default",
    correlation_id: Optional[str] = None,
    operation_type: str = "batch_ai"
) -> List[Dict[str, Any]]:
    """
    Make multiple AI service calls with correlation ID logging.
    
    Args:
        prompts: List of input prompts
        model: AI model to use
        correlation_id: Correlation ID for request tracing
        operation_type: Type of batch AI operation
        
    Returns:
        List of AI responses
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.info(
        "Batch AI service calls started",
        extra={
            "operation_type": operation_type,
            "model": model,
            "batch_size": len(prompts),
            "correlation_id": correlation_id
        }
    )
    
    try:
        # Create a minimal config for the AI processor
        config = {
            'pipeline': {
                'model': model,
                'temperature': 0.7,
                'max_tokens': 2000,
                'timeout': 30.0
            },
            'technical': {
                'rsi_period': 14,
                'sma_short': 20,
                'sma_long': 50,
                'ema_short': 12,
                'ema_long': 26,
                'lookback_days': 90,
                'recent_prices_days': 20,
                'volume_sma_period': 20,
                'bollinger_period': 20,
                'bollinger_std': 2,
                'decimal_places': 2
            }
        }
        ai_processor = AIChatProcessor(config)
        responses = []
        
        for i, prompt in enumerate(prompts):
            logger.debug(
                f"Processing AI call {i+1}/{len(prompts)}",
                extra={
                    "operation_type": operation_type,
                    "model": model,
                    "prompt_index": i,
                    "prompt_length": len(prompt),
                    "correlation_id": correlation_id
                }
            )
            
            response = await ai_processor.process(prompt)
            responses.append(response)
        
        logger.info(
            "Batch AI service calls completed successfully",
            extra={
                "operation_type": operation_type,
                "model": model,
                "batch_size": len(prompts),
                "successful_calls": len(responses),
                "correlation_id": correlation_id
            }
        )
        return responses
        
    except Exception as e:
        logger.error(
            "Batch AI service calls failed",
            extra={
                "operation_type": operation_type,
                "model": model,
                "batch_size": len(prompts),
                "error": str(e),
                "error_type": type(e).__name__,
                "correlation_id": correlation_id
            },
            exc_info=True
        )
        raise


async def validate_ai_response_with_correlation(
    response: Dict[str, Any],
    correlation_id: Optional[str] = None,
    validation_type: str = "response_validation"
) -> Dict[str, Any]:
    """
    Validate AI response with correlation ID logging.
    
    Args:
        response: AI response to validate
        correlation_id: Correlation ID for request tracing
        validation_type: Type of validation
        
    Returns:
        Validation result
    """
    if correlation_id:
        logger.set_correlation_id(correlation_id)
    
    logger.info(
        "AI response validation started",
        extra={
            "validation_type": validation_type,
            "response_keys": list(response.keys()) if isinstance(response, dict) else [],
            "correlation_id": correlation_id
        }
    )
    
    try:
        # Basic validation logic
        validation_result = {
            "is_valid": True,
            "issues": [],
            "warnings": []
        }
        
        # Check for required fields
        required_fields = ["response", "intent"]
        for field in required_fields:
            if field not in response:
                validation_result["is_valid"] = False
                validation_result["issues"].append(f"Missing required field: {field}")
        
        # Check response length
        if "response" in response and len(str(response["response"])) < 10:
            validation_result["warnings"].append("Response seems very short")
        
        logger.info(
            "AI response validation completed",
            extra={
                "validation_type": validation_type,
                "is_valid": validation_result["is_valid"],
                "issues_count": len(validation_result["issues"]),
                "warnings_count": len(validation_result["warnings"]),
                "correlation_id": correlation_id
            }
        )
        
        return validation_result
        
    except Exception as e:
        logger.error(
            "AI response validation failed",
            extra={
                "validation_type": validation_type,
                "error": str(e),
                "error_type": type(e).__name__,
                "correlation_id": correlation_id
            },
            exc_info=True
        )
        raise 