"""
Response Template Engine

Deterministic response generation with template-based routing.
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from datetime import datetime, timedelta
from enum import Enum
import logging
import re
import re
from typing import Dict, Any, Optional, List

import string

from src.core.formatting.technical_analysis import TechnicalAnalysisFormatter
        
        validated_data = data.copy()
        current_time = datetime.now()
        
        # Check if we have timestamp data
        if 'timestamp' in data and data['timestamp']:
            try:
                # Parse timestamp - handle various formats
                timestamp_str = str(data['timestamp'])
                if 'T' in timestamp_str:
                    # ISO format
                    if timestamp_str.endswith('Z'):
                        timestamp_str = timestamp_str[:-1] + '+00:00'
                    timestamp = datetime.fromisoformat(timestamp_str)
                else:
                    # Try other common formats
                    timestamp = datetime.fromisoformat(timestamp_str)
                
                # Calculate age
                age_minutes = (current_time - timestamp).total_seconds() / 60
                
                # CRITICAL: Check for suspicious data patterns that indicate fallback sources
                suspicious_patterns = []
                
                # Pattern 1: High/Low/Open are all 0 or None (common in yfinance fallback)
                if (data.get('high') == 0 or data.get('high') is None) and \
                   (data.get('low') == 0 or data.get('low') is None) and \
                   (data.get('open') == 0 or data.get('open') is None):
                    suspicious_patterns.append("Missing OHLC data (possible fallback source)")
                
                # Pattern 2: Close equals current_price exactly (suspicious in real-time data)
                if data.get('close') == data.get('current_price') and data.get('close') is not None:
                    suspicious_patterns.append("Close equals current price (possible fallback)")
                
                # Pattern 3: Provider indicates fallback
                if data.get('provider') in ['fallback', 'yfinance']:
                    suspicious_patterns.append(f"Data from fallback provider: {data.get('provider')}")
                
                # Pattern 4: Very low volume for active stocks (suspicious)
                if data.get('volume') and data.get('volume') < 1000:
                    suspicious_patterns.append("Unusually low volume (possible stale data)")
                
                # If we detect suspicious patterns, mark data as potentially unreliable
                if suspicious_patterns:
                    logger.warning(f"Suspicious data patterns detected: {suspicious_patterns}")
                    validated_data['data_suspicious'] = True
                    validated_data['suspicious_reasons'] = suspicious_patterns
                    validated_data['data_freshness_warning'] = f"⚠️ Data may be from fallback source - verify prices independently"
                    
                    # For suspicious data, be more conservative with freshness thresholds
                    if age_minutes > 2:  # Lower threshold for suspicious data
                        validated_data['data_stale'] = True
                        validated_data['data_age_minutes'] = int(age_minutes)
                        validated_data['data_freshness_warning'] = f"⚠️ Data is {int(age_minutes)} minutes old and may be from fallback source"
                        
                        if age_minutes > 5:  # Much lower threshold for suspicious data
                            validated_data['prices_unreliable'] = True
                            validated_data['price_warning'] = "⚠️ Prices likely outdated - verify before trading"
                            
                            # Clear out specific price fields to prevent false recommendations
                            for key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                                if key in validated_data:
                                    validated_data[key] = None
                                    validated_data[f"{key}_unreliable"] = True
                            
                            # Clear change percentages
                            for key in ['change', 'change_percent']:
                                if key in validated_data:
                                    validated_data[key] = None
                                    validated_data[f"{key}_unreliable"] = True
                
                # Standard freshness validation (for non-suspicious data)
                elif age_minutes > 5:
                    validated_data['data_stale'] = True
                    validated_data['data_age_minutes'] = int(age_minutes)
                    validated_data['data_freshness_warning'] = f"⚠️ Data is {int(age_minutes)} minutes old"
                    
                    # For very old data (>15 minutes), mark prices as unreliable
                    if age_minutes > 15:
                        validated_data['prices_unreliable'] = True
                        validated_data['price_warning'] = "⚠️ Prices may be outdated - verify before trading"
                        
                        # Clear out specific price fields to prevent false recommendations
                        for key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                            if key in validated_data:
                                validated_data[key] = None
                                validated_data[f"{key}_unreliable"] = True
                        
                        # Clear change percentages
                        for key in ['change', 'change_percent']:
                            if key in validated_data:
                                validated_data[key] = None
                                validated_data[f"{key}_unreliable"] = True
                        
                        # Clear volume if very old
                        if age_minutes > 60:
                            if 'volume' in validated_data:
                                validated_data['volume'] = None
                                validated_data['volume_unreliable'] = True
                
                else:
                    validated_data['data_stale'] = False
                    validated_data['data_age_minutes'] = int(age_minutes)
                    validated_data['data_freshness_warning'] = "✅ Data is current"
                    
            except (ValueError, TypeError) as e:
                logger.warning(f"Could not parse timestamp '{data.get('timestamp')}': {e}")
                validated_data['data_stale'] = True
                validated_data['data_freshness_warning'] = "⚠️ Timestamp validation failed"
                validated_data['prices_unreliable'] = True
        
        # Check individual symbol data freshness
        if 'data' in validated_data and isinstance(validated_data['data'], dict):
            for symbol, symbol_data in validated_data['data'].items():
                if isinstance(symbol_data, dict) and 'timestamp' in symbol_data:
                    try:
                        symbol_timestamp_str = str(symbol_data['timestamp'])
                        if 'T' in symbol_timestamp_str:
                            if symbol_timestamp_str.endswith('Z'):
                                symbol_timestamp_str = symbol_timestamp_str[:-1] + '+00:00'
                            symbol_timestamp = datetime.fromisoformat(symbol_timestamp_str)
                            
                            symbol_age_minutes = (current_time - symbol_timestamp).total_seconds() / 60
                            
                            # Apply same suspicious pattern detection to individual symbols
                            symbol_suspicious = []
                            if (symbol_data.get('high') == 0 or symbol_data.get('high') is None) and \
                               (symbol_data.get('low') == 0 or symbol_data.get('low') is None) and \
                               (symbol_data.get('open') == 0 or symbol_data.get('open') is None):
                                symbol_suspicious.append("Missing OHLC data")
                            
                            if symbol_data.get('close') == symbol_data.get('current_price') and symbol_data.get('close') is not None:
                                symbol_suspicious.append("Close equals current price")
                            
                            if symbol_data.get('provider') in ['fallback', 'yfinance']:
                                symbol_suspicious.append(f"Fallback provider: {symbol_data.get('provider')}")
                            
                            if symbol_suspicious:
                                validated_data['data'][symbol]['data_suspicious'] = True
                                validated_data['data'][symbol]['suspicious_reasons'] = symbol_suspicious
                                
                                # Lower thresholds for suspicious data
                                if symbol_age_minutes > 2:
                                    validated_data['data'][symbol]['data_stale'] = True
                                    validated_data['data'][symbol]['data_age_minutes'] = int(symbol_age_minutes)
                                    
                                    if symbol_age_minutes > 5:
                                        validated_data['data'][symbol]['prices_unreliable'] = True
                                        # Clear price data for stale symbols
                                        for price_key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                                            if price_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][price_key] = None
                                        
                                        # Clear change data
                                        for change_key in ['change', 'change_percent']:
                                            if change_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][change_key] = None
                            else:
                                # Standard validation for non-suspicious data
                                if symbol_age_minutes > 5:
                                    validated_data['data'][symbol]['data_stale'] = True
                                    validated_data['data'][symbol]['data_age_minutes'] = int(symbol_age_minutes)
                                    
                                    if symbol_age_minutes > 15:
                                        validated_data['data'][symbol]['prices_unreliable'] = True
                                        # Clear price data for stale symbols
                                        for price_key in ['current_price', 'price', 'close', 'open', 'high', 'low']:
                                            if price_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][price_key] = None
                                        
                                        # Clear change data
                                        for change_key in ['change', 'change_percent']:
                                            if change_key in validated_data['data'][symbol]:
                                                validated_data['data'][symbol][change_key] = None
                                
                                else:
                                    validated_data['data'][symbol]['data_stale'] = False
                                    validated_data['data'][symbol]['data_age_minutes'] = int(symbol_age_minutes)
                                
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Could not parse symbol timestamp for {symbol}: {e}")
                        validated_data['data'][symbol]['data_stale'] = True
                        validated_data['data'][symbol]['prices_unreliable'] = True
        
        return validated_data

    def _generate_stale_data_warning(self, data: Dict[str, Any]) -> str:
        """Generate appropriate warning for stale data"""
        warnings = []
        
        # Check for suspicious data first
        if data.get('data_suspicious', False):
            suspicious_reasons = data.get('suspicious_reasons', ['Unknown data quality issues'])
            warnings.append(f"🚨 **CRITICAL WARNING:** Data appears to be from fallback source. Issues: {', '.join(suspicious_reasons)}")
            warnings.append("⚠️ **DO NOT** use these prices for trading decisions. Verify current data independently.")
        
        # Check for stale data
        if data.get('data_stale', False):
            age_minutes = data.get('data_age_minutes', 0)
            
            if age_minutes > 60:
                warnings.append("🚨 **CRITICAL WARNING:** Market data is over 1 hour old. **DO NOT** use these prices for trading decisions. Please refresh data for current market conditions.")
            elif age_minutes > 15:
                warnings.append("⚠️ **IMPORTANT:** Market data is over 15 minutes old. Prices may be outdated. Verify current data before making trading decisions.")
            elif age_minutes > 5:
                warnings.append("⚠️ **Note:** Market data is over 5 minutes old. Consider refreshing for most current prices.")
        
        # Check for unreliable prices
        if data.get('prices_unreliable', False):
            warnings.append("🚨 **PRICE RELIABILITY WARNING:** Prices marked as unreliable due to data age or quality issues.")
        
        if warnings:
            return "\n\n" + "\n\n".join(warnings)
        
        return ""

    def _adjust_recommendation_for_stale_data(self, recommendation: Recommendation, data: Dict[str, Any]) -> Recommendation:
        """Adjust trading recommendation based on data freshness"""
        if not data.get('data_stale', False):
            return recommendation
        
        age_minutes = data.get('data_age_minutes', 0)
        
        # For stale data, reduce confidence and change action to HOLD
        if age_minutes > 15:
            recommendation.action = "HOLD"
            recommendation.confidence = max(10, recommendation.confidence * 0.3)  # Reduce confidence significantly
            recommendation.reasoning = f"Data is {age_minutes} minutes old - insufficient for active trading recommendations"
            recommendation.risk_level = "High"
        elif age_minutes > 5:
            recommendation.confidence = max(20, recommendation.confidence * 0.7)  # Reduce confidence moderately
            recommendation.reasoning += f" - Note: Data is {age_minutes} minutes old"
        
        return recommendation

    def _format_symbol_data_with_freshness(self, symbol: str, symbol_data: Dict[str, Any]) -> str:
        """Format individual symbol data with freshness warnings"""
        if not symbol_data.get('data_available', True):
            return f"• **{symbol}**: Data unavailable"
        
        # Check if this symbol's data is stale
        if symbol_data.get('data_stale', False):
            age_minutes = symbol_data.get('data_age_minutes', 0)
            if age_minutes > 15:
                return f"• **{symbol}**: ⚠️ Data {age_minutes}min old - prices unreliable"
            else:
                return f"• **{symbol}**: ⚠️ Data {age_minutes}min old"
        
        # Format current data
        current_price = symbol_data.get('current_price')
        change_percent = symbol_data.get('change_percent', 0)
        
        if current_price is not None:
            change_sign = "+" if change_percent >= 0 else ""
            return f"• **{symbol}**: ${current_price:.2f} ({change_sign}{change_percent:.2f}%)"
        else:
            return f"• **{symbol}**: Price data unavailable"
    
    def _generate_market_overview_with_freshness(self, data: Dict[str, Any]) -> str:
        """Generate market overview with data freshness indicators"""
        if 'data' not in data or not isinstance(data['data'], dict):
            return "Market data unavailable"
        
        overview_parts = []
        
        for symbol, symbol_data in data['data'].items():
            overview_parts.append(self._format_symbol_data_with_freshness(symbol, symbol_data))
        
        return "\n".join(overview_parts)

    def _enhance_response_with_fresh_data(self, response: str, data: Dict[str, Any]) -> str:
        """Enhance the response with fresh data formatting for market overview"""
        if 'data' not in data or not isinstance(data['data'], dict):
            return response
        
        # Find the indices section in the response
        indices_match = re.search(r"📊 Major Indices:\n(.*?)(?=\n📊 Major Indices:|\n💭 Market Sentiment:|\n🎯 Market Trend:|\n🏭 Sector Performance:|\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if indices_match:
            indices_section = indices_match.group(1)
            enhanced_indices_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(indices_section, enhanced_indices_section)
        
        # Find the sentiment and trend section in the response
        sentiment_match = re.search(r"💭 Market Sentiment:\s*(.*?)(?=\n🎯 Market Trend:|\n🏭 Sector Performance:|\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        trend_match = re.search(r"🎯 Market Trend:\s*(.*?)(?=\n🏭 Sector Performance:|\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if sentiment_match and trend_match:
            sentiment_section = sentiment_match.group(1)
            trend_section = trend_match.group(1)
            
            enhanced_sentiment_section = self._generate_market_overview_with_freshness(data)
            enhanced_trend_section = self._generate_market_overview_with_freshness(data)
            
            response = response.replace(sentiment_section, enhanced_sentiment_section)
            response = response.replace(trend_section, enhanced_trend_section)
        
        # Find the sectors section in the response
        sectors_match = re.search(r"🏭 Sector Performance:\n(.*?)(?=\n💡 Market Insights:|\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if sectors_match:
            sectors_section = sectors_match.group(1)
            enhanced_sectors_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(sectors_section, enhanced_sectors_section)
        
        # Find the insights section in the response
        insights_match = re.search(r"💡 Market Insights:\n(.*?)(?=\n⚠️ Risk Factors:|\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if insights_match:
            insights_section = insights_match.group(1)
            enhanced_insights_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(insights_section, enhanced_insights_section)
        
        # Find the risk factors section in the response
        risk_factors_match = re.search(r"⚠️ Risk Factors:\n(.*?)(?=\n🎯 Trading Strategy:|\n*Updated:)", response, re.DOTALL)
        
        if risk_factors_match:
            risk_factors_section = risk_factors_match.group(1)
            enhanced_risk_factors_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(risk_factors_section, enhanced_risk_factors_section)
        
        # Find the strategy section in the response
        strategy_match = re.search(r"🎯 Trading Strategy:\n(.*?)(?=\n*Updated:)", response, re.DOTALL)
        
        if strategy_match:
            strategy_section = strategy_match.group(1)
            enhanced_strategy_section = self._generate_market_overview_with_freshness(data)
            response = response.replace(strategy_section, enhanced_strategy_section)
        
        return response

    def _calculate_dynamic_confidence(self, enhanced_data: Dict[str, Any]) -> int:
        """Calculate confidence dynamically based on data quality, freshness, and completeness."""
        try:
            base_confidence = 50  # Start with neutral confidence
            
            # Data quality impact (0-30 points)
            data_quality = enhanced_data.get('data_quality', 0)
            quality_boost = int((data_quality / 100) * 30)
            
            # Data freshness impact (0-25 points)
            data_age_minutes = enhanced_data.get('data_age_minutes', 0)
            if data_age_minutes <= 1:
                freshness_boost = 25  # Very fresh
            elif data_age_minutes <= 5:
                freshness_boost = 20  # Fresh
            elif data_age_minutes <= 15:
                freshness_boost = 15  # Acceptable
            elif data_age_minutes <= 60:
                freshness_boost = 10  # Old
            else:
                freshness_boost = 0   # Very old
            
            # Data completeness impact (0-20 points)
            data = enhanced_data.get('data', {})
            if data:
                total_fields = 0
                available_fields = 0
                
                for symbol, stock_data in data.items():
                    required_fields = ['current_price', 'change', 'change_percent', 'volume', 'high', 'low', 'open', 'close']
                    total_fields += len(required_fields)
                    available_fields += sum(1 for field in required_fields 
                                        if stock_data.get(field) is not None and stock_data.get(field) != 0)
                
                if total_fields > 0:
                    completeness_ratio = available_fields / total_fields
                    completeness_boost = int(completeness_ratio * 20)
                else:
                    completeness_boost = 0
            else:
                completeness_boost = 0
            
            # Data reliability impact (0-25 points)
            reliability_boost = 25  # Start with full reliability
            
            if enhanced_data.get('data_suspicious', False):
                reliability_boost = 0  # Suspicious data = no reliability
            elif enhanced_data.get('prices_unreliable', False):
                reliability_boost = 5   # Unreliable prices = minimal reliability
            elif enhanced_data.get('data_stale', False):
                reliability_boost = max(10, reliability_boost - 15)  # Stale data reduces reliability
            
            # Calculate final confidence
            final_confidence = base_confidence + quality_boost + freshness_boost + completeness_boost + reliability_boost
            
            # Ensure confidence is within valid range (0-100)
            final_confidence = max(0, min(100, final_confidence))
            
            logger.debug(f"Confidence calculation: base={base_confidence}, quality={quality_boost}, "
                        f"freshness={freshness_boost}, completeness={completeness_boost}, "
                        f"reliability={reliability_boost}, final={final_confidence}")
            
            return final_confidence
            
        except Exception as e:
            logger.warning(f"Confidence calculation failed: {e}, using default 25")
            return 25

    def test_freshness_validation(self) -> bool:
        """Test method to verify freshness validation works correctly"""
        try:
            # Test with fresh data
            fresh_data = {
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'TSLA': {
                        'current_price': 340.01,
                        'change_percent': 5.7,
                        'data_available': True,
                        'timestamp': datetime.now().isoformat()
                    }
                }
            }
            
            validated_fresh = self._validate_data_freshness(fresh_data)
            if validated_fresh.get('data_stale', True):
                logger.error("Fresh data incorrectly marked as stale")
                return False
            
            # Test with stale data
            stale_timestamp = (datetime.now() - timedelta(minutes=20)).isoformat()
            stale_data = {
                'timestamp': stale_timestamp,
                'data': {
                    'TSLA': {
                        'current_price': 340.01,
                        'change_percent': 5.7,
                        'data_available': True,
                        'timestamp': stale_timestamp
                    }
                }
            }
            
            validated_stale = self._validate_data_freshness(stale_data)
            if not validated_stale.get('data_stale', False):
                logger.error("Stale data incorrectly marked as fresh")
                return False
            
            if not validated_stale.get('prices_unreliable', False):
                logger.error("Stale data prices not marked as unreliable")
                return False
            
            logger.info("✅ Freshness validation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Freshness validation test failed: {e}")
            return False

class PatternDetector:
    """Basic technical pattern detection"""
    
    def detect_patterns(self, price_data: Dict[str, Any]) -> List[TechnicalPattern]:
        """Detect basic technical patterns in price data"""
        patterns = []
        
        try:
            # Simple pattern detection logic with null checks
            if 'current_price' in price_data and 'change' in price_data:
                price = price_data['current_price']
                change = price_data['change']
                
                # Handle None values
                if price is None or change is None:
                    logger.warning("price or change is None in pattern detection, skipping pattern analysis")
                    return patterns
                
                # Convert to float safely
                try:
                    price_float = float(price)
                    change_float = float(change)
                except (TypeError, ValueError):
                    logger.error(f"Error converting price or change to float: price={price}, change={change}")
                    return patterns
                
                # Very basic pattern detection (placeholder for more sophisticated logic)
                if change_float > 5:  # Strong upward movement
                    patterns.append(TechnicalPattern(
                        pattern_type=PatternType.BULL_FLAG,
                        confidence=70.0,
                        direction="bullish",
                        strength="moderate",
                        price_targets={"resistance": price_float * 1.05},
                        stop_loss=price_float * 0.95,
                        description="Potential bull flag pattern forming"
                    ))
                elif change_float < -5:  # Strong downward movement
                    patterns.append(TechnicalPattern(
                        pattern_type=PatternType.BEAR_FLAG,
                        confidence=70.0,
                        direction="bearish",
                        strength="moderate",
                        price_targets={"support": price_float * 0.95},
                        stop_loss=price_float * 1.05,
                        description="Potential bear flag pattern forming"
                    ))
            
        except Exception as e:
            logger.warning(f"Pattern detection failed: {e}")
        
        return patterns

class SentimentAnalyzer:
    """Basic market sentiment analysis"""
    
    def analyze_sentiment(self, data: Dict[str, Any]) -> SentimentAnalysis:
        """Analyze market sentiment from available data"""
        try:
            # Simple sentiment analysis based on price movement with null check
            change = data.get('change')
            if change is None:
                logger.warning("change is None in sentiment analysis, using neutral sentiment")
                return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)
            
            # Convert to float safely
            try:
                change_float = float(change)
            except (TypeError, ValueError):
                logger.error(f"change value could not be converted to float: {change}, using neutral sentiment")
                return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)
            
            if change_float > 3:
                sentiment = 0.7  # Bullish
                label = "Bullish"
            elif change_float > 1:
                sentiment = 0.3  # Slightly Bullish
                label = "Slightly Bullish"
            elif change_float < -3:
                sentiment = -0.7  # Bearish
                label = "Bearish"
            elif change_float < -1:
                sentiment = -0.3  # Slightly Bearish
                label = "Slightly Bearish"
            else:
                sentiment = 0.0  # Neutral
                label = "Neutral"
            
            return SentimentAnalysis(
                overall_sentiment=sentiment,
                sentiment_label=label,
                confidence=0.6,  # Basic confidence for simple analysis
                news_count=0,
                recent_headlines=[],
                market_fear_greed=None
            )
            
        except Exception as e:
            logger.warning(f"Sentiment analysis failed: {e}")
            return SentimentAnalysis(0.0, "Neutral", 0.5, 0, [], None)