"""
Test file to verify the pipeline infrastructure is working correctly.

This is a temporary file to ensure our base classes and configuration
are properly set up before we begin the extraction process.
"""

import asyncio
import os
from typing import Dict, Any

from src.bot.pipeline.commands.ask.stages.config import PipelineConfig, PipelineMode, get_config, update_config
from src.bot.pipeline.commands.ask.stages.core.base import <PERSON>ing<PERSON>ontext, ProcessingResult, ProcessingStage
from src.bot.pipeline.commands.ask.stages.core.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType, ErrorSeverity
        
        # Set environment variable
        os.environ["PIPELINE_MODE"] = "legacy"
        os.environ["USE_NEW_PREPROCESSOR"] = "false"
        
        # Create new config (should pick up environment variables)
        config = PipelineConfig()
        assert config.mode == PipelineMode.LEGACY
        assert config.use_new_preprocessor is False
        
        # Clean up
        del os.environ["PIPELINE_MODE"]
        del os.environ["USE_NEW_PREPROCESSOR"]
    
    def test_config_serialization(self):
        """Test configuration serialization to/from dictionary."""
        config = PipelineConfig(
            mode=PipelineMode.MODULAR,
            use_new_preprocessor=False,
            cache_ttl=600
        )
        
        # Convert to dictionary
        config_dict = config.to_dict()
        assert config_dict["mode"] == "modular"
        assert config_dict["feature_flags"]["use_new_preprocessor"] is False
        assert config_dict["performance"]["caching"]["ttl"] == 600
        
        # Create from dictionary
        new_config = PipelineConfig.from_dict(config_dict)
        assert new_config.mode == PipelineMode.MODULAR
        assert new_config.use_new_preprocessor is False
        assert new_config.cache_ttl == 600


if __name__ == "__main__":
    # Run tests
    test_instance = TestPipelineInfrastructure()
    
    # Run all test methods
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
    
    print(f"Running {len(test_methods)} infrastructure tests...")
    
    for method_name in test_methods:
        try:
            method = getattr(test_instance, method_name)
            method()
            print(f"✅ {method_name} - PASSED")
        except Exception as e:
            print(f"❌ {method_name} - FAILED: {e}")
    
    print("\nInfrastructure test completed!") 