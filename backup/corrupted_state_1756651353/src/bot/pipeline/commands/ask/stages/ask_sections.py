"""
Advanced AI-Driven Market Analysis Pipeline

This module implements a sophisticated, modular pipeline for processing user queries 
related to market analysis. The pipeline is designed to:
- Analyze user intent through AI-powered query interpretation
- Collect and process market data dynamically
- Generate intelligent, context-aware responses
- Format responses for Discord communication

Key Features:
- Flexible processing routes (knowledge-based, data-driven, hybrid)
- Robust error handling and fallback mechanisms
- Advanced market data collection and analysis
- Configurable response generation strategies

Architecture:
The pipeline consists of four main sections:
1. Query Analysis: Interprets user intent and extracts key symbols
2. Data Collection: Gathers market data based on analysis
3. Response Generation: Creates intelligent responses
4. Response Formatting: Prepares output for Discord

Dependencies:
- Python 3.8+
- Async programming support
- Market data providers (primary and fallback)
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from datetime import datetime
from datetime import datetime, timedelta
from datetime import datetime, timedelta
from enum import Enum, auto
import logging
import time
from typing import Dict, Any, List, Optional, Union, TypedDict, Literal, Protocol, ClassVar

from config import config
from pipeline_sections import PipelineSection, SectionResult
from query_analyzer import AIQueryAnalyzer, SymbolContext
from response_templates import ResponseTemplateEngine, ResponseDepth, ResponseStyle
from symbol_validator import SymbolValidator
from utils.circuit_breaker import CircuitBreaker, CircuitBreakerConfig
from utils.metrics import metrics_collector
import yfinance as yf
import yfinance as yf

from src.api.data.market_data_service import MarketDataService
from src.api.data.market_data_service import MarketDataService
from src.core.exceptions import PipelineError
from src.core.logger import get_pipeline_logger
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            formatted_time = dt.strftime("%H:%M:%S")
            analysis_parts.append(f"*Updated: {formatted_time}*")
        except:
            pass
    
    return "\n".join(analysis_parts)

async def _generate_hybrid_response(analysis_context: Dict[str, Any], collected_data: Dict[str, Any]) -> str:
    """Generate hybrid response combining data and knowledge"""
    
    # AI decides how to balance data and knowledge
    intent = analysis_context.get("intent", "general_question")
    
    if intent == "comparison":
        return "Comparing the data I collected: I'll analyze the performance metrics, technical indicators, and fundamental factors to provide you with a comprehensive comparison."
    elif intent == "market_overview":
        return "Market overview combining data and insights: I'll give you a complete picture including current market conditions, trends, and actionable insights."
    else:
        return "Here's my analysis combining available data and knowledge: I've gathered market data and will provide you with both technical analysis and strategic insights to help inform your investment decisions."

async def _format_response_for_discord(ai_response: str, response_type: str, analysis_context: Dict[str, Any]) -> str:
    """Format response for Discord with both structured data and natural chat"""
    
    # Generate natural chat response with formatting
    chat_response = (
        "**Chat Response**\n\n"
        f"{ai_response}\n\n"
        "━━━━━━━━━━━━━━━━━━━━\n"
    )
    
    # Generate detailed structured data section
    structured_data = "**Analysis Context**\n"
    if analysis_context:
        structured_data += f"• **Intent**: {analysis_context.get('intent', 'N/A')}\n"
        structured_data += f"• **Confidence**: {analysis_context.get('confidence', 0)*100:.1f}%\n"
        if 'symbols' in analysis_context:
            symbols = ', '.join([
                f"{s.text} ({s.confidence*100:.0f}%)" 
                for s in analysis_context['symbols']
            ])
            structured_data += f"• **Symbols**: {symbols or 'None'}\n"
        if 'processing_route' in analysis_context:
            structured_data += f"• **Processing**: {analysis_context['processing_route']}\n"
    
    return f"{chat_response}{structured_data}"