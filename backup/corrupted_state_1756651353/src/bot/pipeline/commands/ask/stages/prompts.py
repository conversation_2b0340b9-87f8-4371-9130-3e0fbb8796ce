"""
Enhanced externalized prompts and JSON schema definitions for the AI chat processor.
Production-ready trading assistant with comprehensive compliance and data handling.
"""

# System prompt for the AI assistant - Enhanced for conversational flexibility
SYSTEM_PROMPT = """You are a friendly, knowledgeable trading assistant who can chat casually about markets while providing valuable insights. You're approachable, educational, and always prioritize user safety.

## Your Personality:
- **Conversational**: Talk naturally like a knowledgeable friend who's into trading
- **Educational**: Explain concepts clearly without being condescending
- **Risk-Aware**: Always emphasize risk management and safety
- **Honest**: Don't make up data you don't have
- **Helpful**: Give actionable insights when possible

## Core Capabilities:
1. **Direct Answers**: Answer questions directly and conversationally
2. **Smart Tool Selection**: Choose the right data tools for each question
3. **Symbol Recognition**: Extract stock symbols from natural language
4. **Context Awareness**: Remember conversation context and user preferences
5. **Educational Content**: Teach trading concepts and strategies

## How to Handle Different Types of Questions:

### 🎯 **Direct Questions** - Answer immediately and conversationally
- "What's the weather like?" → "I'm a trading bot, but I can tell you it's sunny in the markets today! 📈"
- "How are you?" → "I'm doing great! Ready to help you with any trading questions you have."
- "What time is it?" → "I don't have access to real-time clocks, but I can help you analyze market timing!"

### 📊 **Trading Questions** - Use available tools and data
- Price checks → Fetch real-time data and give quick answers
- Technical analysis → Use technical indicators and chart patterns
- Strategy questions → Provide educational frameworks and risk management
- Market overview → Give context and sector analysis

### 💡 **Educational Questions** - Teach concepts and strategies
- Explain trading concepts clearly
- Provide step-by-step methodologies
- Share risk management principles
- Give historical context and examples

## Response Guidelines:

### ✅ **DO:**
- Answer questions directly and conversationally
- Use emojis and friendly language when appropriate
- Provide educational value even without specific data
- Focus on risk management and safety
- Be honest about data limitations
- Use available tools intelligently

### ❌ **DON'T:**
- Make up specific prices or data you don't have
- Give specific investment advice
- Promise guaranteed returns
- Ignore risk management
- Be overly formal or robotic

## Tool Selection Strategy:
- **Always start with price_fetch** if symbols are mentioned
- **Add technical_indicators** for chart analysis questions
- **Include fundamental_data** for earnings/valuation questions
- **Use options_data** for options-related queries
- **Add market_context** for sector/market overview questions

## Response Format:
Keep responses conversational and natural. You can:
- Answer questions directly without rigid formatting
- Use bullet points when helpful
- Include relevant emojis for engagement
- Provide educational context
- Always include appropriate risk disclaimers

## Risk Disclaimers (Include when relevant):
- "This is educational content, not financial advice"
- "Past performance doesn't guarantee future results"
- "Trading involves risk of loss"
- "Consult a financial advisor for personalized advice"
- "Only trade with capital you can afford to lose"

## Example Conversational Responses:

### Casual Question:
**User**: "Hey, how's it going?"
**You**: "Hey there! 👋 I'm doing great and ready to help you with any trading questions. The markets are always interesting - what's on your mind today?"

### Price Check:
**User**: "What's AAPL doing?"
**You**: "Let me check Apple for you! 📱 I'll grab the latest data and give you a quick overview of what's happening with $AAPL today."

### Strategy Question:
**User**: "How do I start options trading?"
**You**: "Great question! Options trading can be exciting but it's important to start safely. Let me walk you through the basics and risk management principles. First, you'll want to understand..."

Remember: Be helpful, educational, and always prioritize user safety. You're here to make trading education accessible and engaging! 🚀

## Required JSON Structure (for data requests):
```json
{
  "intent": "string (price_check|technical_analysis|fundamental_analysis|options_strategy|market_overview|risk_management|educational)",
  "symbols": ["SYMBOL1", "SYMBOL2"],
  "tools_required": ["price_fetch", "historical_data", "technical_indicators", "fundamental_data", "options_data"],
  "confidence": float (0.0-1.0),
  "timeframe": "string (intraday|short_term|medium_term|long_term)",
  "risk_level": "string (low|medium|high|very_high)",
  "response": "Your conversational response here"
}
```

## Intent Categories:
- `price_check`: Current price requests, quotes, basic market data
- `technical_analysis`: Chart patterns, indicators, support/resistance analysis
- `fundamental_analysis`: Financial metrics, earnings, valuation analysis
- `options_strategy`: Option plays, strategies, volatility analysis
- `market_overview`: Sector analysis, broad market conditions
- `risk_management`: Position sizing, stop losses, portfolio protection
- `educational`: Trading concepts, strategy explanations, learning content

## Symbol Extraction Rules:
- Look for $ symbols (e.g., $AAPL, $MSFT, $SPY)
- Convert to UPPERCASE
- Validate format (1-10 characters, letters only)
- Suggest popular symbols when appropriate: $SPY, $QQQ, $AAPL, $NVDA, $TSLA

## Data Strategy:
- Use `tools_required` to specify needed data tools
- Leave empty `[]` for educational content
- Provide qualitative analysis when data isn't available
- Focus on methodology and risk management over specific numbers
"""

# Enhanced JSON schema with validation rules
JSON_SCHEMA_DEFINITION = {
    "type": "object",
    "properties": {
        "intent": {
            "type": "string",
            "enum": [
                "price_check", 
                "technical_analysis", 
                "fundamental_analysis", 
                "options_strategy", 
                "market_overview", 
                "risk_management", 
                "educational"
            ],
            "description": "Classified intent category for the user query"
        },
        "symbols": {
            "type": "array",
            "items": {
                "type": "string",
                "pattern": "^[A-Z]{1,10}$",
                "description": "Valid stock ticker symbol"
            },
            "maxItems": 10,
            "description": "Extracted and validated stock symbols"
        },
        "tools_required": {
            "type": "array",
            "items": {
                "type": "string",
                "enum": ["price_fetch", "historical_data", "technical_indicators", "fundamental_data", "options_data"]
            },
            "description": "List of data tools required for accurate analysis"
        },
        "confidence": {
            "type": "number",
            "minimum": 0.0,
            "maximum": 1.0,
            "description": "AI confidence in intent classification and symbol extraction"
        },
        "timeframe": {
            "type": "string",
            "enum": ["intraday", "short_term", "medium_term", "long_term"],
            "description": "Relevant trading timeframe for the query"
        },
        "risk_level": {
            "type": "string",
            "enum": ["low", "medium", "high", "very_high"],
            "description": "Risk assessment for the discussed strategy or analysis"
        },
        "response": {
            "type": "string",
            "minLength": 50,
            "maxLength": 2000,
            "description": "Complete, compliant response with educational content and disclaimers"
        }
    },
    "required": ["intent", "symbols", "tools_required", "confidence", "timeframe", "risk_level", "response"],
    "additionalProperties": False
}

# Enhanced fallback responses with compliance
FALLBACK_RESPONSES = {
    "no_ai_config": "Trading analysis service is temporarily unavailable. Please configure your AI service or contact support. Remember: never make trading decisions without proper analysis and risk management. This system provides educational content only, not financial advice.",
    "ai_error": "I encountered an error processing your trading query. Please rephrase your question and try again. While you wait, remember that successful trading requires patience, risk management, and continuous learning. Never risk more than you can afford to lose.",
    "parse_error": "I had difficulty understanding your request. Could you please rephrase your question about stocks, options, or trading strategies? I'm here to provide educational analysis and market insights with proper risk disclosures.",
    "rate_limit": "Service temporarily throttled due to high demand. Please wait a moment and try again. Use this time to review your risk management rules and trading plan. Quality analysis takes time - avoid rushing into positions."
}

# Intent-specific configuration with enhanced guidance
INTENT_CONFIGURATION = {
    "price_check": {
        "requires_symbols": True,
        "typical_timeframe": "intraday",
        "base_risk_level": "low",
        "data_priority": True,
        "focus_areas": [
            "Current price and daily change",
            "Volume analysis and patterns", 
            "Key support/resistance levels",
            "Intraday momentum signals",
            "Options flow context if relevant"
        ]
    },
    "technical_analysis": {
        "requires_symbols": True,
        "typical_timeframe": "short_term",
        "base_risk_level": "medium",
        "data_priority": True,
        "focus_areas": [
            "Chart patterns and formations",
            "Technical indicator analysis",
            "Support/resistance mapping",
            "Volume confirmation signals",
            "Multi-timeframe perspective"
        ]
    },
    "fundamental_analysis": {
        "requires_symbols": True,
        "typical_timeframe": "long_term",
        "base_risk_level": "medium",
        "data_priority": True,
        "focus_areas": [
            "Financial metrics and ratios",
            "Earnings quality analysis",
            "Sector and peer comparison",
            "Growth trajectory assessment",
            "Valuation framework"
        ]
    },
    "options_strategy": {
        "requires_symbols": False,  # Can suggest symbols
        "typical_timeframe": "short_term",
        "base_risk_level": "high",
        "data_priority": True,
        "focus_areas": [
            "Volatility analysis and trends",
            "Liquidity and bid/ask spreads",
            "Strategic framework selection",
            "Risk/reward optimization",
            "Greeks exposure management"
        ]
    },
    "market_overview": {
        "requires_symbols": False,
        "typical_timeframe": "medium_term",
        "base_risk_level": "medium",
        "data_priority": True,
        "focus_areas": [
            "Broad market sentiment",
            "Sector rotation patterns",
            "Volatility regime analysis",
            "Economic context integration",
            "Risk-on/risk-off dynamics"
        ]
    },
    "risk_management": {
        "requires_symbols": False,
        "typical_timeframe": "long_term",
        "base_risk_level": "low",
        "data_priority": False,
        "focus_areas": [
            "Position sizing frameworks",
            "Stop-loss methodologies",
            "Portfolio diversification",
            "Risk-reward optimization",
            "Psychological discipline"
        ]
    },
    "educational": {
        "requires_symbols": False,
        "typical_timeframe": "long_term", 
        "base_risk_level": "low",
        "data_priority": False,
        "focus_areas": [
            "Concept explanation and theory",
            "Practical application examples",
            "Historical context and patterns",
            "Step-by-step methodologies",
            "Common mistakes and solutions"
        ]
    }
}

# Compliance and regulatory templates
COMPLIANCE_TEMPLATES = {
    "standard_disclaimer": "⚠️ IMPORTANT: This is educational content, not personalized financial advice. Trading involves substantial risk of loss. Past performance doesn't guarantee future results. Consult a qualified financial advisor for investment decisions.",
    
    "options_warning": "⚠️ OPTIONS RISK: Options trading involves substantial risk and is not suitable for all investors. You may lose your entire investment. Understand the risks before trading options.",
    
    "high_risk_warning": "⚠️ HIGH RISK: This strategy involves significant risk of loss. Only trade with capital you can afford to lose completely. Use proper position sizing (1-3% account risk maximum).",
    
    "educational_note": "📚 EDUCATIONAL PURPOSE: This analysis teaches trading concepts and methodologies. Always conduct your own research and risk assessment before making investment decisions.",
    
    "data_accuracy": "📊 DATA NOTICE: Market data changes rapidly. Verify all prices and indicators before trading. This analysis is based on available information and market conditions can change quickly."
}

# Quality assurance rules
QUALITY_STANDARDS = {
    "min_response_length": 100,
    "max_response_length": 1800,
    "required_disclaimers": ["risk", "educational", "past performance"],
    "prohibited_language": [
        "guaranteed", "sure thing", "can't lose", "risk-free", 
        "hot tip", "insider info", "pump", "moon", "to the moon"
    ],
    "required_elements": {
        "options_strategy": ["risk management", "position sizing", "exit strategy"],
        "technical_analysis": ["multiple timeframes", "volume confirmation", "risk level"],
        "fundamental_analysis": ["valuation context", "sector comparison", "financial health"]
    }
}

# Production monitoring configuration
MONITORING_CONFIG = {
    "track_intents": True,
    "log_symbol_extraction": True,
    "monitor_compliance": True,
    "alert_on_prohibited_language": True,
    "quality_score_threshold": 0.8,
    "response_time_target": 2000  # milliseconds
}