"""
Enhanced Market Context Service

Provides comprehensive market context including:
- Market hours detection with timezone handling
- Sector and industry analysis
- Market sentiment analysis
- Volatility-based risk assessment
- Market structure insights
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import time
from typing import Dict, Any, List, Optional, Tuple

import pytz


logger = logging.getLogger(__name__)

@dataclass
class MarketHours:
    """Market hours information for different exchanges."""
    exchange: str
    timezone: str
    open_time: str  # HH:MM format
    close_time: str  # HH:MM format
    pre_market_open: str  # HH:MM format
    after_hours_close: str  # HH:MM format
    holidays: List[str]  # List of holiday dates
    is_24h: bool = False  # For crypto markets

@dataclass
class SectorInfo:
    """Sector and industry information."""
    sector: str
    industry: str
    sector_performance: Dict[str, float]  # 1d, 1w, 1m, 3m, 1y
    industry_performance: Dict[str, float]
    sector_rank: int  # Performance rank among sectors
    industry_rank: int  # Performance rank within sector
    volatility: float  # Sector volatility
    correlation: float  # Correlation with market

@dataclass
class MarketSentiment:
    """Market sentiment indicators."""
    overall_sentiment: str  # bullish, bearish, neutral
    sentiment_score: float  # -1.0 to 1.0
    fear_greed_index: float  # 0-100
    volatility_index: float  # VIX-like measure
    news_sentiment: float  # -1.0 to 1.0
    social_sentiment: float  # -1.0 to 1.0
    institutional_flow: float  # Money flow direction

class EnhancedMarketContextService:
    """Enhanced market context service with comprehensive market intelligence."""
    
    def __init__(self):
        """Initialize the market context service."""
        self.logger = logging.getLogger(__name__)
        
        # Market hours configuration for major exchanges
        self.market_hours = {
            'NYSE': MarketHours(
                exchange='NYSE',
                timezone='America/New_York',
                open_time='09:30',
                close_time='16:00',
                pre_market_open='04:00',
                after_hours_close='20:00',
                holidays=[
                    '2024-01-01', '2024-01-15', '2024-02-19', '2024-03-29',
                    '2024-05-27', '2024-06-19', '2024-07-04', '2024-09-02',
                    '2024-11-28', '2024-12-25'
                ]
            ),
            'NASDAQ': MarketHours(
                exchange='NASDAQ',
                timezone='America/New_York',
                open_time='09:30',
                close_time='16:00',
                pre_market_open='04:00',
                after_hours_close='20:00',
                holidays=[
                    '2024-01-01', '2024-01-15', '2024-02-19', '2024-03-29',
                    '2024-05-27', '2024-06-19', '2024-07-04', '2024-09-02',
                    '2024-11-28', '2024-12-25'
                ]
            ),
            'CRYPTO': MarketHours(
                exchange='CRYPTO',
                timezone='UTC',
                open_time='00:00',
                close_time='23:59',
                pre_market_open='00:00',
                after_hours_close='23:59',
                holidays=[],
                is_24h=True
            )
        }
        
        # Sector performance cache
        self.sector_cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        
        # Sentiment analysis cache
        self.sentiment_cache = {}
        self.sentiment_cache_ttl = 1800  # 30 minutes cache
    
    def get_current_market_status(self, exchange: str = 'NYSE') -> Dict[str, Any]:
        """
        Get current market status for a specific exchange.
        
        Args:
            exchange: Exchange name (NYSE, NASDAQ, CRYPTO)
            
        Returns:
            Market status information
        """
        try:
            if exchange not in self.market_hours:
                exchange = 'NYSE'  # Default to NYSE
                
            market_config = self.market_hours[exchange]
            tz = pytz.timezone(market_config.timezone)
            now = datetime.now(tz)
            
            # Check if it's a holiday
            today_str = now.strftime('%Y-%m-%d')
            is_holiday = today_str in market_config.holidays
            
            if is_holiday:
                return {
                    'exchange': exchange,
                    'status': 'holiday',
                    'markets_open': False,
                    'current_time': now.strftime('%H:%M:%S %Z'),
                    'next_open': self._get_next_open_time(market_config, now),
                    'message': f'Markets closed for {exchange} holiday'
                }
            
            # For 24h markets like crypto
            if market_config.is_24h:
                return {
                    'exchange': exchange,
                    'status': 'open',
                    'markets_open': True,
                    'current_time': now.strftime('%H:%M:%S %Z'),
                    'message': f'{exchange} markets are always open'
                }
            
            # Check regular market hours
            current_time = now.time()
            open_time = datetime.strptime(market_config.open_time, '%H:%M').time()
            close_time = datetime.strptime(market_config.close_time, '%H:%M').time()
            pre_market_open = datetime.strptime(market_config.pre_market_open, '%H:%M').time()
            after_hours_close = datetime.strptime(market_config.after_hours_close, '%H:%M').time()
            
            # Determine market status
            if open_time <= current_time <= close_time:
                status = 'open'
                markets_open = True
                message = f'{exchange} markets are open'
            elif pre_market_open <= current_time < open_time:
                status = 'pre_market'
                markets_open = False
                message = f'{exchange} pre-market trading'
            elif close_time < current_time <= after_hours_close:
                status = 'after_hours'
                markets_open = False
                message = f'{exchange} after-hours trading'
            else:
                status = 'closed'
                markets_open = False
                message = f'{exchange} markets are closed'
            
            # Calculate time until next open/close
            time_until_next = self._calculate_time_until_next(market_config, now, current_time)
            
            return {
                'exchange': exchange,
                'status': status,
                'markets_open': markets_open,
                'current_time': now.strftime('%H:%M:%S %Z'),
                'time_until_next': time_until_next,
                'message': message,
                'timezone': market_config.timezone
            }
            
        except Exception as e:
            self.logger.error(f"Error getting market status: {e}")
            return {
                'exchange': exchange,
                'status': 'error',
                'markets_open': False,
                'error': str(e)
            }
    
    def _get_next_open_time(self, market_config: MarketHours, current_time: datetime) -> str:
        """Calculate the next market open time."""
        try:
            open_time = datetime.strptime(market_config.open_time, '%H:%M').time()
            next_open = current_time.replace(
                hour=open_time.hour,
                minute=open_time.minute,
                second=0,
                microsecond=0
            )
            
            # If today's open time has passed, move to tomorrow
            if next_open <= current_time:
                next_open += timedelta(days=1)
            
            return next_open.strftime('%Y-%m-%d %H:%M:%S %Z')
        except Exception as e:
            self.logger.error(f"Error calculating next open time: {e}")
            return "Unknown"
    
    def _calculate_time_until_next(self, market_config: MarketHours, current_time: datetime, current_time_obj: time) -> str:
        """Calculate time until next market event."""
        try:
            open_time = datetime.strptime(market_config.open_time, '%H:%M').time()
            close_time = datetime.strptime(market_config.close_time, '%H:%M').time()
            
            if current_time_obj < open_time:
                # Before market open
                next_event = current_time.replace(
                    hour=open_time.hour,
                    minute=open_time.minute,
                    second=0,
                    microsecond=0
                )
            elif current_time_obj < close_time:
                # During market hours
                next_event = current_time.replace(
                    hour=close_time.hour,
                    minute=close_time.minute,
                    second=0,
                    microsecond=0
                )
            else:
                # After market close
                next_event = current_time.replace(
                    hour=open_time.hour,
                    minute=open_time.minute,
                    second=0,
                    microsecond=0
                ) + timedelta(days=1)
            
            time_diff = next_event - current_time
            hours = int(time_diff.total_seconds() // 3600)
            minutes = int((time_diff.total_seconds() % 3600) // 60)
            
            if hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"
                
        except Exception as e:
            self.logger.error(f"Error calculating time until next: {e}")
            return "Unknown"
    
    async def get_sector_analysis(self, symbol: str) -> Optional[SectorInfo]:
        """
        Get sector and industry analysis for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Sector information or None if not available
        """
        try:
            # Check cache first
            cache_key = f"sector_{symbol}"
            if cache_key in self.sector_cache:
                cache_time, cache_data = self.sector_cache[cache_key]
                if time.time() - cache_time < self.cache_ttl:
                    return cache_data
            
            # TODO: Implement actual sector data fetching from providers
            # For now, return mock data
            sector_info = SectorInfo(
                sector="Technology",
                industry="Software",
                sector_performance={
                    '1d': 1.2,
                    '1w': 3.5,
                    '1m': 8.2,
                    '3m': 15.7,
                    '1y': 45.3
                },
                industry_performance={
                    '1d': 0.8,
                    '1w': 2.9,
                    '1m': 7.1,
                    '3m': 12.4,
                    '1y': 38.7
                },
                sector_rank=3,
                industry_rank=2,
                volatility=0.25,
                correlation=0.78
            )
            
            # Cache the result
            self.sector_cache[cache_key] = (time.time(), sector_info)
            
            return sector_info
            
        except Exception as e:
            self.logger.error(f"Error getting sector analysis for {symbol}: {e}")
            return None
    
    async def get_market_sentiment(self, symbol: str = None) -> MarketSentiment:
        """
        Get market sentiment indicators.
        
        Args:
            symbol: Optional symbol for symbol-specific sentiment
            
        Returns:
            Market sentiment information
        """
        try:
            # Check cache first
            cache_key = f"sentiment_{symbol or 'general'}"
            if cache_key in self.sentiment_cache:
                cache_time, cache_data = self.sentiment_cache[cache_key]
                if time.time() - cache_time < self.sentiment_cache_ttl:
                    return cache_data
            
            # TODO: Implement actual sentiment analysis from news and social media
            # For now, return mock data
            sentiment = MarketSentiment(
                overall_sentiment="bullish",
                sentiment_score=0.65,
                fear_greed_index=72.0,
                volatility_index=18.5,
                news_sentiment=0.58,
                social_sentiment=0.71,
                institutional_flow=0.42
            )
            
            # Cache the result
            self.sentiment_cache[cache_key] = (time.time(), sentiment)
            
            return sentiment
            
        except Exception as e:
            self.logger.error(f"Error getting market sentiment: {e}")
            # Return neutral sentiment on error
            return MarketSentiment(
                overall_sentiment="neutral",
                sentiment_score=0.0,
                fear_greed_index=50.0,
                volatility_index=20.0,
                news_sentiment=0.0,
                social_sentiment=0.0,
                institutional_flow=0.0
            )
    
    def get_market_context_summary(self, exchange: str = 'NYSE') -> str:
        """
        Get a human-readable market context summary.
        
        Args:
            exchange: Exchange to check
            
        Returns:
            Formatted market context string
        """
        try:
            status = self.get_current_market_status(exchange)
            
            if status.get('status') == 'error':
                return f"⚠️ Unable to determine market status for {exchange}"
            
            context_parts = []
            
            # Market status
            if status['markets_open']:
                context_parts.append(f"🟢 **{exchange} Markets OPEN**")
            else:
                context_parts.append(f"🔴 **{exchange} Markets CLOSED**")
            
            # Current time
            context_parts.append(f"⏰ {status['current_time']}")
            
            # Status details
            if status['status'] == 'pre_market':
                context_parts.append("📈 Pre-market trading available")
            elif status['status'] == 'after_hours':
                context_parts.append("📉 After-hours trading available")
            elif status['status'] == 'holiday':
                context_parts.append("🎉 Market holiday")
            
            # Time until next event
            if 'time_until_next' in status and status['time_until_next'] != "Unknown":
                if status['markets_open']:
                    context_parts.append(f"🔒 Closes in {status['time_until_next']}")
                else:
                    context_parts.append(f"🔓 Opens in {status['time_until_next']}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating market context summary: {e}")
            return f"⚠️ Error getting market context: {str(e)}"
    
    async def get_comprehensive_market_context(self, symbol: str = None, exchange: str = 'NYSE') -> Dict[str, Any]:
        """
        Get comprehensive market context including hours, sector analysis, and sentiment.
        
        Args:
            symbol: Optional symbol for symbol-specific analysis
            exchange: Exchange to check
            
        Returns:
            Comprehensive market context
        """
        try:
            # Get market status
            market_status = self.get_current_market_status(exchange)
            
            # Get sector analysis if symbol provided
            sector_info = None
            if symbol:
                sector_info = await self.get_sector_analysis(symbol)
            
            # Get market sentiment
            sentiment = await self.get_market_sentiment(symbol)
            
            # Generate context summary
            context_summary = self.get_market_context_summary(exchange)
            
            return {
                'market_status': market_status,
                'sector_analysis': sector_info,
                'market_sentiment': sentiment,
                'context_summary': context_summary,
                'timestamp': datetime.now().isoformat(),
                'exchange': exchange,
                'symbol': symbol
            }
            
        except Exception as e:
            self.logger.error(f"Error getting comprehensive market context: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            } 