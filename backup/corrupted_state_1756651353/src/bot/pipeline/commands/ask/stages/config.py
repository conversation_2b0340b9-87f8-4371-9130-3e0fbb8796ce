"""
Configuration and feature flags for the /ask pipeline.

This module provides configuration management and feature flags to enable
gradual migration and safe rollbacks during the refactoring process.
"""

from dataclasses import dataclass, field
from enum import Enum
import os
from typing import Dict, Any, Optional



class PipelineMode(Enum):
    """Pipeline execution modes."""
    LEGACY = "legacy"           # Use original monolithic implementation
    HYBRID = "hybrid"           # Use new modules with fallback to legacy
    MODULAR = "modular"         # Use only new modular implementation


@dataclass
class PipelineConfig:
    """Configuration for the pipeline stages."""
    
    # Pipeline execution mode
    mode: PipelineMode = PipelineMode.HYBRID
    
    # Feature flags for gradual migration
    use_new_preprocessor: bool = True
    use_new_core: bool = True
    use_new_postprocessor: bool = True
    use_new_utils: bool = True
    
    # Enhancement flags for existing modules
    enable_symbol_validator_enhancements: bool = True
    enable_query_analyzer_enhancements: bool = True
    enable_enhanced_ai_client: bool = True
    enable_response_generator_enhancements: bool = True
    enable_enhanced_cache_manager: bool = True
    enable_technical_analysis_enhancements: bool = True
    enable_market_context_enhancements: bool = True
    
    # Fallback settings
    fallback_to_legacy: bool = True
    legacy_fallback_timeout: int = 5  # seconds
    
    # Performance settings
    enable_caching: bool = True
    cache_ttl: int = 300  # seconds
    enable_rate_limiting: bool = True
    max_requests_per_minute: int = 60
    
    # Error handling settings
    max_retries: int = 3
    retry_backoff_base: float = 2.0
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60  # seconds
    
    # AI service settings
    primary_ai_service: str = "openai"
    fallback_ai_service: str = "anthropic"
    ai_timeout: int = 30  # seconds
    
    # AI model settings
    model: str = "moonshotai/kimi-k2:free"
    temperature: float = 0.7
    max_tokens: int = 2000
    api_key: Optional[str] = None
    
    # Logging settings
    log_level: str = "INFO"
    enable_structured_logging: bool = True
    log_performance_metrics: bool = True
    
    # Monitoring settings
    enable_health_checks: bool = True
    health_check_interval: int = 30  # seconds
    enable_metrics_collection: bool = True
    
    def __post_init__(self):
        """Post-initialization validation and environment overrides."""
        # Mark which parameters were explicitly set by checking if they differ from defaults
        self._preprocessor_set = self.use_new_preprocessor != True
        self._core_set = self.use_new_core != True
        self._postprocessor_set = self.use_new_postprocessor != True
        self._utils_set = self.use_new_utils != True
            
        self._load_from_environment()
        self._validate_config()
    
    def _load_from_environment(self):
        """Load configuration from environment variables."""
        # Pipeline mode
        mode_str = os.getenv("PIPELINE_MODE", "").lower()
        if mode_str in [m.value for m in PipelineMode]:
            self.mode = PipelineMode(mode_str)
        
        # Feature flags - only override if not explicitly set in constructor
        if not hasattr(self, '_preprocessor_set'):
            self.use_new_preprocessor = os.getenv("USE_NEW_PREPROCESSOR", "true").lower() == "true"
        if not hasattr(self, '_core_set'):
            self.use_new_core = os.getenv("USE_NEW_CORE", "true").lower() == "true"
        if not hasattr(self, '_postprocessor_set'):
            self.use_new_postprocessor = os.getenv("USE_NEW_POSTPROCESSOR", "true").lower() == "true"
        if not hasattr(self, '_utils_set'):
            self.use_new_utils = os.getenv("USE_NEW_UTILS", "true").lower() == "true"
        
        # Fallback settings
        self.fallback_to_legacy = os.getenv("FALLBACK_TO_LEGACY", "true").lower() == "true"
        
        # Performance settings
        self.enable_caching = os.getenv("ENABLE_CACHING", "true").lower() == "true"
        self.enable_rate_limiting = os.getenv("ENABLE_RATE_LIMITING", "true").lower() == "true"
        
        # AI service settings
        self.primary_ai_service = os.getenv("PRIMARY_AI_SERVICE", self.primary_ai_service)
        self.fallback_ai_service = os.getenv("FALLBACK_AI_SERVICE", self.fallback_ai_service)
        
        # Logging settings
        self.log_level = os.getenv("LOG_LEVEL", self.log_level)
    
    def _validate_config(self):
        """Validate configuration values."""
        if self.cache_ttl < 0:
            raise ValueError("Cache TTL must be non-negative")
        
        if self.max_requests_per_minute < 1:
            raise ValueError("Max requests per minute must be positive")
        
        if self.max_retries < 0:
            raise ValueError("Max retries must be non-negative")
        
        if self.circuit_breaker_threshold < 1:
            raise ValueError("Circuit breaker threshold must be positive")
    
    def is_legacy_mode(self) -> bool:
        """Check if pipeline is running in legacy mode."""
        return self.mode == PipelineMode.LEGACY
    
    def is_hybrid_mode(self) -> bool:
        """Check if pipeline is running in hybrid mode."""
        return self.mode == PipelineMode.HYBRID
    
    def is_modular_mode(self) -> bool:
        """Check if pipeline is running in modular mode."""
        return self.mode == PipelineMode.MODULAR
    
    def should_use_new_component(self, component: str) -> bool:
        """Check if a specific new component should be used."""
        if self.is_legacy_mode():
            return False
        
        if self.is_modular_mode():
            return True
        
        # Hybrid mode - check specific component flag
        component_flags = {
            "preprocessor": self.use_new_preprocessor,
            "core": self.use_new_core,
            "postprocessor": self.use_new_postprocessor,
            "utils": self.use_new_utils
        }
        
        return component_flags.get(component, False)
    
    def get_fallback_config(self) -> Dict[str, Any]:
        """Get fallback configuration."""
        return {
            "enabled": self.fallback_to_legacy,
            "timeout": self.legacy_fallback_timeout,
            "mode": self.mode.value
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance-related configuration."""
        return {
            "caching": {
                "enabled": self.enable_caching,
                "ttl": self.cache_ttl
            },
            "rate_limiting": {
                "enabled": self.enable_rate_limiting,
                "max_requests_per_minute": self.max_requests_per_minute
            },
            "retries": {
                "max_retries": self.max_retries,
                "backoff_base": self.retry_backoff_base
            },
            "circuit_breaker": {
                "threshold": self.circuit_breaker_threshold,
                "timeout": self.circuit_breaker_timeout
            }
        }
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI service configuration."""
        return {
            "primary": self.primary_ai_service,
            "fallback": self.fallback_ai_service,
            "timeout": self.ai_timeout
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return {
            "level": self.log_level,
            "structured": self.enable_structured_logging,
            "performance_metrics": self.log_performance_metrics
        }
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration."""
        return {
            "health_checks": {
                "enabled": self.enable_health_checks,
                "interval": self.health_check_interval
            },
            "metrics": {
                "enabled": self.enable_metrics_collection
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "mode": self.mode.value,
            "feature_flags": {
                "use_new_preprocessor": self.use_new_preprocessor,
                "use_new_core": self.use_new_core,
                "use_new_postprocessor": self.use_new_postprocessor,
                "use_new_utils": self.use_new_utils
            },
            "fallback": self.get_fallback_config(),
            "performance": self.get_performance_config(),
            "ai_services": self.get_ai_config(),
            "logging": self.get_logging_config(),
            "monitoring": self.get_monitoring_config()
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'PipelineConfig':
        """Create configuration from dictionary."""
        # Extract mode
        mode = PipelineMode(config_dict.get("mode", "hybrid"))
        
        # Extract feature flags
        feature_flags = config_dict.get("feature_flags", {})
        
        # Create config instance
        config = cls(
            mode=mode,
            use_new_preprocessor=feature_flags.get("use_new_preprocessor", True),
            use_new_core=feature_flags.get("use_new_core", True),
            use_new_postprocessor=feature_flags.get("use_new_postprocessor", True),
            use_new_utils=feature_flags.get("use_new_utils", True)
        )
        
        # Override with dictionary values
        for key, value in config_dict.items():
            if hasattr(config, key) and key not in ["mode", "feature_flags"]:
                setattr(config, key, value)
        
        return config


# Global configuration instance
pipeline_config = PipelineConfig()


def get_config() -> PipelineConfig:
    """Get the global pipeline configuration."""
    return pipeline_config


def update_config(updates: Dict[str, Any]) -> None:
    """Update the global pipeline configuration."""
    global pipeline_config
    
    for key, value in updates.items():
        if hasattr(pipeline_config, key):
            setattr(pipeline_config, key, value)
    
    # Re-validate after updates
    pipeline_config._validate_config()


def reset_config() -> None:
    """Reset configuration to defaults."""
    global pipeline_config
    pipeline_config = PipelineConfig() 