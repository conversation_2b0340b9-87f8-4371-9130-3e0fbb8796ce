"""
Advanced AI Routing Service

Provides intelligent routing of queries to different AI models based on:
- Query complexity analysis
- Model capabilities and performance
- Cost optimization
- Fallback chains
- User preferences
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import logging
import re
import time
from typing import Dict, Any, List, Optional, Tuple


logger = logging.getLogger(__name__)

class ModelCapability(Enum):
    """AI model capabilities."""
    BASIC_ANALYSIS = "basic_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    COMPLEX_ANALYSIS = "complex_analysis"
    REAL_TIME_TRADING = "real_time_trading"
    EDUCATIONAL = "educational"
    RISK_ASSESSMENT = "risk_assessment"

class QueryComplexity(Enum):
    """Query complexity levels."""
    SIMPLE = "simple"           # Basic price/volume questions
    MODERATE = "moderate"       # Technical indicators
    COMPLEX = "complex"         # Multi-timeframe analysis
    EXPERT = "expert"           # Advanced strategies, risk assessment
    REAL_TIME = "real_time"     # Live trading decisions

@dataclass
class AIModel:
    """AI model configuration and capabilities."""
    name: str
    provider: str
    model_id: str
    capabilities: List[ModelCapability]
    max_tokens: int
    cost_per_1k_tokens: float
    response_time_ms: int
    accuracy_score: float  # 0.0 to 1.0
    is_available: bool = True
    last_used: Optional[datetime] = None
    usage_count: int = 0
    error_count: int = 0

@dataclass
class QueryAnalysis:
    """Analysis of query requirements and complexity."""
    complexity: QueryComplexity
    required_capabilities: List[ModelCapability]
    estimated_tokens: int
    urgency: str  # "low", "medium", "high"
    user_experience_level: str  # "beginner", "intermediate", "expert"
    requires_real_time: bool
    is_educational: bool

class AdvancedAIRoutingService:
    """Advanced AI routing service with intelligent model selection."""
    
    def __init__(self):
        """Initialize the AI routing service."""
        self.logger = logging.getLogger(__name__)
        
        # Initialize available AI models
        self.models = self._initialize_models()
        
        # Query complexity patterns
        self.complexity_patterns = {
            QueryComplexity.SIMPLE: [
                r'\b(what is|how much|current price|stock price|share price)\b',
                r'\b(price of|value of|worth)\b',
                r'\b(volume|market cap|pe ratio)\b'
            ],
            QueryComplexity.MODERATE: [
                r'\b(rsi|macd|moving average|bollinger|support|resistance)\b',
                r'\b(technical|indicator|trend|pattern)\b',
                r'\b(analysis|chart|signal)\b'
            ],
            QueryComplexity.COMPLEX: [
                r'\b(multi.*timeframe|multiple.*period|compare.*timeframe)\b',
                r'\b(correlation|correlation.*between|relationship.*between)\b',
                r'\b(advanced.*strategy|complex.*analysis|detailed.*analysis)\b'
            ],
            QueryComplexity.EXPERT: [
                r'\b(risk.*assessment|portfolio.*optimization|hedging.*strategy)\b',
                r'\b(derivatives|options.*strategy|futures.*analysis)\b',
                r'\b(quantitative.*analysis|statistical.*model|backtesting)\b'
            ],
            QueryComplexity.REAL_TIME: [
                r'\b(live.*trading|real.*time.*signal|execute.*now|place.*order)\b',
                r'\b(current.*market.*condition|live.*portfolio|instant.*analysis)\b',
                r'\b(urgent|immediate|now|current.*moment)\b'
            ]
        }
        
        # Capability requirements for each complexity level
        self.complexity_capabilities = {
            QueryComplexity.SIMPLE: [ModelCapability.BASIC_ANALYSIS],
            QueryComplexity.MODERATE: [ModelCapability.TECHNICAL_ANALYSIS],
            QueryComplexity.COMPLEX: [ModelCapability.COMPLEX_ANALYSIS],
            QueryComplexity.EXPERT: [ModelCapability.RISK_ASSESSMENT, ModelCapability.COMPLEX_ANALYSIS],
            QueryComplexity.REAL_TIME: [ModelCapability.REAL_TIME_TRADING, ModelCapability.COMPLEX_ANALYSIS]
        }
        
        # Model performance tracking
        self.performance_metrics = {}
        self.query_history = []
        
        # User preference cache
        self.user_preferences = {}
    
    def _initialize_models(self) -> Dict[str, AIModel]:
        """Initialize available AI models with their capabilities."""
        models = {
            'gpt-4o-mini': AIModel(
                name='GPT-4o Mini',
                provider='OpenAI',
                model_id='gpt-4o-mini',
                capabilities=[
                    ModelCapability.BASIC_ANALYSIS,
                    ModelCapability.TECHNICAL_ANALYSIS,
                    ModelCapability.EDUCATIONAL
                ],
                max_tokens=16384,
                cost_per_1k_tokens=0.00015,
                response_time_ms=2000,
                accuracy_score=0.85
            ),
            'gpt-4o': AIModel(
                name='GPT-4o',
                provider='OpenAI',
                model_id='gpt-4o',
                capabilities=[
                    ModelCapability.BASIC_ANALYSIS,
                    ModelCapability.TECHNICAL_ANALYSIS,
                    ModelCapability.FUNDAMENTAL_ANALYSIS,
                    ModelCapability.COMPLEX_ANALYSIS,
                    ModelCapability.EDUCATIONAL,
                    ModelCapability.RISK_ASSESSMENT
                ],
                max_tokens=128000,
                cost_per_1k_tokens=0.005,
                response_time_ms=3000,
                accuracy_score=0.92
            ),
            'claude-3-5-sonnet': AIModel(
                name='Claude 3.5 Sonnet',
                provider='Anthropic',
                model_id='claude-3-5-sonnet-20241022',
                capabilities=[
                    ModelCapability.BASIC_ANALYSIS,
                    ModelCapability.TECHNICAL_ANALYSIS,
                    ModelCapability.FUNDAMENTAL_ANALYSIS,
                    ModelCapability.COMPLEX_ANALYSIS,
                    ModelCapability.EDUCATIONAL,
                    ModelCapability.RISK_ASSESSMENT
                ],
                max_tokens=200000,
                cost_per_1k_tokens=0.003,
                response_time_ms=2500,
                accuracy_score=0.90
            ),
            'mixtral-8x7b': AIModel(
                name='Mixtral 8x7B',
                provider='Mistral',
                model_id='mixtral-8x7b-instruct',
                capabilities=[
                    ModelCapability.BASIC_ANALYSIS,
                    ModelCapability.TECHNICAL_ANALYSIS,
                    ModelCapability.EDUCATIONAL
                ],
                max_tokens=32768,
                cost_per_1k_tokens=0.00014,
                response_time_ms=1500,
                accuracy_score=0.78
            ),
            'llama-3-8b': AIModel(
                name='Llama 3 8B',
                provider='Meta',
                model_id='llama-3-8b-instruct',
                capabilities=[
                    ModelCapability.BASIC_ANALYSIS,
                    ModelCapability.EDUCATIONAL
                ],
                max_tokens=8192,
                cost_per_1k_tokens=0.00010,
                response_time_ms=1000,
                accuracy_score=0.75
            )
        }
        
        return models
    
    def analyze_query_complexity(self, query: str, user_id: str = None) -> QueryAnalysis:
        """
        Analyze query complexity and requirements.
        
        Args:
            query: User query text
            user_id: Optional user ID for personalized analysis
            
        Returns:
            Query analysis with complexity and requirements
        """
        try:
            query_lower = query.lower()
            
            # Determine complexity level
            complexity = self._determine_complexity(query_lower)
            
            # Get required capabilities
            required_capabilities = self.complexity_capabilities.get(complexity, [ModelCapability.BASIC_ANALYSIS])
            
            # Estimate token requirements
            estimated_tokens = self._estimate_token_requirements(query, complexity)
            
            # Determine urgency
            urgency = self._determine_urgency(query_lower)
            
            # Determine user experience level
            user_experience_level = self._determine_user_experience(query_lower, user_id)
            
            # Check if real-time data is required
            requires_real_time = self._requires_real_time(query_lower)
            
            # Check if query is educational
            is_educational = self._is_educational(query_lower)
            
            return QueryAnalysis(
                complexity=complexity,
                required_capabilities=required_capabilities,
                estimated_tokens=estimated_tokens,
                urgency=urgency,
                user_experience_level=user_experience_level,
                requires_real_time=requires_real_time,
                is_educational=is_educational
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing query complexity: {e}")
            # Return safe defaults
            return QueryAnalysis(
                complexity=QueryComplexity.MODERATE,
                required_capabilities=[ModelCapability.BASIC_ANALYSIS],
                estimated_tokens=1000,
                urgency="medium",
                user_experience_level="intermediate",
                requires_real_time=False,
                is_educational=True
            )
    
    def _determine_complexity(self, query_lower: str) -> QueryComplexity:
        """Determine query complexity based on patterns."""
        try:
            # Check patterns from most complex to least
            for complexity in [QueryComplexity.REAL_TIME, QueryComplexity.EXPERT, 
                             QueryComplexity.COMPLEX, QueryComplexity.MODERATE]:
                patterns = self.complexity_patterns.get(complexity, [])
                for pattern in patterns:
                    if re.search(pattern, query_lower):
                        return complexity
            
            # Default to simple if no patterns match
            return QueryComplexity.SIMPLE
            
        except Exception as e:
            self.logger.error(f"Error determining complexity: {e}")
            return QueryComplexity.MODERATE
    
    def _estimate_token_requirements(self, query: str, complexity: QueryComplexity) -> int:
        """Estimate token requirements for the query."""
        try:
            # Base tokens for query
            base_tokens = len(query.split()) * 1.3  # Rough estimate
            
            # Complexity multipliers
            complexity_multipliers = {
                QueryComplexity.SIMPLE: 1.0,
                QueryComplexity.MODERATE: 1.5,
                QueryComplexity.COMPLEX: 2.0,
                QueryComplexity.EXPERT: 2.5,
                QueryComplexity.REAL_TIME: 3.0
            }
            
            multiplier = complexity_multipliers.get(complexity, 1.0)
            estimated = int(base_tokens * multiplier * 100)  # Convert to approximate tokens
            
            # Ensure reasonable bounds
            return max(500, min(estimated, 10000))
            
        except Exception as e:
            self.logger.error(f"Error estimating token requirements: {e}")
            return 1000
    
    def _determine_urgency(self, query_lower: str) -> str:
        """Determine query urgency level."""
        urgent_keywords = ['urgent', 'immediate', 'now', 'asap', 'quick', 'fast']
        if any(keyword in query_lower for keyword in urgent_keywords):
            return "high"
        
        time_keywords = ['today', 'current', 'live', 'real-time']
        if any(keyword in query_lower for keyword in time_keywords):
            return "medium"
        
        return "low"
    
    def _determine_user_experience(self, query_lower: str, user_id: str = None) -> str:
        """Determine user experience level."""
        # Check user preferences if available
        if user_id and user_id in self.user_preferences:
            return self.user_preferences[user_id].get('experience_level', 'intermediate')
        
        # Analyze query for experience indicators
        beginner_keywords = ['what is', 'explain', 'how to', 'beginner', 'simple']
        expert_keywords = ['quantitative', 'derivatives', 'hedging', 'backtesting', 'statistical']
        
        if any(keyword in query_lower for keyword in expert_keywords):
            return "expert"
        elif any(keyword in query_lower for keyword in beginner_keywords):
            return "beginner"
        else:
            return "intermediate"
    
    def _requires_real_time(self, query_lower: str) -> bool:
        """Check if query requires real-time data."""
        real_time_keywords = ['live', 'real-time', 'current', 'now', 'instant', 'execute']
        return any(keyword in query_lower for keyword in real_time_keywords)
    
    def _is_educational(self, query_lower: str) -> bool:
        """Check if query is educational in nature."""
        educational_keywords = ['explain', 'how to', 'what is', 'why', 'when', 'where', 'learn']
        return any(keyword in query_lower for keyword in educational_keywords)
    
    def select_optimal_model(self, query_analysis: QueryAnalysis, user_id: str = None) -> Tuple[AIModel, List[AIModel]]:
        """
        Select the optimal AI model for a query.
        
        Args:
            query_analysis: Analysis of query requirements
            user_id: Optional user ID for personalized selection
            
        Returns:
            Tuple of (optimal_model, fallback_models)
        """
        try:
            # Filter models by required capabilities
            capable_models = [
                model for model in self.models.values()
                if model.is_available and 
                any(cap in model.capabilities for cap in query_analysis.required_capabilities)
            ]
            
            if not capable_models:
                self.logger.warning("No capable models available for query requirements")
                # Return least capable model as fallback
                return list(self.models.values())[0], []
            
            # Score models based on multiple factors
            scored_models = []
            for model in capable_models:
                score = self._calculate_model_score(model, query_analysis, user_id)
                scored_models.append((model, score))
            
            # Sort by score (highest first)
            scored_models.sort(key=lambda x: x[1], reverse=True)
            
            # Select optimal model
            optimal_model = scored_models[0][0]
            
            # Select fallback models (next 2 best)
            fallback_models = [model for model, _ in scored_models[1:3]]
            
            # Update model usage tracking
            optimal_model.last_used = datetime.now()
            optimal_model.usage_count += 1
            
            self.logger.info(f"Selected model {optimal_model.name} for {query_analysis.complexity.value} query")
            
            return optimal_model, fallback_models
            
        except Exception as e:
            self.logger.error(f"Error selecting optimal model: {e}")
            # Return safe fallback
            fallback_model = list(self.models.values())[0]
            return fallback_model, []
    
    def _calculate_model_score(self, model: AIModel, query_analysis: QueryAnalysis, user_id: str = None) -> float:
        """Calculate a score for model selection."""
        try:
            score = 0.0
            
            # Base accuracy score (40% weight)
            score += model.accuracy_score * 0.4
            
            # Cost efficiency (25% weight)
            cost_score = 1.0 / (1.0 + model.cost_per_1k_tokens * 1000)  # Normalize cost
            score += cost_score * 0.25
            
            # Response time (20% weight)
            time_score = 1.0 / (1.0 + model.response_time_ms / 1000)  # Normalize time
            score += time_score * 0.2
            
            # Token capacity (10% weight)
            if model.max_tokens >= query_analysis.estimated_tokens:
                capacity_score = 1.0
            else:
                capacity_score = model.max_tokens / query_analysis.estimated_tokens
            score += capacity_score * 0.1
            
            # User preference (5% weight)
            if user_id and user_id in self.user_preferences:
                user_pref = self.user_preferences[user_id].get('preferred_models', [])
                if model.name in user_pref:
                    score += 0.05
            
            # Performance history (bonus/penalty)
            if model.name in self.performance_metrics:
                perf = self.performance_metrics[model.name]
                success_rate = perf.get('success_rate', 0.8)
                score += (success_rate - 0.8) * 0.1  # ±10% bonus/penalty
            
            return max(0.0, min(1.0, score))  # Clamp to 0-1 range
            
        except Exception as e:
            self.logger.error(f"Error calculating model score: {e}")
            return 0.5  # Return neutral score on error
    
    def record_model_performance(self, model_name: str, success: bool, response_time_ms: int, 
                                tokens_used: int, cost: float):
        """Record model performance metrics."""
        try:
            if model_name not in self.performance_metrics:
                self.performance_metrics[model_name] = {
                    'total_requests': 0,
                    'successful_requests': 0,
                    'success_rate': 0.0,
                    'avg_response_time': 0.0,
                    'total_tokens': 0,
                    'total_cost': 0.0,
                    'last_updated': datetime.now()
                }
            
            metrics = self.performance_metrics[model_name]
            metrics['total_requests'] += 1
            
            if success:
                metrics['successful_requests'] += 1
            
            # Update success rate
            metrics['success_rate'] = metrics['successful_requests'] / metrics['total_requests']
            
            # Update average response time
            current_avg = metrics['avg_response_time']
            metrics['avg_response_time'] = (current_avg * (metrics['total_requests'] - 1) + response_time_ms) / metrics['total_requests']
            
            # Update token and cost tracking
            metrics['total_tokens'] += tokens_used
            metrics['total_cost'] += cost
            metrics['last_updated'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error recording model performance: {e}")
    
    def get_routing_summary(self) -> Dict[str, Any]:
        """Get a summary of routing performance and model usage."""
        try:
            summary = {
                'total_models': len(self.models),
                'available_models': len([m for m in self.models.values() if m.is_available]),
                'model_performance': self.performance_metrics,
                'recent_queries': len(self.query_history),
                'routing_efficiency': self._calculate_routing_efficiency()
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting routing summary: {e}")
            return {'error': str(e)}
    
    def _calculate_routing_efficiency(self) -> float:
        """Calculate overall routing efficiency score."""
        try:
            if not self.performance_metrics:
                return 0.0
            
            total_success_rate = sum(metrics['success_rate'] for metrics in self.performance_metrics.values())
            avg_success_rate = total_success_rate / len(self.performance_metrics)
            
            return avg_success_rate
            
        except Exception as e:
            self.logger.error(f"Error calculating routing efficiency: {e}")
            return 0.0 