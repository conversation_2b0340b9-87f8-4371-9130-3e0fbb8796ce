"""
Enhanced Cache Manager for the /ask pipeline.

This module extracts the cache management logic from ai_chat_processor.py
including response caching, LRU eviction, cache statistics, and key generation.
"""

import hashlib
import logging
import time
from typing import Dict, Any, List, Optional

from config import get_config


logger = logging.getLogger(__name__)


class EnhancedCacheManager:
    """Enhanced cache manager with LRU eviction, statistics, and monitoring."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or get_config()
        self._enhancements_enabled = getattr(self.config, 'enable_enhanced_cache_manager', True)
        
        # Cache configuration
        self.max_cache_size = getattr(self.config, 'max_cache_size', 1000)
        self.cache_ttl = getattr(self.config, 'cache_ttl', 300)  # 5 minutes default
        
        # Cache storage
        self.response_cache: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.performance_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_evictions': 0,
            'stage_timings': {},
            'total_response_time': 0.0
        }
        
        # Cache statistics
        self.cache_stats = {
            'total_entries': 0,
            'valid_entries': 0,
            'expired_entries': 0,
            'cache_utilization': 0.0,
            'average_response_size': 0.0
        }
        
        logger.info(f"Enhanced cache manager initialized with max_size={self.max_cache_size}, ttl={self.cache_ttl}s")
    
    def get_cached_response(self, query: str, symbols: List[str]) -> Optional[Dict[str, Any]]:
        """
        Get cached response if available and not expired.
        
        This replaces the monolithic _get_cached_response method from ai_chat_processor.py
        
        Args:
            query: User query string
            symbols: List of stock symbols
            
        Returns:
            Cached response if valid, None otherwise
        """
        if not self._enhancements_enabled:
            return self._legacy_get_cached_response(query, symbols)
        
        try:
            cache_key = self._generate_cache_key(query, symbols)
            
            if cache_key in self.response_cache:
                cached_item = self.response_cache[cache_key]
                
                # Check if cache entry is still valid
                if time.time() - cached_item['timestamp'] < self.cache_ttl:
                    logger.debug(f"✅ Cache hit for query: {query[:50]}...")
                    self.performance_metrics['cache_hits'] += 1
                    return cached_item['response']
                else:
                    # Remove expired cache entry
                    del self.response_cache[cache_key]
                    logger.debug(f"🗑️ Expired cache entry removed for: {query[:50]}...")
                    self.performance_metrics['cache_misses'] += 1
            
            self.performance_metrics['cache_misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"❌ Enhanced cache retrieval failed: {e}")
            # Fall back to legacy method
            return self._legacy_get_cached_response(query, symbols)
    
    def _legacy_get_cached_response(self, query: str, symbols: List[str]) -> Optional[Dict[str, Any]]:
        """Legacy cache retrieval method."""
        cache_key = self._generate_cache_key(query, symbols)
        
        if cache_key in self.response_cache:
            cached_item = self.response_cache[cache_key]
            if time.time() - cached_item['timestamp'] < self.cache_ttl:
                logger.debug(f"✅ Legacy cache hit for query: {query[:50]}...")
                return cached_item['response']
            else:
                # Remove expired cache entry
                del self.response_cache[cache_key]
                logger.debug(f"🗑️ Expired cache entry removed for: {query[:50]}...")
        
        return None
    
    def cache_response(self, query: str, symbols: List[str], response: Dict[str, Any]) -> None:
        """
        Cache the response with timestamp and TTL.
        
        This replaces the monolithic _cache_response method from ai_chat_processor.py
        
        Args:
            query: User query string
            symbols: List of stock symbols
            response: Response data to cache
        """
        if not self._enhancements_enabled:
            return self._legacy_cache_response(query, symbols, response)
        
        try:
            cache_key = self._generate_cache_key(query, symbols)
            
            # Implement LRU cache eviction if max size reached
            if len(self.response_cache) >= self.max_cache_size:
                self._evict_oldest_entry()
            
            # Cache the response with enhanced metadata
            self.response_cache[cache_key] = {
                'response': response,
                'timestamp': time.time(),
                'ttl': self.cache_ttl,
                'query_length': len(query),
                'symbols_count': len(symbols),
                'response_size': len(str(response)),
                'cache_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Update statistics
            self._update_cache_statistics()
            
            logger.debug(f"💾 Enhanced response cached for: {query[:50]}...")
            
        except Exception as e:
            logger.error(f"❌ Enhanced cache storage failed: {e}")
            # Fall back to legacy method
            self._legacy_cache_response(query, symbols, response)
    
    def _legacy_cache_response(self, query: str, symbols: List[str], response: Dict[str, Any]) -> None:
        """Legacy cache storage method."""
        cache_key = self._generate_cache_key(query, symbols)
        
        # Implement LRU cache eviction if max size reached
        if len(self.response_cache) >= self.max_cache_size:
            # Remove oldest entry
            oldest_key = min(self.response_cache.keys(), key=lambda k: self.response_cache[k]['timestamp'])
            del self.response_cache[oldest_key]
            logger.debug("🗑️ LRU cache eviction: removed oldest entry")
            self.performance_metrics['cache_evictions'] += 1
        
        # Cache the response
        self.response_cache[cache_key] = {
            'response': response,
            'timestamp': time.time(),
            'ttl': self.cache_ttl
        }
        
        logger.debug(f"💾 Legacy response cached for: {query[:50]}...")
    
    def _evict_oldest_entry(self) -> None:
        """Evict the oldest cache entry using LRU strategy."""
        if not self.response_cache:
            return
        
        # Find oldest entry by timestamp
        oldest_key = min(self.response_cache.keys(), key=lambda k: self.response_cache[k]['timestamp'])
        del self.response_cache[oldest_key]
        
        self.performance_metrics['cache_evictions'] += 1
        logger.debug("🗑️ Enhanced LRU cache eviction: removed oldest entry")
    
    def generate_cache_key(self, query: str, symbols: List[str]) -> str:
        """
        Generate a unique cache key for the query and symbols.
        
        This replaces the monolithic _generate_cache_key method from ai_chat_processor.py
        
        Args:
            query: User query string
            symbols: List of stock symbols
            
        Returns:
            Unique cache key string
        """
        try:
            # Normalize query (remove extra whitespace, convert to lowercase)
            normalized_query = ' '.join(query.lower().split())
            
            # Sort symbols for consistent key generation
            sorted_symbols = sorted(symbols)
            
            # Create cache key
            cache_key = f"{normalized_query}:{':'.join(sorted_symbols)}"
            
            # Hash the key to keep it manageable
            return hashlib.md5(cache_key.encode()).hexdigest()
            
        except Exception as e:
            logger.error(f"❌ Cache key generation failed: {e}")
            # Fallback to simple key
            return f"{query[:50]}:{':'.join(symbols)}"
    
    def invalidate_cache(self, symbols: List[str] = None) -> None:
        """
        Invalidate cache entries for specific symbols or all entries.
        
        This replaces the monolithic _invalidate_cache method from ai_chat_processor.py
        
        Args:
            symbols: List of symbols to invalidate, or None for all entries
        """
        if not self._enhancements_enabled:
            return self._legacy_invalidate_cache(symbols)
        
        try:
            if symbols is None:
                # Invalidate all cache
                cache_size = len(self.response_cache)
                self.response_cache.clear()
                logger.info(f"🗑️ All response cache invalidated ({cache_size} entries)")
                
                # Reset statistics
                self._reset_cache_statistics()
            else:
                # Invalidate cache entries containing specific symbols
                keys_to_remove = []
                for cache_key, cache_item in self.response_cache.items():
                    cached_symbols = cache_item.get('symbols', [])
                    if any(sym in cached_symbols for sym in symbols):
                        keys_to_remove.append(cache_key)
                
                for key in keys_to_remove:
                    del self.response_cache[key]
                
                if keys_to_remove:
                    logger.info(f"🗑️ Enhanced cache invalidated for symbols: {symbols} ({len(keys_to_remove)} entries)")
                    self._update_cache_statistics()
                    
        except Exception as e:
            logger.error(f"❌ Enhanced cache invalidation failed: {e}")
            # Fall back to legacy method
            self._legacy_invalidate_cache(symbols)
    
    def _legacy_invalidate_cache(self, symbols: List[str] = None) -> None:
        """Legacy cache invalidation method."""
        if symbols is None:
            # Invalidate all cache
            self.response_cache.clear()
            logger.info("🗑️ All response cache invalidated")
        else:
            # Invalidate cache entries containing specific symbols
            keys_to_remove = []
            for cache_key, cache_item in self.response_cache.items():
                cached_symbols = cache_item.get('symbols', [])
                if any(sym in cached_symbols for sym in symbols):
                    keys_to_remove.append(cache_key)
            
            for key in keys_to_remove:
                del self.response_cache[key]
            
            if keys_to_remove:
                logger.info(f"🗑️ Legacy cache invalidated for symbols: {symbols}")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive cache statistics for monitoring.
        
        This replaces the monolithic _get_cache_statistics method from ai_chat_processor.py
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            # Calculate current statistics
            total_entries = len(self.response_cache)
            valid_entries = sum(1 for item in self.response_cache.values() 
                              if time.time() - item['timestamp'] < self.cache_ttl)
            expired_entries = total_entries - valid_entries
            
            # Calculate average response size
            total_size = sum(item.get('response_size', 0) for item in self.response_cache.values())
            avg_response_size = total_size / max(1, total_entries)
            
            # Calculate cache utilization
            cache_utilization = (total_entries / self.max_cache_size) * 100
            
            # Update cache stats
            self.cache_stats.update({
                'total_entries': total_entries,
                'valid_entries': valid_entries,
                'expired_entries': expired_entries,
                'cache_utilization': cache_utilization,
                'average_response_size': avg_response_size
            })
            
            # Combine with performance metrics
            stats = {
                **self.cache_stats,
                'cache_ttl': self.cache_ttl,
                'max_cache_size': self.max_cache_size,
                'performance_metrics': self.performance_metrics.copy()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Cache statistics calculation failed: {e}")
            # Return basic stats
            return {
                'total_entries': len(self.response_cache),
                'cache_ttl': self.cache_ttl,
                'max_cache_size': self.max_cache_size,
                'error': str(e)
            }
    
    def _update_cache_statistics(self) -> None:
        """Update cache statistics after modifications."""
        try:
            total_entries = len(self.response_cache)
            valid_entries = sum(1 for item in self.response_cache.values() 
                              if time.time() - item['timestamp'] < self.cache_ttl)
            
            self.cache_stats.update({
                'total_entries': total_entries,
                'valid_entries': valid_entries,
                'cache_utilization': (total_entries / self.max_cache_size) * 100
            })
        except Exception as e:
            logger.error(f"❌ Cache statistics update failed: {e}")
    
    def _reset_cache_statistics(self) -> None:
        """Reset cache statistics."""
        self.cache_stats = {
            'total_entries': 0,
            'valid_entries': 0,
            'expired_entries': 0,
            'cache_utilization': 0.0,
            'average_response_size': 0.0
        }
    
    def start_stage_timing(self, stage_name: str) -> float:
        """
        Start timing for a pipeline stage.
        
        This replaces the monolithic _start_stage_timing method from ai_chat_processor.py
        
        Args:
            stage_name: Name of the pipeline stage
            
        Returns:
            Start time timestamp
        """
        start_time = time.time()
        if stage_name not in self.performance_metrics['stage_timings']:
            self.performance_metrics['stage_timings'][stage_name] = []
        return start_time
    
    def end_stage_timing(self, stage_name: str, start_time: float) -> float:
        """
        End timing for a pipeline stage and record the duration.
        
        This replaces the monolithic _end_stage_timing method from ai_chat_processor.py
        
        Args:
            stage_name: Name of the pipeline stage
            start_time: Start time timestamp
            
        Returns:
            Duration in seconds
        """
        duration = time.time() - start_time
        self.performance_metrics['stage_timings'][stage_name].append(duration)
        return duration
    
    def record_request_metrics(self, success: bool, response_time: float, error_type: str = None) -> None:
        """
        Record request performance metrics.
        
        This replaces the monolithic _record_request_metrics method from ai_chat_processor.py
        
        Args:
            success: Whether the request was successful
            response_time: Response time in seconds
            error_type: Type of error if any
        """
        self.performance_metrics['total_requests'] += 1
        self.performance_metrics['total_response_time'] += response_time
        
        if success:
            self.performance_metrics['successful_requests'] += 1
        
        # Log performance metrics
        logger.debug(f"📊 Request metrics recorded: success={success}, time={response_time:.2f}s")
    
    def record_cache_metrics(self, cache_hit: bool) -> None:
        """
        Record cache performance metrics.
        
        This replaces the monolithic _record_cache_metrics method from ai_chat_processor.py
        
        Args:
            cache_hit: Whether the request was served from cache
        """
        if cache_hit:
            self.performance_metrics['cache_hits'] += 1
        else:
            self.performance_metrics['cache_misses'] += 1
        
        logger.debug(f"📊 Cache metrics recorded: hit={cache_hit}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        return {
            'cache_manager': self.get_cache_statistics(),
            'performance_metrics': self.performance_metrics.copy(),
            'enhancements_enabled': self._enhancements_enabled,
            'config': {
                'max_cache_size': self.max_cache_size,
                'cache_ttl': self.cache_ttl
            }
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Enhanced cache manager enhancements {'enabled' if enable else 'disabled'}")
    
    def reset_metrics(self) -> None:
        """Reset all performance metrics."""
        self.performance_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_evictions': 0,
            'stage_timings': {},
            'total_response_time': 0.0
        }
        self._reset_cache_statistics()
        logger.info("Enhanced cache manager metrics reset")
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update cache configuration."""
        if 'max_cache_size' in new_config:
            self.max_cache_size = new_config['max_cache_size']
        if 'cache_ttl' in new_config:
            self.cache_ttl = new_config['cache_ttl']
        
        logger.info(f"Enhanced cache manager config updated: {new_config}")


# Global instance for easy access
enhanced_cache_manager = EnhancedCacheManager() 