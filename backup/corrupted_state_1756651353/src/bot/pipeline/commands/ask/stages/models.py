"""
Pydantic models for AI response validation in the /ask pipeline.
Provides strict schema validation for AI-generated responses with proper fallbacks.
"""

import logging
from typing import List, Optional

from pydantic import BaseModel, Field, validator


logger = logging.getLogger(__name__)


class AIAskResult(BaseModel):
    """Strict schema for AI-generated ask responses with validation"""
    
    intent: str = Field(
        default="general_question",
        description="The detected intent of the user query (e.g., 'price_check', 'analysis', 'general_question')"
    )
    
    symbols: List[str] = Field(
        default_factory=list,
        description="List of stock symbols extracted from the query"
    )
    
    tools_required: List[str] = Field(
        default_factory=list,
        description="List of data tools required for accurate analysis (price_fetch, historical_data, technical_indicators, fundamental_data, options_data)"
    )
    
    needs_data: bool = Field(
        default=False,
        description="Whether the response requires additional market data to be complete"
    )
    
    response: str = Field(
        default="",
        description="The AI-generated response text"
    )
    
    @validator('intent')
    def validate_intent(cls, v):
        """Validate intent is one of the expected values"""
        valid_intents = {
            'price_check', 'analysis', 'general_question', 
            'technical_analysis', 'fundamental_analysis', 'market_overview'
        }
        if v not in valid_intents:
            logger.warning(f"Invalid intent '{v}', defaulting to 'general_question'")
            return 'general_question'
        return v
    
    @validator('symbols', each_item=True)
    def validate_symbols(cls, v):
        """Clean and validate individual symbols"""
        if not isinstance(v, str):
            return ""
        # Remove $ prefix and clean symbol
        symbol = v.strip().upper().lstrip('$')
        if not symbol or len(symbol) > 10:  # Reasonable symbol length check
            return ""
        return symbol
    
    @validator('symbols')
    def deduplicate_symbols(cls, v):
        """Remove duplicate symbols"""
        return list(dict.fromkeys(v))  # Preserve order while deduplicating
    
    @classmethod
    def from_ai_response(cls, raw_response: dict) -> 'AIAskResult':
        """Create AIAskResult from raw AI response with fallback handling"""
        try:
            return cls(**raw_response)
        except Exception as e:
            logger.warning(f"Failed to parse AI response: {e}. Using fallback.")
            return cls(
                intent="general_question",
                symbols=[],
                tools_required=[],
                needs_data=False,
                response="I encountered an issue processing your request. Please try again."
            )