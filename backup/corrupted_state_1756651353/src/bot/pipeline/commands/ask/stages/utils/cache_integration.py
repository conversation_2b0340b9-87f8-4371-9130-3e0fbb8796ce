"""
Cache Integration for the /ask pipeline.

This module integrates our enhanced cache manager with the existing AI cache system
to provide better caching capabilities while maintaining backward compatibility.
"""

import logging
from typing import Dict, Any, List, Optional

from ai_cache import cache_ai_response
from ai_cache import cache_ai_response
from ai_cache import get_cached_ai_response
from ai_cache import get_cached_ai_response
from config import get_config
from enhanced_cache_manager import enhanced_cache_manager

                await cache_ai_response(query, response)
            except ImportError:
                pass
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        if not self._enhancements_enabled:
            return {"enhancements_disabled": True}
        
        try:
            return enhanced_cache_manager.get_cache_statistics()
        except Exception as e:
            logger.error(f"❌ Cache statistics retrieval failed: {e}")
            return {"error": str(e)}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if not self._enhancements_enabled:
            return {"enhancements_disabled": True}
        
        try:
            return enhanced_cache_manager.get_performance_stats()
        except Exception as e:
            logger.error(f"❌ Performance stats retrieval failed: {e}")
            return {"error": str(e)}
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Cache integration enhancements {'enabled' if enable else 'disabled'}")
    
    def reset_metrics(self) -> None:
        """Reset all cache metrics."""
        if self._enhancements_enabled:
            enhanced_cache_manager.reset_metrics()
            logger.info("Cache integration metrics reset")


# Global instance for easy access
cache_integration = CacheIntegration() 