#!/usr/bin/env python3
"""
Response Validator - Ensures AI responses contain accurate market data.
This prevents the AI from generating fake prices, RSI values, or other market data.
"""

from dataclasses import dataclass
import logging
import re
from typing import Dict, Any, List, Tuple, Optional


logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of response validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    corrected_response: Optional[str] = None
    validation_score: float = 0.0

class ResponseValidator:
    """Validates AI responses for accuracy against real market data"""
    
    def __init__(self):
        self.price_pattern = r'\$(\d+(?:\.\d{1,2})?)'
        self.percentage_pattern = r'([+-]?\d+(?:\.\d{1,2})?)%'
        self.rsi_pattern = r'RSI:\s*(\d+(?:\.\d{1,2})?)'
        self.volume_pattern = r'volume:\s*([+-]?\d+(?:\.\d{1,2})?)%?\s*(?:above|below)?\s*(?:average|avg)?'
        
    def validate_response(self, response: str, market_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate AI response against real market data
        
        Args:
            response: AI generated response
            market_data: Real market data from providers
            
        Returns:
            ValidationResult with validation status and corrections
        """
        errors = []
        warnings = []
        corrected_response = response
        validation_score = 100.0
        
        # Extract symbols mentioned in response
        symbols = self._extract_symbols(response)
        
        # Validate each symbol's data
        for symbol in symbols:
            if symbol in market_data:
                symbol_data = market_data[symbol]
                symbol_validation = self._validate_symbol_data(response, symbol, symbol_data)
                
                if not symbol_validation.is_valid:
                    errors.extend(symbol_validation.errors)
                    validation_score -= 20.0  # Penalty for each invalid symbol
                    
                    # Correct the response with real data
                    corrected_response = self._correct_response_with_real_data(
                        corrected_response, symbol, symbol_data
                    )
                else:
                    warnings.extend(symbol_validation.warnings)
            else:
                warnings.append(f"Symbol {symbol} mentioned but no market data available")
                validation_score -= 10.0
        
        # Check for common AI hallucination patterns
        hallucination_check = self._check_for_hallucinations(response)
        if not hallucination_check.is_valid:
            errors.extend(hallucination_check.errors)
            validation_score -= 30.0
        
        # Ensure validation score doesn't go below 0
        validation_score = max(0.0, validation_score)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            corrected_response=corrected_response,
            validation_score=validation_score
        )
    
    def _extract_symbols(self, response: str) -> List[str]:
        """Extract stock symbols from response text"""
        # Look for patterns like "NVDA (NVIDIA)", "AAPL", "$TSLA", etc.
        symbol_patterns = [
            r'([A-Z]{1,5})\s*\([^)]+\)',  # NVDA (NVIDIA)
            r'\$([A-Z]{1,5})',            # $AAPL
            r'\*\*([A-Z]{1,5})\*\*',      # **TSLA**
            r'([A-Z]{1,5})\s*[-–]',       # NVDA - Current
        ]
        
        symbols = set()
        for pattern in symbol_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            symbols.update(matches)
        
        return list(symbols)
    
    def _validate_symbol_data(self, response: str, symbol: str, market_data: Dict[str, Any]) -> ValidationResult:
        """Validate data for a specific symbol"""
        errors = []
        warnings = []
        
        # Extract claimed prices from response
        claimed_prices = self._extract_claimed_prices(response, symbol)
        real_price = market_data.get('current_price')
        
        if claimed_prices and real_price:
            for claimed_price in claimed_prices:
                price_diff = abs(claimed_price - real_price)
                price_diff_percent = (price_diff / real_price) * 100
                
                if price_diff_percent > 5.0:  # Allow 5% tolerance
                    errors.append(
                        f"Price mismatch for {symbol}: AI claimed ${claimed_price:.2f}, "
                        f"real price is ${real_price:.2f} (diff: {price_diff_percent:.1f}%)"
                    )
                elif price_diff_percent > 1.0:  # Warning for >1% difference
                    warnings.append(
                        f"Minor price difference for {symbol}: AI claimed ${claimed_price:.2f}, "
                        f"real price is ${real_price:.2f}"
                    )
        
        # Validate percentage changes
        claimed_changes = self._extract_claimed_percentages(response, symbol)
        real_change = market_data.get('change_percent')
        
        if claimed_changes and real_change is not None:
            for claimed_change in claimed_changes:
                change_diff = abs(claimed_change - real_change)
                if change_diff > 2.0:  # Allow 2% tolerance for daily changes
                    errors.append(
                        f"Change percentage mismatch for {symbol}: AI claimed {claimed_change:.1f}%, "
                        f"real change is {real_change:.1f}%"
                    )
        
        # Check for unrealistic RSI values
        claimed_rsi = self._extract_claimed_rsi(response, symbol)
        if claimed_rsi:
            if not (0 <= claimed_rsi <= 100):
                errors.append(f"Invalid RSI value for {symbol}: {claimed_rsi} (must be 0-100)")
            elif claimed_rsi > 80 or claimed_rsi < 20:
                warnings.append(f"Extreme RSI value for {symbol}: {claimed_rsi} (may need verification)")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _extract_claimed_prices(self, response: str, symbol: str) -> List[float]:
        """Extract claimed prices for a specific symbol"""
        # Look for patterns like "Current: $875.50", "Price: $320.11", etc.
        price_patterns = [
            rf'{symbol}.*?Current:\s*\$(\d+(?:\.\d{{1,2}})?)',
            rf'{symbol}.*?Price:\s*\$(\d+(?:\.\d{{1,2}})?)',
            rf'\*\*{symbol}\*\*.*?\$(\d+(?:\.\d{{1,2}})?)',
        ]
        
        prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            for match in matches:
                try:
                    prices.append(float(match))
                except ValueError:
                    continue
        
        return prices
    
    def _extract_claimed_percentages(self, response: str, symbol: str) -> List[float]:
        """Extract claimed percentage changes for a specific symbol"""
        # Look for patterns like "(+2.3%)", "(-1.8%)", etc.
        percentage_patterns = [
            rf'{symbol}.*?\(([+-]?\d+(?:\.\d{{1,2}})?)%\)',
            rf'{symbol}.*?([+-]?\d+(?:\.\d{{1,2}})?)%',
        ]
        
        percentages = []
        for pattern in percentage_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            for match in matches:
                try:
                    percentages.append(float(match))
                except ValueError:
                    continue
        
        return percentages
    
    def _extract_claimed_rsi(self, response: str, symbol: str) -> Optional[float]:
        """Extract claimed RSI value for a specific symbol"""
        # Look for patterns like "RSI: 65", "RSI 58", etc.
        rsi_patterns = [
            rf'{symbol}.*?RSI:\s*(\d+(?:\.\d{{1,2}})?)',
            rf'{symbol}.*?RSI\s+(\d+(?:\.\d{{1,2}})?)',
        ]
        
        for pattern in rsi_patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def _check_for_hallucinations(self, response: str) -> ValidationResult:
        """Check for common AI hallucination patterns"""
        errors = []
        warnings = []
        
        # Check for unrealistic price movements
        if re.search(r'[+-]\d{2,3}%', response):
            warnings.append("Large percentage changes detected - verify against real data")
        
        # Check for generic statements without specific data
        generic_phrases = [
            'strong momentum', 'bullish patterns', 'high volatility',
            'good for options', 'showing strength'
        ]
        
        generic_count = sum(1 for phrase in generic_phrases if phrase.lower() in response.lower())
        if generic_count > 3:
            warnings.append("Response contains many generic statements - may need more specific data")
        
        # Check for missing specific data when symbols are mentioned
        symbols = self._extract_symbols(response)
        for symbol in symbols:
            if not re.search(rf'{symbol}.*?\$', response):
                warnings.append(f"Symbol {symbol} mentioned but no specific price data provided")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _correct_response_with_real_data(self, response: str, symbol: str, market_data: Dict[str, Any]) -> str:
        """Correct response with real market data"""
        corrected = response
        
        # Replace fake prices with real ones
        real_price = market_data.get('current_price')
        if real_price:
            # Find and replace price patterns
            price_patterns = [
                rf'({symbol}.*?Current:\s*)\$(\d+(?:\.\d{{1,2}})?)',
                rf'({symbol}.*?Price:\s*)\$(\d+(?:\.\d{{1,2}})?)',
            ]
            
            for pattern in price_patterns:
                corrected = re.sub(pattern, rf'\1${real_price:.2f}', corrected, flags=re.IGNORECASE)
        
        # Replace fake percentage changes with real ones
        real_change = market_data.get('change_percent')
        if real_change is not None:
            change_patterns = [
                rf'({symbol}.*?\()([+-]?\d+(?:\.\d{{1,2}})?)%\)',
                rf'({symbol}.*?)([+-]?\d+(?:\.\d{{1,2}})?)%',
            ]
            
            for pattern in change_patterns:
                corrected = re.sub(pattern, rf'\1{real_change:+.1f}%', corrected, flags=re.IGNORECASE)
        
        # Add data source disclaimer
        if "Data source:" not in corrected:
            corrected += f"\n\n*Data source: Real-time market data from verified providers*"
        
        return corrected

def validate_ai_response(response: str, market_data: Dict[str, Any]) -> ValidationResult:
    """Convenience function to validate AI response"""
    validator = ResponseValidator()
    return validator.validate_response(response, market_data) 