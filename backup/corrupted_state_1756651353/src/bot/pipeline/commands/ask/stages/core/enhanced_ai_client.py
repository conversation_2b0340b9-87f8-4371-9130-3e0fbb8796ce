"""
Enhanced AI Client for the /ask pipeline.

This module extracts the complex AI model calling logic from ai_chat_processor.py
including retry logic, timeout handling, token tracking, and fallback strategies.
"""

import asyncio
import json
import logging
import os
import re
import time
from typing import Dict, Any, Optional, List

from config import get_config
from openai import OpenAI

            
            text = content.strip()
            
            # Extract JSON from fenced code blocks using robust regex
            json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
            matches = re.findall(json_pattern, text, re.DOTALL)
            
            if matches:
                # Try to parse the first JSON match
                json_str = matches[0]
                parsed = json.loads(json_str)
                logger.info(f"✅ [{correlation_id}] Successfully extracted JSO<PERSON> from response")
                return parsed
            
            # Fallback: try to find JSO<PERSON> anywhere in the text
            json_pattern_fallback = r'\{[^{}]*\}'
            matches_fallback = re.findall(json_pattern_fallback, text, re.DOTALL)
            
            if matches_fallback:
                # Try the longest match
                longest_match = max(matches_fallback, key=len)
                try:
                    parsed = json.loads(longest_match)
                    logger.info(f"✅ [{correlation_id}] Successfully extracted JSON using fallback pattern")
                    return parsed
                except json.JSONDecodeError:
                    pass
            
            # If no JSON found, return empty dict
            logger.warning(f"⚠️ [{correlation_id}] No valid JSON found in AI response")
            return {}
            
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] JSON extraction failed: {e}")
            return {}
    
    def _create_fallback_response(self, fallback_type: str, correlation_id: str, error: str = None) -> Dict[str, Any]:
        """Create appropriate fallback response based on failure type."""
        fallback_responses = {
            "no_ai_config": {
                "intent": "general_question",
                "symbols": [],
                "tools_required": [],
                "needs_data": False,
                "response": "I'm currently unable to process your request due to configuration issues. Please try again later.",
                "fallback_type": fallback_type
            },
            "ai_call_failed": {
                "intent": "general_question", 
                "symbols": [],
                "tools_required": [],
                "needs_data": False,
                "response": f"I'm experiencing technical difficulties: {error}. Please try again later.",
                "fallback_type": fallback_type,
                "error": error
            }
        }
        
        response = fallback_responses.get(fallback_type, fallback_responses["ai_call_failed"])
        response['correlation_id'] = correlation_id
        response['timestamp'] = time.time()
        
        return response
    
    def _update_performance_metrics(self, response_time: float, token_usage: Dict[str, int]) -> None:
        """Update performance tracking metrics."""
        # Update response time metrics
        if self.successful_calls == 1:
            self.avg_response_time = response_time
        else:
            self.avg_response_time = (
                (self.avg_response_time * (self.successful_calls - 1) + response_time) 
                / self.successful_calls
            )
        
        # Update token usage
        if token_usage:
            self.total_tokens_used += token_usage.get('total_tokens', 0)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        total_calls = self.total_calls
        success_rate = (self.successful_calls / total_calls * 100) if total_calls > 0 else 0
        
        return {
            'total_calls': total_calls,
            'successful_calls': self.successful_calls,
            'failed_calls': self.failed_calls,
            'success_rate': success_rate,
            'avg_response_time': self.avg_response_time,
            'total_tokens_used': self.total_tokens_used,
            'model': self.model,
            'max_attempts': self.max_attempts,
            'ai_timeout': self.ai_timeout
        }
    
    def reset_metrics(self) -> None:
        """Reset performance metrics."""
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.total_tokens_used = 0
        self.avg_response_time = 0.0
        logger.info("Enhanced AI client metrics reset")
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update client configuration."""
        if 'model' in new_config:
            self.model = new_config['model']
        if 'temperature' in new_config:
            self.temperature = new_config['temperature']
        if 'max_tokens' in new_config:
            self.max_tokens = new_config['max_tokens']
        if 'timeout' in new_config:
            self.ai_timeout = new_config['timeout']
        
        logger.info(f"Enhanced AI client config updated: {new_config}")


# Global instance for easy access
enhanced_ai_client = EnhancedAIClient() 