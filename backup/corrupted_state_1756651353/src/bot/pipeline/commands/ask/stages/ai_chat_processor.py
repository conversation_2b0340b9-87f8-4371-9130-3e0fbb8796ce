"""
Enhanced AI Chat Processor - Flexible and Conversational
AI-driven tool selection and routing for the /ask command
"""

import asyncio
from datetime import datetime
import json
import logging
import os
import time
from typing import Dict, Any, List, Optional, Union, Tuple

from .models import AIAskResult
from .prompts import SYSTEM_PROMPT, FALLBACK_RESPONSES

logger = logging.getLogger(__name__)


class AIChatProcessor:
    """
    Enhanced AI chat processor with flexible, conversational responses
    and intelligent tool selection
    """
    
    def __init__(self, config_dict: Dict[str, Any]):
        self.config = config_dict
        self.client = None
        self.conversation_history = []
        self.tool_registry = self._initialize_tool_registry()
        
        # Initialize OpenAI client
        self._setup_openai_client()
    
    def _setup_openai_client(self):
        """Setup OpenAI client with proper configuration"""
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.warning("OPENAI_API_KEY not found, using fallback responses")
                return
            
            # Try to import OpenAI
            try:
from openai import OpenAI
                self.client = OpenAI(
                    api_key=api_key,
                    timeout=30.0,
                    max_retries=3
                )
                logger.info("OpenAI client initialized successfully")
            except ImportError:
                logger.warning("OpenAI package not available, using fallback responses")
                self.client = None
                
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            self.client = None
    
    def _initialize_tool_registry(self) -> Dict[str, Dict[str, Any]]:
        """Initialize available tools and their capabilities"""
        return {
            "price_fetch": {
                "name": "Real-time Price Data",
                "description": "Get current stock prices, changes, and basic market data",
                "capabilities": ["current_price", "price_change", "volume", "market_cap"],
                "response_time": "fast",
                "data_freshness": "real-time"
            },
            "technical_indicators": {
                "name": "Technical Analysis",
                "description": "RSI, MACD, moving averages, support/resistance levels",
                "capabilities": ["RSI", "MACD", "MA", "support_resistance", "volume_analysis"],
                "response_time": "medium",
                "data_freshness": "real-time"
            },
            "fundamental_data": {
                "name": "Fundamental Analysis",
                "description": "Financial metrics, earnings, valuation ratios",
                "capabilities": ["P/E_ratio", "earnings", "revenue", "debt", "cash_flow"],
                "response_time": "slow",
                "data_freshness": "daily"
            },
            "market_context": {
                "name": "Market Context",
                "description": "Sector performance, market sentiment, global context",
                "capabilities": ["sector_analysis", "market_sentiment", "correlation", "trends"],
                "response_time": "medium",
                "data_freshness": "real-time"
            },
            "options_data": {
                "name": "Options Analysis",
                "description": "Options chain, implied volatility, Greeks",
                "capabilities": ["IV", "Greeks", "options_chain", "volume_analysis"],
                "response_time": "slow",
                "data_freshness": "real-time"
            }
        }
    
    async def process(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process user query with AI-driven intelligence
        
        Args:
            query: User's question or request
            context: Optional context (user preferences, conversation history, etc.)
            
        Returns:
            Dict containing AI response and any fetched data
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing query: {query[:100]}...")
            
            # Step 1: AI Intent Analysis and Tool Selection
            ai_analysis = await self._analyze_query_with_ai(query, context)
            
            # Step 2: Generate Enhanced Response
            enhanced_response = await self._generate_enhanced_response(
                query, ai_analysis, {}, context
            )
            
            # Step 3: Format Final Response
            final_response = self._format_response(enhanced_response, {})
            
            execution_time = time.time() - start_time
            logger.info(f"Query processed successfully in {execution_time:.2f}s")
            
            return final_response
            
        except Exception as e:
            logger.error(f"Error processing query: {e}", exc_info=True)
            return self._generate_fallback_response(query, str(e))
    
    async def _analyze_query_with_ai(self, query: str, context: Optional[Dict[str, Any]]) -> AIAskResult:
        """Use AI to analyze query intent and select required tools"""
        
        if not self.client:
            # Fallback to rule-based analysis
            return self._fallback_query_analysis(query)
        
        try:
            # Prepare conversation context
            messages = self._prepare_conversation_context(query, context)
            
            # Get AI response
            response = await self._get_ai_response(messages)
            
            # Parse and validate AI response
            ai_result = self._parse_ai_response(response)
            
            logger.info(f"AI Analysis - Intent: {ai_result.intent}, Tools: {ai_result.tools_required}")
            return ai_result
            
        except Exception as e:
            logger.warning(f"AI analysis failed: {e}, using fallback")
            return self._fallback_query_analysis(query)
    
    def _prepare_conversation_context(self, query: str, context: Optional[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Prepare conversation context for AI"""
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": query}
        ]
        
        # Add conversation history if available
        if self.conversation_history:
            messages.extend(self.conversation_history[-4:])  # Last 4 exchanges
        
        # Add context if available
        if context:
            context_str = f"Context: {json.dumps(context, default=str)}"
            messages.append({"role": "system", "content": context_str})
        
        return messages
    
    async def _get_ai_response(self, messages: List[Dict[str, str]]) -> str:
        """Get response from OpenAI API"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",  # Fast and cost-effective
                messages=messages,
                temperature=0.7,  # Balanced creativity and consistency
                max_tokens=1000,
                response_format={"type": "json_object"}
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    def _parse_ai_response(self, response: str) -> AIAskResult:
        """Parse and validate AI response"""
        try:
            # Try to parse JSON response
            if response.strip().startswith('```json'):
                response = response.strip()[7:-3]  # Remove markdown code blocks
            
            data = json.loads(response)
            return AIAskResult.from_ai_response(data)
            
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"Failed to parse AI response: {e}")
            # Extract what we can from the response
            return self._extract_fallback_from_response(response)
    
    def _extract_fallback_from_response(self, response: str) -> AIAskResult:
        """Extract useful information from malformed AI response"""
        # Simple symbol extraction
        symbols = []
        if '$' in response:
            import re
            symbol_matches = re.findall(r'\$([A-Z]{1,5})', response.upper())
            symbols = list(set(symbol_matches))
        
        # Intent classification based on keywords
        intent = "general_question"
        if any(word in response.lower() for word in ['price', 'stock', 'ticker']):
            intent = "price_check"
        elif any(word in response.lower() for word in ['technical', 'indicator', 'chart']):
            intent = "technical_analysis"
        elif any(word in response.lower() for word in ['fundamental', 'earnings', 'financial']):
            intent = "fundamental_analysis"
        
        return AIAskResult(
            intent=intent,
            symbols=symbols,
            tools_required=["price_fetch"] if symbols else [],
            needs_data=bool(symbols),
            response=response[:500] + "..." if len(response) > 500 else response
        )
    
    def _fallback_query_analysis(self, query: str) -> AIAskResult:
        """Rule-based fallback analysis when AI is unavailable"""
        query_lower = query.lower()
        
        # Symbol extraction
        symbols = []
        if '$' in query:
            import re
            symbol_matches = re.findall(r'\$([A-Z]{1,5})', query.upper())
            symbols = list(set(symbol_matches))
        
        # Intent classification
        intent = "general_question"
        tools_required = []
        
        if any(word in query_lower for word in ['price', 'how much', 'current']):
            intent = "price_check"
            tools_required = ["price_fetch"]
        elif any(word in query_lower for word in ['technical', 'indicator', 'rsi', 'macd']):
            intent = "technical_analysis"
            tools_required = ["price_fetch", "technical_indicators"]
        elif any(word in query_lower for word in ['fundamental', 'earnings', 'pe ratio']):
            intent = "fundamental_analysis"
            tools_required = ["price_fetch", "fundamental_data"]
        elif any(word in query_lower for word in ['options', 'option', 'volatility']):
            intent = "options_strategy"
            tools_required = ["price_fetch", "options_data"]
        
        # Generate conversational response
        response = self._generate_conversational_response(query, intent, symbols)
        
        return AIAskResult(
            intent=intent,
            symbols=symbols,
            tools_required=tools_required,
            needs_data=bool(symbols),
            response=response
        )
    
    def _generate_conversational_response(self, query: str, intent: str, symbols: List[str]) -> str:
        """Generate conversational response based on intent and symbols"""
        
        if intent == "general_question":
            if "weather" in query.lower():
                return "I'm a trading bot, so I can't tell you about the weather! 😄 But I'd be happy to help you with any trading questions, market analysis, or stock research you might have. What would you like to know about the markets?"
            elif "how are you" in query.lower() or "how's it going" in query.lower():
                return "I'm doing great! Ready to help you with any trading questions. The markets are always interesting - what's on your mind today? 📈"
            elif "favorite color" in query.lower():
                return "I'm partial to green (like profitable trades! 🟢) and red (for those learning opportunities! 🔴). But I'm really here to help you with trading and market analysis. What would you like to learn about?"
            else:
                return "That's an interesting question! While I'm designed to help with trading and market analysis, I'd be happy to chat about markets, stocks, or trading strategies. What would you like to know?"
        
        elif intent == "price_check":
            if symbols:
                return f"I'd be happy to help you get current price data for {', '.join(['$' + s for s in symbols])}! 📊 Let me fetch the latest market information for you."
            else:
                return "I'd be happy to help you get current stock prices! Just mention the stock symbol with a $ (like $AAPL or $TSLA) and I'll fetch the latest data for you."
        
        elif intent == "technical_analysis":
            if symbols:
                return f"Great question! I can help you analyze {', '.join(['$' + s for s in symbols])} with technical indicators like RSI, MACD, moving averages, and support/resistance levels. Let me gather the data and provide you with a comprehensive technical analysis."
            else:
                return "I'd love to help you with technical analysis! Technical analysis involves studying price charts and indicators to identify patterns and trends. Which stock would you like me to analyze? Just mention the symbol with a $ (like $AAPL)."
        
        elif intent == "fundamental_analysis":
            if symbols:
                return f"Excellent choice! Fundamental analysis looks at a company's financial health, earnings, and valuation. I can analyze {', '.join(['$' + s for s in symbols])} for you, examining metrics like P/E ratios, earnings growth, and financial statements."
            else:
                return "Fundamental analysis is a great way to evaluate stocks! It involves analyzing a company's financial statements, earnings, and overall business health. Which company would you like me to research? Just mention the symbol with a $."
        
        elif intent == "options_strategy":
            return "Options trading can be exciting but it's important to start safely! I can help you understand options strategies, risk management, and how to analyze options data. Which stock are you interested in trading options on? Just mention the symbol with a $."
        
        else:
            return "I'm here to help you with trading and market analysis! Whether you want to check stock prices, learn about technical indicators, understand fundamental analysis, or explore options strategies, I'm your AI trading assistant. What would you like to know?"
    
    async def _generate_enhanced_response(self, query: str, ai_analysis: AIAskResult, 
                                       market_data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate enhanced response using AI and market data"""
        
        if not self.client:
            return self._generate_simple_response(ai_analysis, market_data)
        
        try:
            # Prepare enhanced context for AI
            enhanced_messages = [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": query},
                {"role": "system", "content": f"Available market data: {json.dumps(market_data, default=str)}"}
            ]
            
            # Get enhanced AI response
            enhanced_response = await self._get_ai_response(enhanced_messages)
            
            return {
                "ai_response": enhanced_response,
                "intent": ai_analysis.intent,
                "symbols": ai_analysis.symbols,
                "tools_used": ai_analysis.tools_required,
                "data_available": bool(market_data)
            }
            
        except Exception as e:
            logger.warning(f"Enhanced response generation failed: {e}")
            return self._generate_simple_response(ai_analysis, market_data)
    
    def _generate_simple_response(self, ai_analysis: AIAskResult, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate simple response when AI is unavailable"""
        response = ai_analysis.response
        
        # Enhance with available data
        if market_data:
            data_summary = []
            for symbol, data in market_data.items():
                if data.get('current_price'):
                    data_summary.append(f"${symbol}: ${data['current_price']:.2f}")
            
            if data_summary:
                response += f"\n\n**Current Data:**\n" + "\n".join(data_summary)
        
        return {
            "ai_response": response,
            "intent": ai_analysis.intent,
            "symbols": ai_analysis.symbols,
            "tools_used": ai_analysis.tools_required,
            "data_available": bool(market_data)
        }
    
    def _format_response(self, enhanced_response: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format final response for Discord"""
        return {
            "response": enhanced_response.get("ai_response", "I'm sorry, I couldn't process your request."),
            "intent": enhanced_response.get("intent", "general_question"),
            "symbols": enhanced_response.get("symbols", []),
            "tools_used": enhanced_response.get("tools_used", []),
            "data_available": enhanced_response.get("data_available", False),
            "market_data": market_data,
            "timestamp": datetime.now().isoformat(),
            "response_type": "ai_enhanced"
        }
    
    def _generate_fallback_response(self, query: str, error: str) -> Dict[str, Any]:
        """Generate fallback response when processing fails"""
        return {
            "response": FALLBACK_RESPONSES.get("general_error", "I'm having trouble processing your request right now. Please try again later."),
            "intent": "general_question",
            "symbols": [],
            "tools_used": [],
            "data_available": False,
            "market_data": {},
            "timestamp": datetime.now().isoformat(),
            "response_type": "fallback",
            "error": error
        }
    
    def update_conversation_history(self, user_message: str, bot_response: str):
        """Update conversation history for context"""
        self.conversation_history.extend([
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": bot_response}
        ])
        
        # Keep only last 10 exchanges to manage memory
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]


# Global instance for easy access
processor = AIChatProcessor({})
