"""
Context Processor for the /ask pipeline.

This module integrates our enhanced AIQueryAnalyzer with the main pipeline
to replace the monolithic context building logic.
"""

import logging
from typing import Dict, Any, Optional

from config import get_config
from query_analyzer import AIQueryAnalyzer


logger = logging.getLogger(__name__)


class ContextProcessor:
    """Processes and builds context using enhanced modules."""
    
    def __init__(self):
        self.config = get_config()
        self.query_analyzer = AIQueryAnalyzer()
        self._enhancements_enabled = self.config.enable_query_analyzer_enhancements
    
    async def determine_conversation_type(self, query: str) -> str:
        """
        Determine conversation type using enhanced query analysis.
        
        This replaces the monolithic _determine_conversation_type method from ai_chat_processor.py
        
        Args:
            query: User input query
            
        Returns:
            Conversation type string
        """
        try:
            # Use enhanced analysis if enabled
            if self._enhancements_enabled:
                return await self._enhanced_conversation_analysis(query)
            else:
                return self._legacy_conversation_analysis(query)
                
        except Exception as e:
            logger.error(f"Conversation type determination failed: {e}")
            return "general_query"  # Safe fallback
    
    async def _enhanced_conversation_analysis(self, query: str) -> str:
        """Use enhanced AIQueryAnalyzer for conversation analysis."""
        try:
            # Create a simple context object
            context = {"user_id": "default", "session_id": "default"}
            
            # Use enhanced query analysis
            enhanced_result = await self.query_analyzer.enhanced_query_analysis(
                query, 
                context, 
                enable_enhancements=True
            )
            
            if enhanced_result["success"]:
                analysis = enhanced_result["analysis"]
                intent = analysis.intent.value
                
                # Map intent to conversation type
                conversation_type = self._map_intent_to_conversation_type(intent)
                
                logger.info(f"Enhanced conversation analysis: {intent} -> {conversation_type}")
                return conversation_type
            else:
                logger.warning("Enhanced analysis failed, falling back to legacy")
                return self._legacy_conversation_analysis(query)
                
        except Exception as e:
            logger.error(f"Enhanced conversation analysis failed: {e}")
            return self._legacy_conversation_analysis(query)
    
    def _legacy_conversation_analysis(self, query: str) -> str:
        """Fallback to legacy conversation type determination."""
        query_lower = query.lower()
        
        # Technical analysis queries
        if any(word in query_lower for word in ['rsi', 'macd', 'bollinger', 'support', 'resistance', 'trend', 'indicator']):
            return "stock_analysis"
        
        # Risk assessment queries
        if any(word in query_lower for word in ['risk', 'volatility', 'stop loss', 'atr', 'beta', 'sharpe']):
            return "risk_assessment"
        
        # Fundamental analysis queries
        if any(word in query_lower for word in ['pe ratio', 'earnings', 'revenue', 'growth', 'fundamental', 'valuation']):
            return "stock_analysis"
        
        # Market context queries
        if any(word in query_lower for word in ['sector', 'industry', 'market', 'sentiment', 'volatility']):
            return "market_overview"
        
        # Educational queries
        if any(word in query_lower for word in ['what is', 'how to', 'explain', 'learn', 'understand']):
            return "technical_education"
        
        # General queries
        return "general_query"
    
    def _map_intent_to_conversation_type(self, intent: str) -> str:
        """Map AIQueryAnalyzer intent to conversation type."""
        intent_mapping = {
            "stock_analysis": "stock_analysis",
            "technical_analysis": "stock_analysis",
            "fundamental_analysis": "stock_analysis",
            "risk_assessment": "risk_assessment",
            "market_overview": "market_overview",
            "portfolio_advice": "portfolio_review",
            "general_question": "general_query",
            "comparison": "stock_analysis"
        }
        
        return intent_mapping.get(intent, "general_query")
    
    async def get_structured_prompt(self, query: str, conversation_type: str, market_context: Dict[str, Any]) -> str:
        """
        Generate structured AI prompts using enhanced context.
        
        This replaces the monolithic _get_structured_prompt method from ai_chat_processor.py
        
        Args:
            query: User input query
            conversation_type: Type of conversation
            market_context: Market context data
            
        Returns:
            Structured prompt string
        """
        try:
            # Use enhanced context building if enabled
            if self._enhancements_enabled:
                return await self._enhanced_prompt_generation(query, conversation_type, market_context)
            else:
                return self._legacy_prompt_generation(query, conversation_type, market_context)
                
        except Exception as e:
            logger.error(f"Structured prompt generation failed: {e}")
            return self._legacy_prompt_generation(query, conversation_type, market_context)
    
    async def _enhanced_prompt_generation(self, query: str, conversation_type: str, market_context: Dict[str, Any]) -> str:
        """Use enhanced context building for prompt generation."""
        try:
            # Create context object
            context = {"user_id": "default", "session_id": "default"}
            
            # Get enhanced analysis
            enhanced_result = await self.query_analyzer.enhanced_query_analysis(
                query, 
                context, 
                enable_enhancements=True
            )
            
            if enhanced_result["success"]:
                # Use enhanced context for better prompt generation
                enhanced_context = enhanced_result.get("enhanced_context", {})
                recommendations = enhanced_result.get("recommendations", {})
                
                # Build enhanced prompt
                prompt = self._build_enhanced_prompt(
                    query, conversation_type, market_context, 
                    enhanced_context, recommendations
                )
                
                logger.info("Enhanced prompt generation successful")
                return prompt
            else:
                logger.warning("Enhanced analysis failed, using legacy prompt generation")
                return self._legacy_prompt_generation(query, conversation_type, market_context)
                
        except Exception as e:
            logger.error(f"Enhanced prompt generation failed: {e}")
            return self._legacy_prompt_generation(query, conversation_type, market_context)
    
    def _build_enhanced_prompt(self, query: str, conversation_type: str, market_context: Dict[str, Any], 
                              enhanced_context: Dict[str, Any], recommendations: Dict[str, Any]) -> str:
        """Build enhanced prompt using additional context."""
        base_prompt = f"Analyze the following query about {query} with professional trading insights:"
        
        # Add conversation type specific focus
        if conversation_type == "stock_analysis":
            prompt = f"{base_prompt}\n\nFocus on:\n- Technical indicators (RSI, MACD, Bollinger Bands)\n- Support/resistance levels\n- Trend analysis\n- Volume confirmation\n- Entry/exit points"
        elif conversation_type == "risk_assessment":
            prompt = f"{base_prompt}\n\nFocus on:\n- Volatility analysis\n- Risk metrics (Beta, ATR)\n- Stop-loss recommendations\n- Position sizing\n- Risk-reward ratios"
        elif conversation_type == "market_overview":
            prompt = f"{base_prompt}\n\nFocus on:\n- Sector performance\n- Market sentiment\n- Economic indicators\n- Correlation analysis\n- Market structure"
        elif conversation_type == "technical_education":
            prompt = f"{base_prompt}\n\nFocus on:\n- Clear explanations\n- Examples and analogies\n- Practical applications\n- Risk warnings\n- Learning resources"
        else:
            prompt = f"{base_prompt}\n\nProvide comprehensive analysis covering technical, fundamental, and market context factors."
        
        # Add enhanced context if available
        if enhanced_context:
            prompt += f"\n\nEnhanced Context:\n- Query complexity: {enhanced_context.get('query_metadata', {}).get('estimated_complexity', 'unknown')}"
            prompt += f"\n- Symbol analysis: {enhanced_context.get('symbol_analysis', {}).get('total_symbols', 0)} symbols identified"
            prompt += f"\n- Processing optimization: {enhanced_context.get('processing_optimization', {}).get('estimated_processing_time', 'unknown')}"
        
        # Add recommendations if available
        if recommendations:
            prompt += f"\n\nProcessing Recommendations:\n- Priority: {recommendations.get('priority', 'normal')}"
            if recommendations.get('suggested_approaches'):
                prompt += f"\n- Suggested approaches: {', '.join(recommendations['suggested_approaches'])}"
        
        # Add market context if available
        if market_context:
            prompt += f"\n\nMarket Context:\n- Current market hours: {market_context.get('market_status', 'Unknown')}"
            if 'sector_performance' in market_context:
                prompt += f"\n- Sector performance: {market_context.get('sector_performance', {})}"
            if 'market_sentiment' in market_context:
                prompt += f"\n- Market sentiment: {market_context.get('market_sentiment', {})}"
        
        return prompt
    
    def _legacy_prompt_generation(self, query: str, conversation_type: str, market_context: Dict[str, Any]) -> str:
        """Fallback to legacy prompt generation logic."""
        base_prompt = f"Analyze the following query about {query} with professional trading insights:"
        
        if conversation_type == "stock_analysis":
            prompt = f"{base_prompt}\n\nFocus on:\n- Technical indicators (RSI, MACD, Bollinger Bands)\n- Support/resistance levels\n- Trend analysis\n- Volume confirmation\n- Entry/exit points"
        elif conversation_type == "risk_assessment":
            prompt = f"{base_prompt}\n\nFocus on:\n- Volatility analysis\n- Risk metrics (Beta, ATR)\n- Stop-loss recommendations\n- Position sizing\n- Risk-reward ratios"
        elif conversation_type == "market_overview":
            prompt = f"{base_prompt}\n\nFocus on:\n- Sector performance\n- Market sentiment\n- Economic indicators\n- Correlation analysis\n- Market structure"
        elif conversation_type == "technical_education":
            prompt = f"{base_prompt}\n\nFocus on:\n- Clear explanations\n- Examples and analogies\n- Practical applications\n- Risk warnings\n- Learning resources"
        else:
            prompt = f"{base_prompt}\n\nProvide comprehensive analysis covering technical, fundamental, and market context factors."
        
        # Add market context if available
        if market_context:
            prompt += f"\n\nMarket Context:\n- Current market hours: {market_context.get('market_status', 'Unknown')}"
            if 'sector_performance' in market_context:
                prompt += f"\n- Sector performance: {market_context.get('sector_performance', {})}"
            if 'market_sentiment' in market_context:
                prompt += f"\n- Market sentiment: {market_context.get('market_sentiment', {})}"
        
        return prompt
    
    def get_context_stats(self) -> Dict[str, Any]:
        """Get statistics about context processing performance."""
        return {
            "enhancements_enabled": self._enhancements_enabled,
            "query_analyzer_enhanced": self.query_analyzer.is_enhanced_mode(),
            "config": {
                "enable_query_analyzer_enhancements": self.config.enable_query_analyzer_enhancements
            }
        }
    
    def toggle_enhancements(self, enable: bool) -> None:
        """Toggle enhancement features on/off."""
        self._enhancements_enabled = enable
        logger.info(f"Context processor enhancements {'enabled' if enable else 'disabled'}")


# Global instance for easy access
context_processor = ContextProcessor() 