"""
Conversation Memory Service

Provides conversation memory and context management for:
- Follow-up question handling
- Conversation state persistence
- User preference learning
- Context-aware responses
- Memory optimization
"""

import asyncio
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import hashlib
import json
import logging
import re
import time
from typing import Dict, Any, List, Optional, Tuple

        # Look for $ followed by uppercase letters
        symbol_pattern = r'\$([A-Z]+)'
        matches = re.findall(symbol_pattern, text)
        return list(set(matches))
    
    def _get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences for a session."""
        if user_id in self.user_preferences:
            user_pref = self.user_preferences[user_id]
            return asdict(user_pref)
        return {}
    
    def _determine_memory_priority(self, conversation_type: ConversationType) -> MemoryPriority:
        """Determine memory priority for a conversation type."""
        priority_mapping = {
            ConversationType.STOCK_ANALYSIS: MemoryPriority.MEDIUM,
            ConversationType.TECHNICAL_EDUCATION: MemoryPriority.HIGH,
            ConversationType.RISK_ASSESSMENT: MemoryPriority.HIGH,
            ConversationType.PORTFOLIO_REVIEW: MemoryPriority.CRITICAL,
            ConversationType.MARKET_OVERVIEW: MemoryPriority.LOW,
            ConversationType.GENERAL_QUERY: MemoryPriority.LOW
        }
        return priority_mapping.get(conversation_type, MemoryPriority.MEDIUM)
    
    def _generate_context_summary(self, session: ConversationSession) -> str:
        """Generate a summary of conversation context."""
        try:
            if not session.turns:
                return "New conversation started"
            
            # Get key information from recent turns
            recent_turns = session.turns[-3:]  # Last 3 turns
            symbols = list(set(session.symbols_analyzed))
            tools_used = list(set([tool for turn in recent_turns for tool in turn.tools_used]))
            
            summary_parts = []
            
            if symbols:
                summary_parts.append(f"Analyzing: {', '.join(symbols[:3])}")
            
            if tools_used:
                summary_parts.append(f"Tools: {', '.join(tools_used[:3])}")
            
            summary_parts.append(f"Turns: {len(session.turns)}")
            
            return " | ".join(summary_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating context summary: {e}")
            return "Context summary unavailable"
    
    def _cleanup_old_sessions(self):
        """Clean up old sessions to free memory."""
        try:
            if len(self.active_sessions) <= self.max_sessions:
                return
            
            # Sort sessions by last activity and priority
            sessions_to_cleanup = []
            for session_id, session in self.active_sessions.items():
                age_hours = (datetime.now() - session.last_activity).total_seconds() / 3600
                priority_score = {
                    MemoryPriority.LOW: 1,
                    MemoryPriority.MEDIUM: 2,
                    MemoryPriority.HIGH: 3,
                    MemoryPriority.CRITICAL: 4
                }.get(session.memory_priority, 2)
                
                # Calculate cleanup score (lower = more likely to be cleaned up)
                cleanup_score = age_hours / priority_score
                sessions_to_cleanup.append((session_id, cleanup_score))
            
            # Sort by cleanup score (highest first)
            sessions_to_cleanup.sort(key=lambda x: x[1], reverse=True)
            
            # Remove oldest sessions until we're under the limit
            sessions_to_remove = len(self.active_sessions) - self.max_sessions
            for session_id, _ in sessions_to_cleanup[:sessions_to_remove]:
                del self.active_sessions[session_id]
                self.logger.info(f"Cleaned up old session {session_id}")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old sessions: {e}")
    
    async def _cleanup_old_memories(self):
        """Background task to clean up old memories."""
        try:
            self._cleanup_old_sessions()
            self.last_cleanup = time.time()
            
        except Exception as e:
            self.logger.error(f"Error in memory cleanup: {e}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        try:
            total_turns = sum(len(session.turns) for session in self.active_sessions.values())
            total_symbols = len(set([symbol for session in self.active_sessions.values() 
                                   for symbol in session.symbols_analyzed]))
            
            stats = {
                'active_sessions': len(self.active_sessions),
                'total_turns': total_turns,
                'total_symbols': total_symbols,
                'user_preferences': len(self.user_preferences),
                'memory_usage_mb': self._estimate_memory_usage(),
                'last_cleanup': self.last_cleanup,
                'cleanup_interval': self.memory_cleanup_interval
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {'error': str(e)}
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB."""
        try:
            # Rough estimation
            session_size = len(self.active_sessions) * 1024  # 1KB per session
            turn_size = sum(len(session.turns) for session in self.active_sessions.values()) * 512  # 512B per turn
            preference_size = len(self.user_preferences) * 256  # 256B per preference
            
            total_bytes = session_size + turn_size + preference_size
            return round(total_bytes / (1024 * 1024), 2)  # Convert to MB
            
        except Exception as e:
            self.logger.error(f"Error estimating memory usage: {e}")
            return 0.0 