from datetime import datetime
import logging
import re
from typing import Dict, Any, Optional

from response_validator import ResponseValidator, validate_ai_response

from src.core.formatting.response_templates import ResponseGenerator
    RESPONSE_VALIDATOR_AVAILABLE = True
except ImportError:
    RESPONSE_VALIDATOR_AVAILABLE = False
    logging.warning("ResponseValidator module not available - numeric validation disabled")

logger = logging.getLogger(__name__)

class ResponseAuditor:
    """Audit and validate AI responses before sending to Discord"""
    
    def __init__(self):
        self.template_placeholders = [
            r'\{\{[^}]+\}\}',  # {{placeholder}}
            r'\[PLACEHOLDER\]',
            r'\[TODO\]',
            r'\[FILL IN\]',
            r'<placeholder>',
            r'\$\{[^}]+\}',  # ${placeholder}
        ]
        
        # Configurable symbol skip list for common indices
        self.ignore_symbols = ['SPX', 'VIX', 'TSX', 'DJI', 'NDX', 'RUT']
        
        # Initialize response validator if available
        self.response_validator = ResponseValidator() if RESPONSE_VALIDATOR_AVAILABLE else None
        
        self.quality_checks = [
            self._check_for_placeholders,
            self._check_response_length,
            self._check_symbol_references,
            self._check_basic_factual_accuracy,
            self._check_response_completeness,
        ]
        
        # Add numeric validation check if validator is available
        if RESPONSE_VALIDATOR_AVAILABLE:
            self.quality_checks.append(self._check_numeric_accuracy)

    def audit_response(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive audit of the AI response
        
        Args:
            response: The AI-generated response
            context: Pipeline context with symbols, intent, etc.
            
        Returns:
            Dict with audit results and validation status
        """
        logger.info("🔍 Starting response audit...")
        
        audit_results = {
            'passed': True,
            'issues': [],
            'warnings': [],
            'quality_score': 1.0,
            'validated_response': response,
            'corrections_made': []
        }
        
        # Run all quality checks
        for check in self.quality_checks:
            try:
                check_result = check(response, context)
                if not check_result['passed']:
                    audit_results['issues'].extend(check_result['issues'])
                    # Additive penalty: -0.2 per issue instead of multiplicative
                    audit_results['quality_score'] -= 0.2 * len(check_result['issues'])
                
                if 'warnings' in check_result:
                    audit_results['warnings'].extend(check_result['warnings'])
                    # Additive penalty: -0.05 per warning
                    audit_results['quality_score'] -= 0.05 * len(check_result['warnings'])
                
                if 'corrected_response' in check_result:
                    audit_results['validated_response'] = check_result['corrected_response']
                    audit_results['corrections_made'].append(check_result.get('correction_type', 'unknown'))
                    
            except Exception as e:
                logger.error(f"Error during audit check: {e}")
                audit_results['issues'].append(f"Audit check failed: {str(e)}")
        
        # Ensure quality score stays within bounds [0.0, 1.0]
        audit_results['quality_score'] = max(0.0, min(1.0, audit_results['quality_score']))
        
        # Determine final pass/fail status
        audit_results['passed'] = len(audit_results['issues']) == 0
        
        # Log audit results
        logger.info(f"🔍 Audit completed - Passed: {audit_results['passed']}")
        logger.info(f"📊 Quality Score: {audit_results['quality_score']:.2f}")
        if audit_results['issues']:
            logger.warning(f"🔍 Issues found: {audit_results['issues']}")
        if audit_results['warnings']:
            logger.info(f"🔍 Warnings: {audit_results['warnings']}")
        
        return audit_results
    
    def _check_for_placeholders(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Check for template placeholders that shouldn't be in final response"""
        issues = []
        
        for pattern in self.template_placeholders:
            matches = re.findall(pattern, response, re.IGNORECASE)
            if matches:
                issues.extend([f"Found placeholder: {match}" for match in matches])
        
        return {
            'passed': len(issues) == 0,
            'issues': issues
        }
    
    def _check_response_length(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Check if response length is appropriate"""
        warnings = []
        length = len(response)
        
        if length < 50:
            warnings.append("Response is very short (< 50 characters)")
        elif length > 2000:
            warnings.append("Response is very long (> 2000 characters) - may exceed Discord limits")
        
        return {
            'passed': True,  # Warnings don't fail the audit
            'warnings': warnings
        }
    
    def _check_symbol_references(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Check if symbol references in response match query symbols"""
        symbols = context.get('symbols', [])
        if not symbols:
            return {'passed': True, 'warnings': []}
        
        warnings = []
        response_upper = response.upper()
        
        for symbol in symbols:
            symbol_upper = symbol.upper()
            if symbol_upper not in response_upper and symbol_upper not in self.ignore_symbols:
                warnings.append(f"Symbol {symbol} mentioned in query but not in response")
        
        return {
            'passed': True,  # Warnings don't fail the audit
            'warnings': warnings
        }
    
    def _check_basic_factual_accuracy(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Basic factual checks for trading responses"""
        issues = []
        response_lower = response.lower()
        
        # Check for obvious contradictions or impossible statements
        if "stock price" in response_lower and "negative" in response_lower:
            issues.append("Potential factual issue: negative stock price mentioned")
        
        # Check for impossible P/E ratios
        if re.search(r'p/e\s*[<>]\s*0', response_lower) or re.search(r'price-to-earnings\s*[<>]\s*0', response_lower):
            issues.append("Potential factual issue: P/E ratio cannot be negative")
        
        # Check for impossible market caps
        if re.search(r'market cap\s*[<>]\s*0', response_lower) or re.search(r'market capitalization\s*[<>]\s*0', response_lower):
            issues.append("Potential factual issue: market cap cannot be negative")
        
        # Check for future dates that are too far out
        current_year = datetime.now().year
        # Find standalone 4-digit years 2030-2099 using word boundaries to avoid matching years embedded in other numbers
        future_years = re.findall(r'(?<!\d)(20[3-9]\d)(?!\d)', response)
        # Also check common date patterns like YYYY-MM-DD or YYYY/MM/DD where the year may be followed by a separator
        if not future_years:
            future_years = re.findall(r'(?<!\d)(20[3-9]\d)(?=[-/\/])', response)

        for year in future_years:
            try:
                y = int(year)
            except ValueError:
                continue

            if y > current_year + 5:
                # Log a small context snippet to help diagnose false positives
                idx = response.find(year)
                start = max(0, idx - 20)
                end = min(len(response), idx + len(year) + 20)
                context_snippet = response[start:end]
                logger.warning(f"Found future year {year} in response context: {context_snippet!r}")
                issues.append(f"Potential factual issue: unrealistic future year {year}")
        
        return {
            'passed': len(issues) == 0,
            'issues': issues
        }
    
    def _check_response_completeness(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Check if response is complete and properly formatted"""
        warnings = []
        
        # Check for incomplete sentences using better heuristics
        sentences = re.split(r'[.!?]+', response)
        incomplete_count = 0
        
        for sentence in sentences:
            s = sentence.strip()
            if not s:
                continue
                
            # Check for trailing ellipsis indicating incompleteness
            if s.endswith('...'):
                incomplete_count += 1
                continue
                
            # Check for abrupt cutoff (ends with comma, dash, or other continuation markers)
            if re.search(r'[,-;:]$', s):
                incomplete_count += 1
                continue
                
            # Check for very short sentences that might be fragments
            if len(s) < 8 and len(s.split()) < 3:
                incomplete_count += 1
                continue
        
        if incomplete_count > 0:
            warnings.append(f"Found {incomplete_count} potentially incomplete sentences or fragments")
        
        return {
            'passed': True,
            'warnings': warnings
        }
    
    def _check_numeric_accuracy(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform numeric validation using ResponseValidator when market data is available"""
        if not RESPONSE_VALIDATOR_AVAILABLE or not self.response_validator:
            return {'passed': True, 'warnings': ['Numeric validation unavailable']}
        
        # Check if we have market data available for validation
        market_data = context.get('market_data', {})
        if not market_data:
            return {'passed': True, 'warnings': ['No market data available for numeric validation']}
        
        logger.info("🔢 Performing numeric validation against real market data...")
        
        # Use the ResponseValidator to check numeric accuracy
        validation_result = self.response_validator.validate_response(response, market_data)
        
        result = {
            'passed': validation_result.is_valid,
            'issues': validation_result.errors,
            'warnings': validation_result.warnings
        }
        
        # Apply corrections if validation found issues but provided corrected response
        if not validation_result.is_valid and validation_result.corrected_response:
            result['corrected_response'] = validation_result.corrected_response
            result['correction_type'] = 'numeric_validation'
        
        return result

# Global auditor instance
response_auditor = ResponseAuditor()
