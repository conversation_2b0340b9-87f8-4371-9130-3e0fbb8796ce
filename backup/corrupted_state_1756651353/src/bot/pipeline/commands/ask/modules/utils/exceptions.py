"""
Exception Classes for Bot Utilities
==================================

Custom exception classes for the trading bot pipeline utilities.
"""

from typing import Optional, Dict, Any, List



class BotUtilsError(Exception):
    """Base exception for bot utility errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self):
        return f"{self.__class__.__name__}: {self.message}"


class ValidationError(BotUtilsError):
    """Raised when validation fails."""
    pass


class InvalidSymbolFormatError(ValidationError):
    """Raised when symbol format is invalid."""
    pass


class CacheError(BotUtilsError):
    """Raised when cache operations fail."""
    pass


class RetryExhaustedError(BotUtilsError):
    """Raised when retry attempts are exhausted."""
    pass


class RateLimitError(BotUtilsError):
    """Raised when rate limits are exceeded."""
    pass


class ConfigurationError(BotUtilsError):
    """Raised when configuration is invalid or missing."""
    pass


class DataSourceError(BotUtilsError):
    """Raised when data source operations fail."""
    pass


class APIError(BotUtilsError):
    """Raised when API operations fail."""
    pass


class InsufficientDataError(BotUtilsError):
    """Raised when insufficient data is available."""
    pass


class ProcessingError(BotUtilsError):
    """Raised when data processing fails."""
    pass


class AuthenticationError(BotUtilsError):
    """Raised when authentication fails."""
    pass


class ServiceUnavailableError(BotUtilsError):
    """Raised when a service is unavailable."""
    pass


class TimeoutError(BotUtilsError):
    """Raised when operations timeout."""
    pass


class NetworkError(BotUtilsError):
    """Raised when network operations fail."""
    pass


class DatabaseError(BotUtilsError):
    """Raised when database operations fail."""
    pass


class SerializationError(BotUtilsError):
    """Raised when serialization/deserialization fails."""
    pass


class InvalidStateError(BotUtilsError):
    """Raised when an operation is performed in an invalid state."""
    pass


class DependencyError(BotUtilsError):
    """Raised when required dependencies are missing or invalid."""
    pass


class ResourceError(BotUtilsError):
    """Raised when resource operations fail."""
    pass


class SecurityError(BotUtilsError):
    """Raised when security checks fail."""
    pass


class BusinessLogicError(BotUtilsError):
    """Raised when business logic validation fails."""
    pass


class MarketDataError(BotUtilsError):
    """Raised when market data operations fail."""
    pass


class IndicatorCalculationError(BotUtilsError):
    """Raised when technical indicator calculations fail."""
    pass


class PipelineError(BotUtilsError):
    """Raised when pipeline operations fail."""
    pass


class StageError(BotUtilsError):
    """Raised when pipeline stage operations fail."""
    pass


class ContextualError(BotUtilsError):
    """Raised when context-related operations fail."""
    pass


class ErrorCollector:
    """Collects and manages multiple errors."""
    
    def __init__(self):
        self.errors: List[BotUtilsError] = []
    
    def add_error(self, error: BotUtilsError):
        """Add an error to the collection."""
        self.errors.append(error)
    
    def has_errors(self) -> bool:
        """Check if any errors have been collected."""
        return len(self.errors) > 0
    
    def get_errors(self) -> List[BotUtilsError]:
        """Get all collected errors."""
        return self.errors.copy()
    
    def clear_errors(self):
        """Clear all collected errors."""
        self.errors.clear()
    
    def raise_if_errors(self):
        """Raise a combined error if any errors exist."""
        if self.has_errors():
            messages = [str(error) for error in self.errors]
            raise BotUtilsError(f"Multiple errors occurred: {'; '.join(messages)}")


class ErrorHandler:
    """Handles errors with fallback strategies."""
    
    def __init__(self, fallback_strategy: str = "log"):
        self.fallback_strategy = fallback_strategy
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> Optional[Any]:
        """
        Handle an error according to the configured strategy.
        
        Args:
            error: The error to handle
            context: Additional context for error handling
            
        Returns:
            Fallback value if strategy allows, None otherwise
        """
        if self.fallback_strategy == "log":
            # Just log the error
            print(f"Error handled: {error}")
            return None
        elif self.fallback_strategy == "raise":
            # Re-raise the error
            raise error
        elif self.fallback_strategy == "return_none":
            # Return None on error
            return None
        else:
            # Default to logging
            print(f"Error handled: {error}")
            return None


def get_user_friendly_error_message(error: Exception) -> str:
    """
    Convert technical error messages to user-friendly versions.
    
    Args:
        error: The exception to convert
        
    Returns:
        User-friendly error message
    """
    if isinstance(error, ValidationError):
        return f"Validation failed: {error.message}"
    elif isinstance(error, CacheError):
        return "Cache operation failed. Please try again."
    elif isinstance(error, RateLimitError):
        return "Rate limit exceeded. Please wait a moment and try again."
    elif isinstance(error, TimeoutError):
        return "Operation timed out. Please try again."
    elif isinstance(error, NetworkError):
        return "Network error occurred. Please check your connection and try again."
    elif isinstance(error, AuthenticationError):
        return "Authentication failed. Please check your credentials."
    elif isinstance(error, ServiceUnavailableError):
        return "Service temporarily unavailable. Please try again later."
    else:
        return "An unexpected error occurred. Please try again." 