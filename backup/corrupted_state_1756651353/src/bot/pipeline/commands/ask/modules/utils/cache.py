
"""
Caching Utilities
================

Memory, Redis, and hybrid caching with TTL support and automatic cleanup.

This module provides comprehensive caching solutions with multiple backends,
TTL support, automatic cleanup, and cache warming capabilities.
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import hashlib
import json
import logging
import os
from pathlib import Path
import pickle
import threading
import time
from typing import Any, Dict, List, Optional, Callable, Union, Tuple

from exceptions import CacheError, ValidationError
import redis

        self.host = host or os.getenv('REDIS_HOST', 'localhost')
        self.port = port or int(os.getenv('REDIS_PORT', '6379'))
        self.db = db
        self.password = password
        self.key_prefix = key_prefix
        self._client = None
        self._connect()
    
    def _connect(self):
        """Connect to Redis."""
        try:
            self._client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                decode_responses=False
            )
            self._client.ping()
        except redis.ConnectionError as e:
            raise CacheError(f"Failed to connect to Redis: {e}")
    
    def _get_key(self, key: str) -> str:
        """Get prefixed key."""
        return f"{self.key_prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            value = self._client.get(self._get_key(key))
            if value is None:
                return None
            return pickle.loads(value)
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            serialized = pickle.dumps(value)
            redis_key = self._get_key(key)
            
            if ttl is not None:
                return bool(self._client.setex(redis_key, ttl, serialized))
            else:
                return bool(self._client.set(redis_key, serialized))
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            return bool(self._client.delete(self._get_key(key)))
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            return bool(self._client.exists(self._get_key(key)))
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            keys = self._client.keys(f"{self.key_prefix}*")
            if keys:
                return bool(self._client.delete(*keys))
            return True
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
            return False
    
    def size(self) -> int:
        """Get cache size."""
        try:
            keys = self._client.keys(f"{self.key_prefix}*")
            return len(keys) if keys else 0
        except Exception as e:
            logger.error(f"Redis size error: {e}")
            return 0


class CacheManager:
    """Central cache manager supporting multiple backends."""
    
    def __init__(self, backend: Optional[CacheBackend] = None):
        self.backend = backend or MemoryCacheBackend()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            try:
                value = self.backend.get(key)
                if value is not None:
                    self._stats['hits'] += 1
                else:
                    self._stats['misses'] += 1
                return value
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Cache get error: {e}")
                return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        with self._lock:
            try:
                success = self.backend.set(key, value, ttl)
                if success:
                    self._stats['sets'] += 1
                return success
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Cache set error: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        with self._lock:
            try:
                success = self.backend.delete(key)
                if success:
                    self._stats['deletes'] += 1
                return success
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Cache delete error: {e}")
                return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        with self._lock:
            try:
                return self.backend.exists(key)
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Cache exists error: {e}")
                return False
    
    def clear(self) -> bool:
        """Clear all cache entries."""
        with self._lock:
            try:
                return self.backend.clear()
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Cache clear error: {e}")
                return False
    
    def size(self) -> int:
        """Get cache size."""
        with self._lock:
            try:
                return self.backend.size()
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Cache size error: {e}")
                return 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        with self._lock:
            return self._stats.copy()
    
    def reset_stats(self):
        """Reset cache statistics."""
        with self._lock:
            self._stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0,
                'errors': 0
            }
   