"""
Retry Utility
=============

Retry utilities with exponential backoff and circuit breaker patterns.
"""

import asyncio
import functools
import logging
import random
import random
import random
import time
from typing import Callable, Any, Optional, Tuple, Type

from config import get_config
from exceptions import RetryExhaustedError

            delay *= (0.5 + random.random() * 0.5)
        
        logger.warning(
            f"Operation failed on attempt {self.attempt}. "
            f"Retrying in {delay:.2f} seconds. Error: {str(exc_val)}"
        )
        
        time.sleep(delay)
        return True  # Retry


# Example usage functions
def with_retry(func: Callable, *args, max_retries: int = 3, **kwargs) -> Any:
    """
    Execute function with retry logic.
    
    Args:
        func: Function to execute
        *args: Function arguments
        max_retries: Maximum retry attempts
        **kwargs: Function keyword arguments
        
    Returns:
        Function result
    """
    return retry_with_backoff(max_retries=max_retries)(func)(*args, **kwargs)