"""
Data Models for AI Chat Processor
================================

Comprehensive Pydantic models for data validation and serialization
across the AI chat processor system.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Union

import pandas as pd
from pydantic import BaseModel, Field, validator

from src.shared.models.base_config import get_base_config
from src.shared.models.base_config import get_base_config
        json_encoders = get_base_config()["json_encoders"]


class CacheKey(BaseModel):
    """Cache key structure for data caching."""
    symbol: str
    timeframe: str
    indicators: List[str] = Field(default_factory=list)
    include_chart: bool = False
    
    def to_string(self) -> str:
        """Convert to cache key string."""
        indicators_str = ",".join(sorted(self.indicators)) if self.indicators else "all"
        return f"{self.symbol}:{self.timeframe}:{indicators_str}:{self.include_chart}"
    
    class Config:
        frozen = True


class ServiceConfig(BaseModel):
    """Configuration for services."""
    timeout_seconds: int = Field(default=30, ge=1, le=300)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1, le=10.0)
    cache_ttl: int = Field(default=300, ge=60, le=3600)
    enable_caching: bool = Field(default=True)
    log_level: str = Field(default="INFO")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}")
        return v.upper()