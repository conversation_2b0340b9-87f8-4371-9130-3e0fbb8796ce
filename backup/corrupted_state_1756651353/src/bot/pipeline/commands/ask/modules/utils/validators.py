"""
Data Validators
===============

Validation utilities for data integrity and input sanitization.
"""

import re
from typing import List, Optional

from exceptions import ValidationError, InvalidSymbolFormatError



def validate_symbol(symbol: str) -> str:
    """
    Validate and normalize a stock symbol.
    
    Args:
        symbol: Stock symbol to validate
        
    Returns:
        Normalized symbol in uppercase
        
    Raises:
        InvalidSymbolFormatError: If symbol format is invalid
    """
    if not symbol or not isinstance(symbol, str):
        raise InvalidSymbolFormatError(str(symbol))
    
    # Clean and normalize
    symbol = symbol.strip().upper()
    
    # Remove common suffixes and prefixes
    symbol = re.sub(r'\.[A-Z]+$', '', symbol)  # Remove exchange suffix
    symbol = re.sub(r'^[A-Z]+:', '', symbol)   # Remove prefix
    
    # Validate format: only letters and numbers, 1-5 characters
    if not re.match(r'^[A-Z0-9]{1,5}$', symbol):
        raise InvalidSymbolFormatError(symbol)
    
    # Check for common invalid patterns
    invalid_patterns = [
        r'^TEST$',
        r'^DEMO$',
        r'^FAKE$',
        r'^\d+$',  # Only numbers
    ]
    
    for pattern in invalid_patterns:
        if re.match(pattern, symbol, re.IGNORECASE):
            raise InvalidSymbolFormatError(symbol)
    
    return symbol


def validate_symbols(symbols: List[str]) -> List[str]:
    """
    Validate and normalize a list of symbols.
    
    Args:
        symbols: List of symbols to validate
        
    Returns:
        List of normalized symbols
        
    Raises:
        InvalidSymbolFormatError: If any symbol is invalid
    """
    if not isinstance(symbols, list):
        raise ValidationError("Symbols must be a list", field="symbols", value=symbols)
    
    validated_symbols = []
    seen = set()
    
    for symbol in symbols:
        validated = validate_symbol(symbol)
        if validated not in seen:
            validated_symbols.append(validated)
            seen.add(validated)
    
    return validated_symbols


def validate_timeframe(timeframe: str) -> str:
    """
    Validate a timeframe string.
    
    Args:
        timeframe: Timeframe string to validate
        
    Returns:
        Normalized timeframe
        
    Raises:
        ValidationError: If timeframe is invalid
    """
    if not timeframe or not isinstance(timeframe, str):
        raise ValidationError("Timeframe must be a non-empty string", field="timeframe", value=timeframe)
    
    timeframe = timeframe.lower().strip()
    
    valid_timeframes = {
        '1d': '1d',
        '1day': '1d',
        'daily': '1d',
        '1wk': '1wk',
        '1week': '1wk',
        'weekly': '1wk',
        '1mo': '1mo',
        '1month': '1mo',
        'monthly': '1mo',
        '5m': '5m',
        '5min': '5m',
        '5minute': '5m',
        '15m': '15m',
        '15min': '15m',
        '15minute': '15m',
        '1h': '1h',
        '1hr': '1h',
        '1hour': '1h',
        '4h': '4h',
        '4hr': '4h',
        '4hour': '4h'
    }
    
    if timeframe not in valid_timeframes:
        raise ValidationError(
            f"Invalid timeframe: {timeframe}. Valid options: {list(valid_timeframes.keys())}",
            field="timeframe",
            value=timeframe
        )
    
    return valid_timeframes[timeframe]


def validate_query(query: str) -> str:
    """
    Validate and sanitize an AI query.
    
    Args:
        query: Query string to validate
        
    Returns:
        Sanitized query string
        
    Raises:
        ValidationError: If query is invalid
    """
    if not query or not isinstance(query, str):
        raise ValidationError("Query must be a non-empty string", field="query", value=query)
    
    query = query.strip()
    
    if len(query) < 1:
        raise ValidationError("Query must be at least 1 character", field="query", value=query)
    
    if len(query) > 1000:
        raise ValidationError("Query must be 1000 characters or less", field="query", value=query)
    
    # Basic sanitization
    query = re.sub(r'\s+', ' ', query)  # Normalize whitespace
    query = re.sub(r'[^\w\s\-.,!?\'"]', '', query)  # Remove special chars
    
    # Check for SQL injection attempts
    sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'UNION', 'SCRIPT']
    query_upper = query.upper()
    for keyword in sql_keywords:
        if keyword in query_upper:
            raise ValidationError(
                f"Query contains potentially harmful content: {keyword}",
                field="query",
                value=query
            )
    
    return query.strip()


def extract_symbols_from_text(text: str) -> List[str]:
    """
    Extract potential stock symbols from text.
    
    Args:
        text: Text to extract symbols from
        
    Returns:
        List of potential symbols
    """
    if not text:
        return []
    
    # Common patterns for stock symbols
    patterns = [
        r'\$[A-Z]{1,5}\b',  # $AAPL format
        r'\b[A-Z]{1,5}\b',   # AAPL format
        r'\b[A-Z]{1,5}\.[A-Z]{1,4}\b',  # AAPL.NASDAQ format
    ]
    
    symbols = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            # Remove $ prefix if present
            symbol = match.lstrip('$')
            try:
                validated = validate_symbol(symbol)
                if validated not in symbols:
                    symbols.append(validated)
            except InvalidSymbolFormatError:
                continue
    
    return symbols


def is_valid_price(price: float) -> bool:
    """
    Check if a price value is valid.
    
    Args:
        price: Price to validate
        
    Returns:
        True if valid, False otherwise
    """
    return isinstance(price, (int, float)) and price > 0 and price < 100000


def is_valid_volume(volume: int) -> bool:
    """
    Check if a volume value is valid.
    
    Args:
        volume: Volume to validate
        
    Returns:
        True if valid, False otherwise
    """
    return isinstance(volume, int) and volume >= 0


def clean_numeric_string(value: str) -> Optional[float]:
    """
    Clean and convert numeric string to float.
    
    Args:
        value: String containing numeric value
        
    Returns:
        Float value or None if invalid
    """
    if not value or not isinstance(value, str):
        return None
    
    value = value.strip()
    if not value:
        return None
    
    # Remove common formatting
    value = value.replace(',', '').replace('$', '').replace('%', '')
    
    try:
        return float(value)
    except (ValueError, TypeError):
        return None