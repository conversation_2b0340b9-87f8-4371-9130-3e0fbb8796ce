
"""
Validation Utilities
====================

Input validation and data sanitization utilities.
"""

from datetime import datetime, timezone
import json
import re
from typing import Optional, Tuple, List

from exceptions import ValidationError

    
    if not isinstance(data, str):
        raise ValidationError("JSON data must be a string")
    
    try:
        return json.loads(data)
    except json.JSONDecodeError as e:
        raise ValidationError(f"Invalid JSON: {e}")


def validate_choice(value: str, choices: List[str], case_sensitive: bool = True) -> str:
    """
    Validate choice from predefined list.
    
    Args:
        value: Value to validate
        choices: List of valid choices
        case_sensitive: Whether comparison is case-sensitive
        
    Returns:
        Validated choice
        
    Raises:
        ValidationError: If choice is invalid
    """
    if not choices:
        raise ValidationError("Choices list cannot be empty")
    
    if not isinstance(value, str):
        raise ValidationError("Choice must be a string")
    
