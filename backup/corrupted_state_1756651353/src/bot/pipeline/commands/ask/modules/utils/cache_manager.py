"""
Cache Manager
=============

Thread-safe caching implementation with TTL support and multiple backends.
"""

from functools import wraps
import json
from pathlib import Path
import pickle
import threading
import time
from typing import Any, Dict, Optional, Callable

from exceptions import CacheError



class CacheManager:
    """Thread-safe cache manager with TTL support."""
    
    def __init__(self, backend: str = "memory", max_size: int = 1000, ttl: int = 300):
        """
        Initialize cache manager.
        
        Args:
            backend: Cache backend type ("memory", "file", "redis")
            max_size: Maximum number of items in cache
            ttl: Default TTL in seconds
        """
        self.backend = backend
        self.max_size = max_size
        self.default_ttl = ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        if backend == "file":
            self._cache_dir = Path("cache")
            self._cache_dir.mkdir(exist_ok=True)
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        with self._lock:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            
            # Check TTL
            if entry['expires'] and time.time() > entry['expires']:
                del self._cache[key]
                return None
            
            return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: TTL in seconds (uses default if None)
        """
        ttl = ttl or self.default_ttl
        
        with self._lock:
            # Implement LRU eviction if at max size
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            self._cache[key] = {
                'value': value,
                'expires': time.time() + ttl if ttl > 0 else None,
                'access_time': time.time()
            }
    
    def delete(self, key: str) -> bool:
        """
        Delete key from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was deleted, False if not found
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        with self._lock:
            return len(self._cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            now = time.time()
            total = len(self._cache)
            expired = sum(1 for entry in self._cache.values() 
                         if entry['expires'] and now > entry['expires'])
            
            return {
                'total_items': total,
                'expired_items': expired,
                'active_items': total - expired,
                'max_size': self.max_size,
                'backend': self.backend
            }
    
    def _evict_lru(self) -> None:
        """Remove least recently used item."""
        if not self._cache:
            return
            
        lru_key = min(self._cache.keys(), 
                     key=lambda k: self._cache[k]['access_time'])
        del self._cache[lru_key]
    
    def cache_key(self, prefix: str, *args) -> str:
        """
        Generate a cache key from arguments.
        
        Args:
            prefix: Key prefix
            *args: Arguments to include in key
            
        Returns:
            Generated cache key
        """
        key_parts = [str(arg) for arg in args]
        return f"{prefix}:{':'.join(key_parts)}"
    
    def cached(self, ttl: Optional[int] = None, key_func: Optional[Callable] = None):
        """
        Decorator for caching function results.
        
        Args:
            ttl: TTL for cached results
            key_func: Function to generate cache key from function args
            
        Returns:
            Decorated function
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    # Default key generation
                    key_parts = [func.__name__]
                    key_parts.extend(str(arg) for arg in args)
                    key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
                    cache_key = self.cache_key("func", *key_parts)
                
                # Try to get from cache
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator


class FileCacheManager(CacheManager):
    """File-based cache manager for persistence."""
    
    def __init__(self, cache_dir: str = "cache", max_size: int = 1000, ttl: int = 300):
        super().__init__(backend="file", max_size=max_size, ttl=ttl)
        self._cache_dir = Path(cache_dir)
        self._cache_dir.mkdir(exist_ok=True)
    
    def _get_filename(self, key: str) -> Path:
        """Get filename for cache key."""
        # Sanitize key for filename
        safe_key = "".join(c for c in key if c.isalnum() or c in "._-")
        return self._cache_dir / f"{safe_key}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from file cache."""
        filename = self._get_filename(key)
        
        if not filename.exists():
            return None
        
        try:
            with open(filename, 'rb') as f:
                entry = pickle.load(f)
                
            # Check TTL
            if entry['expires'] and time.time() > entry['expires']:
                filename.unlink(missing_ok=True)
                return None
            
            return entry['value']
            
        except (IOError, pickle.PickleError) as e:
            raise CacheError(f"Failed to read cache file: {e}")
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in file cache."""
        ttl = ttl or self.default_ttl
        
        filename = self._get_filename(key)
        
        try:
            entry = {
                'value': value,
                'expires': time.time() + ttl if ttl > 0 else None,
                'access_time': time.time()
            }
            
            with open(filename, 'wb') as f:
                pickle.dump(entry, f)
                
        except (IOError, pickle.PickleError) as e:
            raise CacheError(f"Failed to write cache file: {e}")
    
    def delete(self, key: str) -> bool:
        """Delete key from file cache."""
        filename = self._get_filename(key)
        
        if filename.exists():
            try:
                filename.unlink()
                return True
            except IOError as e:
                raise CacheError(f"Failed to delete cache file: {e}")
        
        return False
    
    def clear(self) -> None:
        """Clear all cache files."""
        try:
            for filename in self._cache_dir.glob("*.cache"):
                filename.unlink()
        except IOError as e:
            raise CacheError(f"Failed to clear cache directory: {e}")


# Global cache instance
_global_cache: Optional[CacheManager] = None


def get_cache() -> CacheManager:
    """Get global cache instance."""
    global _global_cache
    if _global_cache is None:
        _global_cache = CacheManager()
    return _global_cache


def configure_cache(backend: str = "memory", **kwargs) -> CacheManager:
    """Configure global cache."""
    global _global_cache
    
    if backend == "file":
        _global_cache = FileCacheManager(**kwargs)
    else:
        _global_cache = CacheManager(backend=backend, **kwargs)
    
    return _global_cache