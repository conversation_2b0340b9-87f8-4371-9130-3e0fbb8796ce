# Ask Command Pipeline Configuration

pipeline:
  name: "ask"
  version: "1.0.0"
  description: "Multi-stage pipeline for AI-powered trading insights"
  max_execution_time: 20.0  # Increased from 15.0
  parallel_execution: false  # Sequential execution for now

stages:
  query_classifier:
    timeout: 2.0  # Increased from 1.0
    retry_attempts: 2  # Increased from 1
    critical: false
    quality_threshold: 0.7  # Restored to professional standard
    
  data_collection:
    timeout: 15.0  # Increased from 10.0
    retry_attempts: 3  # Increased from 2
    critical: true
    fallback_sources: ["yahoo_finance"]
    quality_threshold: 0.7  # Restored to professional standard
    
  data_validation:
    timeout: 3.0  # Increased from 2.0
    retry_attempts: 2  # Increased from 1
    critical: true
    quality_threshold: 0.7  # Restored to professional standard
    
  ai_processing:
    timeout: 10.0  # Increased from 2.0
    retry_attempts: 2
    critical: false
    quality_threshold: 0.7  # Restored to professional standard
    
  response_formatting:
    timeout: 5.0
    retry_attempts: 1
    critical: false
    quality_threshold: 0.5  # Restored to professional standard

data_sources:
  priority_order:
    - "polygon"
    - "finnhub"
    - "alpha_vantage"
    - "yahoo_finance"
  
  rate_limits:
    polygon: 10  # Increased from 5
    finnhub: 50  # Increased from 30
    alpha_vantage: 10  # Increased from 5
    yahoo_finance: 20  # Increased from 10
  
  timeouts:
    polygon: 10.0  # Increased from 5.0
    finnhub: 5.0   # Increased from 3.0
    alpha_vantage: 10.0  # Increased from 5.0
    yahoo_finance: 5.0   # Increased from 2.0

quality_thresholds:
  excellent: 0.9
  good: 0.7  # Restored to professional standard
  fair: 0.5  # Restored to professional standard
  poor: 0.3  # Restored to professional standard
  unreliable: 0.0

ai_models:
  primary: "anthropic/claude-3.5-sonnet"
  fallback: "deepseek/deepseek-v3.1"
  max_tokens: 1500  # Increased from 1000
  temperature: 0.7

monitoring:
  enable_audit_trail: true
  log_performance_metrics: true
  track_data_quality: true
  alert_on_failures: true 