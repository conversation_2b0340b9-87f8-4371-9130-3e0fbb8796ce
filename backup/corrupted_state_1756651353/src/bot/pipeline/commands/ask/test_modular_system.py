"""
Test Modular Pipeline System

Simple test to verify the n8n-style modular pipeline works.
"""

import asyncio
import logging
from typing import Dict, Any

from stages.ask_sections import AskPipelineSections
from stages.pipeline_sections import PipelineSectionManager

        
        logger.info("✅ Successfully imported modular components")
        
        # Test pipeline section creation
        section_manager = PipelineSectionManager()
        ask_sections = AskPipelineSections()
        
        # Add sections
        section_manager.add_section(ask_sections.create_query_analysis_section())
        section_manager.add_section(ask_sections.create_data_collection_section())
        section_manager.add_section(ask_sections.create_response_generation_section())
        section_manager.add_section(ask_sections.create_response_formatting_section())
        
        logger.info("✅ Successfully created pipeline sections")
        
        # Set execution order
        section_manager.set_execution_order([
            "query_analysis",
            "data_collection", 
            "response_generation",
            "response_formatting"
        ])
        
        logger.info("✅ Successfully set execution order")
        
        # Test section manager
        summary = section_manager.get_pipeline_summary()
        logger.info(f"✅ Pipeline summary: {summary}")
        
        logger.info("🎉 All modular pipeline tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Modular pipeline test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_modular_pipeline()) 