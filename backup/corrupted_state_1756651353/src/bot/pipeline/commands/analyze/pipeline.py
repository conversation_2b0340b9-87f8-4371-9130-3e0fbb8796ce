"""
Analyze Command Pipeline

This module implements the analyze pipeline for generating structured technical analysis reports.
"""

import asyncio
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional

from stages.price_targets import run
from stages.report_generator import run
from stages.technical_analysis import run

from src.core.formatting.analysis_template import AnalysisTemplate
from src.core.logger import get_logger
from src.core.logger import get_logger
from src.core.logger import get_trading_logger
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.data_providers.unified_base import UnifiedDataProvider, ProviderError
from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
        logger = get_logger(__name__)
        logger.warning(f"Failed to calculate real price targets: {e}")
        # Return basic targets as fallback
        current_price = market_data.get('current_price', 0)
        return {
            "short_term": round(current_price * 1.05, 2),
            "medium_term": round(current_price * 1.15, 2),
            "long_term": round(current_price * 1.25, 2),
            "risk_reward_ratio": 2.0
        }
