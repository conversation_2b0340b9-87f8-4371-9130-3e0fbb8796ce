"""
Analysis Report Template - Strict Format for /analyze command

This template defines the strict structure for comprehensive stock analysis reports.
The AI will fill this template with data collected from various sources to generate
professional-grade analysis reports with price targets, stances, and forecasts.
"""

import logging
from typing import Dict, Any, List


logger = logging.getLogger(__name__)


class AnalysisReportTemplate:
    """Strict template for comprehensive stock analysis reports"""
    
    @staticmethod
    def generate_report(ticker: str, market_data: Dict[str, Any], 
                       technical_data: Dict[str, Any], sentiment_data: Dict[str, Any]) -> str:
        """
        Generate a comprehensive analysis report with strict formatting
        
        Args:
            ticker: Stock symbol
            market_data: Market data including price, volume, fundamentals
            technical_data: Technical indicators and analysis
            sentiment_data: Bullish/bearish stances and probabilities
            
        Returns:
            Formatted analysis report string
        """
        try:
            # Extract data with fallbacks
            current_price = market_data.get('current_price', 'N/A')
            daily_change = market_data.get('daily_change', 'N/A')
            volume = market_data.get('volume', 'N/A')
            market_cap = market_data.get('market_cap', 'N/A')
            pe_ratio = market_data.get('pe_ratio', 'N/A')
            week_high = market_data.get('52_week_high', 'N/A')
            week_low = market_data.get('52_week_low', 'N/A')
            
            trend = technical_data.get('trend', 'N/A')
            rsi = technical_data.get('rsi', 'N/A')
            macd = technical_data.get('macd', 'N/A')
            support_levels = technical_data.get('support_levels', [])
            resistance_levels = technical_data.get('resistance_levels', [])
            moving_averages = technical_data.get('moving_averages', {})
            volatility = technical_data.get('volatility', 'N/A')
            
            bullish_prob = sentiment_data.get('bullish_probability', 50)
            bearish_prob = sentiment_data.get('bearish_probability', 50)
            stance = sentiment_data.get('overall_stance', 'Neutral')
            confidence = sentiment_data.get('confidence_level', 'Medium')
            
            # Format numbers
            if isinstance(volume, (int, float)):
                volume = f"{volume:,.0f}"
            if isinstance(market_cap, (int, float)):
                market_cap = f"${market_cap:,.0f}"
            
            # Generate price targets based on current price
            if isinstance(current_price, (int, float)):
                short_term_target = current_price * 1.05
                medium_term_target = current_price * 1.15
                long_term_target = current_price * 1.25
                downside_risk = current_price * 0.85
            else:
                short_term_target = medium_term_target = long_term_target = downside_risk = 'N/A'
            
            # Build the comprehensive report
            report = f"""
🔍 **COMPREHENSIVE ANALYSIS REPORT: {ticker.upper()}**

📊 **MARKET DATA SUMMARY**
• Current Price: ${current_price if isinstance(current_price, str) else f'{current_price:.2f}'}
• Daily Change: {daily_change if isinstance(daily_change, str) else f'{daily_change:.2f}'}%
• Volume: {volume}
• Market Cap: {market_cap}
• P/E Ratio: {pe_ratio}
• 52W High: ${week_high if isinstance(week_high, str) else f'{week_high:.2f}'}
• 52W Low: ${week_low if isinstance(week_low, str) else f'{week_low:.2f}'}

📈 **TECHNICAL ANALYSIS**
• Trend: {trend}
• RSI: {rsi} ({AnalysisReportTemplate._get_rsi_sentiment(rsi)})
• MACD: {macd}
• 50-Day MA: ${moving_averages.get('50_day', 'N/A')}
• 200-Day MA: ${moving_averages.get('200_day', 'N/A')}
• Volatility: {volatility}

🎯 **PRICE TARGETS & KEY LEVELS**
• Support Levels: ${', $'.join(map(str, support_levels)) if support_levels else 'N/A'}
• Resistance Levels: ${', $'.join(map(str, resistance_levels)) if resistance_levels else 'N/A'}
• Short-term Target (1-2 weeks): ${short_term_target if isinstance(short_term_target, str) else f'{short_term_target:.2f}'}
• Medium-term Target (1-3 months): ${medium_term_target if isinstance(medium_term_target, str) else f'{medium_term_target:.2f}'}
• Long-term Target (3-12 months): ${long_term_target if isinstance(long_term_target, str) else f'{long_term_target:.2f}'}
• Downside Risk Level: ${downside_risk if isinstance(downside_risk, str) else f'{downside_risk:.2f}'}

📊 **SENTIMENT & STANCE ANALYSIS**
• Overall Stance: {stance}
• Bullish Probability: {bullish_prob}%
• Bearish Probability: {bearish_prob}%
• Confidence Level: {confidence}

⏰ **TIME FRAME ANALYSIS**
• Intraday (15min-1H): {AnalysisReportTemplate._get_intraday_analysis(trend, volatility)}
• Short-term (1-7 days): {AnalysisReportTemplate._get_short_term_analysis(trend, support_levels, resistance_levels)}
• Medium-term (1-4 weeks): {AnalysisReportTemplate._get_medium_term_analysis(trend, moving_averages)}
• Long-term (1-3 months): {AnalysisReportTemplate._get_long_term_analysis(trend, pe_ratio)}

⚠️ **RISK ASSESSMENT**
• Volatility Risk: {AnalysisReportTemplate._get_volatility_risk(volatility)}
• Market Correlation: High with tech sector
• Recommended Position Size: 2-3% of portfolio
• Stop-Loss Recommendation: 5-8% below entry

💡 **TRADING RECOMMENDATION**
{AnalysisReportTemplate._get_trading_recommendation(stance, bullish_prob, current_price, support_levels)}

📅 **KEY CATALYSTS TO WATCH**
• Earnings reports (next quarter)
• Federal Reserve announcements
• Sector rotation patterns
• Macroeconomic data releases

*Note: This analysis is for educational purposes only. Always conduct your own research and consider professional advice before trading. Past performance is not indicative of future results.*
"""
            
            return report.strip()
            
        except Exception as e:
            logger.error(f"Error generating analysis report: {e}")
            return f"❌ Error generating analysis report for {ticker}. Please try again later."
    
    @staticmethod
    def _get_rsi_sentiment(rsi_value) -> str:
        """Get RSI sentiment description"""
        if isinstance(rsi_value, (int, float)):
            if rsi_value > 70:
                return "Overbought"
            elif rsi_value < 30:
                return "Oversold"
            else:
                return "Neutral"
        return "Unknown"
    
    @staticmethod
    def _get_intraday_analysis(trend: str, volatility: str) -> str:
        """Generate intraday analysis"""
        if "bull" in trend.lower():
            return "Expect upward momentum with possible pullbacks. Watch for breakout above resistance levels."
        elif "bear" in trend.lower():
            return "Potential downward pressure. Look for bounces at support levels for short-term opportunities."
        else:
            return "Sideways movement expected. Range trading opportunities between support and resistance."
    
    @staticmethod
    def _get_short_term_analysis(trend: str, support_levels: List, resistance_levels: List) -> str:
        """Generate short-term analysis"""
        if support_levels and resistance_levels:
            return f"Trading range expected between ${support_levels[0]} and ${resistance_levels[0]}. Breakouts could signal next directional move."
        return "Monitor key technical levels for direction confirmation."
    
    @staticmethod
    def _get_medium_term_analysis(trend: str, moving_averages: Dict) -> str:
        """Generate medium-term analysis"""
        ma_50 = moving_averages.get('50_day')
        ma_200 = moving_averages.get('200_day')
        
        if ma_50 and ma_200:
            if ma_50 > ma_200:
                return "Bullish momentum expected to continue. Use pullbacks as buying opportunities."
            else:
                return "Bearish pressure may persist. Rally attempts could face resistance."
        return "Monitor moving average crossovers for trend confirmation."
    
    @staticmethod
    def _get_long_term_analysis(trend: str, pe_ratio: Any) -> str:
        """Generate long-term analysis"""
        if isinstance(pe_ratio, (int, float)):
            if pe_ratio > 50:
                return "High valuation suggests careful position sizing. Growth expectations must be met."
            elif pe_ratio < 15:
                return "Attractive valuation for long-term investors if fundamentals are strong."
            else:
                return "Reasonable valuation. Focus on execution and market conditions."
        return "Valuation analysis requires current P/E ratio data."
    
    @staticmethod
    def _get_volatility_risk(volatility: str) -> str:
        """Get volatility risk assessment"""
        volatility = volatility.lower()
        if "high" in volatility:
            return "High - Increased risk and potential reward"
        elif "low" in volatility:
            return "Low - Stable but limited movement"
        else:
            return "Moderate - Balanced risk/reward profile"
    
    @staticmethod
    def _get_trading_recommendation(stance: str, bullish_prob: int, 
                                  current_price: Any, support_levels: List) -> str:
        """Generate trading recommendation"""
        if not isinstance(current_price, (int, float)):
            return "Recommendation requires current price data."
        
        if "bull" in stance.lower() and bullish_prob > 60:
            if support_levels:
                return f"Consider buying near ${support_levels[0]} with target at ${current_price * 1.08:.2f}. Stop loss at ${current_price * 0.92:.2f}."
            else:
                return f"Bullish momentum detected. Consider entry with stop loss at ${current_price * 0.92:.2f}."
        
        elif "bear" in stance.lower() and bullish_prob < 40:
            return f"Bearish pressure expected. Consider short positions on rallies with stop loss at ${current_price * 1.08:.2f}."
        
        else:
            return "Neutral market conditions. Wait for clearer signals or consider range trading strategies."


# Example usage and test data
if __name__ == "__main__":
    # Test data
    test_market_data = {
        'current_price': 250.50,
        'daily_change': 2.5,
        'volume': 45000000,
        'market_cap': 800000000000,
        'pe_ratio': 65.2,
        '52_week_high': 300.25,
        '52_week_low': 180.75
    }
    
    test_technical_data = {
        'trend': 'Bullish',
        'rsi': 58.5,
        'macd': 'Positive',
        'support_levels': [240.0, 235.0, 230.0],
        'resistance_levels': [255.0, 260.0, 265.0],
        'moving_averages': {'50_day': 245.0, '200_day': 230.0},
        'volatility': 'High'
    }
    
    test_sentiment_data = {
        'bullish_probability': 65,
        'bearish_probability': 35,
        'overall_stance': 'Bullish',
        'confidence_level': 'High'
    }
    
    # Generate report
    report = AnalysisReportTemplate.generate_report(
        "TSLA", test_market_data, test_technical_data, test_sentiment_data
    )
    print(report)