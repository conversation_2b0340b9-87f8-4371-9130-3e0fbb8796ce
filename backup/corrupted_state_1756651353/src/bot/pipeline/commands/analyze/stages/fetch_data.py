"""Stage: fetch_data

Fetch market data for the requested ticker and attach it to the PipelineContext.

This version mirrors the comprehensive fetch flow used by the /ask pipeline: it prefers
an enhanced MarketDataService if available, and falls back to the project's
DataProviderAggregator. It collects current price, technical indicators and
historical data and stores them under context.processing_results['market_data'].
"""

import asyncio
from datetime import datetime
import logging
from typing import Any, Dict

from pipeline import PipelineContext

from src.api.data.market_data_service import MarketDataService
from src.core.logger import get_trading_logger
from src.shared.data_providers.aggregator import DataProviderAggregator
        market_data.setdefault('timestamp', datetime.utcnow().isoformat())

        # Final sanity defaults
        market_data.setdefault('symbol', ticker)
        market_data.setdefault('status', market_data.get('status', 'success'))

        context.processing_results['market_data'] = market_data
        logger.info(f"[fetch_data] Completed comprehensive data collection for {ticker}")

    except Exception as exc:
        logger.error(f"[fetch_data] Unexpected error while fetching data for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'fetch_data',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context 