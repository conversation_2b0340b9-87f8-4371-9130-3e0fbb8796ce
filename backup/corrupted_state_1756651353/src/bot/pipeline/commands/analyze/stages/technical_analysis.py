"""Stage: technical_analysis

Compute technical indicators and attach the results to PipelineContext.processing_results['technical_analysis'].
"""

from typing import Dict, Any

from pipeline import PipelineContext

from src.core.logger import get_trading_logger
from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator
        calculator = TechnicalAnalysisCalculator()

        # Attempt to get historical closes in a few possible shapes
        historical = market_data.get('historical') or market_data.get('history') or {}
        closes = []
        if isinstance(historical, dict):
            closes = historical.get('closes', [])
        elif isinstance(historical, list):
            # assume list of dicts with 'close'
            closes = [dp.get('close') for dp in historical if dp.get('close') is not None]

        if len(closes) < 14:
            logger.warning(f"[technical_analysis] Insufficient closes ({len(closes)}) for indicators; need at least 14 data points")
            context.processing_results['technical_analysis'] = {
                'error': 'insufficient_data',
                'message': f'Need at least 14 data points, only {len(closes)} available',
                'data_points_available': len(closes),
                'minimum_required': 14
            }
            return context

        # Calculate indicators
        rsi = calculator.calculate_rsi(closes, period=14)
        macd_data = calculator.calculate_macd(closes)
        macd_signal_label = 'bullish' if macd_data['macd'] > macd_data['signal'] else 'bearish'

        support_levels = calculator.calculate_support_levels(closes)
        resistance_levels = calculator.calculate_resistance_levels(closes)

        trend = 'uptrend' if closes[-1] > closes[0] else 'downtrend' if closes[-1] < closes[0] else 'sideways'
        volatility = calculator.calculate_volatility(closes)
        volatility_level = 'high' if volatility > 0.05 else 'medium' if volatility > 0.02 else 'low'

        result: Dict[str, Any] = {
            'rsi': rsi,
            'macd': macd_signal_label,
            'support_levels': support_levels,
            'resistance_levels': resistance_levels,
            'trend': trend,
            'volatility': volatility_level,
        }

        context.processing_results['technical_analysis'] = result
        logger.info(f"[technical_analysis] Completed for {ticker}")

    except Exception as exc:
        logger.error(f"[technical_analysis] Error for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'technical_analysis',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context 