"""Stage: report_generator

Format a comprehensive analysis report from context.processing_results and attach it to
context.processing_results['response'].
"""

from datetime import datetime

from pipeline import PipelineContext

from src.core.logger import get_trading_logger


def _format_report(ticker: str, market_data: dict, technical: dict, targets: dict) -> str:
    current_price = market_data.get('current_price') or market_data.get('price') or 0
    rsi = technical.get('rsi', 'N/A')
    macd = technical.get('macd', 'N/A')
    trend = technical.get('trend', 'N/A')

    tp1 = targets.get('tp1')
    tp2 = targets.get('tp2')
    tp3 = targets.get('tp3')
    sl = targets.get('sl')

    report = f"""
📊 ANALYSIS REPORT FOR {ticker}

💰 CURRENT PRICE: ${current_price:.2f}

📈 TREND: {trend}
• RSI: {rsi}
• MACD: {macd}

🎯 PRICE TARGETS:
• TP1: ${tp1}
• TP2: ${tp2}
• TP3: ${tp3}
• SL: ${sl}

🕒 Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC

⚠️ Disclaimer: For informational purposes only.
""".strip()

    return report


async def run(context: PipelineContext) -> PipelineContext:
    logger = get_trading_logger()
    ticker = context.ticker
    logger.info(f"[report_generator] Building report for {ticker}")

    try:
        market_data = context.processing_results.get('market_data', {})
        technical = context.processing_results.get('technical_analysis', {})
        targets = context.processing_results.get('price_targets', {})

        report = _format_report(ticker, market_data, technical, targets)
        context.processing_results['response'] = report
        logger.info(f"[report_generator] Report built for {ticker}")

    except Exception as exc:
        logger.error(f"[report_generator] Error for {ticker}: {exc}")
        context.error_log.append({
            'stage': 'report_generator',
            'error_message': str(exc),
            'error_type': type(exc).__name__,
        })

    return context 