version: '3.8'

services:
  pipeline-system:
    build: .
    container_name: trading-pipeline-system
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - LOG_LEVEL=DEBUG
      - LOG_FILE=/app/logs/pipeline_dev.log
      - PYTHONPATH=/app
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
    ports:
      - "8001:8000"  # Different port to avoid conflicts
    command: python test_pipeline.py
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3

  pipeline-monitor:
    build: .
    container_name: trading-pipeline-monitor
    volumes:
      - .:/app
      - ./logs:/app/logs
    environment:
      - LOG_LEVEL=DEBUG
      - LOG_FILE=/app/logs/monitor.log
      - PYTHONPATH=/app
    ports:
      - "8002:8000"
    command: ["python", "-c", "import time; import os; from core.logger import logger; [print(f'Pipeline Log Summary: {logger.get_log_summary()}') or time.sleep(60) for _ in iter(int, 1)]"]
    restart: unless-stopped
    depends_on:
      - pipeline-system

  # Optional: Add a simple web interface for viewing logs
  log-viewer:
    image: nginx:alpine
    container_name: trading-log-viewer
    volumes:
      - ./logs:/usr/share/nginx/html/logs:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8003:80"
    restart: unless-stopped
    depends_on:
      - pipeline-system

volumes:
  pipeline_logs:
  pipeline_data:

networks:
  default:
    driver: bridge 