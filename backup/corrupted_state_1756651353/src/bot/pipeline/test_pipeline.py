"""
Test Script for the New Pipeline System

This script demonstrates how the multi-stage pipeline works
and can be used for testing and development.
"""

import asyncio
import os
import sys

from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
from src.bot.pipeline.core.context_manager import PipelineContext
from src.bot.pipeline.core.context_manager import QualityScore
    quality = QualityScore(overall_score=85.0, freshness_score=90.0)
    context.set_quality_score("test_data", quality)
    
    print(f"Overall Quality: {context.get_overall_quality().quality_level.value}")
    
    # Test audit trail
    context.add_audit_entry(
        stage="test_stage",
        action="test_action",
        input_data={"test": "input"},
        output_data={"test": "output"},
        execution_time=0.1,
        success=True
    )
    
    print(f"Audit Entries: {len(context.audit_trail)}")
    
    # Finalize
    context.finalize()
    print(f"Final Status: {context.status.value}")
    print(f"Total Time: {context.total_execution_time:.3f}s")


async def main():
    """Main test function"""
    print("🎯 Multi-Stage Pipeline System Test")
    print("This demonstrates the new n8n-style pipeline architecture")
    
    # Test context functionality
    await test_pipeline_context()
    
    # Test full pipeline
    await test_ask_pipeline()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main()) 