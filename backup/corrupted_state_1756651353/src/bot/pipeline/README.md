# Multi-Stage Pipeline System

## 🎯 Overview

This is a production-ready, n8n-style multi-step pipeline system for Discord bot commands. Each command gets its own pipeline with multiple stages that can run sequentially or in parallel.

## 🏗️ Architecture

### Command-Based Organization
```
src/bot/pipeline/
├── core/                    # Shared pipeline infrastructure
├── commands/                # Command-specific pipelines
│   ├── ask/                # /ask command pipeline
│   ├── analyze/            # /analyze command pipeline
│   └── watchlist/          # /watchlist command pipeline
├── shared/                  # Reusable components
└── monitoring/              # Pipeline monitoring
```

### Pipeline Stages
Each command pipeline consists of multiple stages:

1. **Query Classification** - Analyze and categorize user input
2. **Data Collection** - Gather data from multiple sources
3. **Data Validation** - Quality checks and consistency validation
4. **AI Processing** - Generate AI responses with real data
5. **Response Formatting** - Format output for Discord

## 🚀 Quick Start

### Running the Test Pipeline
```bash
cd src/bot/pipeline
python test_pipeline.py
```

### Using in Your Bot
```python
from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline

# Execute the complete pipeline
context = await execute_ask_pipeline(
    query="What's the price of $AAPL?",
    user_id="12345",
    guild_id="67890"
)

# Get the formatted response
response = context.processing_results.get("formatted_response")
```

## 🔧 Pipeline Configuration

Each command has its own `config.yaml` file:

```yaml
pipeline:
  name: "ask"
  max_execution_time: 15.0
  parallel_execution: false

stages:
  query_classifier:
    timeout: 1.0
    retry_attempts: 1
    critical: false
```

## 📊 Data Flow

### Pipeline Context
The `PipelineContext` class manages data flow between stages:

```python
context = PipelineContext(
    original_query="What's the price of $AAPL?",
    user_id="12345"
)

# Data flows through stages:
# Stage 1: query_classifier → context.processing_results
# Stage 2: data_collection → context.collected_data
# Stage 3: data_validation → context.validated_data
# Stage 4: ai_processing → context.ai_responses
# Stage 5: response_formatting → context.processing_results
```

### Quality Scoring
Each data source gets a quality score:

```python
quality = QualityScore(
    overall_score=85.0,
    freshness_score=90.0,
    consistency_score=80.0,
    completeness_score=85.0,
    source_reliability=90.0
)
```

## 🛡️ Error Handling & Recovery

### Circuit Breakers
- Failed data sources are automatically bypassed
- Fallback sources are used when primary sources fail
- Pipeline continues with partial data when possible

### Quality Thresholds
- Data below quality threshold triggers warnings
- Critical failures stop pipeline execution
- Non-critical failures allow pipeline to continue

## 📈 Monitoring & Observability

### Audit Trail
Every pipeline execution is fully logged:

```python
# Access audit trail
for entry in context.audit_trail:
    print(f"{entry.stage}: {entry.action} - {entry.execution_time:.3f}s")
```

### Performance Metrics
- Stage execution times
- Overall pipeline performance
- Resource usage tracking
- Error rates and patterns

## 🔄 Adding New Commands

### 1. Create Command Directory
```bash
mkdir -p src/bot/pipeline/commands/newcommand/stages
```

### 2. Create Pipeline Stages
```python
class NewStage(BasePipelineStage):
    def __init__(self):
        super().__init__("new_stage")
        self.required_inputs = ["input_data"]
        self.outputs = ["output_data"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        # Your stage logic here
        pass
```

### 3. Create Pipeline
```python
def create_newcommand_pipeline() -> PipelineEngine:
    return (PipelineBuilder("newcommand")
            .add_stage(NewStage())
            .build())
```

## 🧪 Testing

### Unit Tests
Test individual stages:
```python
async def test_query_classifier():
    stage = QueryClassifierStage()
    context = PipelineContext(original_query="What's $AAPL price?")
    result = await stage.execute(context)
    assert result.success
```

### Integration Tests
Test complete pipelines:
```python
async def test_ask_pipeline():
    context = await execute_ask_pipeline("What's $AAPL price?")
    assert context.status == PipelineStatus.COMPLETED
    assert "formatted_response" in context.processing_results
```

## 📋 Current Status

### ✅ Completed
- [x] Core pipeline architecture
- [x] Pipeline context management
- [x] Base stage classes
- [x] Ask command pipeline
- [x] Configuration system
- [x] Test framework

### 🚧 In Progress
- [ ] Data source integration
- [ ] Error handling improvements
- [ ] Performance optimization

### 📋 Next Steps
- [ ] Analyze command pipeline
- [ ] Watchlist command pipeline
- [ ] Parallel execution
- [ ] Advanced monitoring
- [ ] Production deployment

## 🎯 Benefits

1. **Modularity** - Each stage is independent and testable
2. **Scalability** - Easy to add new stages and commands
3. **Reliability** - Comprehensive error handling and recovery
4. **Observability** - Full audit trail and performance metrics
5. **Maintainability** - Clear separation of concerns
6. **Flexibility** - Configurable pipelines per command

## 🔗 Integration

This system integrates with your existing:
- Market data services (Polygon, Finnhub, Alpha Vantage)
- AI assistant (OpenRouter)
- Discord bot framework
- Database and caching systems

## 📚 Documentation

- [Pipeline Architecture](core/)
- [Command Pipelines](commands/)
- [Configuration Guide](config.yaml)
- [Testing Guide](test_pipeline.py)
- [API Reference](core/pipeline_engine.py) 