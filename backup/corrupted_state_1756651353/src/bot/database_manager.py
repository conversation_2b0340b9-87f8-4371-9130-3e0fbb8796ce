"""
Database Manager

Handles database connections and operations.
Requires POSTGRES_PASSWORD environment variable to be set.
"""

import os
import logging
from typing import Optional

import psycopg2
from psycopg2.extras import RealDictCursor

from src.core.logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self):
        self.host = os.getenv('POSTGRES_HOST', 'localhost')
        self.port = int(os.getenv('POSTGRES_PORT', '5432'))
        self.database = os.getenv('POSTGRES_DB', 'tradingview')
        self.user = os.getenv('POSTGRES_USER', 'tradingview')
        
        # Get password from environment - no default
        self.password = os.getenv('POSTGRES_PASSWORD')
        if not self.password:
            raise EnvironmentError(
                "POSTGRES_PASSWORD environment variable must be set. "
                "For security reasons, default passwords are not allowed."
            )
        
        self._connection = None
        self._cursor = None
    
    def connect(self) -> bool:
        """
        Establish database connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            if self._connection is None or self._connection.closed:
                self._connection = psycopg2.connect(
                    host=self.host,
                    port=self.port,
                    database=self.database,
                    user=self.user,
                    password=self.password,
                    cursor_factory=RealDictCursor
                )
                self._cursor = self._connection.cursor()
                logger.info("Database connection established successfully")
                return True
            return True
            
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            self._connection = None
            self._cursor = None
            return False
    
    def disconnect(self):
        """Close database connection."""
        try:
            if self._cursor:
                self._cursor.close()
            if self._connection:
                self._connection.close()
                logger.info("Database connection closed")
            
        except Exception as e:
            logger.error(f"Error closing database connection: {e}")
        finally:
            self._connection = None
            self._cursor = None
    
    def execute_query(self, query: str, params: tuple = None) -> Optional[list]:
        """
        Execute SQL query and return results.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Query results or None on error
        """
        try:
            if not self.connect():
                return None
            
            self._cursor.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                return self._cursor.fetchall()
            else:
                self._connection.commit()
                return None
            
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            if self._connection:
                self._connection.rollback()
            return None
    
    def __del__(self):
        """Ensure connection is closed on deletion."""
        self.disconnect() 