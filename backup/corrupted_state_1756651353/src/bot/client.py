#!/usr/bin/env python3
"""
Enhanced trading bot with modular pipeline system, slash commands, and timeframe service integration.
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional

import discord
from discord.ext import commands

from src.core.logger import get_logger
from src.shared.utils.rate_limiter import RateLimiter
from src.database.connection import db_manager

# Import command setup functions
from .commands.analyze import setup_analyze_command
from .commands.ask import setup_ask_command
from .commands.enhanced_analyze_with_timeframe import setup_enhanced_analyze_command
from .commands.manipulation_alerts import setup_manipulation_alerts_command
from .commands.global_market_context import setup_global_market_context_command

logger = get_logger(__name__)


class TradingBot:
    """
    Enhanced trading bot with modular pipeline system, slash commands, and timeframe service integration.
    """
    
    def __init__(self, token: str):
        self.token = token
        self.bot = commands.Bot(
            command_prefix='!',
            intents=discord.Intents.default(),
            help_command=None
        )
        self.db_manager = db_manager
        # Remove watchlist manager for now to avoid import issues
        # self.watchlist_manager = WatchlistManager()
        self.rate_limiter = RateLimiter(max_requests=10, time_window=60)
        
        # Setup event handlers
        self.setup_events()
        
        # Setup slash commands
        self.setup_slash_commands()
        
        # Setup legacy prefix commands
        self.setup_prefix_commands()
        
        logger.info("TradingBot initialized with timeframe service integration")
    
    def setup_events(self):
        """Setup bot event handlers."""
        
        @self.bot.event
        async def on_ready():
            logger.info(f"Bot is ready! Logged in as {self.bot.user}")
            logger.info(f"Bot ID: {self.bot.user.id}")
            logger.info(f"Guilds: {len(self.bot.guilds)}")
            
            # Sync slash commands
            try:
                synced = await self.bot.tree.sync()
                logger.info(f"Synced {len(synced)} slash command(s)")
            except Exception as e:
                logger.warning(f"Failed to sync slash commands: {e}")
            
            # Set bot status
            await self.bot.change_presence(
                activity=discord.Activity(
                    type=discord.ActivityType.watching,
                    name="the markets 📊 | /help for commands"
                )
            )
            
            logger.info("🎉 Bot is fully operational with timeframe service integration!")
        
        @self.bot.event
        async def on_command_error(ctx, error):
            if isinstance(error, commands.CommandNotFound):
                return  # Ignore unknown commands
            
            if isinstance(error, commands.MissingPermissions):
                await ctx.send("❌ You don't have permission to use this command.")
                return
            
            if isinstance(error, commands.BotMissingPermissions):
                await ctx.send("❌ I don't have the required permissions to execute this command.")
                return
            
            logger.error(f"Command error in {ctx.command}: {error}", exc_info=True)
            await ctx.send("❌ An error occurred while executing the command. Please try again later.")
    
    def setup_slash_commands(self):
        """Setup Discord slash commands with timeframe service integration."""
        logger.info("Setting up slash commands with timeframe service...")
        
        # Setup core commands
        asyncio.create_task(setup_analyze_command(self.bot))
        asyncio.create_task(setup_ask_command(self.bot))
        
        # Setup timeframe service integration commands
        asyncio.create_task(setup_enhanced_analyze_command(self.bot))
        asyncio.create_task(setup_manipulation_alerts_command(self.bot))
        asyncio.create_task(setup_global_market_context_command(self.bot))
        
        # Basic slash commands
        @self.bot.tree.command(name="help", description="Get help with bot commands")
        async def help_command(interaction: discord.Interaction):
            """Show available commands with timeframe service features."""
            embed = discord.Embed(
                title="🤖 **Trading Bot Commands**",
                description="Available slash commands for market analysis and trading",
                color=discord.Color.blue()
            )
            
            embed.add_field(
                name="📊 **Core Analysis Commands**",
                value="• `/analyze <symbol>` - Get technical analysis for a stock\n"
                      "• `/ask <question>` - Ask AI about trading, markets, or strategies\n"
                      "• `/watchlist` - Manage your stock watchlist",
                inline=False
            )
            
            embed.add_field(
                name="🚀 **Enhanced Timeframe Service Commands**",
                value="• `/analyze_enhanced <symbol>` - Real-time analysis with ORB detection\n"
                      "• `/manipulation_alerts [symbol]` - Detect manipulation patterns\n"
                      "• `/global_context` - Global market analysis & NYSE prediction",
                inline=False
            )
            
            embed.add_field(
                name="🔧 **Utility Commands**",
                value="• `/help` - Show this help message\n"
                      "• `/status` - Check bot status\n"
                      "• `/ping` - Test bot response time",
                inline=False
            )
            
            embed.add_field(
                name="🎯 **New Features**",
                value="• **Real-time ORB Detection** - Opening Range Breakout analysis\n"
                      "• **Manipulation Pattern Detection** - Stop hunting, volume manipulation\n"
                      "• **Global Market Context** - Multi-session market analysis\n"
                      "• **NYSE Behavior Prediction** - Market behavior forecasting",
                inline=False
            )
            
            embed.set_footer(text="Use slash commands (/) for the best experience!")
            await interaction.response.send_message(embed=embed)
        
        @self.bot.tree.command(name="status", description="Check bot status and performance")
        async def status_command(interaction: discord.Interaction):
            """Check bot status with timeframe service information."""
            embed = discord.Embed(
                title="🤖 **Bot Status**",
                color=discord.Color.green()
            )
            
            embed.add_field(
                name="🟢 **Status**",
                value="Online and ready with timeframe service",
                inline=True
            )
            
            embed.add_field(
                name="⏱️ **Uptime**",
                value=f"Since {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                inline=True
            )
            
            embed.add_field(
                name="📊 **Guilds**",
                value=len(self.bot.guilds),
                inline=True
            )
            
            embed.add_field(
                name="🔧 **Commands**",
                value="Slash commands + Timeframe service ready",
                inline=True
            )
            
            embed.add_field(
                name="🚀 **Timeframe Service**",
                value="✅ ORB Detection\n✅ Manipulation Alerts\n✅ Global Context\n✅ NYSE Prediction",
                inline=False
            )
            
            await interaction.response.send_message(embed=embed)
        
        @self.bot.tree.command(name="ping", description="Test bot response time")
        async def ping_command(interaction: discord.Interaction):
            """Test bot response time."""
            latency = round(self.bot.latency * 1000)
            await interaction.response.send_message(f"🏓 Pong! Latency: {latency}ms")
        
        logger.info("✅ All slash commands setup complete with timeframe service integration")
    
    def setup_prefix_commands(self):
        """Setup legacy prefix commands for backward compatibility."""
        
        # Basic help command
        @self.bot.command(name="help")
        async def help_command(ctx):
            """Show available commands."""
            embed = discord.Embed(
                title="🤖 **Trading Bot Commands**",
                description="Available commands for market analysis and trading",
                color=discord.Color.blue()
            )
            
            embed.add_field(
                name="📊 **Analysis Commands**",
                value="• `!analyze <symbol>` - Get technical analysis for a stock\n"
                      "• `!watchlist` - Manage your stock watchlist\n"
                      "• `!scan` - Scan watchlist for opportunities",
                inline=False
            )
            
            embed.add_field(
                name="🔧 **Utility Commands**",
                value="• `!help` - Show this help message\n"
                      "• `!status` - Check bot status\n"
                      "• `!ping` - Test bot response time",
                inline=False
            )
            
            embed.add_field(
                name="💡 **Pro Tip**",
                value="Use slash commands (/) for the best experience with timeframe service!",
                inline=False
            )
            
            embed.set_footer(text="Use !help <command> for detailed information about a specific command")
            await ctx.send(embed=embed)
        
        # Status command
        @self.bot.command(name="status")
        async def status_command(ctx):
            """Check bot status."""
            embed = discord.Embed(
                title="🤖 **Bot Status**",
                color=discord.Color.green()
            )
            
            embed.add_field(
                name="🟢 **Status**",
                value="Online and ready with timeframe service",
                inline=True
            )
            
            embed.add_field(
                name="⏱️ **Uptime**",
                value=f"Since {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                inline=True
            )
            
            embed.add_field(
                name="📊 **Guilds**",
                value=len(self.bot.guilds),
                inline=True
            )
            
            embed.add_field(
                name="🔧 **Commands**",
                value="Prefix + Slash commands + Timeframe service",
                inline=True
            )
            
            embed.add_field(
                name="🚀 **Timeframe Service**",
                value="✅ ORB Detection\n✅ Manipulation Alerts\n✅ Global Context\n✅ NYSE Prediction",
                inline=False
            )
            
            await ctx.send(embed=embed)
        
        # Ping command
        @self.bot.command(name="ping")
        async def ping_command(ctx):
            """Test bot response time."""
            latency = round(self.bot.latency * 1000)
            await ctx.send(f"🏓 Pong! Latency: {latency}ms")
        
        logger.info("✅ Legacy prefix commands setup complete")
    
    async def start(self):
        """Start the bot."""
        try:
            logger.info("🚀 Starting TradingBot with timeframe service integration...")
            await self.bot.start(self.token)
        except Exception as e:
            logger.error(f"Failed to start bot: {e}", exc_info=True)
            raise
    
    async def stop(self):
        """Stop the bot."""
        try:
            logger.info("🛑 Stopping TradingBot...")
            await self.bot.close()
        except Exception as e:
            logger.error(f"Error stopping bot: {e}", exc_info=True)


# Global bot instance
bot_instance: Optional[TradingBot] = None


def create_bot(token: str) -> TradingBot:
    """Create and return a new bot instance."""
    global bot_instance
    bot_instance = TradingBot(token)
    return bot_instance


def get_bot() -> Optional[TradingBot]:
    """Get the current bot instance."""
    return bot_instance


async def main():
    """Main entry point."""
    import os
    
    # Get bot token from environment
    token = os.getenv('DISCORD_BOT_TOKEN')
    if not token:
        logger.error("❌ DISCORD_BOT_TOKEN environment variable not set")
        return
    
    # Create and start bot
    bot = create_bot(token)
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal, shutting down...")
    except Exception as e:
        logger.error(f"❌ Bot crashed: {e}", exc_info=True)
    finally:
        if bot_instance:
            await bot_instance.stop()


if __name__ == "__main__":
    asyncio.run(main())