"""
Discord /ask Command Handler
AI-powered trading assistant with conversational interface
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
from typing import Optional

from ..pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor
from src.core.logger import get_logger
from src.shared.utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


class AskCommand(commands.Cog):
    """AI-powered /ask command for trading insights"""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.ai_processor = AIChatProcessor({})
        self.rate_limiter = RateLimiter(max_requests=5, time_window=60)  # 5 requests per minute
        
        # Command cooldowns
        self.user_cooldowns = {}
    
    @app_commands.command(
        name="ask",
        description="Ask the AI about trading, markets, or any financial questions"
    )
    @app_commands.describe(
        question="Your question about trading, markets, stocks, or strategies"
    )
    async def ask_command(self, interaction: discord.Interaction, question: str):
        """Handle the /ask command with AI-powered responses"""
        
        # Defer response for longer processing
        await interaction.response.defer(thinking=True)
        
        try:
            # Rate limiting check
            user_id = str(interaction.user.id)
            if not self.rate_limiter.can_make_request(user_id):
                await interaction.followup.send(
                    "⏰ You're asking questions too quickly! Please wait a moment before asking another question.",
                    ephemeral=True
                )
                return
            
            # Process the question
            response = await self._process_question(question, interaction.user.id)
            
            # Send response
            await self._send_response(interaction, response)
            
        except Exception as e:
            logger.error(f"Error in ask command: {e}", exc_info=True)
            await interaction.followup.send(
                "❌ Sorry, I encountered an error processing your question. Please try again later.",
                ephemeral=True
            )
    
    async def _process_question(self, question: str, user_id: int) -> dict:
        """Process user question using AI processor"""
        
        try:
            # Use the AI processor for flexible, conversational responses
            result = await self.ai_processor.process(question, {"user_id": user_id})
            
            # Update conversation history
            self.ai_processor.update_conversation_history(question, result.get("response", ""))
            
            return result
            
        except Exception as e:
            logger.error(f"AI processing failed: {e}")
            # Fallback response
            return {
                "response": "I'm having trouble processing your request right now. Please try again later.",
                "intent": "general_question",
                "symbols": [],
                "tools_used": [],
                "data_available": False
            }
    
    async def _send_response(self, interaction: discord.Interaction, response: dict):
        """Send formatted response to Discord"""
        
        try:
            # Create Discord embed
            embed = self._create_response_embed(response)
            
            # Add market data if available
            if response.get("market_data"):
                embed = self._add_market_data_to_embed(embed, response["market_data"])
            
            # Send response
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error sending response: {e}")
            # Fallback to simple text response
            await interaction.followup.send(response.get("response", "Response sent."))
    
    def _create_response_embed(self, response: dict) -> discord.Embed:
        """Create Discord embed from AI response"""
        
        # Determine embed color based on intent
        color_map = {
            "price_check": discord.Color.green(),
            "technical_analysis": discord.Color.blue(),
            "fundamental_analysis": discord.Color.purple(),
            "options_strategy": discord.Color.orange(),
            "market_overview": discord.Color.dark_blue(),
            "risk_management": discord.Color.red(),
            "educational": discord.Color.dark_green()
        }
        
        intent = response.get("intent", "general_question")
        color = color_map.get(intent, discord.Color.default())
        
        # Create embed
        embed = discord.Embed(
            title="🤖 AI Trading Assistant",
            description=response.get("response", "No response generated"),
            color=color,
            timestamp=discord.utils.utcnow()
        )
        
        # Add metadata fields
        if response.get("symbols"):
            embed.add_field(
                name="📊 Symbols Analyzed",
                value=", ".join([f"${s}" for s in response["symbols"]]),
                inline=True
            )
        
        if response.get("tools_used"):
            tools_display = ", ".join(response["tools_used"])
            embed.add_field(
                name="🔧 Tools Used",
                value=tools_display,
                inline=True
            )
        
        if response.get("data_available"):
            embed.add_field(
                name="📈 Data Status",
                value="✅ Real-time data available" if response["data_available"] else "❌ Limited data",
                inline=True
            )
        
        # Add intent classification
        intent_display = intent.replace("_", " ").title()
        embed.add_field(
            name="🎯 Query Type",
            value=intent_display,
            inline=True
        )
        
        # Add footer with risk disclaimer
        embed.set_footer(text="⚠️ This is educational content, not financial advice. Trading involves risk.")
        
        return embed
    
    def _add_market_data_to_embed(self, embed: discord.Embed, market_data: dict) -> discord.Embed:
        """Add market data to the embed"""
        
        for symbol, data in market_data.items():
            if not data:
                continue
            
            # Create symbol summary
            symbol_summary = []
            
            if data.get("current_price"):
                price = data["current_price"]
                change = data.get("change", 0)
                change_percent = data.get("change_percent", 0)
                
                # Determine change emoji
                change_emoji = "🟢" if change >= 0 else "🔴"
                
                symbol_summary.append(f"{change_emoji} **${price:.2f}** ({change:+.2f}, {change_percent:+.2f}%)")
            
            if data.get("volume"):
                volume = data["volume"]
                if volume > 1000000:
                    volume_str = f"{volume/1000000:.1f}M"
                else:
                    volume_str = f"{volume:,}"
                symbol_summary.append(f"📊 Volume: {volume_str}")
            
            if data.get("market_cap"):
                market_cap = data["market_cap"]
                if market_cap > 1000000000:
                    market_cap_str = f"${market_cap/1000000000:.1f}B"
                else:
                    market_cap_str = f"${market_cap:,}"
                symbol_summary.append(f"💰 Market Cap: {market_cap_str}")
            
            if symbol_summary:
                embed.add_field(
                    name=f"📊 ${symbol.upper()}",
                    value="\n".join(symbol_summary),
                    inline=False
                )
        
        return embed


async def setup_ask_command(bot: commands.Bot):
    """Setup the ask command cog"""
    await bot.add_cog(AskCommand(bot))
    logger.info("Ask command cog loaded successfully") 