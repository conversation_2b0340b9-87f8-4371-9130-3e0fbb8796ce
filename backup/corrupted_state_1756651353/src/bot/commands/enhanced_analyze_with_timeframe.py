"""
Enhanced Analyze Command with Timeframe Service Integration

Provides comprehensive market analysis using:
- Market Structure (<PERSON><PERSON><PERSON><PERSON>, SMC)
- Trend Following
- Mean Reversion
- Quantitative/Statistical
- Macro/News
"""

import logging
from typing import Dict, Optional
from datetime import datetime

import discord
from discord import app_commands
from discord.ext import commands

from src.analysis.orchestration.analysis_orchestrator import AnalysisOrchestrator
from src.core.logger import get_logger

logger = get_logger(__name__)


class EnhancedAnalyzeWithTimeframeCommand(commands.Cog):
    """
    Enhanced analyze command with timeframe service integration.
    Provides comprehensive market analysis using hybrid approach.
    """
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.orchestrator = AnalysisOrchestrator()
        
        # Register the slash command
        self.bot.tree.add_command(
            app_commands.Command(
                name="analyze_enhanced",
                description="Get comprehensive market analysis with timeframe service",
                callback=self.enhanced_analyze_command,
                parent=None
            )
        )
        
        logger.info("EnhancedAnalyzeWithTimeframeCommand initialized")
    
    @app_commands.describe(
        ticker="Stock ticker symbol (e.g., AAPL, TSLA, SPY)",
        timeframe="Analysis timeframe (1h, 4h, 1d)",
        include_macro="Include macro analysis and events",
        include_execution="Include execution parameters"
    )
    async def enhanced_analyze_command(
        self,
        interaction: discord.Interaction,
        ticker: str,
        timeframe: str = "1h",
        include_macro: bool = True,
        include_execution: bool = True
    ):
        """
        Get comprehensive market analysis using hybrid approach.
        
        This enhanced command provides:
        - Market structure analysis (Wyckoff, SMC)
        - Trend following signals
        - Mean reversion opportunities
        - Statistical analysis
        - Macro context
        """
        try:
            # Defer response since analysis may take time
            await interaction.response.defer(thinking=True)
            
            # Validate ticker symbol
            ticker = ticker.upper().strip()
            if not self._validate_ticker(ticker):
                await interaction.followup.send(
                    f"❌ Invalid ticker symbol: **{ticker}**\n"
                    "Please use a valid stock symbol (e.g., AAPL, TSLA, SPY)"
                )
                return
            
            # Validate timeframe
            if timeframe not in ["1h", "4h", "1d"]:
                await interaction.followup.send(
                    f"❌ Invalid timeframe: **{timeframe}**\n"
                    "Please use: 1h, 4h, or 1d"
                )
                return
            
            # Get user ID for personalization
            user_id = str(interaction.user.id)
            
            # Send initial status
            status_embed = discord.Embed(
                title="🔍 **Enhanced Analysis in Progress**",
                description=f"Analyzing **${ticker}** using hybrid approach...\n"
                           "This comprehensive analysis may take a few moments.",
                color=0x0099ff
            )
            
            features = [
                "• Market Structure Analysis",
                "• Trend Following Signals",
                "• Mean Reversion Analysis",
                "• Statistical Analysis"
            ]
            
            if include_macro:
                features.append("• Macro Context")
            if include_execution:
                features.append("• Execution Parameters")
            
            status_embed.add_field(
                name="🚀 **Analysis Components**",
                value="\n".join(features),
                inline=False
            )
            
            await interaction.followup.send(embed=status_embed)
            
            # Perform analysis
            logger.info(f"Starting enhanced analysis for {ticker}")
            
            analysis = await self.orchestrator.analyze_symbol(
                symbol=ticker,
                timeframe=timeframe,
                user_id=user_id
            )
            
            # Create response embed
            response_embed = await self._create_analysis_embed(
                ticker,
                analysis,
                include_macro,
                include_execution
            )
            
            # Send the response
            await interaction.followup.send(embed=response_embed)
            
            logger.info(f"Enhanced analysis completed for {ticker}")
            
        except Exception as e:
            logger.error(f"Error in enhanced analyze command: {e}", exc_info=True)
            await interaction.followup.send(
                f"❌ Enhanced analysis failed for **{ticker}**\n"
                "Please try again later or contact support if the issue persists.",
                ephemeral=True
            )
    
    async def _create_analysis_embed(
        self,
        ticker: str,
        analysis: Dict,
        include_macro: bool,
        include_execution: bool
    ) -> discord.Embed:
        """Create comprehensive analysis embed."""
        try:
            # Determine embed color based on signals
            signals = analysis.get('signals', [])
            if signals:
                signal_type = signals[0].get('type')
                if signal_type == 'long':
                    color = 0x00ff00  # Green
                elif signal_type == 'short':
                    color = 0xff0000  # Red
                else:
                    color = 0xffff00  # Yellow
            else:
                color = 0x808080  # Gray
            
            # Create main embed
            embed = discord.Embed(
                title=f"🎯 **Enhanced Analysis: ${ticker}**",
                description="Comprehensive market analysis using hybrid approach",
                color=color,
                timestamp=datetime.now()
            )
            
            # Add trading signals
            if signals:
                signals_text = []
                for signal in signals:
                    signal_emoji = "🟢" if signal['type'] == 'long' else "🔴"
                    signals_text.append(
                        f"{signal_emoji} **{signal['type'].title()}** ({signal['timeframe']})\n"
                        f"   💰 Entry: ${signal['entry']:.2f}\n"
                        f"   🛑 Stop: ${signal['stop_loss']:.2f}\n"
                        f"   🎯 Targets: {', '.join(f'${t:.2f}' for t in signal['targets'])}\n"
                        f"   📊 Size: {signal['size']*100:.0f}%\n"
                        f"   ✨ Confidence: {signal['confidence']*100:.0f}%"
                    )
                
                embed.add_field(
                    name="📈 **Trading Signals**",
                    value="\n\n".join(signals_text),
                    inline=False
                )
            else:
                embed.add_field(
                    name="📈 **Trading Signals**",
                    value="No actionable signals at this time",
                    inline=False
                )
            
            # Add market context
            context = analysis.get('context', {})
            context_text = [
                f"📊 **Structure**: {context.get('structure_phase', 'unknown').title()}",
                f"📈 **Trend**: {context.get('trend_state', 'unknown').title()}",
                f"🔄 **Mean Reversion**: {context.get('mean_reversion_state', 'unknown').title()}",
                f"🌍 **Regime**: {context.get('regime', 'unknown').title()}"
            ]
            
            if include_macro:
                context_text.append(
                    f"🏛️ **Macro**: {context.get('macro_environment', 'unknown').title()}"
                )
            
            embed.add_field(
                name="🌍 **Market Context**",
                value="\n".join(context_text),
                inline=False
            )
            
            # Add risk assessment
            risk = analysis.get('risk', {})
            risk_text = [
                f"📊 **Volatility Risk**: {risk.get('volatility_risk', 0)*100:.0f}%",
                f"💧 **Liquidity Risk**: {risk.get('liquidity_risk', 0)*100:.0f}%",
                f"🔗 **Correlation Risk**: {risk.get('correlation_risk', 0)*100:.0f}%",
                f"⚠️ **Event Risk**: {risk.get('event_risk', 0)*100:.0f}%",
                f"❗ **Total Risk**: {risk.get('total_risk', 0)*100:.0f}%"
            ]
            
            embed.add_field(
                name="⚠️ **Risk Assessment**",
                value="\n".join(risk_text),
                inline=False
            )
            
            # Add key levels
            levels = analysis.get('key_levels', {})
            if levels:
                levels_text = []
                for name, level in levels.items():
                    levels_text.append(f"• **{name.replace('_', ' ').title()}**: ${level:.2f}")
                
                embed.add_field(
                    name="🎯 **Key Levels**",
                    value="\n".join(levels_text),
                    inline=False
                )
            
            # Add execution parameters
            if include_execution:
                execution = analysis.get('execution', {})
                if execution:
                    exec_text = [
                        f"📊 **Position Size**: {execution.get('position_size', 0)*100:.0f}%",
                        f"📥 **Entry Type**: {execution.get('entry_type', 'unknown').title()}",
                        f"🛑 **Stop Type**: {execution.get('stop_type', 'unknown').title()}",
                        f"🎯 **Target Type**: {execution.get('target_type', 'unknown').title()}",
                        f"⏰ **Timeframe**: {execution.get('timeframe', 'unknown').upper()}"
                    ]
                    
                    embed.add_field(
                        name="⚙️ **Execution Parameters**",
                        value="\n".join(exec_text),
                        inline=False
                    )
            
            # Add confidence score
            confidence = analysis.get('confidence_score', 0)
            confidence_emoji = "🟢" if confidence > 0.7 else "🟡" if confidence > 0.5 else "🔴"
            
            embed.add_field(
                name="✨ **Analysis Confidence**",
                value=f"{confidence_emoji} {confidence*100:.0f}%",
                inline=False
            )
            
            # Add footer
            embed.set_footer(
                text=f"Enhanced Analysis | {analysis.get('analysis_version', 'v2.0.0')} | {analysis.get('analysis_type', 'hybrid')}",
                icon_url="https://cdn.discordapp.com/emojis/📊.png"
            )
            
            return embed
            
        except Exception as e:
            logger.error(f"Error creating analysis embed: {e}")
            return discord.Embed(
                title="❌ **Analysis Error**",
                description="Failed to create analysis visualization",
                color=0xff0000
            )
    
    def _validate_ticker(self, ticker: str) -> bool:
        """Validate stock ticker symbol."""
        if not ticker:
            return False
        
        # Basic validation: alphanumeric, 1-5 characters
        if not ticker.isalnum() or len(ticker) > 5:
            return False
        
        # Common invalid tickers
        invalid_tickers = {'TEST', 'NULL', 'NONE', 'ERROR'}
        if ticker.upper() in invalid_tickers:
            return False
        
        return True


async def setup_enhanced_analyze_command(bot: commands.Bot):
    """Setup the enhanced analyze command."""
    await bot.add_cog(EnhancedAnalyzeWithTimeframeCommand(bot))
    logger.info("Enhanced analyze command with timeframe service setup complete") 