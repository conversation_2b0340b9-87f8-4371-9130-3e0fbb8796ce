"""
Manipulation Alerts Command

Provides real-time alerts for market manipulation patterns including
stop hunting, volume manipulation, and price manipulation.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import asyncio
from typing import Optional, List
from datetime import datetime

from src.services.command_integration_service import command_integration_service
from src.core.logger import get_logger

logger = get_logger(__name__)


class ManipulationAlertsCommand(commands.Cog):
    """
    Command for real-time manipulation pattern alerts.
    Detects stop hunting, volume manipulation, and price manipulation.
    """
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.integration_service = command_integration_service
        
        # Register the slash command
        self.bot.tree.add_command(
            app_commands.Command(
                name="manipulation_alerts",
                description="Get real-time manipulation pattern alerts",
                callback=self.manipulation_alerts_command,
                parent=None
            )
        )
        
        logger.info("ManipulationAlertsCommand initialized")
    
    @app_commands.describe(
        symbol="Specific stock symbol to check (optional)",
        include_details="Include detailed pattern analysis"
    )
    async def manipulation_alerts_command(
        self,
        interaction: discord.Interaction,
        symbol: Optional[str] = None,
        include_details: bool = True
    ):
        """
        Get real-time alerts for market manipulation patterns.
        
        This command detects:
        - Stop hunting patterns
        - Volume manipulation
        - Price manipulation
        - Liquidity traps
        - Fake breakouts
        """
        try:
            # Defer response since analysis may take time
            await interaction.response.defer(thinking=True)
            
            # Validate symbol if provided
            if symbol:
                symbol = symbol.upper().strip()
                if not self._validate_symbol(symbol):
                    await interaction.followup.send(
                        f"❌ Invalid symbol: **{symbol}**\n"
                        "Please use a valid stock symbol (e.g., AAPL, TSLA, SPY)"
                    )
                    return
            
            # Send initial status
            status_embed = discord.Embed(
                title="🔍 **Scanning for Manipulation Patterns**",
                description="Analyzing market data for manipulation patterns...\n"
                           "This may take a few moments.",
                color=0xff9900
            )
            
            if symbol:
                status_embed.add_field(
                    name="🎯 **Target Symbol**",
                    value=f"${symbol}",
                    inline=True
                )
            else:
                status_embed.add_field(
                    name="🌍 **Scan Scope**",
                    value="All monitored symbols",
                    inline=True
                )
            
            await interaction.followup.send(embed=status_embed)
            
            # Get manipulation alerts
            logger.info(f"Scanning for manipulation patterns: {symbol or 'all symbols'}")
            
            alerts = await self.integration_service.get_manipulation_alerts(symbol)
            
            # Create response embed
            response_embed = await self._create_alerts_embed(alerts, symbol, include_details)
            
            # Send the response
            await interaction.followup.send(embed=response_embed)
            
            logger.info(f"Manipulation alerts scan completed: {len(alerts)} patterns found")
            
        except Exception as e:
            logger.error(f"Error in manipulation alerts command: {e}", exc_info=True)
            await interaction.followup.send(
                "❌ Failed to scan for manipulation patterns\n"
                "Please try again later or contact support if the issue persists.",
                ephemeral=True
            )
    
    async def _create_alerts_embed(
        self,
        alerts: List[dict],
        symbol: Optional[str],
        include_details: bool
    ) -> discord.Embed:
        """Create Discord embed from manipulation alerts."""
        
        if not alerts:
            # No alerts found
            embed = discord.Embed(
                title="✅ **No Manipulation Patterns Detected**",
                description="Market appears to be trading normally at this time.",
                color=0x00ff00,  # Green
                timestamp=datetime.now()
            )
            
            if symbol:
                embed.add_field(
                    name="🎯 **Symbol Checked**",
                    value=f"${symbol}",
                    inline=True
                )
            else:
                embed.add_field(
                    name="🌍 **Scan Scope**",
                    value="All monitored symbols",
                    inline=True
                )
            
            embed.set_footer(
                text="Manipulation Pattern Detection | Real-time monitoring",
                icon_url="https://cdn.discordapp.com/emojis/✅.png"
            )
            
            return embed
        
        # Create alerts embed
        embed = discord.Embed(
            title="⚠️ **Manipulation Patterns Detected**",
            description=f"Found **{len(alerts)}** potential manipulation pattern(s)",
            color=0xff0000,  # Red for alerts
            timestamp=datetime.now()
        )
        
        # Group alerts by type
        alert_types = {}
        for alert in alerts:
            alert_type = alert.get("type", "unknown")
            if alert_type not in alert_types:
                alert_types[alert_type] = []
            alert_types[alert_type].append(alert)
        
        # Add alerts by type
        for alert_type, type_alerts in alert_types.items():
            await self._add_alert_type_field(embed, alert_type, type_alerts, include_details)
        
        # Add summary
        total_confidence = sum(alert.get("confidence", 0) for alert in alerts)
        avg_confidence = total_confidence / len(alerts) if alerts else 0
        
        embed.add_field(
            name="📊 **Alert Summary**",
            value=f"• **Total Patterns**: {len(alerts)}\n"
                  f"• **Pattern Types**: {len(alert_types)}\n"
                  f"• **Average Confidence**: {avg_confidence * 100:.0f}%\n"
                  f"• **Scan Time**: {datetime.now().strftime('%H:%M:%S')}",
            inline=False
        )
        
        # Add risk warning
        embed.add_field(
            name="🚨 **Risk Warning**",
            value="Manipulation patterns may indicate:\n"
                  "• Increased volatility\n"
                  "• Potential stop hunting\n"
                  "• Unusual market behavior\n"
                  "• Higher risk trading conditions",
            inline=False
        )
        
        embed.set_footer(
            text="Manipulation Pattern Detection | Exercise caution",
            icon_url="https://cdn.discordapp.com/emojis/⚠️.png"
        )
        
        return embed
    
    async def _add_alert_type_field(
        self,
        embed: discord.Embed,
        alert_type: str,
        alerts: List[dict],
        include_details: bool
    ):
        """Add a field for a specific alert type."""
        
        # Get emoji for alert type
        type_emoji = {
            "stop_hunting": "🎯",
            "volume_manipulation": "📊",
            "price_manipulation": "💰",
            "liquidity_trap": "🪤",
            "fake_breakout": "🚪",
            "unknown": "❓"
        }.get(alert_type, "❓")
        
        # Create alert type summary
        type_info = []
        
        for alert in alerts:
            symbol = alert.get("symbol", "Unknown")
            confidence = alert.get("confidence", 0)
            description = alert.get("description", "No description")
            timestamp = alert.get("timestamp", "Unknown")
            
            # Format timestamp
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_str = dt.strftime('%H:%M:%S')
            except:
                time_str = "Unknown"
            
            # Color code by confidence
            if confidence > 0.8:
                confidence_emoji = "🔴"
            elif confidence > 0.6:
                confidence_emoji = "🟡"
            else:
                confidence_emoji = "🟢"
            
            if include_details:
                type_info.append(
                    f"{confidence_emoji} **${symbol}** ({confidence * 100:.0f}%)\n"
                    f"   📝 {description}\n"
                    f"   ⏰ {time_str}"
                )
            else:
                type_info.append(
                    f"{confidence_emoji} **${symbol}** ({confidence * 100:.0f}%)"
                )
        
        # Add field
        embed.add_field(
            name=f"{type_emoji} **{alert_type.replace('_', ' ').title()}** ({len(alerts)})",
            value="\n\n".join(type_info),
            inline=False
        )
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate stock symbol format."""
        if not symbol:
            return False
        
        # Basic validation: alphanumeric, 1-5 characters
        if not symbol.isalnum() or len(symbol) > 5:
            return False
        
        # Common invalid tickers
        invalid_tickers = {'TEST', 'NULL', 'NONE', 'ERROR'}
        if symbol.upper() in invalid_tickers:
            return False
        
        return True


async def setup_manipulation_alerts_command(bot: commands.Bot):
    """Setup the manipulation alerts command."""
    await bot.add_cog(ManipulationAlertsCommand(bot))
    logger.info("Manipulation alerts command setup complete") 