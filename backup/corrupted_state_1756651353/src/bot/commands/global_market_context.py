"""
Global Market Context Command

Provides global market analysis, sector rotation insights, and
NYSE behavior prediction using the timeframe service system.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import asyncio
from typing import Optional
from datetime import datetime

from src.services.command_integration_service import command_integration_service
from src.core.logger import get_logger

logger = get_logger(__name__)


class GlobalMarketContextCommand(commands.Cog):
    """
    Command for global market context and NYSE behavior prediction.
    Provides insights into market-wide patterns and sector rotation.
    """
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.integration_service = command_integration_service
        
        # Register the slash command
        self.bot.tree.add_command(
            app_commands.Command(
                name="global_context",
                description="Get global market context and NYSE behavior prediction",
                callback=self.global_context_command,
                parent=None
            )
        )
        
        logger.info("GlobalMarketContextCommand initialized")
    
    @app_commands.describe(
        include_prediction="Include NYSE behavior prediction",
        include_sectors="Include sector rotation analysis"
    )
    async def global_context_command(
        self,
        interaction: discord.Interaction,
        include_prediction: bool = True,
        include_sectors: bool = True
    ):
        """
        Get comprehensive global market context and analysis.
        
        This command provides:
        - Global market trend analysis
        - Sector rotation insights
        - Market sentiment assessment
        - NYSE behavior prediction
        - Multi-session market context
        """
        try:
            # Defer response since analysis may take time
            await interaction.response.defer(thinking=True)
            
            # Send initial status
            status_embed = discord.Embed(
                title="🌍 **Analyzing Global Market Context**",
                description="Gathering market-wide data and analyzing global patterns...\n"
                           "This comprehensive analysis may take a few moments.",
                color=0x0099ff
            )
            
            features = ["• Global Market Trends", "• Market Sentiment"]
            if include_sectors:
                features.append("• Sector Rotation")
            if include_prediction:
                features.append("• NYSE Behavior Prediction")
            
            status_embed.add_field(
                name="🚀 **Analysis Features**",
                value="\n".join(features),
                inline=False
            )
            
            await interaction.followup.send(embed=status_embed)
            
            # Get global market context
            logger.info("Starting global market context analysis")
            
            global_context = await self.integration_service.get_global_market_context()
            
            # Create response embed
            response_embed = await self._create_global_context_embed(
                global_context, include_prediction, include_sectors
            )
            
            # Send the response
            await interaction.followup.send(embed=response_embed)
            
            logger.info("Global market context analysis completed")
            
        except Exception as e:
            logger.error(f"Error in global context command: {e}", exc_info=True)
            await interaction.followup.send(
                "❌ Failed to analyze global market context\n"
                "Please try again later or contact support if the issue persists.",
                ephemeral=True
            )
    
    async def _create_global_context_embed(
        self,
        global_context: dict,
        include_prediction: bool,
        include_sectors: bool
    ) -> discord.Embed:
        """Create Discord embed from global market context."""
        
        if "error" in global_context:
            # Error occurred
            embed = discord.Embed(
                title="❌ **Global Market Analysis Failed**",
                description="Unable to analyze global market context at this time.",
                color=0xff0000,  # Red
                timestamp=datetime.now()
            )
            
            embed.add_field(
                name="🔍 **Error Details**",
                value=global_context.get("error", "Unknown error"),
                inline=False
            )
            
            embed.set_footer(
                text="Global Market Context | Analysis failed",
                icon_url="https://cdn.discordapp.com/emojis/❌.png"
            )
            
            return embed
        
        # Create main embed
        embed = discord.Embed(
            title="🌍 **Global Market Context**",
            description="Comprehensive analysis of global market conditions and patterns",
            color=0x00ff00,  # Green for success
            timestamp=datetime.now()
        )
        
        # Add global context
        if "global_context" in global_context:
            await self._add_global_context_field(embed, global_context["global_context"])
        
        # Add sector analysis if requested
        if include_sectors and "global_context" in global_context:
            await self._add_sector_analysis_field(embed, global_context["global_context"])
        
        # Add NYSE prediction if requested
        if include_prediction and "nyse_prediction" in global_context:
            await self._add_nyse_prediction_field(embed, global_context["nyse_prediction"])
        
        # Add market sentiment
        if "global_context" in global_context:
            await self._add_market_sentiment_field(embed, global_context["global_context"])
        
        # Add confidence and timestamp
        confidence = global_context.get("confidence", 0)
        embed.add_field(
            name="📊 **Analysis Confidence**",
            value=f"🎯 **Overall Confidence**: {confidence * 100:.0f}%\n"
                  f"⏰ **Analysis Time**: {datetime.now().strftime('%H:%M:%S')}\n"
                  f"🌍 **Data Sources**: Global market feeds",
            inline=False
        )
        
        # Add market hours context
        await self._add_market_hours_context(embed)
        
        embed.set_footer(
            text="Global Market Context | Multi-session analysis",
            icon_url="https://cdn.discordapp.com/emojis/🌍.png"
        )
        
        return embed
    
    async def _add_global_context_field(self, embed: discord.Embed, context: dict):
        """Add global context field to the embed."""
        
        if not context:
            return
        
        # Get trend emoji
        trend = context.get("global_trend", "neutral")
        trend_emoji = {
            "bullish": "🟢",
            "bearish": "🔴",
            "neutral": "🟡",
            "unknown": "⚪"
        }.get(trend, "⚪")
        
        # Create context summary
        context_info = [
            f"{trend_emoji} **Global Trend**: {trend.title()}",
            f"📊 **Market Status**: Active",
            f"🌍 **Scope**: Global markets"
        ]
        
        embed.add_field(
            name="🌍 **Global Market Overview**",
            value="\n".join(context_info),
            inline=False
        )
    
    async def _add_sector_analysis_field(self, embed: discord.Embed, context: dict):
        """Add sector rotation analysis field to the embed."""
        
        if not context:
            return
        
        sector_rotation = context.get("sector_rotation", "unknown")
        
        # Get sector emoji
        sector_emoji = {
            "technology": "💻",
            "healthcare": "🏥",
            "financial": "🏦",
            "energy": "⚡",
            "consumer": "🛒",
            "industrial": "🏭",
            "materials": "🏗️",
            "utilities": "🔌",
            "real_estate": "🏠",
            "communication": "📱",
            "unknown": "❓"
        }.get(sector_rotation.lower(), "❓")
        
        embed.add_field(
            name="🔄 **Sector Rotation**",
            value=f"{sector_emoji} **Current Focus**: {sector_rotation.title()}\n"
                  f"📈 **Rotation Status**: Active\n"
                  f"⏰ **Last Update**: Recent",
            inline=True
        )
    
    async def _add_nyse_prediction_field(self, embed: discord.Embed, prediction: dict):
        """Add NYSE behavior prediction field to the embed."""
        
        if not prediction or "error" in prediction:
            return
        
        pred_type = prediction.get("prediction", "neutral")
        confidence = prediction.get("confidence", 0)
        factors = prediction.get("factors", [])
        
        # Get prediction emoji
        pred_emoji = {
            "bullish": "🟢",
            "bearish": "🔴",
            "neutral": "🟡",
            "volatile": "🟠",
            "unknown": "⚪"
        }.get(pred_type, "⚪")
        
        # Color code by confidence
        if confidence > 0.8:
            confidence_emoji = "🟢"
        elif confidence > 0.6:
            confidence_emoji = "🟡"
        else:
            confidence_emoji = "🔴"
        
        # Format factors
        factor_list = "\n".join([f"• {factor}" for factor in factors[:3]])  # Limit to 3 factors
        
        embed.add_field(
            name="🏛️ **NYSE Behavior Prediction**",
            value=f"{pred_emoji} **Prediction**: {pred_type.title()}\n"
                  f"{confidence_emoji} **Confidence**: {confidence * 100:.0f}%\n"
                  f"📋 **Key Factors**:\n{factor_list}",
            inline=False
        )
    
    async def _add_market_sentiment_field(self, embed: discord.Embed, context: dict):
        """Add market sentiment field to the embed."""
        
        if not context:
            return
        
        sentiment = context.get("market_sentiment", "neutral")
        
        # Get sentiment emoji
        sentiment_emoji = {
            "bullish": "🟢",
            "bearish": "🔴",
            "neutral": "🟡",
            "fearful": "😨",
            "greedy": "😈",
            "unknown": "❓"
        }.get(sentiment, "❓")
        
        embed.add_field(
            name="📊 **Market Sentiment**",
            value=f"{sentiment_emoji} **Current Sentiment**: {sentiment.title()}\n"
                  f"🎭 **Mood**: Market participants' collective feeling\n"
                  f"📈 **Impact**: Influences trading decisions",
            inline=True
        )
    
    async def _add_market_hours_context(self, embed: discord.Embed):
        """Add market hours context to the embed."""
        
        # Get current time in Eastern Time (approximate)
        now = datetime.now()
        
        # Simple market hours check (9:30 AM - 4:00 PM ET)
        # This is a simplified version - in production you'd use proper timezone handling
        hour = now.hour
        minute = now.minute
        
        # Convert to approximate ET (UTC-5)
        et_hour = (hour - 5) % 24
        
        if 9 <= et_hour < 16 or (et_hour == 9 and minute >= 30):
            market_status = "🟢 **Open**"
            status_color = "Market is currently open for trading"
        elif 4 <= et_hour < 9:
            market_status = "🟡 **Pre-Market**"
            status_color = "Pre-market trading session"
        else:
            market_status = "🔴 **Closed**"
            status_color = "Market is closed for the day"
        
        embed.add_field(
            name="⏰ **Market Hours Status**",
            value=f"{market_status}\n{status_color}\n"
                  f"🕐 **Current Time**: {now.strftime('%H:%M:%S')} UTC",
            inline=True
        )


async def setup_global_market_context_command(bot: commands.Bot):
    """Setup the global market context command."""
    await bot.add_cog(GlobalMarketContextCommand(bot))
    logger.info("Global market context command setup complete") 