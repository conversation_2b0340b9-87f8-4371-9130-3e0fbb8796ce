"""
Enhanced Analyze Command for Discord Bot

Provides comprehensive stock analysis using the enhanced analyzer engine.
This command uses all available data from the TradingView alert system.
"""

import discord
from discord import app_commands
from discord.ext import commands
import logging
import asyncio
from typing import Optional

from src.analysis.orchestration.enhanced_analyzer import Enhanced<PERSON><PERSON>yzer
from src.analysis.orchestration.response_formatter import AnalysisResponseFormatter
from src.shared.error_handling.decorators import handle_errors, log_errors

logger = logging.getLogger(__name__)


class EnhancedAnalyzeCommand(commands.Cog):
    """
    Enhanced analyze command that provides comprehensive stock analysis.
    """
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.analyzer = EnhancedAnalyzer()
        self.formatter = AnalysisResponseFormatter()
        
        # Register the slash command
        self.bot.tree.add_command(
            app_commands.Command(
                name="analyze",
                description="Get comprehensive analysis of a stock ticker",
                callback=self.analyze_command,
                parent=None
            )
        )
    
    @app_commands.describe(
        ticker="Stock ticker symbol (e.g., AAPL, TSLA, SPY)",
        compact="Use compact format for mobile devices"
    )
    @app_commands.choices(compact=[
        app_commands.Choice(name="Full Analysis", value=False),
        app_commands.Choice(name="Compact Mobile", value=True)
    ])
    async def analyze_command(
        self, 
        interaction: discord.Interaction, 
        ticker: str,
        compact: bool = False
    ):
        """
        Get comprehensive analysis of a stock ticker using all available data.
        
        This command analyzes:
        - Current price action and volume
        - Technical indicators (RSI, MACD, Moving Averages)
        - Support/resistance levels
        - Alert patterns and intelligence
        - Risk assessment
        - Trade thesis and targets
        - Market context and relative strength
        """
        try:
            # Defer response since analysis may take time
            await interaction.response.defer(thinking=True)
            
            # Validate ticker symbol
            ticker = ticker.upper().strip()
            if not self._validate_ticker(ticker):
                await interaction.followup.send(
                    f"❌ Invalid ticker symbol: **{ticker}**\n"
                    "Please use a valid stock symbol (e.g., AAPL, TSLA, SPY)"
                )
                return
            
            # Get user ID for personalized analysis
            user_id = str(interaction.user.id)
            
            # Send initial status
            status_embed = discord.Embed(
                title="🔍 **Analysis in Progress**",
                description=f"Analyzing **${ticker}** using all available data...\n"
                           "This may take a few moments.",
                color=0x0099ff
            )
            status_embed.add_field(
                name="📊 **Data Sources**",
                value="• TradingView Alert System\n• Watchlist Data\n• Historical Market Data\n• Technical Indicators\n• Alert Patterns",
                inline=False
            )
            
            await interaction.followup.send(embed=status_embed)
            
            # Perform comprehensive analysis
            logger.info(f"Starting enhanced analysis for {ticker} for user {user_id}")
            
            analysis_result = await self.analyzer.analyze_ticker(ticker, user_id)
            
            # Format the response
            response_embed = self.formatter.create_analysis_embed(
                analysis_result, 
                compact=compact
            )
            
            # Add analysis metadata
            response_embed.add_field(
                name="⚡ **Analysis Details**",
                value=f"• Data Points: {self._count_data_points(analysis_result)}\n"
                      f"• Analysis Time: {self._get_analysis_time()}\n"
                      f"• Data Quality: {self._assess_data_quality(analysis_result)}",
                inline=False
            )
            
            # Send the final analysis
            await interaction.followup.send(embed=response_embed)
            
            logger.info(f"Enhanced analysis completed for {ticker}")
            
        except Exception as e:
            logger.error(f"Error in enhanced analyze command for {ticker}: {e}", exc_info=True)
            
            error_embed = discord.Embed(
                title="❌ **Analysis Failed**",
                description=f"Unable to analyze **${ticker}** at this time.",
                color=0xff0000
            )
            error_embed.add_field(
                name="🔧 **Troubleshooting**",
                value="• Check if the ticker symbol is correct\n"
                      "• Verify market hours and data availability\n"
                      "• Try again in a few moments\n"
                      "• Contact support if the issue persists",
                inline=False
            )
            
            await interaction.followup.send(embed=error_embed)
    
    def _validate_ticker(self, ticker: str) -> bool:
        """Validate ticker symbol format."""
        if not ticker:
            return False
        
        # Basic validation: alphanumeric, 1-5 characters
        if not ticker.isalnum() or len(ticker) > 5:
            return False
        
        # Common invalid tickers
        invalid_tickers = {'TEST', 'NULL', 'NONE', 'ERROR'}
        if ticker in invalid_tickers:
            return False
        
        return True
    
    def _count_data_points(self, result) -> str:
        """Count the number of data points used in analysis."""
        try:
            count = 0
            
            # Price data points
            if result.current_price > 0:
                count += 1
            if result.volume > 0:
                count += 1
            
            # Technical indicators
            if result.rsi > 0:
                count += 1
            if result.macd_histogram != 0:
                count += 1
            if result.moving_averages:
                count += len(result.moving_averages)
            
            # Alert data
            if result.alert_count_24h > 0:
                count += 1
            
            # Key levels
            if result.resistance_levels:
                count += len(result.resistance_levels)
            if result.support_levels:
                count += len(result.support_levels)
            
            if count > 20:
                return "20+ (Comprehensive)"
            elif count > 15:
                return "15-20 (Detailed)"
            elif count > 10:
                return "10-15 (Good)"
            elif count > 5:
                return "5-10 (Basic)"
            else:
                return f"{count} (Limited)"
                
        except Exception:
            return "Unknown"
    
    def _get_analysis_time(self) -> str:
        """Get the time taken for analysis."""
        # This would be calculated from actual timing
        # For now, return a placeholder
        return "< 5 seconds"
    
    def _assess_data_quality(self, result) -> str:
        """Assess the quality of available data."""
        try:
            quality_score = 0
            
            # Price data quality
            if result.current_price > 0:
                quality_score += 2
            if result.volume > 0:
                quality_score += 1
            
            # Technical data quality
            if result.rsi > 0:
                quality_score += 1
            if result.macd_histogram != 0:
                quality_score += 1
            if result.moving_averages:
                quality_score += len(result.moving_averages)
            
            # Alert data quality
            if result.alert_count_24h > 0:
                quality_score += 2
            
            # Key levels quality
            if result.resistance_levels or result.support_levels:
                quality_score += 2
            
            if quality_score >= 10:
                return "🟢 Excellent"
            elif quality_score >= 7:
                return "🟡 Good"
            elif quality_score >= 4:
                return "🟠 Fair"
            else:
                return "🔴 Poor"
                
        except Exception:
            return "Unknown"
    
    @commands.command(name="analyze_legacy")
    @handle_errors
    @log_errors
    async def analyze_legacy(self, ctx: commands.Context, ticker: str):
        """
        Legacy analyze command for backward compatibility.
        """
        # Convert to slash command format
        interaction = ctx.interaction if hasattr(ctx, 'interaction') else None
        if interaction:
            await self.analyze_command(interaction, ticker, False)
        else:
            await ctx.send("❌ This command requires slash command format. Use `/analyze $TICKER`")


async def setup(bot: commands.Bot):
    """Setup function for the enhanced analyze command."""
    await bot.add_cog(EnhancedAnalyzeCommand(bot))
    logger.info("Enhanced Analyze Command loaded successfully")


async def teardown(bot: commands.Bot):
    """Teardown function for the enhanced analyze command."""
    await bot.remove_cog("EnhancedAnalyzeCommand")
    logger.info("Enhanced Analyze Command unloaded successfully") 