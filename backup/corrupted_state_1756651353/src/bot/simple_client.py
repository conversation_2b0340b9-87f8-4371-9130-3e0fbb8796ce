"""
Simple Discord Bot Client for Testing

A minimal Discord bot that focuses on the analyze command
without complex dependencies.
"""

import asyncio
import os
import logging
from datetime import datetime
from typing import Optional

import discord
from discord.ext import commands

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleTradingBot(commands.Bot):
    """
    Simple trading bot for testing the analyze command.
    """
    
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        super().__init__(command_prefix=None, intents=intents)  # No prefix for slash commands only
        
        # Setup commands
        self.setup_commands()
    
    def setup_commands(self):
        """Setup slash commands."""
        
        @self.tree.command(name="analyze", description="Get comprehensive analysis of a stock ticker")
        async def analyze_command(interaction: discord.Interaction, symbol: str):
            """
            Get comprehensive analysis of a stock ticker.
            
            Usage: /analyze AAPL
            """
            try:
                # Validate symbol
                if not self._validate_symbol(symbol):
                    await interaction.response.send_message(f"❌ Invalid symbol: **{symbol}**. Please use a valid stock ticker.")
                    return
                
                # Defer response since analysis may take time
                await interaction.response.defer()
                
                # Create mock analysis result (this would come from your enhanced analyzer)
                analysis_result = self._create_mock_analysis(symbol)
                
                # Create and send embed
                embed = self._create_analysis_embed(analysis_result)
                await interaction.followup.send(embed=embed)
                
            except Exception as e:
                logger.error(f"Error in analyze command: {e}", exc_info=True)
                await interaction.followup.send(f"❌ Analysis failed for **{symbol}**. Please try again later.")
        
        # Ping command
        @self.tree.command(name="ping", description="Test bot response time")
        async def ping_command(interaction: discord.Interaction):
            """Test bot response time."""
            latency = round(self.latency * 1000)
            await interaction.response.send_message(f"🏓 Pong! Latency: {latency}ms")
        
        # Sync commands on startup
        @self.event
        async def on_ready():
            logger.info(f'Bot is ready! Logged in as {self.user}')
            logger.info(f'Bot ID: {self.user.id}')
            logger.info(f'Guilds: {len(self.guilds)}')
            
            # Sync commands to guild if specified
            if hasattr(self, 'guild_id') and self.guild_id:
                guild = discord.Object(id=self.guild_id)
                self.tree.copy_global_to(guild=guild)
                await self.tree.sync(guild=guild)
                logger.info(f"Synced commands to guild {self.guild_id}")
            else:
                await self.tree.sync()
                logger.warning("No GUILD_ID provided, syncing commands globally (may take up to an hour).")
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate stock symbol format."""
        if not symbol:
            return False
        
        # Basic validation: alphanumeric, 1-5 characters
        if not symbol.isalnum() or len(symbol) > 5:
            return False
        
        # Common invalid tickers
        invalid_tickers = {'TEST', 'NULL', 'NONE', 'ERROR'}
        if symbol.upper() in invalid_tickers:
            return False
        
        return True
    
    def _create_mock_analysis(self, symbol: str) -> dict:
        """Create mock analysis data (this would come from your enhanced analyzer)."""
        return {
            'symbol': symbol.upper(),
            'timestamp': datetime.now(),
            'current_price': 175.43,
            'price_change': 3.75,
            'price_change_pct': 2.19,
            'day_range': (171.20, 176.15),
            'year_range': (124.17, 199.62),
            'volume': 45200000,
            'volume_ratio': 1.8,
            'trend_direction': 'bullish',
            'trend_strength': 7.5,
            'rsi': 68.5,
            'macd_signal': 'bullish',
            'resistance_levels': [(178.50, 0.8), (182.00, 3.8)],
            'support_levels': [(172.15, -1.9), (168.90, -3.7)],
            'stop_loss': 170.00,
            'alert_count_24h': 7,
            'alert_bullish_count': 5,
            'alert_pattern': 'bullish_cluster',
            'breakout_probability': 0.72,
            'risk_factors': ['Near-term overbought (RSI 68.5)', 'Earnings in 12 days'],
            'target_price': 178.50,
            'confidence_score': 8.2
        }
    
    def _create_analysis_embed(self, result: dict) -> discord.Embed:
        """Create a beautiful analysis embed."""
        
        # Determine embed color based on trend
        color_map = {
            'bullish': 0x00ff00,      # Green
            'bearish': 0xff0000,      # Red
            'neutral': 0x808080       # Gray
        }
        color = color_map.get(result['trend_direction'], 0x808080)
        
        # Create main embed
        embed = discord.Embed(
            title=f"🎯 **${result['symbol']} Analysis** | Updated: {self._format_timestamp(result['timestamp'])}",
            color=color,
            description=""
        )
        
        # Price Action Section
        price_action = self._format_price_action(result)
        embed.add_field(name="📈 **Price Action**", value=price_action, inline=False)
        
        # Technical Snapshot Section
        technical_snapshot = self._format_technical_snapshot(result)
        embed.add_field(name="📊 **Technical Snapshot**", value=technical_snapshot, inline=False)
        
        # Key Levels Section
        key_levels = self._format_key_levels(result)
        embed.add_field(name="🎯 **Key Levels**", value=key_levels, inline=False)
        
        # Alert Intelligence Section
        alert_intelligence = self._format_alert_intelligence(result)
        embed.add_field(name="🔔 **Alert Intelligence**", value=alert_intelligence, inline=False)
        
        # Trade Thesis Section
        trade_thesis = self._format_trade_thesis(result)
        embed.add_field(name="💡 **Trade Thesis**", value=trade_thesis, inline=False)
        
        # Risk Factors Section
        risk_factors = self._format_risk_factors(result)
        embed.add_field(name="⚠️ **Risk Factors**", value=risk_factors, inline=False)
        
        # Footer
        embed.set_footer(text=f"Analysis powered by TradingView Alert System | Confidence: {result['confidence_score']:.1f}/10")
        
        return embed
    
    def _format_price_action(self, result: dict) -> str:
        """Format the price action section."""
        change_sign = "+" if result['price_change'] >= 0 else ""
        change_str = f"${result['price_change']:.2f}"
        change_pct_str = f"{change_sign}{result['price_change_pct']:.2f}%"
        
        day_low, day_high = result['day_range']
        day_range_str = f"${day_low:.2f} - ${day_high:.2f}"
        
        year_low, year_high = result['year_range']
        year_range_str = f"${year_low:.2f} - ${year_high:.2f}"
        
        volume_str = self._format_volume(result['volume'], result['volume_ratio'])
        
        return (f"Current: ${result['current_price']:.2f} ({change_str}, {change_pct_str}) | "
               f"Volume: {volume_str}\n"
               f"Day Range: {day_range_str} | 52W: {year_range_str}")
    
    def _format_technical_snapshot(self, result: dict) -> str:
        """Format the technical snapshot section."""
        trend_emoji = "🟢" if result['trend_direction'] == "bullish" else "🔴" if result['trend_direction'] == "bearish" else "🟡"
        trend_str = f"{trend_emoji} Trend: {result['trend_direction'].title()} (above all MAs) | Strength: {result['trend_strength']:.1f}/10"
        
        rsi_status = "overbought" if result['rsi'] > 70 else "oversold" if result['rsi'] < 30 else "neutral"
        rsi_emoji = "🟡" if result['rsi'] > 70 or result['rsi'] < 30 else "🟢"
        momentum_str = f"{rsi_emoji} Momentum: RSI {result['rsi']:.1f} ({rsi_status}) | MACD {result['macd_signal']} crossover"
        
        volatility_str = "🔵 Volatility: 22% (below 30D avg) | Bollinger: Middle band test"
        
        return f"{trend_str}\n{momentum_str}\n{volatility_str}"
    
    def _format_key_levels(self, result: dict) -> str:
        """Format the key levels section."""
        # Resistance levels
        resistance_str = "Resistance: "
        if result['resistance_levels']:
            resistance_parts = []
            for price, distance in result['resistance_levels'][:3]:
                distance_str = f"{distance:.1f}%" if distance > 0 else f"{abs(distance):.1f}%"
                resistance_parts.append(f"${price:.2f} ({distance_str})")
            resistance_str += " | ".join(resistance_parts)
        else:
            resistance_str += "N/A"
        
        # Support levels
        support_str = "Support: "
        if result['support_levels']:
            support_parts = []
            for price, distance in result['support_levels'][:3]:
                distance_str = f"{distance:.1f}%" if distance > 0 else f"{abs(distance):.1f}%"
                support_parts.append(f"${price:.2f} ({distance_str})")
            support_str += " | ".join(support_parts)
        else:
            support_str += "N/A"
        
        # Stop loss
        stop_distance = ((result['current_price'] - result['stop_loss']) / result['current_price']) * 100
        stop_str = f"Stop Loss: ${result['stop_loss']:.2f} ({stop_distance:.1f}% from current)"
        
        return f"{resistance_str}\n{support_str}\n{stop_str}"
    
    def _format_alert_intelligence(self, result: dict) -> str:
        """Format the alert intelligence section."""
        alert_str = f"Last 24h: {result['alert_count_24h']} alerts ({result['alert_bullish_count']} bullish, 2 neutral)"
        pattern_str = f"Pattern: {result['alert_pattern'].replace('_', ' ').title()} alerts clustering at ${result['current_price']:.0f} resistance"
        probability_str = f"Probability: {result['breakout_probability']:.0%} chance of ${result['target_price']:.2f} test within 2 sessions"
        
        return f"{alert_str}\n{pattern_str}\n{probability_str}"
    
    def _format_trade_thesis(self, result: dict) -> str:
        """Format the trade thesis section."""
        return f"{result['symbol']} showing strong {result['trend_direction']} continuation above ${result['current_price']:.0f} breakout level. Volume confirmation present. Target ${result['target_price']:.2f} with ${result['stop_loss']:.2f} stop."
    
    def _format_risk_factors(self, result: dict) -> str:
        """Format the risk factors section."""
        if not result['risk_factors']:
            return "No significant risk factors identified"
        
        risk_list = []
        for factor in result['risk_factors']:
            risk_list.append(f"• {factor}")
        
        return "\n".join(risk_list)
    
    def _format_volume(self, volume: float, ratio: float) -> str:
        """Format volume with ratio indicator."""
        if ratio > 0:
            ratio_str = f"({ratio:.1f}x avg)"
        else:
            ratio_str = "(avg)"
        
        # Format volume (convert to M/B if large)
        if volume >= 1_000_000_000:
            volume_str = f"{volume / 1_000_000_000:.1f}B"
        elif volume >= 1_000_000:
            volume_str = f"{volume / 1_000_000:.1f}M"
        elif volume >= 1_000:
            volume_str = f"{volume / 1_000:.1f}K"
        else:
            volume_str = f"{volume:.0f}"
        
        return f"{volume_str} {ratio_str}"
    
    def _format_timestamp(self, timestamp: datetime) -> str:
        """Format timestamp for display."""
        try:
            # Convert to EST (UTC-5) for US market hours
            from datetime import timedelta
            est_offset = timedelta(hours=5)
            est_time = timestamp - est_offset
            
            # Format as "2:34 PM EST"
            return est_time.strftime("%-I:%M %p EST")
        except Exception:
            return timestamp.strftime("%-I:%M %p UTC")
    
    async def on_ready(self):
        """Called when the bot is ready."""
        logger.info(f"Bot is ready! Logged in as {self.user}")
        logger.info(f"Bot ID: {self.user.id}")
        logger.info(f"Guilds: {len(self.guilds)}")
        
        # Set bot status
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.watching,
                name="the markets 📊"
            )
        )


async def main():
    """Main function to run the bot."""
    # Get Discord token from environment
    token = os.getenv('DISCORD_BOT_TOKEN')
    
    if not token:
        logger.error("DISCORD_BOT_TOKEN environment variable not set!")
        return
    
    # Create and run bot
    bot = SimpleTradingBot()
    
    try:
        logger.info("Starting simple trading bot...")
        await bot.start(token)
    except discord.errors.LoginFailure as e:
        logger.error(f"Discord authentication failed: {e}")
    except Exception as e:
        logger.error(f"Bot failed to start: {e}", exc_info=True)
    finally:
        if not bot.is_closed():
            await bot.close()


if __name__ == "__main__":
    asyncio.run(main()) 