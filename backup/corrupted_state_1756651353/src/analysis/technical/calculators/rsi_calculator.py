from dataclasses import dataclass
import logging
from typing import List, Optional, Dict

import numpy as np


logger = logging.getLogger(__name__)

@dataclass
class RSIAnalysis:
    """Data class for RSI analysis results"""
    rsi: Optional[float]
    signal: str
    strength: str
    confidence: float

class RSICalculator:
    """Calculator for Relative Strength Index (RSI) technical indicator"""
    
    def __init__(self, period: int = 14, overbought: float = 70, oversold: float = 30):
        """
        Initialize RSI calculator
        
        Args:
            period: RSI calculation period (default 14)
            overbought: Overbought threshold (default 70)
            oversold: Oversold threshold (default 30)
        """
        self.period = period
        self.overbought = overbought
        self.oversold = oversold
        self.min_data_points = period + 1
    
    def calculate_rsi(self, prices: List[float]) -> Optional[float]:
        """
        Calculate RSI for given price data
        
        Args:
            prices: List of closing prices in chronological order
            
        Returns:
            RSI value or None if insufficient data
        """
        if len(prices) < self.min_data_points:
            return None
            
        try:
            # Calculate price changes
            changes = []
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                changes.append(change)
            
            # Separate gains and losses
            gains = [max(change, 0) for change in changes]
            losses = [abs(min(change, 0)) for change in changes]
            
            # Calculate average gains and losses for the period
            avg_gain = self._calculate_smoothed_avg(gains, self.period)
            avg_loss = self._calculate_smoothed_avg(losses, self.period)
            
            if avg_loss == 0:
                return 100.0  # Avoid division by zero
                
            # Calculate RS and RSI
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return None
    
    def analyze_rsi(self, prices: List[float]) -> RSIAnalysis:
        """
        Comprehensive RSI analysis
        
        Args:
            prices: List of closing prices
            
        Returns:
            RSIAnalysis object with detailed analysis
        """
        rsi = self.calculate_rsi(prices)
        
        if rsi is None:
            return RSIAnalysis(
                rsi=None,
                signal="INSUFFICIENT_DATA",
                strength="NONE",
                confidence=0.0
            )
        
        # Determine signal and strength
        signal, strength = self._determine_signal(rsi)
        
        # Calculate confidence
        confidence = self._calculate_confidence(prices)
        
        return RSIAnalysis(
            rsi=rsi,
            signal=signal,
            strength=strength,
            confidence=confidence
        )
    
    def calculate_rolling_rsi(self, prices: List[float]) -> List[Optional[float]]:
        """
        Calculate rolling RSI values
        
        Args:
            prices: List of closing prices
            
        Returns:
            List of RSI values (None for insufficient data periods)
        """
        rsi_values = []
        for i in range(len(prices)):
            if i < self.period:
                rsi_values.append(None)
            else:
                window_prices = prices[i-self.period:i+1]
                rsi = self.calculate_rsi(window_prices)
                rsi_values.append(rsi)
                
        return rsi_values
    
    def _calculate_smoothed_avg(self, values: List[float], period: int) -> float:
        """
        Calculate smoothed average (Wilder's smoothing method)
        
        Args:
            values: List of values to average
            period: Smoothing period
            
        Returns:
            Smoothed average
        """
        # First average is simple average
        first_avg = sum(values[:period]) / period
        
        # Subsequent averages use Wilder's smoothing
        current_avg = first_avg
        for i in range(period, len(values)):
            current_avg = (current_avg * (period - 1) + values[i]) / period
            
        return current_avg
    
    def _determine_signal(self, rsi: float) -> tuple:
        """
        Determine trading signal based on RSI value
        
        Args:
            rsi: RSI value
            
        Returns:
            Tuple of (signal, strength)
        """
        if rsi >= self.overbought:
            if rsi >= 80:
                return "BEARISH", "STRONG"
            else:
                return "BEARISH", "MODERATE"
        elif rsi <= self.oversold:
            if rsi <= 20:
                return "BULLISH", "STRONG"
            else:
                return "BULLISH", "MODERATE"
        else:
            return "NEUTRAL", "NONE"
    
    def _calculate_confidence(self, prices: List[float]) -> float:
        """
        Calculate analysis confidence
        
        Args:
            prices: Price data
            
        Returns:
            Confidence score (0-1)
        """
        if len(prices) < self.min_data_points * 2:
            return 0.4
        elif len(prices) < self.min_data_points * 3:
            return 0.6
        elif len(prices) < self.min_data_points * 4:
            return 0.8
        else:
            return 0.95

# Factory function
def create_rsi_calculator(period: int = 14, overbought: float = 70, oversold: float = 30) -> RSICalculator:
    """Create and return an RSICalculator instance"""
    return RSICalculator(period, overbought, oversold)