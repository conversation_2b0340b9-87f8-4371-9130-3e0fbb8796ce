import logging
from typing import List, Optional, Dict, Any

import numpy as np
import pandas as pd

from src.data.models.stock_data import HistoricalData, TechnicalIndicators

logger = logging.getLogger(__name__)

class TechnicalIndicatorsCalculator:
    """Calculate technical analysis indicators"""
    
    @staticmethod
    def calculate_rsi(prices: List[float], period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index (RSI)"""
        try:
            if len(prices) < period + 1:
                return None
                
            # Convert to pandas series for easier calculation
            series = pd.Series(prices)
            
            # Calculate price changes
            delta = series.diff()
            
            # Separate gains and losses
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            # Calculate RS
            rs = gain / loss
            
            # Calculate RSI
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1]
            
        except Exception as e:
            logger.error(f"RSI calculation error: {e}")
            return None
    
    @staticmethod
    def calculate_macd(
        prices: List[float], 
        fast_period: int = 12, 
        slow_period: int = 26, 
        signal_period: int = 9
    ) -> Optional[Dict[str, float]]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        try:
            if len(prices) < slow_period:
                return None
                
            series = pd.Series(prices)
            
            # Calculate EMAs
            fast_ema = series.ewm(span=fast_period, adjust=False).mean()
            slow_ema = series.ewm(span=slow_period, adjust=False).mean()
            
            # Calculate MACD line
            macd_line = fast_ema - slow_ema
            
            # Calculate signal line
            signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
            
            # Calculate histogram
            histogram = macd_line - signal_line
            
            return {
                'macd_line': macd_line.iloc[-1],
                'signal_line': signal_line.iloc[-1],
                'histogram': histogram.iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"MACD calculation error: {e}")
            return None
    
    @staticmethod
    def calculate_moving_averages(
        prices: List[float], 
        periods: List[int] = [50, 200]
    ) -> Dict[str, float]:
        """Calculate Simple Moving Averages (SMA)"""
        try:
            result = {}
            series = pd.Series(prices)
            
            for period in periods:
                if len(prices) >= period:
                    sma = series.rolling(window=period).mean()
                    result[f'sma_{period}'] = sma.iloc[-1]
                else:
                    result[f'sma_{period}'] = None
                    
            return result
            
        except Exception as e:
            logger.error(f"Moving averages calculation error: {e}")
            return {}
    
    @staticmethod
    def calculate_bollinger_bands(
        prices: List[float], 
        period: int = 20, 
        std_dev: float = 2.0
    ) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands"""
        try:
            if len(prices) < period:
                return None
                
            series = pd.Series(prices)
            
            # Calculate SMA
            sma = series.rolling(window=period).mean()
            
            # Calculate standard deviation
            std = series.rolling(window=period).std()
            
            # Calculate bands
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            return {
                'sma': sma.iloc[-1],
                'upper_band': upper_band.iloc[-1],
                'lower_band': lower_band.iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"Bollinger Bands calculation error: {e}")
            return None
    
    @staticmethod
    def find_support_resistance(
        highs: List[float], 
        lows: List[float], 
        prices: List[float]
    ) -> Dict[str, float]:
        """Find support and resistance levels using price action"""
        try:
            # Simple implementation - find recent swing highs/lows
            recent_prices = prices[-20:]  # Last 20 days
            recent_highs = highs[-20:]
            recent_lows = lows[-20:]
            
            # Support level - recent low
            support = min(recent_lows) if recent_lows else None
            
            # Resistance level - recent high
            resistance = max(recent_highs) if recent_highs else None
            
            return {
                'support': support,
                'resistance': resistance
            }
            
        except Exception as e:
            logger.error(f"Support/Resistance calculation error: {e}")
            return {'support': None, 'resistance': None}
    
    @staticmethod
    def calculate_volume_sma(volumes: List[Optional[float]], period: int = 20) -> Optional[float]:
        """Calculate Volume Simple Moving Average"""
        try:
            # Filter out None values
            valid_volumes = [v for v in volumes if v is not None]
            
            if len(valid_volumes) < period:
                return None
                
            series = pd.Series(valid_volumes)
            volume_sma = series.rolling(window=period).mean()
            
            return volume_sma.iloc[-1]
            
        except Exception as e:
            logger.error(f"Volume SMA calculation error: {e}")
            return None
    
    @classmethod
    def calculate_all_indicators(cls, historical_data: HistoricalData) -> TechnicalIndicators:
        """Calculate all technical indicators"""
        try:
            # Extract price data
            prices = historical_data.prices
            highs = historical_data.highs
            lows = historical_data.lows
            volumes = historical_data.volumes
            
            # Calculate indicators
            rsi = cls.calculate_rsi(prices)
            macd = cls.calculate_macd(prices)
            moving_averages = cls.calculate_moving_averages(prices)
            bollinger_bands = cls.calculate_bollinger_bands(prices)
            support_resistance = cls.find_support_resistance(highs, lows, prices)
            volume_sma = cls.calculate_volume_sma(volumes)
            
            return TechnicalIndicators(
                rsi=rsi,
                macd=macd,
                sma_50=moving_averages.get('sma_50'),
                sma_200=moving_averages.get('sma_200'),
                ema_12=None,  # Not implemented yet
                ema_26=None,  # Not implemented yet
                bollinger_bands=bollinger_bands,
                support_level=support_resistance['support'],
                resistance_level=support_resistance['resistance'],
                volume_sma=volume_sma
            )
            
        except Exception as e:
            logger.error(f"Technical indicators calculation error: {e}")
            return TechnicalIndicators()
