"""
Mean Reversion Analyzer

Implements statistical arbitrage and oscillator concepts for mean reversion trading.
Focuses on:
- Price deviation from moving averages
- Bollinger Bands
- RSI and other oscillators
- Statistical significance testing
"""

import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import numpy as np
import pandas as pd
from scipy import stats

from src.core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MeanReversionSignal:
    """Represents a mean reversion trading signal."""
    price: float
    mean: float
    deviation: float  # in standard deviations
    probability: float  # probability of reversion
    target: float
    stop_loss: float
    timeframe: str  # expected reversion timeframe
    confidence: float  # 0-1 scale


@dataclass
class OscillatorSignals:
    """Combined oscillator signals."""
    rsi: float
    stochastic: float
    cci: float  # Commodity Channel Index
    mfi: float  # Money Flow Index
    combined_signal: float  # -1 to 1 scale
    confidence: float  # 0-1 scale


@dataclass
class StatisticalMetrics:
    """Statistical metrics for mean reversion."""
    z_score: float
    p_value: float
    mean: float
    std_dev: float
    skew: float
    kurtosis: float
    is_stationary: bool
    confidence: float  # 0-1 scale


@dataclass
class ReversionAnalysis:
    """Complete mean reversion analysis result."""
    timestamp: datetime
    signals: List[MeanReversionSignal]
    oscillators: OscillatorSignals
    statistics: StatisticalMetrics
    bollinger_bands: Dict[str, float]
    key_levels: Dict[str, float]
    trade_parameters: Dict[str, float]
    confidence_score: float


class MeanReversionAnalyzer:
    """
    Analyzes price action for mean reversion opportunities.
    Uses statistical methods and technical oscillators.
    """
    
    def __init__(self):
        self.lookback_period = 20  # Standard lookback for calculations
        self.zscore_threshold = 2.0  # Standard deviations for signals
        self.confidence_threshold = 0.7  # Minimum confidence for signals
    
    async def analyze_reversion(
        self,
        prices: List[float],
        volumes: List[float],
        timestamps: List[datetime],
        timeframe: str = "1h"
    ) -> ReversionAnalysis:
        """
        Perform comprehensive mean reversion analysis.
        
        Args:
            prices: List of price points
            volumes: List of volume points
            timestamps: List of timestamp points
            timeframe: Data timeframe (e.g., "1h", "4h", "1d")
            
        Returns:
            ReversionAnalysis result
        """
        try:
            # Convert to pandas for easier analysis
            df = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })
            
            # 1. Generate mean reversion signals
            signals = self._generate_reversion_signals(df)
            
            # 2. Calculate oscillator signals
            oscillators = self._calculate_oscillators(df)
            
            # 3. Calculate statistical metrics
            statistics = self._calculate_statistics(df)
            
            # 4. Calculate Bollinger Bands
            bollinger_bands = self._calculate_bollinger_bands(df)
            
            # 5. Generate key levels
            key_levels = self._generate_key_levels(df, bollinger_bands)
            
            # 6. Calculate trade parameters
            trade_parameters = self._calculate_trade_parameters(
                df, signals, statistics
            )
            
            # 7. Calculate confidence score
            confidence = self._calculate_confidence_score(
                signals, oscillators, statistics
            )
            
            return ReversionAnalysis(
                timestamp=datetime.now(),
                signals=signals,
                oscillators=oscillators,
                statistics=statistics,
                bollinger_bands=bollinger_bands,
                key_levels=key_levels,
                trade_parameters=trade_parameters,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Mean reversion analysis error: {e}")
            return self._get_fallback_analysis()
    
    def _generate_reversion_signals(self, df: pd.DataFrame) -> List[MeanReversionSignal]:
        """Generate mean reversion trading signals."""
        signals = []
        
        try:
            # Calculate moving average and standard deviation
            ma = df['price'].rolling(self.lookback_period).mean()
            std = df['price'].rolling(self.lookback_period).std()
            
            # Current price and deviation
            current_price = df['price'].iloc[-1]
            current_ma = ma.iloc[-1]
            current_std = std.iloc[-1]
            
            # Calculate z-score
            z_score = (current_price - current_ma) / current_std
            
            # Generate signals for significant deviations
            if abs(z_score) > self.zscore_threshold:
                # Calculate reversion probability
                prob = self._calculate_reversion_probability(df, z_score)
                
                if prob > self.confidence_threshold:
                    signals.append(
                        MeanReversionSignal(
                            price=current_price,
                            mean=current_ma,
                            deviation=z_score,
                            probability=prob,
                            target=self._calculate_reversion_target(current_price, current_ma, z_score),
                            stop_loss=self._calculate_stop_loss(current_price, current_std, z_score),
                            timeframe=self._estimate_reversion_timeframe(z_score),
                            confidence=self._calculate_signal_confidence(df, z_score, prob)
                        )
                    )
            
            return signals
            
        except Exception as e:
            logger.error(f"Reversion signal generation error: {e}")
            return []
    
    def _calculate_oscillators(self, df: pd.DataFrame) -> OscillatorSignals:
        """Calculate technical oscillator signals."""
        try:
            # Calculate RSI
            rsi = self._calculate_rsi(df)
            
            # Calculate Stochastic
            stoch = self._calculate_stochastic(df)
            
            # Calculate CCI
            cci = self._calculate_cci(df)
            
            # Calculate MFI
            mfi = self._calculate_mfi(df)
            
            # Combine signals
            combined = self._combine_oscillator_signals(rsi, stoch, cci, mfi)
            
            # Calculate confidence
            confidence = self._calculate_oscillator_confidence(rsi, stoch, cci, mfi)
            
            return OscillatorSignals(
                rsi=rsi,
                stochastic=stoch,
                cci=cci,
                mfi=mfi,
                combined_signal=combined,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Oscillator calculation error: {e}")
            return OscillatorSignals(0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
    
    def _calculate_statistics(self, df: pd.DataFrame) -> StatisticalMetrics:
        """Calculate statistical metrics for mean reversion."""
        try:
            # Calculate basic statistics
            mean = df['price'].mean()
            std = df['price'].std()
            skew = stats.skew(df['price'])
            kurt = stats.kurtosis(df['price'])
            
            # Perform stationarity test
            adf_result = self._test_stationarity(df['price'])
            
            # Calculate z-score
            z_score = (df['price'].iloc[-1] - mean) / std
            
            # Calculate p-value
            p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            
            # Calculate confidence
            confidence = self._calculate_statistical_confidence(
                z_score, p_value, adf_result
            )
            
            return StatisticalMetrics(
                z_score=z_score,
                p_value=p_value,
                mean=mean,
                std_dev=std,
                skew=skew,
                kurtosis=kurt,
                is_stationary=adf_result[0],
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Statistical metrics calculation error: {e}")
            return StatisticalMetrics(0.0, 1.0, 0.0, 0.0, 0.0, 0.0, False, 0.0)
    
    def _calculate_bollinger_bands(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate Bollinger Bands."""
        try:
            # Calculate middle band (20-period SMA)
            middle = df['price'].rolling(20).mean().iloc[-1]
            
            # Calculate standard deviation
            std = df['price'].rolling(20).std().iloc[-1]
            
            # Calculate bands
            upper = middle + (2 * std)
            lower = middle - (2 * std)
            
            return {
                'upper': upper,
                'middle': middle,
                'lower': lower,
                'bandwidth': (upper - lower) / middle
            }
            
        except Exception as e:
            logger.error(f"Bollinger Bands calculation error: {e}")
            return {}
    
    def _generate_key_levels(
        self,
        df: pd.DataFrame,
        bollinger_bands: Dict[str, float]
    ) -> Dict[str, float]:
        """Generate key price levels for mean reversion."""
        try:
            current_price = df['price'].iloc[-1]
            
            levels = {
                'mean': df['price'].mean(),
                'upper_band': bollinger_bands.get('upper', current_price * 1.02),
                'lower_band': bollinger_bands.get('lower', current_price * 0.98),
                'recent_high': df['price'].rolling(20).max().iloc[-1],
                'recent_low': df['price'].rolling(20).min().iloc[-1]
            }
            
            return levels
            
        except Exception as e:
            logger.error(f"Key level generation error: {e}")
            return {}
    
    def _calculate_trade_parameters(
        self,
        df: pd.DataFrame,
        signals: List[MeanReversionSignal],
        statistics: StatisticalMetrics
    ) -> Dict[str, float]:
        """Calculate trade parameters for mean reversion."""
        try:
            # Get current signal if available
            current_signal = signals[0] if signals else None
            
            params = {
                'entry_zscore': statistics.z_score,
                'target_mean': statistics.mean,
                'stop_width': statistics.std_dev * 2,
                'position_size': self._calculate_position_size(statistics),
                'expected_duration': self._estimate_reversion_duration(statistics)
            }
            
            if current_signal:
                params.update({
                    'entry_price': current_signal.price,
                    'target_price': current_signal.target,
                    'stop_loss': current_signal.stop_loss
                })
            
            return params
            
        except Exception as e:
            logger.error(f"Trade parameter calculation error: {e}")
            return {}
    
    def _calculate_confidence_score(
        self,
        signals: List[MeanReversionSignal],
        oscillators: OscillatorSignals,
        statistics: StatisticalMetrics
    ) -> float:
        """Calculate overall confidence score."""
        try:
            # Confidence factors
            factors = [
                statistics.confidence > 0.7,  # Strong statistical evidence
                oscillators.confidence > 0.7,  # Strong oscillator signals
                any(s.confidence > 0.7 for s in signals),  # Valid signals
                statistics.is_stationary  # Price is stationary
            ]
            
            # Weight factors
            weights = [0.3, 0.3, 0.2, 0.2]
            confidence = sum(f * w for f, w in zip(factors, weights))
            
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Confidence score calculation error: {e}")
            return 0.0
    
    def _get_fallback_analysis(self) -> ReversionAnalysis:
        """Get fallback analysis when calculations fail."""
        return ReversionAnalysis(
            timestamp=datetime.now(),
            signals=[],
            oscillators=OscillatorSignals(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
            statistics=StatisticalMetrics(0.0, 1.0, 0.0, 0.0, 0.0, 0.0, False, 0.0),
            bollinger_bands={},
            key_levels={},
            trade_parameters={},
            confidence_score=0.0
        )
    
    # Helper methods
    def _calculate_reversion_probability(
        self,
        df: pd.DataFrame,
        z_score: float
    ) -> float:
        """Calculate probability of mean reversion."""
        try:
            # Use historical reversion frequency
            historical_reversions = self._count_historical_reversions(df, z_score)
            total_occurrences = self._count_zscore_occurrences(df, z_score)
            
            if total_occurrences > 0:
                return historical_reversions / total_occurrences
            return 0.0
        except:
            return 0.0
    
    def _calculate_reversion_target(
        self,
        current_price: float,
        mean: float,
        z_score: float
    ) -> float:
        """Calculate price target for reversion."""
        try:
            # Target partial reversion based on z-score
            reversion_factor = min(abs(z_score) / 4, 0.8)  # Max 80% reversion
            return current_price + ((mean - current_price) * reversion_factor)
        except:
            return current_price
    
    def _calculate_stop_loss(
        self,
        current_price: float,
        std_dev: float,
        z_score: float
    ) -> float:
        """Calculate stop loss for mean reversion trade."""
        try:
            # Use standard deviation-based stop
            stop_width = std_dev * (1 + abs(z_score) / 4)  # Wider stop for larger deviation
            
            if z_score > 0:  # Short trade
                return current_price + stop_width
            else:  # Long trade
                return current_price - stop_width
        except:
            return current_price
    
    def _estimate_reversion_timeframe(self, z_score: float) -> str:
        """Estimate expected timeframe for reversion."""
        try:
            # Larger deviations typically take longer to revert
            if abs(z_score) > 3:
                return "1d"
            elif abs(z_score) > 2:
                return "4h"
            else:
                return "1h"
        except:
            return "4h"
    
    def _calculate_signal_confidence(
        self,
        df: pd.DataFrame,
        z_score: float,
        probability: float
    ) -> float:
        """Calculate confidence in a reversion signal."""
        try:
            # Consider multiple factors
            zscore_factor = min(abs(z_score) / 4, 1.0)
            prob_factor = probability
            history_factor = self._analyze_historical_accuracy(df, z_score)
            
            # Weight the factors
            weights = [0.4, 0.4, 0.2]
            factors = [zscore_factor, prob_factor, history_factor]
            
            confidence = sum(f * w for f, w in zip(factors, weights))
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_rsi(self, df: pd.DataFrame) -> float:
        """Calculate Relative Strength Index."""
        try:
            # Calculate price changes
            delta = df['price'].diff()
            
            # Separate gains and losses
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            
            # Calculate RS and RSI
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1]
        except:
            return 50.0
    
    def _calculate_stochastic(self, df: pd.DataFrame) -> float:
        """Calculate Stochastic Oscillator."""
        try:
            # Calculate %K
            low_14 = df['price'].rolling(14).min()
            high_14 = df['price'].rolling(14).max()
            k = 100 * (df['price'] - low_14) / (high_14 - low_14)
            
            # Calculate %D (3-period SMA of %K)
            d = k.rolling(3).mean()
            
            return d.iloc[-1]
        except:
            return 50.0
    
    def _calculate_cci(self, df: pd.DataFrame) -> float:
        """Calculate Commodity Channel Index."""
        try:
            # Calculate typical price
            tp = df['price']
            
            # Calculate SMA of typical price
            sma = tp.rolling(20).mean()
            
            # Calculate mean deviation
            md = abs(tp - sma).rolling(20).mean()
            
            # Calculate CCI
            cci = (tp - sma) / (0.015 * md)
            
            return cci.iloc[-1]
        except:
            return 0.0
    
    def _calculate_mfi(self, df: pd.DataFrame) -> float:
        """Calculate Money Flow Index."""
        try:
            # Calculate typical price
            tp = df['price']
            
            # Calculate raw money flow
            rmf = tp * df['volume']
            
            # Separate positive and negative money flow
            positive_flow = (rmf.where(tp > tp.shift(1), 0)).rolling(14).sum()
            negative_flow = (rmf.where(tp < tp.shift(1), 0)).rolling(14).sum()
            
            # Calculate money ratio and MFI
            mr = positive_flow / negative_flow
            mfi = 100 - (100 / (1 + mr))
            
            return mfi.iloc[-1]
        except:
            return 50.0
    
    def _combine_oscillator_signals(
        self,
        rsi: float,
        stoch: float,
        cci: float,
        mfi: float
    ) -> float:
        """Combine oscillator signals into single signal."""
        try:
            # Normalize all signals to -1 to 1 scale
            norm_rsi = (rsi - 50) / 50
            norm_stoch = (stoch - 50) / 50
            norm_cci = cci / 100
            norm_mfi = (mfi - 50) / 50
            
            # Weight and combine
            weights = [0.3, 0.3, 0.2, 0.2]
            signals = [norm_rsi, norm_stoch, norm_cci, norm_mfi]
            
            return sum(s * w for s, w in zip(signals, weights))
        except:
            return 0.0
    
    def _calculate_oscillator_confidence(
        self,
        rsi: float,
        stoch: float,
        cci: float,
        mfi: float
    ) -> float:
        """Calculate confidence in oscillator signals."""
        try:
            # Check for agreement between oscillators
            signals = [
                rsi < 30 or rsi > 70,
                stoch < 20 or stoch > 80,
                abs(cci) > 100,
                mfi < 20 or mfi > 80
            ]
            
            # More agreement = higher confidence
            agreement = sum(signals) / len(signals)
            return agreement
        except:
            return 0.0
    
    def _test_stationarity(self, series: pd.Series) -> Tuple[bool, float]:
        """Test price series for stationarity."""
        try:
            # Perform Augmented Dickey-Fuller test
            adf_result = stats.adfuller(series)
            is_stationary = adf_result[1] < 0.05  # p-value < 0.05
            confidence = 1 - adf_result[1]  # Convert p-value to confidence
            
            return (is_stationary, confidence)
        except:
            return (False, 0.0)
    
    def _calculate_statistical_confidence(
        self,
        z_score: float,
        p_value: float,
        adf_result: Tuple[bool, float]
    ) -> float:
        """Calculate confidence in statistical analysis."""
        try:
            # Consider multiple factors
            zscore_factor = min(abs(z_score) / 4, 1.0)
            pvalue_factor = 1 - p_value
            stationary_factor = adf_result[1]
            
            # Weight the factors
            weights = [0.4, 0.3, 0.3]
            factors = [zscore_factor, pvalue_factor, stationary_factor]
            
            confidence = sum(f * w for f, w in zip(factors, weights))
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_position_size(self, statistics: StatisticalMetrics) -> float:
        """Calculate suggested position size."""
        try:
            # Reduce size for less confident setups
            base_size = 1.0
            
            # Adjust for statistical confidence
            if statistics.confidence < 0.7:
                base_size *= 0.5
            
            # Adjust for non-stationarity
            if not statistics.is_stationary:
                base_size *= 0.75
            
            return base_size
        except:
            return 0.5
    
    def _estimate_reversion_duration(self, statistics: StatisticalMetrics) -> int:
        """Estimate number of periods for reversion."""
        try:
            # Base duration on z-score
            base_duration = int(abs(statistics.z_score) * 5)  # 5 periods per std dev
            
            # Adjust for market conditions
            if not statistics.is_stationary:
                base_duration *= 1.5
            
            return max(base_duration, 5)  # Minimum 5 periods
        except:
            return 10
    
    def _count_historical_reversions(
        self,
        df: pd.DataFrame,
        z_score: float
    ) -> int:
        """Count historical mean reversions."""
        try:
            # Calculate historical z-scores
            ma = df['price'].rolling(self.lookback_period).mean()
            std = df['price'].rolling(self.lookback_period).std()
            z_scores = (df['price'] - ma) / std
            
            # Count reversions
            reversions = 0
            for i in range(self.lookback_period, len(df)-1):
                if (abs(z_scores[i]) > abs(z_score) and
                    abs(z_scores[i+1]) < abs(z_score)):
                    reversions += 1
            
            return reversions
        except:
            return 0
    
    def _count_zscore_occurrences(
        self,
        df: pd.DataFrame,
        z_score: float
    ) -> int:
        """Count occurrences of similar z-scores."""
        try:
            # Calculate historical z-scores
            ma = df['price'].rolling(self.lookback_period).mean()
            std = df['price'].rolling(self.lookback_period).std()
            z_scores = (df['price'] - ma) / std
            
            # Count occurrences
            return len(z_scores[abs(z_scores) > abs(z_score)])
        except:
            return 0
    
    def _analyze_historical_accuracy(
        self,
        df: pd.DataFrame,
        z_score: float
    ) -> float:
        """Analyze historical accuracy of similar signals."""
        try:
            reversions = self._count_historical_reversions(df, z_score)
            occurrences = self._count_zscore_occurrences(df, z_score)
            
            if occurrences > 0:
                return reversions / occurrences
            return 0.0
        except:
            return 0.0 