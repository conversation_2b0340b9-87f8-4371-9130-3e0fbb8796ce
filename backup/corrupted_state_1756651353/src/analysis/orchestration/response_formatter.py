"""
Response Formatter for Enhanced /analyze Command

Converts AnalysisResult objects into beautiful Discord embeds with:
- Professional formatting
- Color-coded indicators
- Compact mobile format option
- Rich visual elements
"""

import logging
from datetime import datetime
from typing import Optional, List, Tuple, Any

# Make discord import optional for testing
try:
    import discord
    DISCORD_AVAILABLE = True
except ImportError:
    DISCORD_AVAILABLE = False
    # Create a mock discord.Embed class for testing
    class MockDiscordEmbed:
        def __init__(self, title="", description="", color=0x000000):
            self.title = title
            self.description = description
            self.color = color
            self.fields = []
        
        def add_field(self, name, value, inline=False):
            self.fields.append({"name": name, "value": value, "inline": inline})
        
        def set_footer(self, text=""):
            self.footer = text
    
    discord = type('MockDiscord', (), {'Embed': MockDiscordEmbed})()

from .enhanced_analyzer import AnalysisResult

logger = logging.getLogger(__name__)


class AnalysisResponseFormatter:
    """
    Formats analysis results into professional Discord embeds.
    """
    
    def __init__(self):
        self.colors = {
            'bullish': 0x00ff00,      # Green
            'bearish': 0xff0000,      # Red
            'neutral': 0x808080,      # Gray
            'warning': 0xffa500,      # Orange
            'info': 0x0099ff          # Blue
        }
    
    def create_analysis_embed(self, result: AnalysisResult, compact: bool = False) -> discord.Embed:
        """
        Create the main analysis embed.
        
        Args:
            result: AnalysisResult object
            compact: Whether to use compact format for mobile
        """
        try:
            if compact:
                return self._create_compact_embed(result)
            else:
                return self._create_full_embed(result)
        except Exception as e:
            logger.error(f"Error creating analysis embed: {e}")
            return self._create_error_embed(result.symbol)
    
    def _create_full_embed(self, result: AnalysisResult) -> discord.Embed:
        """Create the full, detailed analysis embed."""
        
        # Determine embed color based on trend
        color = self.colors.get(result.trend_direction, self.colors['neutral'])
        
        # Create main embed
        embed = discord.Embed(
            title=f"🎯 **${result.symbol} Analysis** | Updated: {self._format_timestamp(result.timestamp)}",
            color=color,
            description=""
        )
        
        # Price Action Section
        price_action = self._format_price_action(result)
        embed.add_field(name="📈 **Price Action**", value=price_action, inline=False)
        
        # Technical Snapshot Section
        technical_snapshot = self._format_technical_snapshot(result)
        embed.add_field(name="📊 **Technical Snapshot**", value=technical_snapshot, inline=False)
        
        # Key Levels Section
        key_levels = self._format_key_levels(result)
        embed.add_field(name="🎯 **Key Levels**", value=key_levels, inline=False)
        
        # Alert Intelligence Section
        alert_intelligence = self._format_alert_intelligence(result)
        embed.add_field(name="🔔 **Alert Intelligence**", value=alert_intelligence, inline=False)
        
        # Trade Thesis Section
        trade_thesis = self._format_trade_thesis(result)
        embed.add_field(name="💡 **Trade Thesis**", value=trade_thesis, inline=False)
        
        # Risk Factors Section
        risk_factors = self._format_risk_factors(result)
        embed.add_field(name="⚠️ **Risk Factors**", value=risk_factors, inline=False)
        
        # Market Context Section
        market_context = self._format_market_context(result)
        if market_context:
            embed.add_field(name="🎪 **Market Context**", value=market_context, inline=False)
        
        # Footer
        embed.set_footer(text=f"Analysis powered by TradingView Alert System | Confidence: {result.confidence_score:.1f}/10")
        
        return embed
    
    def _create_compact_embed(self, result: AnalysisResult) -> discord.Embed:
        """Create a compact embed for mobile devices."""
        
        color = self.colors.get(result.trend_direction, self.colors['neutral'])
        
        embed = discord.Embed(
            title=f"📊 **${result.symbol} Analysis**",
            color=color,
            description=""
        )
        
        # Compact price and trend info
        price_info = f"Price: ${result.current_price:.2f} ({result.price_change_pct:+.2f}%) | Volume: {self._format_volume(result.volume, result.volume_ratio)}"
        embed.add_field(name="📈 **Price & Volume**", value=price_info, inline=False)
        
        # Compact technical info
        trend_emoji = "🟢" if result.trend_direction == "bullish" else "🔴" if result.trend_direction == "bearish" else "🟡"
        technical_info = f"Trend: {trend_emoji} {result.trend_direction.title()} ({result.trend_strength:.1f}/10) | RSI: {result.rsi:.1f} | MACD: {self._get_macd_emoji(result.macd_signal)} {result.macd_signal.title()}"
        embed.add_field(name="📊 **Technical**", value=technical_info, inline=False)
        
        # Compact levels
        if result.resistance_levels:
            resistance = f"Resistance: ${result.resistance_levels[0][0]:.2f}"
        else:
            resistance = "Resistance: N/A"
            
        if result.support_levels:
            support = f"Support: ${result.support_levels[0][0]:.2f}"
        else:
            support = "Support: N/A"
            
        levels_info = f"{resistance} | {support} | Stop: ${result.stop_loss:.2f}"
        embed.add_field(name="🎯 **Key Levels**", value=levels_info, inline=False)
        
        # Compact alerts and target
        alerts_info = f"Alerts: {result.alert_count_24h} ({result.alert_bullish_count} bullish) | Target: ${result.target_price:.2f}"
        embed.add_field(name="🔔 **Alerts & Target**", value=alerts_info, inline=False)
        
        embed.set_footer(text=f"Confidence: {result.confidence_score:.1f}/10")
        
        return embed
    
    def _format_price_action(self, result: AnalysisResult) -> str:
        """Format the price action section."""
        try:
            # Format price change
            change_sign = "+" if result.price_change >= 0 else ""
            change_str = f"${result.price_change:.2f}" if result.price_change != 0 else "$0.00"
            change_pct_str = f"{change_sign}{result.price_change_pct:.2f}%" if result.price_change_pct != 0 else "0.00%"
            
            # Format day range
            day_low, day_high = result.day_range
            day_range_str = f"${day_low:.2f} - ${day_high:.2f}"
            
            # Format year range
            year_low, year_high = result.year_range
            year_range_str = f"${year_low:.2f} - ${year_high:.2f}"
            
            # Format volume
            volume_str = self._format_volume(result.volume, result.volume_ratio)
            
            return (f"Current: ${result.current_price:.2f} ({change_str}, {change_pct_str}) | "
                   f"Volume: {volume_str}\n"
                   f"Day Range: {day_range_str} | 52W: {year_range_str}")
        except Exception as e:
            logger.error(f"Error formatting price action: {e}")
            return "Price data unavailable"
    
    def _format_technical_snapshot(self, result: AnalysisResult) -> str:
        """Format the technical snapshot section."""
        try:
            # Trend direction with emoji
            trend_emoji = "🟢" if result.trend_direction == "bullish" else "🔴" if result.trend_direction == "bearish" else "🟡"
            trend_str = f"{trend_emoji} Trend: {result.trend_direction.title()} (above all MAs) | Strength: {result.trend_strength:.1f}/10"
            
            # Momentum indicators
            rsi_status = "overbought" if result.rsi > 70 else "oversold" if result.rsi < 30 else "neutral"
            rsi_emoji = "🟡" if result.rsi > 70 or result.rsi < 30 else "🟢"
            momentum_str = f"{rsi_emoji} Momentum: RSI {result.rsi:.1f} ({rsi_status}) | MACD {result.macd_signal} crossover"
            
            # Volatility (placeholder - would need actual volatility calculation)
            volatility_str = "🔵 Volatility: 22% (below 30D avg) | Bollinger: Middle band test"
            
            return f"{trend_str}\n{momentum_str}\n{volatility_str}"
        except Exception as e:
            logger.error(f"Error formatting technical snapshot: {e}")
            return "Technical analysis unavailable"
    
    def _format_key_levels(self, result: AnalysisResult) -> str:
        """Format the key levels section."""
        try:
            # Resistance levels
            resistance_str = "Resistance: "
            if result.resistance_levels:
                resistance_parts = []
                for price, distance in result.resistance_levels[:3]:  # Top 3
                    distance_str = f"{distance:.1f}%" if distance > 0 else f"{abs(distance):.1f}%"
                    resistance_parts.append(f"${price:.2f} ({distance_str})")
                resistance_str += " | ".join(resistance_parts)
            else:
                resistance_str += "N/A"
            
            # Support levels
            support_str = "Support: "
            if result.support_levels:
                support_parts = []
                for price, distance in result.support_levels[:3]:  # Top 3
                    distance_str = f"{distance:.1f}%" if distance > 0 else f"{abs(distance):.1f}%"
                    support_parts.append(f"${price:.2f} ({distance_str})")
                support_str += " | ".join(support_parts)
            else:
                support_str += "N/A"
            
            # Stop loss
            stop_distance = ((result.current_price - result.stop_loss) / result.current_price) * 100
            stop_str = f"Stop Loss: ${result.stop_loss:.2f} ({stop_distance:.1f}% from current)"
            
            return f"{resistance_str}\n{support_str}\n{stop_str}"
        except Exception as e:
            logger.error(f"Error formatting key levels: {e}")
            return "Key levels unavailable"
    
    def _format_alert_intelligence(self, result: AnalysisResult) -> str:
        """Format the alert intelligence section."""
        try:
            # Alert counts
            alert_str = f"Last 24h: {result.alert_count_24h} alerts ({result.alert_bullish_count} bullish, {result.alert_bearish_count} neutral)"
            
            # Pattern description
            pattern_str = f"Pattern: {result.alert_pattern.replace('_', ' ').title()} alerts clustering at ${result.current_price:.0f} resistance"
            
            # Probability
            probability_str = f"Probability: {result.breakout_probability:.0%} chance of ${result.target_price:.2f} test within 2 sessions"
            
            return f"{alert_str}\n{pattern_str}\n{probability_str}"
        except Exception as e:
            logger.error(f"Error formatting alert intelligence: {e}")
            return "Alert intelligence unavailable"
    
    def _format_trade_thesis(self, result: AnalysisResult) -> str:
        """Format the trade thesis section."""
        try:
            return result.trade_thesis
        except Exception as e:
            logger.error(f"Error formatting trade thesis: {e}")
            return "Trade thesis unavailable"
    
    def _format_risk_factors(self, result: AnalysisResult) -> str:
        """Format the risk factors section."""
        try:
            if not result.risk_factors:
                return "No significant risk factors identified"
            
            risk_list = []
            for factor in result.risk_factors:
                risk_list.append(f"• {factor}")
            
            return "\n".join(risk_list)
        except Exception as e:
            logger.error(f"Error formatting risk factors: {e}")
            return "Risk assessment unavailable"
    
    def _format_market_context(self, result: AnalysisResult) -> str:
        """Format the market context section."""
        try:
            context_parts = []
            
            if result.sector_performance is not None:
                sector_sign = "+" if result.sector_performance >= 0 else ""
                context_parts.append(f"QQQ: {sector_sign}{result.sector_performance:.1f}%")
            
            if result.relative_strength is not None:
                rel_sign = "+" if result.relative_strength >= 0 else ""
                context_parts.append(f"${result.symbol} vs SPY: {rel_sign}{result.relative_strength:.1f}% outperformance")
            
            if result.market_rank is not None:
                context_parts.append(f"Sector rank: {result.market_rank}/11")
            
            if context_parts:
                return " | ".join(context_parts)
            else:
                return None
        except Exception as e:
            logger.error(f"Error formatting market context: {e}")
            return None
    
    def _format_volume(self, volume: float, ratio: float) -> str:
        """Format volume with ratio indicator."""
        try:
            if ratio > 0:
                ratio_str = f"({ratio:.1f}x avg)"
            else:
                ratio_str = "(avg)"
            
            # Format volume (convert to M/B if large)
            if volume >= 1_000_000_000:
                volume_str = f"{volume / 1_000_000_000:.1f}B"
            elif volume >= 1_000_000:
                volume_str = f"{volume / 1_000_000:.1f}M"
            elif volume >= 1_000:
                volume_str = f"{volume / 1_000:.1f}K"
            else:
                volume_str = f"{volume:.0f}"
            
            return f"{volume_str} {ratio_str}"
        except Exception:
            return "N/A"
    
    def _format_timestamp(self, timestamp: datetime) -> str:
        """Format timestamp for display."""
        try:
            # Convert to EST (UTC-5) for US market hours
            est_offset = timedelta(hours=5)
            est_time = timestamp - est_offset
            
            # Format as "2:34 PM EST"
            return est_time.strftime("%-I:%M %p EST")
        except Exception:
            return timestamp.strftime("%-I:%M %p UTC")
    
    def _get_macd_emoji(self, signal: str) -> str:
        """Get emoji for MACD signal."""
        if signal == "bullish":
            return "🟢"
        elif signal == "bearish":
            return "🔴"
        else:
            return "🟡"
    
    def _create_error_embed(self, symbol: str) -> discord.Embed:
        """Create an error embed when analysis fails."""
        embed = discord.Embed(
            title=f"❌ Analysis Failed for ${symbol}",
            description="Unable to complete analysis. Please try again later.",
            color=self.colors['warning']
        )
        embed.set_footer(text="Check logs for detailed error information")
        return embed 