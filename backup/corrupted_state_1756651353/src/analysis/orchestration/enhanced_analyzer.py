"""
Enhanced Analyzer for /analyze Command

Provides comprehensive stock analysis using all available data from:
- TradingView alert system database
- Watchlist data
- Historical market data
- Alert patterns and history
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio

from src.database.models import MarketData, MarketAlert, AlertHistory, SymbolMetadata
from src.core.watchlist.watchlist_manager import WatchlistManager
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.analysis.technical.calculators import RSICal<PERSON>tor, MACDCalculator
from src.analysis.technical.indicators import calculate_moving_averages

logger = logging.getLogger(__name__)


@dataclass
class AnalysisResult:
    """Comprehensive analysis result for a ticker."""
    symbol: str
    timestamp: datetime
    
    # Price Action
    current_price: float
    price_change: float
    price_change_pct: float
    day_range: Tuple[float, float]
    year_range: Tuple[float, float]
    volume: float
    volume_avg: float
    volume_ratio: float
    
    # Technical Analysis
    trend_direction: str  # "bullish", "bearish", "neutral"
    trend_strength: float  # 1-10 scale
    rsi: float
    macd_signal: str
    macd_histogram: float
    moving_averages: Dict[str, float]
    above_mas: List[str]
    
    # Key Levels
    resistance_levels: List[Tuple[float, float]]  # (price, distance_pct)
    support_levels: List[Tuple[float, float]]
    stop_loss: float
    
    # Alert Intelligence
    alert_count_24h: int
    alert_bullish_count: int
    alert_bearish_count: int
    alert_pattern: str
    breakout_probability: float
    
    # Risk Assessment
    risk_factors: List[str]
    risk_score: float  # 1-10 scale
    
    # Market Context
    sector_performance: Optional[float]
    relative_strength: Optional[float]
    market_rank: Optional[int]
    
    # Trade Thesis
    trade_thesis: str
    target_price: float
    confidence_score: float


class EnhancedAnalyzer:
    """
    Comprehensive stock analyzer that uses all available data sources.
    """
    
    def __init__(self):
        self.data_aggregator = DataProviderAggregator()
        self.rsi_calculator = RSICalculator()
        self.macd_calculator = MACDCalculator()
        
    async def analyze_ticker(self, symbol: str, user_id: str) -> AnalysisResult:
        """
        Perform comprehensive analysis of a ticker using all available data.
        """
        try:
            logger.info(f"Starting comprehensive analysis for {symbol}")
            
            # 1. Collect all available data
            market_data = await self._collect_market_data(symbol)
            watchlist_data = await self._collect_watchlist_data(symbol, user_id)
            alert_data = await self._collect_alert_data(symbol, user_id)
            technical_data = await self._collect_technical_data(symbol)
            
            # 2. Perform technical analysis
            technical_analysis = await self._perform_technical_analysis(
                symbol, market_data, technical_data
            )
            
            # 3. Analyze alert patterns
            alert_intelligence = await self._analyze_alert_patterns(
                symbol, alert_data
            )
            
            # 4. Generate key levels
            key_levels = await self._generate_key_levels(
                symbol, market_data, technical_data, alert_data
            )
            
            # 5. Assess risk factors
            risk_assessment = await self._assess_risk_factors(
                symbol, market_data, technical_analysis, alert_data
            )
            
            # 6. Generate trade thesis
            trade_thesis = await self._generate_trade_thesis(
                symbol, technical_analysis, alert_intelligence, key_levels
            )
            
            # 7. Compile final result
            result = AnalysisResult(
                symbol=symbol.upper(),
                timestamp=datetime.now(),
                
                # Price Action
                current_price=market_data.get('current_price', 0),
                price_change=market_data.get('price_change', 0),
                price_change_pct=market_data.get('price_change_pct', 0),
                day_range=market_data.get('day_range', (0, 0)),
                year_range=market_data.get('year_range', (0, 0)),
                volume=market_data.get('volume', 0),
                volume_avg=market_data.get('volume_avg', 0),
                volume_ratio=market_data.get('volume_ratio', 0),
                
                # Technical Analysis
                trend_direction=technical_analysis.get('trend_direction', 'neutral'),
                trend_strength=technical_analysis.get('trend_strength', 5.0),
                rsi=technical_analysis.get('rsi', 50.0),
                macd_signal=technical_analysis.get('macd_signal', 'neutral'),
                macd_histogram=technical_analysis.get('macd_histogram', 0.0),
                moving_averages=technical_analysis.get('moving_averages', {}),
                above_mas=technical_analysis.get('above_mas', []),
                
                # Key Levels
                resistance_levels=key_levels.get('resistance', []),
                support_levels=key_levels.get('support', []),
                stop_loss=key_levels.get('stop_loss', 0),
                
                # Alert Intelligence
                alert_count_24h=alert_intelligence.get('count_24h', 0),
                alert_bullish_count=alert_intelligence.get('bullish_count', 0),
                alert_bearish_count=alert_intelligence.get('bearish_count', 0),
                alert_pattern=alert_intelligence.get('pattern', 'none'),
                breakout_probability=alert_intelligence.get('breakout_probability', 0.0),
                
                # Risk Assessment
                risk_factors=risk_assessment.get('factors', []),
                risk_score=risk_assessment.get('score', 5.0),
                
                # Market Context
                sector_performance=market_data.get('sector_performance'),
                relative_strength=market_data.get('relative_strength'),
                market_rank=market_data.get('market_rank'),
                
                # Trade Thesis
                trade_thesis=trade_thesis.get('thesis', ''),
                target_price=trade_thesis.get('target_price', 0),
                confidence_score=trade_thesis.get('confidence', 0.0)
            )
            
            logger.info(f"Analysis completed for {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}", exc_info=True)
            raise
    
    async def _collect_market_data(self, symbol: str) -> Dict[str, Any]:
        """Collect current market data from all available sources."""
        try:
            # Get real-time data from data providers
            real_time_data = await self.data_aggregator.get_real_time_data(symbol)
            
            # Get historical data for calculations
            historical_data = await self.data_aggregator.get_historical_data(
                symbol, period="1y", interval="1d"
            )
            
            # Calculate additional metrics
            volume_avg = self._calculate_average_volume(historical_data)
            volume_ratio = real_time_data.get('volume', 0) / volume_avg if volume_avg > 0 else 0
            
            return {
                'current_price': real_time_data.get('price', 0),
                'price_change': real_time_data.get('change', 0),
                'price_change_pct': real_time_data.get('change_pct', 0),
                'day_range': (real_time_data.get('low', 0), real_time_data.get('high', 0)),
                'year_range': self._get_year_range(historical_data),
                'volume': real_time_data.get('volume', 0),
                'volume_avg': volume_avg,
                'volume_ratio': volume_ratio,
                'sector_performance': real_time_data.get('sector_performance'),
                'relative_strength': real_time_data.get('relative_strength'),
                'market_rank': real_time_data.get('market_rank')
            }
        except Exception as e:
            logger.error(f"Error collecting market data for {symbol}: {e}")
            return {}
    
    async def _collect_watchlist_data(self, symbol: str, user_id: str) -> Dict[str, Any]:
        """Collect watchlist-specific data for the symbol."""
        try:
            # This would integrate with your watchlist system
            # For now, return basic structure
            return {
                'in_watchlist': True,
                'priority': 'medium',
                'notes': '',
                'last_analysis': None
            }
        except Exception as e:
            logger.error(f"Error collecting watchlist data for {symbol}: {e}")
            return {}
    
    async def _collect_alert_data(self, symbol: str, user_id: str) -> Dict[str, Any]:
        """Collect alert history and patterns for the symbol."""
        try:
            # This would query your TradingView alert database
            # For now, return mock data structure
            return {
                'alerts_24h': [],
                'alert_history': [],
                'alert_patterns': []
            }
        except Exception as e:
            logger.error(f"Error collecting alert data for {symbol}: {e}")
            return {}
    
    async def _collect_technical_data(self, symbol: str) -> Dict[str, Any]:
        """Collect technical indicator data."""
        try:
            # Get historical data for technical calculations
            historical_data = await self.data_aggregator.get_historical_data(
                symbol, period="6m", interval="1d"
            )
            
            return {
                'historical_prices': historical_data.get('prices', []),
                'historical_volumes': historical_data.get('volumes', []),
                'historical_dates': historical_data.get('dates', [])
            }
        except Exception as e:
            logger.error(f"Error collecting technical data for {symbol}: {e}")
            return {}
    
    async def _perform_technical_analysis(self, symbol: str, market_data: Dict, technical_data: Dict) -> Dict[str, Any]:
        """Perform comprehensive technical analysis."""
        try:
            prices = technical_data.get('historical_prices', [])
            if not prices:
                return {'trend_direction': 'neutral', 'trend_strength': 5.0}
            
            # Calculate RSI
            rsi = self.rsi_calculator.calculate(prices)
            
            # Calculate MACD
            macd_result = self.macd_calculator.calculate(prices)
            
            # Calculate moving averages
            mas = calculate_moving_averages(prices, [20, 50, 200])
            current_price = market_data.get('current_price', 0)
            
            # Determine trend direction and strength
            above_mas = [period for period, ma in mas.items() if current_price > ma]
            trend_direction = self._determine_trend_direction(current_price, mas, rsi)
            trend_strength = self._calculate_trend_strength(current_price, mas, rsi, macd_result)
            
            return {
                'trend_direction': trend_direction,
                'trend_strength': trend_strength,
                'rsi': rsi,
                'macd_signal': macd_result.get('signal', 'neutral'),
                'macd_histogram': macd_result.get('histogram', 0.0),
                'moving_averages': mas,
                'above_mas': above_mas
            }
        except Exception as e:
            logger.error(f"Error performing technical analysis for {symbol}: {e}")
            return {'trend_direction': 'neutral', 'trend_strength': 5.0}
    
    async def _analyze_alert_patterns(self, symbol: str, alert_data: Dict) -> Dict[str, Any]:
        """Analyze alert patterns and generate intelligence."""
        try:
            alerts_24h = alert_data.get('alerts_24h', [])
            alert_history = alert_data.get('alert_history', [])
            
            # Count alert types
            bullish_count = len([a for a in alerts_24h if a.get('sentiment') == 'bullish'])
            bearish_count = len([a for a in alerts_24h if a.get('sentiment') == 'bearish'])
            
            # Analyze patterns
            pattern = self._identify_alert_pattern(alerts_24h, alert_history)
            breakout_probability = self._calculate_breakout_probability(alerts_24h, pattern)
            
            return {
                'count_24h': len(alerts_24h),
                'bullish_count': bullish_count,
                'bearish_count': bearish_count,
                'pattern': pattern,
                'breakout_probability': breakout_probability
            }
        except Exception as e:
            logger.error(f"Error analyzing alert patterns for {symbol}: {e}")
            return {'count_24h': 0, 'bullish_count': 0, 'bearish_count': 0, 'pattern': 'none', 'breakout_probability': 0.0}
    
    async def _generate_key_levels(self, symbol: str, market_data: Dict, technical_data: Dict, alert_data: Dict) -> Dict[str, Any]:
        """Generate support, resistance, and stop loss levels."""
        try:
            current_price = market_data.get('current_price', 0)
            if current_price == 0:
                return {'resistance': [], 'support': [], 'stop_loss': 0}
            
            # Get moving averages for support/resistance
            mas = technical_data.get('moving_averages', {})
            
            # Generate resistance levels (above current price)
            resistance_levels = []
            if mas.get('20'):
                resistance_levels.append((mas['20'], ((mas['20'] - current_price) / current_price) * 100))
            if mas.get('50'):
                resistance_levels.append((mas['50'], ((mas['50'] - current_price) / current_price) * 100))
            
            # Generate support levels (below current price)
            support_levels = []
            if mas.get('20'):
                support_levels.append((mas['20'], ((current_price - mas['20']) / current_price) * 100))
            if mas.get('50'):
                support_levels.append((mas['50'], ((current_price - mas['50']) / current_price) * 100))
            
            # Calculate stop loss (below key support)
            stop_loss = min([level[0] for level in support_levels]) if support_levels else current_price * 0.95
            
            return {
                'resistance': sorted(resistance_levels, key=lambda x: x[0]),
                'support': sorted(support_levels, key=lambda x: x[0], reverse=True),
                'stop_loss': stop_loss
            }
        except Exception as e:
            logger.error(f"Error generating key levels for {symbol}: {e}")
            return {'resistance': [], 'support': [], 'stop_loss': 0}
    
    async def _assess_risk_factors(self, symbol: str, market_data: Dict, technical_analysis: Dict, alert_data: Dict) -> Dict[str, Any]:
        """Assess risk factors and generate risk score."""
        try:
            risk_factors = []
            risk_score = 5.0  # Base risk score
            
            # RSI overbought/oversold
            rsi = technical_analysis.get('rsi', 50)
            if rsi > 70:
                risk_factors.append("RSI overbought (>70)")
                risk_score += 1.5
            elif rsi < 30:
                risk_factors.append("RSI oversold (<30)")
                risk_score += 1.0
            
            # High volatility
            if market_data.get('volume_ratio', 0) > 2.0:
                risk_factors.append("High volume volatility")
                risk_score += 1.0
            
            # Trend weakness
            trend_strength = technical_analysis.get('trend_strength', 5.0)
            if trend_strength < 4.0:
                risk_factors.append("Weak trend strength")
                risk_score += 1.5
            
            # Alert patterns
            if alert_data.get('alert_count_24h', 0) > 10:
                risk_factors.append("High alert frequency")
                risk_score += 0.5
            
            # Normalize risk score to 1-10 range
            risk_score = max(1.0, min(10.0, risk_score))
            
            return {
                'factors': risk_factors,
                'score': risk_score
            }
        except Exception as e:
            logger.error(f"Error assessing risk factors for {symbol}: {e}")
            return {'factors': [], 'score': 5.0}
    
    async def _generate_trade_thesis(self, symbol: str, technical_analysis: Dict, alert_intelligence: Dict, key_levels: Dict) -> Dict[str, Any]:
        """Generate actionable trade thesis."""
        try:
            trend_direction = technical_analysis.get('trend_direction', 'neutral')
            trend_strength = technical_analysis.get('trend_strength', 5.0)
            breakout_probability = alert_intelligence.get('breakout_probability', 0.0)
            
            # Generate thesis based on analysis
            if trend_direction == 'bullish' and trend_strength > 6.0:
                thesis = f"{symbol} showing strong bullish momentum with {trend_strength}/10 trend strength. "
                if breakout_probability > 0.6:
                    thesis += f"High probability ({breakout_probability:.0%}) of continued upside."
                target_price = key_levels.get('resistance', [(0, 0)])[0][0] if key_levels.get('resistance') else 0
                confidence = min(9.0, trend_strength + (breakout_probability * 3))
            elif trend_direction == 'bearish' and trend_strength > 6.0:
                thesis = f"{symbol} showing bearish pressure with {trend_strength}/10 trend strength. "
                thesis += "Consider defensive positioning or short opportunities."
                target_price = key_levels.get('support', [(0, 0)])[0][0] if key_levels.get('support') else 0
                confidence = min(9.0, trend_strength)
            else:
                thesis = f"{symbol} showing neutral to mixed signals. Monitor for clearer directional bias."
                target_price = 0
                confidence = 5.0
            
            return {
                'thesis': thesis,
                'target_price': target_price,
                'confidence': confidence
            }
        except Exception as e:
            logger.error(f"Error generating trade thesis for {symbol}: {e}")
            return {'thesis': 'Analysis incomplete', 'target_price': 0, 'confidence': 0.0}
    
    def _calculate_average_volume(self, historical_data: Dict) -> float:
        """Calculate average volume from historical data."""
        try:
            volumes = historical_data.get('volumes', [])
            if not volumes:
                return 0
            return sum(volumes) / len(volumes)
        except Exception:
            return 0
    
    def _get_year_range(self, historical_data: Dict) -> Tuple[float, float]:
        """Get 52-week high/low range."""
        try:
            prices = historical_data.get('prices', [])
            if not prices:
                return (0, 0)
            return (min(prices), max(prices))
        except Exception:
            return (0, 0)
    
    def _determine_trend_direction(self, current_price: float, mas: Dict, rsi: float) -> str:
        """Determine overall trend direction."""
        try:
            if not mas:
                return 'neutral'
            
            # Check if price is above key moving averages
            above_20 = current_price > mas.get('20', 0)
            above_50 = current_price > mas.get('50', 0)
            above_200 = current_price > mas.get('200', 0)
            
            # RSI confirmation
            rsi_bullish = rsi > 50
            rsi_bearish = rsi < 50
            
            if above_20 and above_50 and above_200 and rsi_bullish:
                return 'bullish'
            elif not above_20 and not above_50 and not above_200 and rsi_bearish:
                return 'bearish'
            else:
                return 'neutral'
        except Exception:
            return 'neutral'
    
    def _calculate_trend_strength(self, current_price: float, mas: Dict, rsi: float, macd: Dict) -> float:
        """Calculate trend strength on a 1-10 scale."""
        try:
            strength = 5.0  # Base strength
            
            # Moving average alignment
            if current_price > mas.get('20', 0):
                strength += 1.0
            if current_price > mas.get('50', 0):
                strength += 1.0
            if current_price > mas.get('200', 0):
                strength += 1.0
            
            # RSI strength
            if 40 <= rsi <= 60:
                strength += 0.5  # Neutral
            elif 30 <= rsi <= 70:
                strength += 1.0  # Good momentum
            elif rsi < 30 or rsi > 70:
                strength += 1.5  # Strong momentum
            
            # MACD confirmation
            if macd.get('signal') == 'bullish':
                strength += 0.5
            elif macd.get('signal') == 'bearish':
                strength -= 0.5
            
            return max(1.0, min(10.0, strength))
        except Exception:
            return 5.0
    
    def _identify_alert_pattern(self, alerts_24h: List, alert_history: List) -> str:
        """Identify patterns in alert activity."""
        try:
            if not alerts_24h:
                return 'none'
            
            # Simple pattern detection
            bullish_count = len([a for a in alerts_24h if a.get('sentiment') == 'bullish'])
            bearish_count = len([a for a in alerts_24h if a.get('sentiment') == 'bearish'])
            
            if bullish_count > bearish_count * 2:
                return 'bullish_cluster'
            elif bearish_count > bullish_count * 2:
                return 'bearish_cluster'
            elif len(alerts_24h) > 5:
                return 'high_activity'
            else:
                return 'normal'
        except Exception:
            return 'none'
    
    def _calculate_breakout_probability(self, alerts_24h: List, pattern: str) -> float:
        """Calculate probability of breakout based on alert patterns."""
        try:
            base_probability = 0.5
            
            if pattern == 'bullish_cluster':
                base_probability += 0.2
            elif pattern == 'bearish_cluster':
                base_probability -= 0.1
            elif pattern == 'high_activity':
                base_probability += 0.1
            
            # Adjust based on alert count
            if len(alerts_24h) > 10:
                base_probability += 0.1
            
            return max(0.0, min(1.0, base_probability))
        except Exception:
            return 0.5 