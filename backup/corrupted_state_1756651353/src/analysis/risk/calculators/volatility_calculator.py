from dataclasses import dataclass
import logging
from typing import Dict, List, Optional

import numpy as np
import pandas as pd


logger = logging.getLogger(__name__)

@dataclass
class VolatilityAnalysis:
    """Data class for volatility analysis results"""
    daily_volatility: Optional[float]
    annualized_volatility: Optional[float]
    volatility_percentile: Optional[float]
    risk_level: str
    confidence: float

class VolatilityCalculator:
    """Calculator for stock price volatility analysis"""
    
    def __init__(self):
        self.trading_days_per_year = 252  # Approximate trading days in a year
    
    def calculate_daily_volatility(self, prices: List[float]) -> Optional[float]:
        """
        Calculate daily volatility (standard deviation of daily returns)
        
        Args:
            prices: List of daily closing prices in chronological order
            
        Returns:
            Daily volatility as decimal or None if insufficient data
        """
        if len(prices) < 2:
            return None
            
        try:
            # Calculate daily returns
            returns = []
            for i in range(1, len(prices)):
                if prices[i-1] != 0:  # Avoid division by zero
                    daily_return = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(daily_return)
            
            if len(returns) < 1:
                return None
                
            # Calculate standard deviation of returns
            volatility = np.std(returns)
            return volatility
            
        except Exception as e:
            logger.error(f"Error calculating daily volatility: {e}")
            return None
    
    def calculate_annualized_volatility(self, prices: List[float]) -> Optional[float]:
        """
        Calculate annualized volatility
        
        Args:
            prices: List of daily closing prices
            
        Returns:
            Annualized volatility as decimal
        """
        daily_vol = self.calculate_daily_volatility(prices)
        if daily_vol is None:
            return None
            
        return daily_vol * np.sqrt(self.trading_days_per_year)
    
    def analyze_volatility(self, prices: List[float], 
                          benchmark_prices: Optional[List[float]] = None) -> VolatilityAnalysis:
        """
        Comprehensive volatility analysis
        
        Args:
            prices: List of stock prices
            benchmark_prices: Optional list of benchmark prices (e.g., SPY)
            
        Returns:
            VolatilityAnalysis object with detailed analysis
        """
        daily_volatility = self.calculate_daily_volatility(prices)
        annualized_volatility = self.calculate_annualized_volatility(prices)
        
        # Calculate volatility percentile if benchmark provided
        volatility_percentile = None
        if benchmark_prices and len(benchmark_prices) >= 2:
            bench_vol = self.calculate_annualized_volatility(benchmark_prices)
            if bench_vol and bench_vol > 0:
                if annualized_volatility:
                    volatility_percentile = min(1.0, max(0.0, annualized_volatility / bench_vol))
        
        # Determine risk level
        risk_level = self._determine_risk_level(annualized_volatility)
        
        # Calculate confidence
        confidence = self._calculate_confidence(prices)
        
        return VolatilityAnalysis(
            daily_volatility=daily_volatility,
            annualized_volatility=annualized_volatility,
            volatility_percentile=volatility_percentile,
            risk_level=risk_level,
            confidence=confidence
        )
    
    def calculate_rolling_volatility(self, prices: List[float], window: int = 20) -> List[Optional[float]]:
        """
        Calculate rolling volatility over a specified window
        
        Args:
            prices: List of prices
            window: Rolling window size
            
        Returns:
            List of rolling volatilities
        """
        if len(prices) < window + 1:
            return [None] * len(prices)
            
        rolling_vols = []
        for i in range(len(prices)):
            if i < window:
                rolling_vols.append(None)
            else:
                window_prices = prices[i-window:i+1]
                vol = self.calculate_daily_volatility(window_prices)
                rolling_vols.append(vol)
                
        return rolling_vols
    
    def _determine_risk_level(self, annualized_volatility: Optional[float]) -> str:
        """
        Determine risk level based on volatility
        
        Args:
            annualized_volatility: Annualized volatility
            
        Returns:
            Risk level string
        """
        if annualized_volatility is None:
            return "UNKNOWN"
            
        if annualized_volatility < 0.15:
            return "LOW"
        elif annualized_volatility < 0.30:
            return "MODERATE"
        elif annualized_volatility < 0.50:
            return "HIGH"
        else:
            return "EXTREME"
    
    def _calculate_confidence(self, prices: List[float]) -> float:
        """
        Calculate analysis confidence
        
        Args:
            prices: Price data
            
        Returns:
            Confidence score (0-1)
        """
        if len(prices) < 20:
            return 0.3
        elif len(prices) < 50:
            return 0.6
        elif len(prices) < 100:
            return 0.8
        else:
            return 0.95

# Factory function
def create_volatility_calculator() -> VolatilityCalculator:
    """Create and return a VolatilityCalculator instance"""
    return VolatilityCalculator()