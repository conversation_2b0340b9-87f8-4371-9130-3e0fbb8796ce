import logging
from typing import List, Optional

import pandas as pd
import statistics

from src.data.models.stock_data import HistoricalData, StockQuote, RiskAssessment

logger = logging.getLogger(__name__)

class RiskAssessmentCalculator:
    """Calculate risk metrics and assessments"""
    
    def assess_risk(self, historical: HistoricalData, quote: StockQuote) -> RiskAssessment:
        """Perform comprehensive risk assessment"""
        try:
            # Calculate volatility
            volatility = self._calculate_volatility(historical.prices)
            
            # Calculate beta (placeholder - would need market data)
            beta = None  # Requires market index data
            
            # Generate risk warnings
            risk_warnings = self._generate_risk_warnings(historical, quote, volatility)
            
            # Determine risk level
            risk_level = self._determine_risk_level(volatility, risk_warnings)
            
            # Calculate stop loss recommendation
            stop_loss = self._calculate_stop_loss(quote, volatility, risk_level)
            
            return RiskAssessment(
                volatility=volatility,
                beta=beta,
                risk_warnings=risk_warnings,
                stop_loss_recommendation=stop_loss,
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"Risk assessment error: {e}")
            return RiskAssessment(
                volatility=None,
                beta=None,
                risk_warnings=["Risk assessment unavailable"],
                stop_loss_recommendation=None,
                risk_level="UNKNOWN"
            )
    
    def _calculate_volatility(self, prices: List[float]) -> Optional[float]:
        """Calculate annualized volatility"""
        try:
            if len(prices) < 10:
                return None
                
            # Calculate daily returns
            series = pd.Series(prices)
            daily_returns = series.pct_change().dropna()
            
            # Calculate daily volatility
            daily_volatility = daily_returns.std()
            
            # Annualize (assuming 252 trading days)
            annualized_volatility = daily_volatility * (252 ** 0.5)
            
            return annualized_volatility
            
        except Exception as e:
            logger.error(f"Volatility calculation error: {e}")
            return None
    
    def _generate_risk_warnings(self, historical: HistoricalData, quote: StockQuote, volatility: Optional[float]) -> List[str]:
        """Generate risk warnings based on analysis"""
        warnings = []
        
        # Price volatility warning
        if volatility is not None:
            if volatility > 0.50:  # 50% annualized volatility
                warnings.append("High volatility - price can swing significantly")
            elif volatility > 0.30:  # 30% annualized volatility
                warnings.append("Moderate volatility - expect price fluctuations")
        
        # Volume warning
        if quote.volume and historical.volumes:
            recent_avg_volume = sum(v for v in historical.volumes[-20:] if v is not None) / 20
            if quote.volume < (recent_avg_volume * 0.5):
                warnings.append("Low trading volume - may be difficult to exit position")
        
        # Price gap warning
        if historical.prices and len(historical.prices) >= 2:
            yesterday_close = historical.prices[-2]
            today_open = historical.opens[-1] if historical.opens and historical.opens[-1] else historical.prices[-1]
            
            if today_open and yesterday_close:
                gap_percent = abs(today_open - yesterday_close) / yesterday_close * 100
                if gap_percent > 5:
                    warnings.append(f"Significant price gap of {gap_percent:.1f}% - increased volatility")
        
        # Trend warning
        if historical.prices and len(historical.prices) >= 50:
            recent_prices = historical.prices[-20:]
            older_prices = historical.prices[-50:-30]
            
            if recent_prices and older_prices:
                recent_avg = sum(recent_prices) / len(recent_prices)
                older_avg = sum(older_prices) / len(older_prices)
                
                if older_avg > 0:
                    trend_change = (recent_avg - older_avg) / older_avg * 100
                    if abs(trend_change) > 20:
                        warnings.append(f"Significant trend change of {trend_change:+.1f}%")
        
        return warnings[:3]  # Limit to top 3 warnings
    
    def _determine_risk_level(self, volatility: Optional[float], warnings: List[str]) -> str:
        """Determine overall risk level"""
        risk_score = 0
        
        # Volatility-based scoring
        if volatility is not None:
            if volatility > 0.40:
                risk_score += 3  # Very High
            elif volatility > 0.30:
                risk_score += 2  # High
            elif volatility > 0.20:
                risk_score += 1  # Medium
        
        # Warning-based scoring
        risk_score += len(warnings)
        
        # Determine level
        if risk_score >= 4:
            return "HIGH"
        elif risk_score >= 2:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _calculate_stop_loss(self, quote: StockQuote, volatility: Optional[float], risk_level: str) -> Optional[float]:
        """Calculate recommended stop loss level"""
        try:
            if not quote.price:
                return None
                
            # Base stop loss percentages
            if risk_level == "HIGH":
                stop_loss_percent = 0.15  # 15% stop loss
            elif risk_level == "MEDIUM":
                stop_loss_percent = 0.10  # 10% stop loss
            else:
                stop_loss_percent = 0.05  # 5% stop loss
            
            # Adjust for volatility
            if volatility is not None:
                if volatility > 0.30:
                    stop_loss_percent += 0.05
                elif volatility < 0.15:
                    stop_loss_percent -= 0.02
            
            # Calculate stop loss price
            stop_loss_price = quote.price * (1 - stop_loss_percent)
            
            return stop_loss_price
            
        except Exception as e:
            logger.error(f"Stop loss calculation error: {e}")
            return None
