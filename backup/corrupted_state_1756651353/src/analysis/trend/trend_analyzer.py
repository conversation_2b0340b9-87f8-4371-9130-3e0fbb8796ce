"""
Trend Analyzer

Implements systematic trend-following concepts with:
- Moving average systems
- Breakout detection
- Volatility-based position sizing
- Momentum confirmation
"""

import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import numpy as np
import pandas as pd

from src.core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TrendStrength:
    """Measures trend strength across timeframes."""
    value: float  # 0-1 scale
    momentum: float  # -1 to 1 scale
    consistency: float  # 0-1 scale
    duration: int  # periods
    confidence: float  # 0-1 scale


@dataclass
class BreakoutSignal:
    """Represents a breakout signal."""
    price: float
    type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 scale
    volume_confirmation: float  # 0-1 scale
    stop_loss: float
    target: float
    confidence: float  # 0-1 scale


@dataclass
class VolatilityMetrics:
    """Volatility-based metrics for position sizing."""
    atr: float
    atr_multiple: float
    volatility_percentile: float  # 0-1 scale
    position_size_factor: float  # 0-1 scale
    risk_per_trade: float  # in points


@dataclass
class TrendAnalysis:
    """Complete trend analysis result."""
    timestamp: datetime
    trend_strength: TrendStrength
    breakout_signals: List[BreakoutSignal]
    volatility_metrics: VolatilityMetrics
    moving_averages: Dict[str, float]
    trend_state: str  # 'uptrend', 'downtrend', 'sideways'
    key_levels: Dict[str, float]
    confidence_score: float


class TrendAnalyzer:
    """
    Analyzes price trends using systematic trend-following concepts.
    Focuses on moving averages, breakouts, and volatility.
    """
    
    def __init__(self):
        self.ma_periods = [20, 50, 200]  # Standard moving average periods
        self.breakout_threshold = 2.0  # Standard deviations for breakout
        self.volume_threshold = 1.5  # Volume increase for confirmation
    
    async def analyze_trend(
        self,
        prices: List[float],
        volumes: List[float],
        timestamps: List[datetime],
        timeframe: str = "1h"
    ) -> TrendAnalysis:
        """
        Perform comprehensive trend analysis.
        
        Args:
            prices: List of price points
            volumes: List of volume points
            timestamps: List of timestamp points
            timeframe: Data timeframe (e.g., "1h", "4h", "1d")
            
        Returns:
            TrendAnalysis result
        """
        try:
            # Convert to pandas for easier analysis
            df = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })
            
            # 1. Calculate trend strength
            trend_strength = self._calculate_trend_strength(df)
            
            # 2. Detect breakout signals
            breakout_signals = self._detect_breakouts(df)
            
            # 3. Calculate volatility metrics
            volatility_metrics = self._calculate_volatility_metrics(df)
            
            # 4. Calculate moving averages
            moving_averages = self._calculate_moving_averages(df)
            
            # 5. Determine trend state
            trend_state = self._determine_trend_state(df, moving_averages, trend_strength)
            
            # 6. Generate key levels
            key_levels = self._generate_key_levels(df, moving_averages)
            
            # 7. Calculate confidence score
            confidence = self._calculate_confidence_score(
                trend_strength, breakout_signals, volatility_metrics
            )
            
            return TrendAnalysis(
                timestamp=datetime.now(),
                trend_strength=trend_strength,
                breakout_signals=breakout_signals,
                volatility_metrics=volatility_metrics,
                moving_averages=moving_averages,
                trend_state=trend_state,
                key_levels=key_levels,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Trend analysis error: {e}")
            return self._get_fallback_analysis()
    
    def _calculate_trend_strength(self, df: pd.DataFrame) -> TrendStrength:
        """Calculate comprehensive trend strength metrics."""
        try:
            # Calculate price momentum
            momentum = self._calculate_momentum(df)
            
            # Calculate trend consistency
            consistency = self._calculate_trend_consistency(df)
            
            # Calculate trend duration
            duration = self._calculate_trend_duration(df)
            
            # Calculate overall strength
            strength = (momentum + consistency) / 2
            
            # Calculate confidence
            confidence = self._calculate_trend_confidence(
                momentum, consistency, duration
            )
            
            return TrendStrength(
                value=strength,
                momentum=momentum,
                consistency=consistency,
                duration=duration,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Trend strength calculation error: {e}")
            return TrendStrength(0.0, 0.0, 0.0, 0, 0.0)
    
    def _detect_breakouts(self, df: pd.DataFrame) -> List[BreakoutSignal]:
        """Detect potential breakout signals."""
        breakouts = []
        
        try:
            # Calculate price statistics
            rolling_mean = df['price'].rolling(20).mean()
            rolling_std = df['price'].rolling(20).std()
            
            # Calculate volume statistics
            volume_ma = df['volume'].rolling(20).mean()
            
            # Look for breakouts
            for i in range(20, len(df)):
                # Bullish breakout
                if (df['price'].iloc[i] > rolling_mean.iloc[i] + 
                    self.breakout_threshold * rolling_std.iloc[i]):
                    
                    if df['volume'].iloc[i] > volume_ma.iloc[i] * self.volume_threshold:
                        breakouts.append(
                            BreakoutSignal(
                                price=df['price'].iloc[i],
                                type='bullish',
                                strength=self._calculate_breakout_strength(df, i, 'bullish'),
                                volume_confirmation=df['volume'].iloc[i] / volume_ma.iloc[i],
                                stop_loss=self._calculate_stop_loss(df, i, 'bullish'),
                                target=self._calculate_target(df, i, 'bullish'),
                                confidence=self._calculate_breakout_confidence(df, i, 'bullish')
                            )
                        )
                
                # Bearish breakout
                if (df['price'].iloc[i] < rolling_mean.iloc[i] - 
                    self.breakout_threshold * rolling_std.iloc[i]):
                    
                    if df['volume'].iloc[i] > volume_ma.iloc[i] * self.volume_threshold:
                        breakouts.append(
                            BreakoutSignal(
                                price=df['price'].iloc[i],
                                type='bearish',
                                strength=self._calculate_breakout_strength(df, i, 'bearish'),
                                volume_confirmation=df['volume'].iloc[i] / volume_ma.iloc[i],
                                stop_loss=self._calculate_stop_loss(df, i, 'bearish'),
                                target=self._calculate_target(df, i, 'bearish'),
                                confidence=self._calculate_breakout_confidence(df, i, 'bearish')
                            )
                        )
            
            return breakouts
            
        except Exception as e:
            logger.error(f"Breakout detection error: {e}")
            return []
    
    def _calculate_volatility_metrics(self, df: pd.DataFrame) -> VolatilityMetrics:
        """Calculate volatility-based metrics."""
        try:
            # Calculate ATR
            high_low = df['price'].rolling(2).max() - df['price'].rolling(2).min()
            high_close = abs(df['price'].rolling(2).max() - df['price'].shift(1))
            low_close = abs(df['price'].rolling(2).min() - df['price'].shift(1))
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]
            
            # Calculate ATR multiple
            current_price = df['price'].iloc[-1]
            atr_multiple = atr / current_price
            
            # Calculate volatility percentile
            volatility_history = true_range.rolling(100).std()
            current_vol = volatility_history.iloc[-1]
            vol_percentile = len(volatility_history[volatility_history <= current_vol]) / len(volatility_history)
            
            # Calculate position size factor
            pos_size = self._calculate_position_size_factor(atr_multiple, vol_percentile)
            
            # Calculate risk per trade
            risk_points = atr * 2  # 2 ATR for initial stop
            
            return VolatilityMetrics(
                atr=atr,
                atr_multiple=atr_multiple,
                volatility_percentile=vol_percentile,
                position_size_factor=pos_size,
                risk_per_trade=risk_points
            )
            
        except Exception as e:
            logger.error(f"Volatility metrics calculation error: {e}")
            return VolatilityMetrics(0.0, 0.0, 0.0, 0.0, 0.0)
    
    def _calculate_moving_averages(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate moving averages."""
        try:
            mas = {}
            for period in self.ma_periods:
                ma = df['price'].rolling(period).mean().iloc[-1]
                mas[f'MA_{period}'] = ma
            return mas
            
        except Exception as e:
            logger.error(f"Moving average calculation error: {e}")
            return {}
    
    def _determine_trend_state(
        self,
        df: pd.DataFrame,
        moving_averages: Dict[str, float],
        trend_strength: TrendStrength
    ) -> str:
        """Determine overall trend state."""
        try:
            current_price = df['price'].iloc[-1]
            
            # Check moving average alignment
            ma_alignment = all(current_price > ma for ma in moving_averages.values())
            ma_bearish = all(current_price < ma for ma in moving_averages.values())
            
            # Consider trend strength
            if trend_strength.value > 0.7:
                if ma_alignment:
                    return 'uptrend'
                elif ma_bearish:
                    return 'downtrend'
            
            return 'sideways'
            
        except Exception as e:
            logger.error(f"Trend state determination error: {e}")
            return 'sideways'
    
    def _generate_key_levels(
        self,
        df: pd.DataFrame,
        moving_averages: Dict[str, float]
    ) -> Dict[str, float]:
        """Generate key price levels."""
        try:
            current_price = df['price'].iloc[-1]
            
            levels = {
                'support_1': self._find_nearest_support(df, current_price),
                'resistance_1': self._find_nearest_resistance(df, current_price)
            }
            
            # Add moving averages as key levels
            levels.update(moving_averages)
            
            return levels
            
        except Exception as e:
            logger.error(f"Key level generation error: {e}")
            return {}
    
    def _calculate_confidence_score(
        self,
        trend_strength: TrendStrength,
        breakout_signals: List[BreakoutSignal],
        volatility_metrics: VolatilityMetrics
    ) -> float:
        """Calculate overall confidence score."""
        try:
            # Confidence factors
            factors = [
                trend_strength.confidence > 0.7,  # Strong trend
                any(b.confidence > 0.7 for b in breakout_signals),  # Valid breakouts
                0.3 <= volatility_metrics.volatility_percentile <= 0.7  # Normal volatility
            ]
            
            # Weight factors
            weights = [0.4, 0.4, 0.2]
            confidence = sum(f * w for f, w in zip(factors, weights))
            
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Confidence score calculation error: {e}")
            return 0.0
    
    def _get_fallback_analysis(self) -> TrendAnalysis:
        """Get fallback analysis when calculations fail."""
        return TrendAnalysis(
            timestamp=datetime.now(),
            trend_strength=TrendStrength(0.0, 0.0, 0.0, 0, 0.0),
            breakout_signals=[],
            volatility_metrics=VolatilityMetrics(0.0, 0.0, 0.0, 0.0, 0.0),
            moving_averages={},
            trend_state='sideways',
            key_levels={},
            confidence_score=0.0
        )
    
    # Helper methods
    def _calculate_momentum(self, df: pd.DataFrame) -> float:
        """Calculate price momentum."""
        try:
            # Use ROC (Rate of Change)
            roc = (df['price'].iloc[-1] - df['price'].iloc[-20]) / df['price'].iloc[-20]
            return min(max((roc + 1) / 2, 0.0), 1.0)  # Normalize to 0-1
        except:
            return 0.0
    
    def _calculate_trend_consistency(self, df: pd.DataFrame) -> float:
        """Calculate trend consistency."""
        try:
            # Calculate price direction changes
            direction_changes = np.diff(np.sign(np.diff(df['price'])))
            consistency = 1 - (np.count_nonzero(direction_changes) / len(direction_changes))
            return consistency
        except:
            return 0.0
    
    def _calculate_trend_duration(self, df: pd.DataFrame) -> int:
        """Calculate trend duration in periods."""
        try:
            # Count consecutive higher/lower closes
            price_diff = np.diff(df['price'])
            current_trend = np.sign(price_diff[-1])
            duration = 0
            
            for diff in reversed(price_diff):
                if np.sign(diff) == current_trend:
                    duration += 1
                else:
                    break
                    
            return duration
        except:
            return 0
    
    def _calculate_trend_confidence(
        self,
        momentum: float,
        consistency: float,
        duration: int
    ) -> float:
        """Calculate trend confidence score."""
        try:
            # Weight the factors
            momentum_weight = 0.4
            consistency_weight = 0.4
            duration_weight = 0.2
            
            # Normalize duration (assume max 100 periods)
            norm_duration = min(duration / 100, 1.0)
            
            confidence = (
                momentum * momentum_weight +
                consistency * consistency_weight +
                norm_duration * duration_weight
            )
            
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_breakout_strength(
        self,
        df: pd.DataFrame,
        index: int,
        breakout_type: str
    ) -> float:
        """Calculate strength of a breakout."""
        try:
            # Calculate price move relative to average range
            avg_range = (df['price'].rolling(20).max() - df['price'].rolling(20).min()).iloc[index]
            price_move = abs(df['price'].iloc[index] - df['price'].iloc[index-1])
            
            strength = min(price_move / avg_range, 1.0)
            return strength
        except:
            return 0.0
    
    def _calculate_stop_loss(
        self,
        df: pd.DataFrame,
        index: int,
        breakout_type: str
    ) -> float:
        """Calculate stop loss for a breakout."""
        try:
            # Use recent swing low/high
            if breakout_type == 'bullish':
                return df['price'].iloc[index-20:index].min()
            else:
                return df['price'].iloc[index-20:index].max()
        except:
            return df['price'].iloc[index]
    
    def _calculate_target(
        self,
        df: pd.DataFrame,
        index: int,
        breakout_type: str
    ) -> float:
        """Calculate price target for a breakout."""
        try:
            # Project move based on volatility
            volatility = df['price'].rolling(20).std().iloc[index]
            if breakout_type == 'bullish':
                return df['price'].iloc[index] + (volatility * 2)
            else:
                return df['price'].iloc[index] - (volatility * 2)
        except:
            return df['price'].iloc[index]
    
    def _calculate_breakout_confidence(
        self,
        df: pd.DataFrame,
        index: int,
        breakout_type: str
    ) -> float:
        """Calculate confidence in a breakout signal."""
        try:
            # Consider multiple factors
            price_strength = self._calculate_breakout_strength(df, index, breakout_type)
            volume_factor = df['volume'].iloc[index] / df['volume'].rolling(20).mean().iloc[index]
            trend_aligned = self._is_breakout_trend_aligned(df, index, breakout_type)
            
            # Weight the factors
            weights = [0.4, 0.3, 0.3]
            factors = [price_strength, min(volume_factor/2, 1.0), float(trend_aligned)]
            
            confidence = sum(f * w for f, w in zip(factors, weights))
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_position_size_factor(
        self,
        atr_multiple: float,
        volatility_percentile: float
    ) -> float:
        """Calculate position size scaling factor."""
        try:
            # Reduce size in high volatility
            if volatility_percentile > 0.8:
                return 0.5
            elif volatility_percentile > 0.6:
                return 0.75
            else:
                return 1.0
        except:
            return 0.5
    
    def _find_nearest_support(self, df: pd.DataFrame, current_price: float) -> float:
        """Find nearest support level."""
        try:
            # Look for recent lows
            return df['price'].rolling(20).min().iloc[-1]
        except:
            return current_price * 0.95
    
    def _find_nearest_resistance(self, df: pd.DataFrame, current_price: float) -> float:
        """Find nearest resistance level."""
        try:
            # Look for recent highs
            return df['price'].rolling(20).max().iloc[-1]
        except:
            return current_price * 1.05
    
    def _is_breakout_trend_aligned(
        self,
        df: pd.DataFrame,
        index: int,
        breakout_type: str
    ) -> bool:
        """Check if breakout aligns with larger trend."""
        try:
            # Calculate longer-term trend
            ma_50 = df['price'].rolling(50).mean().iloc[index]
            current_price = df['price'].iloc[index]
            
            if breakout_type == 'bullish':
                return current_price > ma_50
            else:
                return current_price < ma_50
        except:
            return False 