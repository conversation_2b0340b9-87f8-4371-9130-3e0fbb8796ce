"""
Hybrid Analyzer

Combines multiple trading ideologies into a unified system:
- Market Structure (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)
- Trend Following
- Mean Reversion
- Quantitative/Statistical
- Macro/News
"""

import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

import numpy as np
import pandas as pd

from src.analysis.market_structure.structure_analyzer import MarketStructureAnalyzer
from src.analysis.trend.trend_analyzer import TrendAnalyzer
from src.analysis.mean_reversion.reversion_analyzer import MeanReversionAnalyzer
from src.analysis.quantitative.statistical_analyzer import QuantitativeAnalyzer
from src.analysis.macro.news_analyzer import MacroNewsAnalyzer
from src.core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class HybridSignal:
    """Combined trading signal from all analyzers."""
    type: str  # 'long', 'short', 'neutral'
    timeframe: str  # trading timeframe
    entry: float
    stop_loss: float
    targets: List[float]
    size: float  # position size (0-1)
    confidence: float  # 0-1 scale


@dataclass
class MarketContext:
    """Complete market context from all analyzers."""
    structure_phase: str  # market structure phase
    trend_state: str  # trend following state
    mean_reversion_state: str  # mean reversion state
    regime: str  # quantitative regime
    macro_environment: str  # macro environment
    confidence: float  # 0-1 scale


@dataclass
class RiskAssessment:
    """Comprehensive risk assessment."""
    volatility_risk: float  # 0-1 scale
    liquidity_risk: float  # 0-1 scale
    correlation_risk: float  # 0-1 scale
    event_risk: float  # 0-1 scale
    total_risk: float  # 0-1 scale
    confidence: float  # 0-1 scale


@dataclass
class HybridAnalysis:
    """Complete hybrid analysis result."""
    timestamp: datetime
    signals: List[HybridSignal]
    context: MarketContext
    risk: RiskAssessment
    key_levels: Dict[str, float]
    execution_params: Dict[str, Any]
    confidence_score: float


class HybridAnalyzer:
    """
    Combines multiple trading ideologies into a unified analysis system.
    Provides comprehensive market analysis and trading signals.
    """
    
    def __init__(self):
        # Initialize component analyzers
        self.structure_analyzer = MarketStructureAnalyzer()
        self.trend_analyzer = TrendAnalyzer()
        self.reversion_analyzer = MeanReversionAnalyzer()
        self.quant_analyzer = QuantitativeAnalyzer()
        self.macro_analyzer = MacroNewsAnalyzer()
        
        # Analysis parameters
        self.confidence_threshold = 0.7
        self.risk_threshold = 0.8
        self.min_signal_agreement = 0.6
    
    async def analyze_hybrid(
        self,
        prices: List[float],
        volumes: List[float],
        timestamps: List[datetime],
        events: List[Dict] = None,
        timeframe: str = "1h"
    ) -> HybridAnalysis:
        """
        Perform comprehensive hybrid analysis.
        
        Args:
            prices: List of price points
            volumes: List of volume points
            timestamps: List of timestamp points
            events: List of macro events/news
            timeframe: Data timeframe (e.g., "1h", "4h", "1d")
            
        Returns:
            HybridAnalysis result
        """
        try:
            # 1. Run all component analyzers
            structure = await self.structure_analyzer.analyze_structure(
                prices, volumes, timestamps, timeframe
            )
            
            trend = await self.trend_analyzer.analyze_trend(
                prices, volumes, timestamps, timeframe
            )
            
            reversion = await self.reversion_analyzer.analyze_reversion(
                prices, volumes, timestamps, timeframe
            )
            
            quant = await self.quant_analyzer.analyze_quantitative(
                prices, volumes, timestamps, timeframe
            )
            
            macro = await self.macro_analyzer.analyze_macro(
                prices, volumes, timestamps, events, timeframe
            )
            
            # 2. Generate hybrid signals
            signals = self._generate_hybrid_signals(
                structure, trend, reversion, quant, macro
            )
            
            # 3. Build market context
            context = self._build_market_context(
                structure, trend, reversion, quant, macro
            )
            
            # 4. Assess risk
            risk = self._assess_hybrid_risk(
                structure, trend, reversion, quant, macro
            )
            
            # 5. Generate key levels
            key_levels = self._generate_key_levels(
                structure, trend, reversion, quant, macro
            )
            
            # 6. Calculate execution parameters
            execution_params = self._calculate_execution_params(
                signals, context, risk
            )
            
            # 7. Calculate confidence score
            confidence = self._calculate_confidence_score(
                signals, context, risk
            )
            
            return HybridAnalysis(
                timestamp=datetime.now(),
                signals=signals,
                context=context,
                risk=risk,
                key_levels=key_levels,
                execution_params=execution_params,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Hybrid analysis error: {e}")
            return self._get_fallback_analysis()
    
    def _generate_hybrid_signals(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        reversion: ReversionAnalysis,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> List[HybridSignal]:
        """Generate combined trading signals."""
        signals = []
        
        try:
            # Get component signals
            structure_signals = self._extract_structure_signals(structure)
            trend_signals = self._extract_trend_signals(trend)
            reversion_signals = self._extract_reversion_signals(reversion)
            quant_signals = self._extract_quant_signals(quant)
            macro_signals = self._extract_macro_signals(macro)
            
            # Combine signals
            all_signals = (
                structure_signals +
                trend_signals +
                reversion_signals +
                quant_signals +
                macro_signals
            )
            
            # Group by type
            long_signals = [s for s in all_signals if s.type == 'long']
            short_signals = [s for s in all_signals if s.type == 'short']
            
            # Check signal agreement
            if self._check_signal_agreement(long_signals):
                signals.append(
                    self._create_hybrid_signal(long_signals, 'long')
                )
            
            if self._check_signal_agreement(short_signals):
                signals.append(
                    self._create_hybrid_signal(short_signals, 'short')
                )
            
            return signals
            
        except Exception as e:
            logger.error(f"Hybrid signal generation error: {e}")
            return []
    
    def _build_market_context(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        reversion: ReversionAnalysis,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> MarketContext:
        """Build comprehensive market context."""
        try:
            # Extract context from each analyzer
            structure_phase = structure.current_phase.phase
            trend_state = trend.trend_state
            reversion_state = self._determine_reversion_state(reversion)
            regime = quant.regime_state
            macro_env = macro.regime.type
            
            # Calculate context confidence
            confidence = self._calculate_context_confidence(
                structure, trend, reversion, quant, macro
            )
            
            return MarketContext(
                structure_phase=structure_phase,
                trend_state=trend_state,
                mean_reversion_state=reversion_state,
                regime=regime,
                macro_environment=macro_env,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Market context building error: {e}")
            return MarketContext(
                'unknown', 'unknown', 'unknown', 'unknown', 'unknown', 0.0
            )
    
    def _assess_hybrid_risk(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        reversion: ReversionAnalysis,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> RiskAssessment:
        """Perform comprehensive risk assessment."""
        try:
            # Calculate risk components
            vol_risk = self._calculate_volatility_risk(
                structure, trend, quant
            )
            
            liq_risk = self._calculate_liquidity_risk(
                structure, macro
            )
            
            corr_risk = self._calculate_correlation_risk(
                quant, macro
            )
            
            event_risk = self._calculate_event_risk(
                macro
            )
            
            # Calculate total risk
            total_risk = self._calculate_total_risk(
                vol_risk, liq_risk, corr_risk, event_risk
            )
            
            # Calculate confidence
            confidence = self._calculate_risk_confidence(
                structure, trend, reversion, quant, macro
            )
            
            return RiskAssessment(
                volatility_risk=vol_risk,
                liquidity_risk=liq_risk,
                correlation_risk=corr_risk,
                event_risk=event_risk,
                total_risk=total_risk,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Risk assessment error: {e}")
            return RiskAssessment(0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
    
    def _generate_key_levels(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        reversion: ReversionAnalysis,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> Dict[str, float]:
        """Generate combined key price levels."""
        try:
            levels = {}
            
            # Combine levels from all analyzers
            levels.update(structure.key_levels)
            levels.update(trend.key_levels)
            levels.update(reversion.key_levels)
            levels.update(quant.key_levels)
            levels.update(macro.key_levels)
            
            # Remove duplicates and sort
            unique_levels = {}
            for name, level in levels.items():
                # Round to avoid floating point issues
                rounded = round(level, 2)
                if rounded not in unique_levels.values():
                    unique_levels[name] = rounded
            
            return dict(sorted(unique_levels.items()))
            
        except Exception as e:
            logger.error(f"Key level generation error: {e}")
            return {}
    
    def _calculate_execution_params(
        self,
        signals: List[HybridSignal],
        context: MarketContext,
        risk: RiskAssessment
    ) -> Dict[str, Any]:
        """Calculate trade execution parameters."""
        try:
            params = {
                'position_size': self._calculate_position_size(signals, risk),
                'entry_type': self._determine_entry_type(context),
                'stop_type': self._determine_stop_type(context),
                'target_type': self._determine_target_type(context),
                'timeframe': self._determine_timeframe(context),
                'order_types': self._determine_order_types(context),
                'risk_limits': self._calculate_risk_limits(risk)
            }
            
            return params
            
        except Exception as e:
            logger.error(f"Execution parameter calculation error: {e}")
            return {}
    
    def _calculate_confidence_score(
        self,
        signals: List[HybridSignal],
        context: MarketContext,
        risk: RiskAssessment
    ) -> float:
        """Calculate overall confidence score."""
        try:
            # Confidence factors
            factors = [
                any(s.confidence > self.confidence_threshold for s in signals),
                context.confidence > self.confidence_threshold,
                risk.confidence > self.confidence_threshold,
                risk.total_risk < self.risk_threshold
            ]
            
            # Weight factors
            weights = [0.3, 0.3, 0.2, 0.2]
            confidence = sum(f * w for f, w in zip(factors, weights))
            
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Confidence score calculation error: {e}")
            return 0.0
    
    def _get_fallback_analysis(self) -> HybridAnalysis:
        """Get fallback analysis when calculations fail."""
        return HybridAnalysis(
            timestamp=datetime.now(),
            signals=[],
            context=MarketContext(
                'unknown', 'unknown', 'unknown', 'unknown', 'unknown', 0.0
            ),
            risk=RiskAssessment(0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
            key_levels={},
            execution_params={},
            confidence_score=0.0
        )
    
    # Helper methods
    def _extract_structure_signals(
        self,
        structure: MarketStructureAnalysis
    ) -> List[HybridSignal]:
        """Extract signals from market structure analysis."""
        signals = []
        
        try:
            # Convert order blocks to signals
            for block in structure.order_blocks:
                if block.confidence > self.confidence_threshold:
                    signals.append(
                        HybridSignal(
                            type='long' if block.type == 'bullish' else 'short',
                            timeframe='4h',  # Order blocks work best on higher timeframes
                            entry=block.price,
                            stop_loss=block.low if block.type == 'bullish' else block.high,
                            targets=self._calculate_block_targets(block),
                            size=block.strength,
                            confidence=block.confidence
                        )
                    )
            
            return signals
            
        except Exception as e:
            logger.error(f"Structure signal extraction error: {e}")
            return []
    
    def _extract_trend_signals(
        self,
        trend: TrendAnalysis
    ) -> List[HybridSignal]:
        """Extract signals from trend analysis."""
        signals = []
        
        try:
            # Convert breakout signals to hybrid signals
            for breakout in trend.breakout_signals:
                if breakout.confidence > self.confidence_threshold:
                    signals.append(
                        HybridSignal(
                            type='long' if breakout.type == 'bullish' else 'short',
                            timeframe='1h',  # Breakouts work well on lower timeframes
                            entry=breakout.price,
                            stop_loss=breakout.stop_loss,
                            targets=[breakout.target],
                            size=breakout.strength,
                            confidence=breakout.confidence
                        )
                    )
            
            return signals
            
        except Exception as e:
            logger.error(f"Trend signal extraction error: {e}")
            return []
    
    def _extract_reversion_signals(
        self,
        reversion: ReversionAnalysis
    ) -> List[HybridSignal]:
        """Extract signals from mean reversion analysis."""
        signals = []
        
        try:
            # Convert reversion signals to hybrid signals
            for signal in reversion.signals:
                if signal.confidence > self.confidence_threshold:
                    signals.append(
                        HybridSignal(
                            type=signal.type,
                            timeframe=signal.timeframe,
                            entry=signal.price,
                            stop_loss=signal.stop_loss,
                            targets=[signal.target],
                            size=signal.probability,
                            confidence=signal.confidence
                        )
                    )
            
            return signals
            
        except Exception as e:
            logger.error(f"Reversion signal extraction error: {e}")
            return []
    
    def _extract_quant_signals(
        self,
        quant: QuantitativeAnalysis
    ) -> List[HybridSignal]:
        """Extract signals from quantitative analysis."""
        signals = []
        
        try:
            # Convert statistical signals to hybrid signals
            for signal in quant.signals:
                if signal.confidence > self.confidence_threshold:
                    signals.append(
                        HybridSignal(
                            type=signal.type,
                            timeframe='1h',  # Statistical signals work on any timeframe
                            entry=signal.price,
                            stop_loss=signal.stop_loss,
                            targets=[signal.target],
                            size=signal.probability,
                            confidence=signal.confidence
                        )
                    )
            
            return signals
            
        except Exception as e:
            logger.error(f"Quant signal extraction error: {e}")
            return []
    
    def _extract_macro_signals(
        self,
        macro: MacroAnalysis
    ) -> List[HybridSignal]:
        """Extract signals from macro analysis."""
        signals = []
        
        try:
            # Convert macro events to hybrid signals
            for event in macro.events:
                if event.confidence > self.confidence_threshold:
                    signals.append(
                        HybridSignal(
                            type='long' if event.impact > 0 else 'short',
                            timeframe='1d',  # Macro events work best on daily timeframe
                            entry=0.0,  # Macro doesn't provide specific entry
                            stop_loss=0.0,  # Macro doesn't provide specific stop
                            targets=[],  # Macro doesn't provide specific targets
                            size=abs(event.impact),
                            confidence=event.confidence
                        )
                    )
            
            return signals
            
        except Exception as e:
            logger.error(f"Macro signal extraction error: {e}")
            return []
    
    def _check_signal_agreement(self, signals: List[HybridSignal]) -> bool:
        """Check if signals agree enough to generate trade."""
        try:
            if not signals:
                return False
            
            # Calculate weighted agreement
            total_confidence = sum(s.confidence for s in signals)
            if total_confidence == 0:
                return False
            
            agreement = len(signals) / 5  # We have 5 analyzers
            
            return agreement >= self.min_signal_agreement
            
        except Exception as e:
            logger.error(f"Signal agreement check error: {e}")
            return False
    
    def _create_hybrid_signal(
        self,
        signals: List[HybridSignal],
        signal_type: str
    ) -> HybridSignal:
        """Create combined signal from component signals."""
        try:
            if not signals:
                raise ValueError("No signals to combine")
            
            # Weight by confidence
            weights = [s.confidence for s in signals]
            total_weight = sum(weights)
            
            if total_weight == 0:
                raise ValueError("No confidence in signals")
            
            # Calculate weighted parameters
            entry = sum(s.entry * w for s, w in zip(signals, weights)) / total_weight
            stop = sum(s.stop_loss * w for s, w in zip(signals, weights)) / total_weight
            size = sum(s.size * w for s, w in zip(signals, weights)) / total_weight
            
            # Combine targets
            all_targets = []
            for signal in signals:
                all_targets.extend(signal.targets)
            targets = sorted(all_targets) if signal_type == 'long' else sorted(all_targets, reverse=True)
            
            # Use most conservative timeframe
            timeframes = {'1h': 1, '4h': 4, '1d': 24}
            tf = max(signals, key=lambda s: timeframes.get(s.timeframe, 0)).timeframe
            
            # Calculate combined confidence
            confidence = sum(s.confidence * w for s, w in zip(signals, weights)) / total_weight
            
            return HybridSignal(
                type=signal_type,
                timeframe=tf,
                entry=entry,
                stop_loss=stop,
                targets=targets[:3],  # Keep top 3 targets
                size=size,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Hybrid signal creation error: {e}")
            return None
    
    def _determine_reversion_state(
        self,
        reversion: ReversionAnalysis
    ) -> str:
        """Determine mean reversion state."""
        try:
            if not reversion.signals:
                return 'neutral'
            
            # Check signal strength
            signal_strength = max(s.confidence for s in reversion.signals)
            
            if signal_strength > self.confidence_threshold:
                return 'active'
            elif signal_strength > 0.5:
                return 'potential'
            else:
                return 'inactive'
            
        except Exception as e:
            logger.error(f"Reversion state determination error: {e}")
            return 'unknown'
    
    def _calculate_context_confidence(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        reversion: ReversionAnalysis,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> float:
        """Calculate confidence in market context."""
        try:
            # Component confidences
            confidences = [
                structure.confidence_score,
                trend.confidence_score,
                reversion.confidence_score,
                quant.confidence_score,
                macro.confidence_score
            ]
            
            # Weight the components
            weights = [0.3, 0.2, 0.2, 0.15, 0.15]
            
            return sum(c * w for c, w in zip(confidences, weights))
            
        except Exception as e:
            logger.error(f"Context confidence calculation error: {e}")
            return 0.0
    
    def _calculate_volatility_risk(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        quant: QuantitativeAnalysis
    ) -> float:
        """Calculate volatility risk."""
        try:
            # Combine volatility measures
            measures = [
                trend.volatility_metrics.volatility_percentile,
                quant.statistics.std_dev / quant.statistics.mean,
                1.0 if structure.manipulation_probability > 0.7 else 0.0
            ]
            
            return sum(measures) / len(measures)
            
        except Exception as e:
            logger.error(f"Volatility risk calculation error: {e}")
            return 0.0
    
    def _calculate_liquidity_risk(
        self,
        structure: MarketStructureAnalysis,
        macro: MacroAnalysis
    ) -> float:
        """Calculate liquidity risk."""
        try:
            # Consider multiple factors
            factors = [
                len(structure.liquidity_levels) == 0,
                macro.regime.type == 'risk_off',
                structure.institutional_interest < 0.3
            ]
            
            return sum(factors) / len(factors)
            
        except Exception as e:
            logger.error(f"Liquidity risk calculation error: {e}")
            return 0.0
    
    def _calculate_correlation_risk(
        self,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> float:
        """Calculate correlation risk."""
        try:
            # Consider regime factors
            factors = [
                macro.regime.correlation_state == 'risk_off',
                quant.regime_state == 'highly_correlated',
                macro.flows.rotation_state == 'defensive'
            ]
            
            return sum(factors) / len(factors)
            
        except Exception as e:
            logger.error(f"Correlation risk calculation error: {e}")
            return 0.0
    
    def _calculate_event_risk(
        self,
        macro: MacroAnalysis
    ) -> float:
        """Calculate event-based risk."""
        try:
            if not macro.events:
                return 0.0
            
            # Consider negative events
            negative_events = [
                e for e in macro.events
                if e.impact < 0 and e.confidence > self.confidence_threshold
            ]
            
            if not negative_events:
                return 0.0
            
            # Calculate weighted impact
            total_impact = sum(abs(e.impact) * e.confidence for e in negative_events)
            return min(total_impact, 1.0)
            
        except Exception as e:
            logger.error(f"Event risk calculation error: {e}")
            return 0.0
    
    def _calculate_total_risk(
        self,
        vol_risk: float,
        liq_risk: float,
        corr_risk: float,
        event_risk: float
    ) -> float:
        """Calculate total risk score."""
        try:
            # Weight the components
            weights = [0.3, 0.2, 0.2, 0.3]
            risks = [vol_risk, liq_risk, corr_risk, event_risk]
            
            return sum(r * w for r, w in zip(risks, weights))
            
        except Exception as e:
            logger.error(f"Total risk calculation error: {e}")
            return 0.0
    
    def _calculate_risk_confidence(
        self,
        structure: MarketStructureAnalysis,
        trend: TrendAnalysis,
        reversion: ReversionAnalysis,
        quant: QuantitativeAnalysis,
        macro: MacroAnalysis
    ) -> float:
        """Calculate confidence in risk assessment."""
        try:
            # Consider quality of inputs
            factors = [
                structure.confidence_score > 0.7,
                trend.confidence_score > 0.7,
                reversion.confidence_score > 0.7,
                quant.confidence_score > 0.7,
                macro.confidence_score > 0.7
            ]
            
            return sum(factors) / len(factors)
            
        except Exception as e:
            logger.error(f"Risk confidence calculation error: {e}")
            return 0.0
    
    def _calculate_position_size(
        self,
        signals: List[HybridSignal],
        risk: RiskAssessment
    ) -> float:
        """Calculate appropriate position size."""
        try:
            if not signals:
                return 0.0
            
            # Base size on signal confidence
            base_size = max(s.size for s in signals)
            
            # Adjust for risk
            risk_factor = 1 - risk.total_risk
            
            return base_size * risk_factor
            
        except Exception as e:
            logger.error(f"Position size calculation error: {e}")
            return 0.0
    
    def _determine_entry_type(self, context: MarketContext) -> str:
        """Determine appropriate entry type."""
        try:
            if context.structure_phase == 'accumulation':
                return 'limit'
            elif context.trend_state == 'strong_trend':
                return 'market'
            else:
                return 'hybrid'
            
        except Exception as e:
            logger.error(f"Entry type determination error: {e}")
            return 'market'
    
    def _determine_stop_type(self, context: MarketContext) -> str:
        """Determine appropriate stop type."""
        try:
            if context.structure_phase == 'manipulation':
                return 'wide'
            elif context.volatility_state == 'high':
                return 'atr_based'
            else:
                return 'standard'
            
        except Exception as e:
            logger.error(f"Stop type determination error: {e}")
            return 'standard'
    
    def _determine_target_type(self, context: MarketContext) -> str:
        """Determine appropriate target type."""
        try:
            if context.mean_reversion_state == 'active':
                return 'mean_reversion'
            elif context.trend_state == 'strong_trend':
                return 'trend_following'
            else:
                return 'hybrid'
            
        except Exception as e:
            logger.error(f"Target type determination error: {e}")
            return 'hybrid'
    
    def _determine_timeframe(self, context: MarketContext) -> str:
        """Determine appropriate trading timeframe."""
        try:
            if context.macro_environment == 'high_impact':
                return '1d'
            elif context.structure_phase == 'manipulation':
                return '4h'
            else:
                return '1h'
            
        except Exception as e:
            logger.error(f"Timeframe determination error: {e}")
            return '1h'
    
    def _determine_order_types(self, context: MarketContext) -> List[str]:
        """Determine appropriate order types."""
        try:
            orders = ['market']
            
            if context.structure_phase == 'accumulation':
                orders.extend(['limit', 'stop'])
            
            if context.volatility_state == 'high':
                orders.append('bracket')
            
            return list(set(orders))
            
        except Exception as e:
            logger.error(f"Order type determination error: {e}")
            return ['market']
    
    def _calculate_risk_limits(self, risk: RiskAssessment) -> Dict[str, float]:
        """Calculate risk-based trading limits."""
        try:
            return {
                'max_position_size': 1 - risk.total_risk,
                'max_correlation': 0.7,
                'max_leverage': 2 * (1 - risk.total_risk),
                'min_liquidity': 1000000
            }
            
        except Exception as e:
            logger.error(f"Risk limits calculation error: {e}")
            return {} 