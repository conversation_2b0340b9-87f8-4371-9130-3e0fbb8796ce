"""
Quantitative/Statistical Analyzer

Implements statistical and machine learning concepts for market analysis.
Focuses on:
- Probability distributions
- Statistical testing
- Machine learning models
- Data-driven execution
"""

import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

from src.core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ProbabilityDistribution:
    """Statistical distribution metrics."""
    mean: float
    std_dev: float
    skewness: float
    kurtosis: float
    is_normal: bool
    percentile_current: float  # Current price percentile
    confidence: float  # 0-1 scale


@dataclass
class MLPrediction:
    """Machine learning model prediction."""
    direction: str  # 'up', 'down', 'sideways'
    probability: float
    features_importance: Dict[str, float]
    confidence: float  # 0-1 scale


@dataclass
class StatisticalSignal:
    """Statistical trading signal."""
    type: str  # 'long', 'short', 'neutral'
    z_score: float
    p_value: float
    expected_value: float
    risk_reward: float
    confidence: float  # 0-1 scale


@dataclass
class QuantitativeAnalysis:
    """Complete quantitative analysis result."""
    timestamp: datetime
    distribution: ProbabilityDistribution
    ml_prediction: MLPrediction
    signals: List[StatisticalSignal]
    regime_state: str  # 'trending', 'mean_reverting', 'random'
    key_levels: Dict[str, float]
    risk_metrics: Dict[str, float]
    confidence_score: float


class QuantitativeAnalyzer:
    """
    Analyzes market data using statistical and machine learning methods.
    Focuses on probability and data-driven decisions.
    """
    
    def __init__(self):
        self.lookback_period = 100  # Standard lookback for calculations
        self.confidence_threshold = 0.7  # Minimum confidence for signals
        self.ml_model = self._initialize_ml_model()
        self.scaler = StandardScaler()
    
    async def analyze_quantitative(
        self,
        prices: List[float],
        volumes: List[float],
        timestamps: List[datetime],
        timeframe: str = "1h"
    ) -> QuantitativeAnalysis:
        """
        Perform comprehensive quantitative analysis.
        
        Args:
            prices: List of price points
            volumes: List of volume points
            timestamps: List of timestamp points
            timeframe: Data timeframe (e.g., "1h", "4h", "1d")
            
        Returns:
            QuantitativeAnalysis result
        """
        try:
            # Convert to pandas for easier analysis
            df = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })
            
            # 1. Analyze probability distribution
            distribution = self._analyze_distribution(df)
            
            # 2. Generate ML prediction
            prediction = self._generate_ml_prediction(df)
            
            # 3. Generate statistical signals
            signals = self._generate_statistical_signals(df)
            
            # 4. Determine market regime
            regime = self._determine_market_regime(df)
            
            # 5. Calculate key levels
            key_levels = self._calculate_key_levels(df)
            
            # 6. Calculate risk metrics
            risk_metrics = self._calculate_risk_metrics(df)
            
            # 7. Calculate confidence score
            confidence = self._calculate_confidence_score(
                distribution, prediction, signals
            )
            
            return QuantitativeAnalysis(
                timestamp=datetime.now(),
                distribution=distribution,
                ml_prediction=prediction,
                signals=signals,
                regime_state=regime,
                key_levels=key_levels,
                risk_metrics=risk_metrics,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Quantitative analysis error: {e}")
            return self._get_fallback_analysis()
    
    def _initialize_ml_model(self) -> RandomForestClassifier:
        """Initialize the machine learning model."""
        try:
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=5,
                random_state=42
            )
        except Exception as e:
            logger.error(f"ML model initialization error: {e}")
            return None
    
    def _analyze_distribution(self, df: pd.DataFrame) -> ProbabilityDistribution:
        """Analyze probability distribution of prices."""
        try:
            # Calculate distribution statistics
            prices = df['price'].values
            mean = np.mean(prices)
            std = np.std(prices)
            skew = stats.skew(prices)
            kurt = stats.kurtosis(prices)
            
            # Test for normality
            _, p_value = stats.normaltest(prices)
            is_normal = p_value > 0.05
            
            # Calculate current price percentile
            current_price = prices[-1]
            percentile = stats.percentileofscore(prices, current_price)
            
            # Calculate confidence
            confidence = self._calculate_distribution_confidence(
                p_value, skew, kurt
            )
            
            return ProbabilityDistribution(
                mean=mean,
                std_dev=std,
                skewness=skew,
                kurtosis=kurt,
                is_normal=is_normal,
                percentile_current=percentile,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Distribution analysis error: {e}")
            return ProbabilityDistribution(0.0, 0.0, 0.0, 0.0, False, 50.0, 0.0)
    
    def _generate_ml_prediction(self, df: pd.DataFrame) -> MLPrediction:
        """Generate machine learning prediction."""
        try:
            # Prepare features
            features = self._prepare_ml_features(df)
            
            # Scale features
            scaled_features = self.scaler.fit_transform(features)
            
            # Make prediction
            if self.ml_model:
                probabilities = self.ml_model.predict_proba(scaled_features[-1:])
                direction_idx = np.argmax(probabilities)
                
                # Map direction
                directions = ['down', 'sideways', 'up']
                direction = directions[direction_idx]
                
                # Get feature importance
                importance = dict(zip(
                    features.columns,
                    self.ml_model.feature_importances_
                ))
                
                # Calculate confidence
                confidence = self._calculate_ml_confidence(
                    probabilities[0][direction_idx],
                    importance
                )
                
                return MLPrediction(
                    direction=direction,
                    probability=probabilities[0][direction_idx],
                    features_importance=importance,
                    confidence=confidence
                )
            
            return MLPrediction('sideways', 0.33, {}, 0.0)
            
        except Exception as e:
            logger.error(f"ML prediction error: {e}")
            return MLPrediction('sideways', 0.33, {}, 0.0)
    
    def _generate_statistical_signals(self, df: pd.DataFrame) -> List[StatisticalSignal]:
        """Generate statistical trading signals."""
        signals = []
        
        try:
            # Calculate z-score
            ma = df['price'].rolling(self.lookback_period).mean()
            std = df['price'].rolling(self.lookback_period).std()
            z_score = (df['price'].iloc[-1] - ma.iloc[-1]) / std.iloc[-1]
            
            # Calculate p-value
            p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            
            if p_value < 0.05:  # Statistically significant
                # Generate signal
                signal_type = 'short' if z_score > 0 else 'long'
                
                # Calculate expected value
                expected_value = self._calculate_expected_value(
                    df, signal_type, z_score
                )
                
                # Calculate risk/reward
                risk_reward = self._calculate_risk_reward(
                    df, signal_type, z_score
                )
                
                # Calculate confidence
                confidence = self._calculate_signal_confidence(
                    z_score, p_value, risk_reward
                )
                
                signals.append(
                    StatisticalSignal(
                        type=signal_type,
                        z_score=z_score,
                        p_value=p_value,
                        expected_value=expected_value,
                        risk_reward=risk_reward,
                        confidence=confidence
                    )
                )
            
            return signals
            
        except Exception as e:
            logger.error(f"Statistical signal generation error: {e}")
            return []
    
    def _determine_market_regime(self, df: pd.DataFrame) -> str:
        """Determine current market regime."""
        try:
            # Calculate Hurst exponent
            hurst = self._calculate_hurst_exponent(df['price'])
            
            # Interpret Hurst exponent
            if hurst > 0.6:
                return 'trending'
            elif hurst < 0.4:
                return 'mean_reverting'
            else:
                return 'random'
            
        except Exception as e:
            logger.error(f"Market regime determination error: {e}")
            return 'random'
    
    def _calculate_key_levels(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate statistically significant price levels."""
        try:
            prices = df['price'].values
            
            levels = {
                'mean': np.mean(prices),
                'upper_1std': np.mean(prices) + np.std(prices),
                'lower_1std': np.mean(prices) - np.std(prices),
                'upper_2std': np.mean(prices) + 2 * np.std(prices),
                'lower_2std': np.mean(prices) - 2 * np.std(prices),
                'percentile_90': np.percentile(prices, 90),
                'percentile_10': np.percentile(prices, 10)
            }
            
            return levels
            
        except Exception as e:
            logger.error(f"Key level calculation error: {e}")
            return {}
    
    def _calculate_risk_metrics(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate quantitative risk metrics."""
        try:
            returns = df['price'].pct_change().dropna()
            
            metrics = {
                'volatility': returns.std() * np.sqrt(252),  # Annualized
                'var_95': returns.quantile(0.05),  # 95% VaR
                'expected_shortfall': returns[returns < returns.quantile(0.05)].mean(),
                'sharpe_ratio': returns.mean() / returns.std() * np.sqrt(252),
                'max_drawdown': self._calculate_max_drawdown(df['price'])
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Risk metrics calculation error: {e}")
            return {}
    
    def _calculate_confidence_score(
        self,
        distribution: ProbabilityDistribution,
        prediction: MLPrediction,
        signals: List[StatisticalSignal]
    ) -> float:
        """Calculate overall confidence score."""
        try:
            # Confidence factors
            factors = [
                distribution.confidence > 0.7,  # Strong distribution analysis
                prediction.confidence > 0.7,  # Strong ML prediction
                any(s.confidence > 0.7 for s in signals)  # Valid statistical signals
            ]
            
            # Weight factors
            weights = [0.4, 0.4, 0.2]
            confidence = sum(f * w for f, w in zip(factors, weights))
            
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Confidence score calculation error: {e}")
            return 0.0
    
    def _get_fallback_analysis(self) -> QuantitativeAnalysis:
        """Get fallback analysis when calculations fail."""
        return QuantitativeAnalysis(
            timestamp=datetime.now(),
            distribution=ProbabilityDistribution(0.0, 0.0, 0.0, 0.0, False, 50.0, 0.0),
            ml_prediction=MLPrediction('sideways', 0.33, {}, 0.0),
            signals=[],
            regime_state='random',
            key_levels={},
            risk_metrics={},
            confidence_score=0.0
        )
    
    # Helper methods
    def _prepare_ml_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for machine learning."""
        try:
            # Calculate technical features
            features = pd.DataFrame()
            
            # Price-based features
            features['returns'] = df['price'].pct_change()
            features['ma_20'] = df['price'].rolling(20).mean()
            features['ma_50'] = df['price'].rolling(50).mean()
            features['std_20'] = df['price'].rolling(20).std()
            
            # Volume-based features
            features['volume_ma'] = df['volume'].rolling(20).mean()
            features['volume_std'] = df['volume'].rolling(20).std()
            
            # Momentum features
            features['momentum'] = df['price'].diff(20)
            features['acceleration'] = features['momentum'].diff()
            
            return features.dropna()
            
        except Exception as e:
            logger.error(f"Feature preparation error: {e}")
            return pd.DataFrame()
    
    def _calculate_distribution_confidence(
        self,
        p_value: float,
        skew: float,
        kurt: float
    ) -> float:
        """Calculate confidence in distribution analysis."""
        try:
            # Consider multiple factors
            normality_factor = 1 - p_value
            skew_factor = 1 / (1 + abs(skew))
            kurt_factor = 1 / (1 + abs(kurt - 3))  # 3 is normal kurtosis
            
            # Weight the factors
            weights = [0.4, 0.3, 0.3]
            factors = [normality_factor, skew_factor, kurt_factor]
            
            confidence = sum(f * w for f, w in zip(factors, weights))
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_ml_confidence(
        self,
        probability: float,
        feature_importance: Dict[str, float]
    ) -> float:
        """Calculate confidence in ML prediction."""
        try:
            # Consider probability and feature importance
            prob_factor = probability
            
            # Check if important features are significant
            key_features = ['returns', 'momentum', 'volume_ma']
            importance_factor = sum(
                feature_importance.get(f, 0) for f in key_features
            ) / len(key_features)
            
            # Weight the factors
            weights = [0.6, 0.4]
            factors = [prob_factor, importance_factor]
            
            confidence = sum(f * w for f, w in zip(factors, weights))
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_expected_value(
        self,
        df: pd.DataFrame,
        signal_type: str,
        z_score: float
    ) -> float:
        """Calculate expected value of a trade."""
        try:
            # Use historical similar situations
            similar_scores = self._find_similar_situations(df, z_score)
            
            if similar_scores:
                if signal_type == 'long':
                    return np.mean([s > 0 for s in similar_scores])
                else:
                    return np.mean([s < 0 for s in similar_scores])
            
            return 0.0
        except:
            return 0.0
    
    def _calculate_risk_reward(
        self,
        df: pd.DataFrame,
        signal_type: str,
        z_score: float
    ) -> float:
        """Calculate risk/reward ratio."""
        try:
            # Use standard deviation for risk/reward
            std = df['price'].std()
            current_price = df['price'].iloc[-1]
            
            if signal_type == 'long':
                reward = std * abs(z_score)  # Move back to mean
                risk = std  # One standard deviation stop
            else:
                reward = std * abs(z_score)
                risk = std
            
            return reward / risk if risk > 0 else 0.0
        except:
            return 0.0
    
    def _calculate_signal_confidence(
        self,
        z_score: float,
        p_value: float,
        risk_reward: float
    ) -> float:
        """Calculate confidence in statistical signal."""
        try:
            # Consider multiple factors
            zscore_factor = min(abs(z_score) / 4, 1.0)
            pvalue_factor = 1 - p_value
            rr_factor = min(risk_reward / 3, 1.0)
            
            # Weight the factors
            weights = [0.4, 0.3, 0.3]
            factors = [zscore_factor, pvalue_factor, rr_factor]
            
            confidence = sum(f * w for f, w in zip(factors, weights))
            return min(max(confidence, 0.0), 1.0)
        except:
            return 0.0
    
    def _calculate_hurst_exponent(self, series: pd.Series) -> float:
        """Calculate Hurst exponent for time series."""
        try:
            # Calculate range over multiple time periods
            lags = range(2, 100)
            tau = [np.sqrt(np.std(np.subtract(series[lag:], series[:-lag])))
                   for lag in lags]
            
            # Perform regression
            reg = np.polyfit(np.log(lags), np.log(tau), 1)
            
            return reg[0] / 2.0  # Hurst exponent is slope/2
        except:
            return 0.5
    
    def _calculate_max_drawdown(self, series: pd.Series) -> float:
        """Calculate maximum drawdown."""
        try:
            # Calculate running maximum
            running_max = series.expanding().max()
            drawdown = (series - running_max) / running_max
            
            return abs(drawdown.min())
        except:
            return 0.0
    
    def _find_similar_situations(
        self,
        df: pd.DataFrame,
        z_score: float
    ) -> List[float]:
        """Find historically similar situations."""
        try:
            # Calculate historical z-scores
            ma = df['price'].rolling(self.lookback_period).mean()
            std = df['price'].rolling(self.lookback_period).std()
            z_scores = (df['price'] - ma) / std
            
            # Find similar z-scores
            similar_indices = np.where(
                abs(z_scores - z_score) < 0.5
            )[0]
            
            # Get subsequent returns
            returns = []
            for idx in similar_indices:
                if idx + 20 < len(df):  # Look forward 20 periods
                    fwd_return = (
                        df['price'].iloc[idx + 20] -
                        df['price'].iloc[idx]
                    ) / df['price'].iloc[idx]
                    returns.append(fwd_return)
            
            return returns
        except:
            return [] 