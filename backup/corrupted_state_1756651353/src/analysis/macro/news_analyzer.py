"""
Macro/News Analyzer

Implements event and regime awareness for macro analysis.
Focuses on:
- Interest rate impacts
- Earnings analysis
- Macro event processing
- Market regime detection
"""

import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import numpy as np
import pandas as pd
from scipy import stats

from src.core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MacroEvent:
    """Represents a significant macro event."""
    type: str  # 'earnings', 'economic', 'fed', 'geopolitical'
    timestamp: datetime
    impact: float  # -1 to 1 scale
    duration: str  # expected impact duration
    confidence: float  # 0-1 scale


@dataclass
class MarketRegime:
    """Current market regime characteristics."""
    type: str  # 'risk_on', 'risk_off', 'transition'
    drivers: List[str]
    correlation_state: str  # 'normal', 'risk_off', 'mixed'
    volatility_state: str  # 'low', 'high', 'extreme'
    confidence: float  # 0-1 scale


@dataclass
class SectorFlow:
    """Sector rotation and flow analysis."""
    leading_sectors: List[str]
    lagging_sectors: List[str]
    rotation_state: str  # 'defensive', 'cyclical', 'mixed'
    flow_strength: float  # 0-1 scale
    confidence: float  # 0-1 scale


@dataclass
class MacroAnalysis:
    """Complete macro/news analysis result."""
    timestamp: datetime
    events: List[MacroEvent]
    regime: MarketRegime
    flows: SectorFlow
    sentiment: str  # 'bullish', 'bearish', 'neutral'
    key_levels: Dict[str, float]
    risk_factors: List[str]
    confidence_score: float


class MacroNewsAnalyzer:
    """
    Analyzes macro events and news for market impact.
    Focuses on regime changes and event processing.
    """
    
    def __init__(self):
        self.lookback_days = 30  # Standard lookback for regime analysis
        self.confidence_threshold = 0.7  # Minimum confidence for signals
        self.correlation_threshold = 0.7  # Threshold for correlation analysis
    
    async def analyze_macro(
        self,
        prices: List[float],
        volumes: List[float],
        timestamps: List[datetime],
        events: List[Dict] = None,
        timeframe: str = "1d"
    ) -> MacroAnalysis:
        """
        Perform comprehensive macro/news analysis.
        
        Args:
            prices: List of price points
            volumes: List of volume points
            timestamps: List of timestamp points
            events: List of macro events/news
            timeframe: Data timeframe (e.g., "1h", "4h", "1d")
            
        Returns:
            MacroAnalysis result
        """
        try:
            # Convert to pandas for easier analysis
            df = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })
            
            # 1. Process macro events
            macro_events = self._process_events(events)
            
            # 2. Determine market regime
            regime = self._determine_regime(df)
            
            # 3. Analyze sector flows
            flows = self._analyze_sector_flows(df)
            
            # 4. Calculate sentiment
            sentiment = self._calculate_sentiment(df, macro_events, regime)
            
            # 5. Generate key levels
            key_levels = self._generate_key_levels(df, regime)
            
            # 6. Identify risk factors
            risk_factors = self._identify_risk_factors(df, macro_events, regime)
            
            # 7. Calculate confidence score
            confidence = self._calculate_confidence_score(
                macro_events, regime, flows
            )
            
            return MacroAnalysis(
                timestamp=datetime.now(),
                events=macro_events,
                regime=regime,
                flows=flows,
                sentiment=sentiment,
                key_levels=key_levels,
                risk_factors=risk_factors,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Macro analysis error: {e}")
            return self._get_fallback_analysis()
    
    def _process_events(self, events: List[Dict]) -> List[MacroEvent]:
        """Process and analyze macro events."""
        processed_events = []
        
        try:
            if not events:
                return []
            
            for event in events:
                # Calculate impact
                impact = self._calculate_event_impact(event)
                
                # Estimate duration
                duration = self._estimate_event_duration(event)
                
                # Calculate confidence
                confidence = self._calculate_event_confidence(event)
                
                if confidence > self.confidence_threshold:
                    processed_events.append(
                        MacroEvent(
                            type=event.get('type', 'unknown'),
                            timestamp=event.get('timestamp', datetime.now()),
                            impact=impact,
                            duration=duration,
                            confidence=confidence
                        )
                    )
            
            return processed_events
            
        except Exception as e:
            logger.error(f"Event processing error: {e}")
            return []
    
    def _determine_regime(self, df: pd.DataFrame) -> MarketRegime:
        """Determine current market regime."""
        try:
            # Calculate regime indicators
            volatility = self._calculate_regime_volatility(df)
            correlations = self._calculate_regime_correlations(df)
            trend = self._calculate_regime_trend(df)
            
            # Determine regime type
            if volatility == 'low' and correlations == 'normal':
                regime_type = 'risk_on'
            elif volatility == 'high' and correlations == 'risk_off':
                regime_type = 'risk_off'
            else:
                regime_type = 'transition'
            
            # Identify regime drivers
            drivers = self._identify_regime_drivers(
                df, volatility, correlations, trend
            )
            
            # Calculate confidence
            confidence = self._calculate_regime_confidence(
                volatility, correlations, trend
            )
            
            return MarketRegime(
                type=regime_type,
                drivers=drivers,
                correlation_state=correlations,
                volatility_state=volatility,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Regime determination error: {e}")
            return MarketRegime('unknown', [], 'unknown', 'unknown', 0.0)
    
    def _analyze_sector_flows(self, df: pd.DataFrame) -> SectorFlow:
        """Analyze sector rotation and flows."""
        try:
            # Identify sector performance
            leading = self._identify_leading_sectors(df)
            lagging = self._identify_lagging_sectors(df)
            
            # Determine rotation state
            rotation = self._determine_rotation_state(leading, lagging)
            
            # Calculate flow strength
            strength = self._calculate_flow_strength(df)
            
            # Calculate confidence
            confidence = self._calculate_flow_confidence(
                leading, lagging, rotation, strength
            )
            
            return SectorFlow(
                leading_sectors=leading,
                lagging_sectors=lagging,
                rotation_state=rotation,
                flow_strength=strength,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Sector flow analysis error: {e}")
            return SectorFlow([], [], 'unknown', 0.0, 0.0)
    
    def _calculate_sentiment(
        self,
        df: pd.DataFrame,
        events: List[MacroEvent],
        regime: MarketRegime
    ) -> str:
        """Calculate overall market sentiment."""
        try:
            # Calculate component scores
            price_score = self._calculate_price_sentiment(df)
            event_score = self._calculate_event_sentiment(events)
            regime_score = self._calculate_regime_sentiment(regime)
            
            # Combine scores
            weights = [0.4, 0.3, 0.3]
            scores = [price_score, event_score, regime_score]
            
            total_score = sum(s * w for s, w in zip(scores, weights))
            
            # Convert to sentiment
            if total_score > 0.2:
                return 'bullish'
            elif total_score < -0.2:
                return 'bearish'
            else:
                return 'neutral'
            
        except Exception as e:
            logger.error(f"Sentiment calculation error: {e}")
            return 'neutral'
    
    def _generate_key_levels(
        self,
        df: pd.DataFrame,
        regime: MarketRegime
    ) -> Dict[str, float]:
        """Generate key price levels based on macro context."""
        try:
            current_price = df['price'].iloc[-1]
            
            levels = {
                'support_macro': self._find_macro_support(df, regime),
                'resistance_macro': self._find_macro_resistance(df, regime),
                'regime_pivot': self._find_regime_pivot(df, regime),
                'event_level': self._find_event_level(df)
            }
            
            return {k: v for k, v in levels.items() if v is not None}
            
        except Exception as e:
            logger.error(f"Key level generation error: {e}")
            return {}
    
    def _identify_risk_factors(
        self,
        df: pd.DataFrame,
        events: List[MacroEvent],
        regime: MarketRegime
    ) -> List[str]:
        """Identify current macro risk factors."""
        try:
            risk_factors = []
            
            # Check volatility
            if regime.volatility_state == 'high':
                risk_factors.append('Elevated Market Volatility')
            
            # Check correlations
            if regime.correlation_state == 'risk_off':
                risk_factors.append('Risk-Off Correlation Regime')
            
            # Check events
            negative_events = [e for e in events if e.impact < -0.5]
            if negative_events:
                risk_factors.append('Significant Negative Events')
            
            # Check market stress
            if self._detect_market_stress(df):
                risk_factors.append('Market Stress Conditions')
            
            return risk_factors
            
        except Exception as e:
            logger.error(f"Risk factor identification error: {e}")
            return []
    
    def _calculate_confidence_score(
        self,
        events: List[MacroEvent],
        regime: MarketRegime,
        flows: SectorFlow
    ) -> float:
        """Calculate overall confidence score."""
        try:
            # Confidence factors
            factors = [
                regime.confidence > 0.7,  # Clear regime
                flows.confidence > 0.7,  # Clear sector flows
                any(e.confidence > 0.7 for e in events)  # Significant events
            ]
            
            # Weight factors
            weights = [0.4, 0.3, 0.3]
            confidence = sum(f * w for f, w in zip(factors, weights))
            
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Confidence score calculation error: {e}")
            return 0.0
    
    def _get_fallback_analysis(self) -> MacroAnalysis:
        """Get fallback analysis when calculations fail."""
        return MacroAnalysis(
            timestamp=datetime.now(),
            events=[],
            regime=MarketRegime('unknown', [], 'unknown', 'unknown', 0.0),
            flows=SectorFlow([], [], 'unknown', 0.0, 0.0),
            sentiment='neutral',
            key_levels={},
            risk_factors=[],
            confidence_score=0.0
        )
    
    # Helper methods
    def _calculate_event_impact(self, event: Dict) -> float:
        """Calculate impact score for an event."""
        try:
            # Consider event type
            type_weights = {
                'earnings': 0.6,
                'economic': 0.8,
                'fed': 1.0,
                'geopolitical': 0.7
            }
            
            # Base impact
            base_impact = event.get('impact', 0.0)
            
            # Adjust for type
            weight = type_weights.get(event.get('type', 'unknown'), 0.5)
            
            return base_impact * weight
        except:
            return 0.0
    
    def _estimate_event_duration(self, event: Dict) -> str:
        """Estimate duration of event impact."""
        try:
            # Map event types to typical durations
            duration_map = {
                'earnings': '1d',
                'economic': '1w',
                'fed': '1m',
                'geopolitical': '2w'
            }
            
            return duration_map.get(event.get('type', 'unknown'), '1w')
        except:
            return '1w'
    
    def _calculate_event_confidence(self, event: Dict) -> float:
        """Calculate confidence in event analysis."""
        try:
            # Consider multiple factors
            factors = [
                event.get('reliability', 0.5),  # Source reliability
                event.get('clarity', 0.5),  # Event clarity
                event.get('precedent', 0.5)  # Historical precedent
            ]
            
            return sum(factors) / len(factors)
        except:
            return 0.0
    
    def _calculate_regime_volatility(self, df: pd.DataFrame) -> str:
        """Calculate regime volatility state."""
        try:
            # Calculate volatility
            returns = df['price'].pct_change()
            current_vol = returns.std()
            historical_vol = returns.rolling(100).std().mean()
            
            # Classify volatility
            if current_vol > historical_vol * 2:
                return 'extreme'
            elif current_vol > historical_vol * 1.5:
                return 'high'
            else:
                return 'low'
        except:
            return 'unknown'
    
    def _calculate_regime_correlations(self, df: pd.DataFrame) -> str:
        """Calculate regime correlation state."""
        try:
            # This would use cross-asset correlations
            # For now, return placeholder
            return 'normal'
        except:
            return 'unknown'
    
    def _calculate_regime_trend(self, df: pd.DataFrame) -> float:
        """Calculate regime trend strength."""
        try:
            # Calculate trend using moving averages
            ma_50 = df['price'].rolling(50).mean()
            ma_200 = df['price'].rolling(200).mean()
            
            if len(ma_50) < 200:
                return 0.0
            
            # Calculate trend strength
            trend = (ma_50.iloc[-1] - ma_200.iloc[-1]) / ma_200.iloc[-1]
            
            return min(max(trend, -1.0), 1.0)
        except:
            return 0.0
    
    def _identify_regime_drivers(
        self,
        df: pd.DataFrame,
        volatility: str,
        correlations: str,
        trend: float
    ) -> List[str]:
        """Identify key regime drivers."""
        try:
            drivers = []
            
            if volatility == 'high':
                drivers.append('Elevated Volatility')
            
            if correlations == 'risk_off':
                drivers.append('Risk-Off Correlations')
            
            if abs(trend) > 0.1:
                direction = 'Upward' if trend > 0 else 'Downward'
                drivers.append(f'{direction} Trend')
            
            return drivers
        except:
            return []
    
    def _calculate_regime_confidence(
        self,
        volatility: str,
        correlations: str,
        trend: float
    ) -> float:
        """Calculate confidence in regime assessment."""
        try:
            # Consider clarity of signals
            vol_clear = volatility != 'unknown'
            corr_clear = correlations != 'unknown'
            trend_clear = abs(trend) > 0.1
            
            # Weight the factors
            weights = [0.4, 0.3, 0.3]
            factors = [vol_clear, corr_clear, trend_clear]
            
            return sum(f * w for f, w in zip(factors, weights))
        except:
            return 0.0
    
    def _identify_leading_sectors(self, df: pd.DataFrame) -> List[str]:
        """Identify leading sectors."""
        try:
            # This would use sector performance data
            # For now, return placeholder
            return ['Technology', 'Healthcare']
        except:
            return []
    
    def _identify_lagging_sectors(self, df: pd.DataFrame) -> List[str]:
        """Identify lagging sectors."""
        try:
            # This would use sector performance data
            # For now, return placeholder
            return ['Energy', 'Utilities']
        except:
            return []
    
    def _determine_rotation_state(
        self,
        leading: List[str],
        lagging: List[str]
    ) -> str:
        """Determine sector rotation state."""
        try:
            # Classify based on sector characteristics
            defensive = ['Utilities', 'Healthcare', 'Consumer Staples']
            cyclical = ['Technology', 'Consumer Discretionary', 'Industrials']
            
            # Count sector types
            def_count = sum(1 for s in leading if s in defensive)
            cyc_count = sum(1 for s in leading if s in cyclical)
            
            if def_count > cyc_count:
                return 'defensive'
            elif cyc_count > def_count:
                return 'cyclical'
            else:
                return 'mixed'
        except:
            return 'unknown'
    
    def _calculate_flow_strength(self, df: pd.DataFrame) -> float:
        """Calculate sector flow strength."""
        try:
            # This would use fund flow data
            # For now, return placeholder
            return 0.5
        except:
            return 0.0
    
    def _calculate_flow_confidence(
        self,
        leading: List[str],
        lagging: List[str],
        rotation: str,
        strength: float
    ) -> float:
        """Calculate confidence in flow analysis."""
        try:
            # Consider multiple factors
            factors = [
                len(leading) > 0,  # Have leading sectors
                len(lagging) > 0,  # Have lagging sectors
                rotation != 'unknown',  # Clear rotation state
                strength > 0.3  # Significant flow strength
            ]
            
            return sum(factors) / len(factors)
        except:
            return 0.0
    
    def _calculate_price_sentiment(self, df: pd.DataFrame) -> float:
        """Calculate price-based sentiment."""
        try:
            # Use moving averages
            ma_20 = df['price'].rolling(20).mean()
            ma_50 = df['price'].rolling(50).mean()
            
            if len(ma_20) < 50:
                return 0.0
            
            # Calculate sentiment score
            score = (ma_20.iloc[-1] - ma_50.iloc[-1]) / ma_50.iloc[-1]
            
            return min(max(score, -1.0), 1.0)
        except:
            return 0.0
    
    def _calculate_event_sentiment(self, events: List[MacroEvent]) -> float:
        """Calculate event-based sentiment."""
        try:
            if not events:
                return 0.0
            
            # Weight events by confidence
            weighted_impacts = [
                e.impact * e.confidence for e in events
            ]
            
            return sum(weighted_impacts) / len(weighted_impacts)
        except:
            return 0.0
    
    def _calculate_regime_sentiment(self, regime: MarketRegime) -> float:
        """Calculate regime-based sentiment."""
        try:
            # Map regime types to sentiment
            sentiment_map = {
                'risk_on': 0.5,
                'risk_off': -0.5,
                'transition': 0.0
            }
            
            base_sentiment = sentiment_map.get(regime.type, 0.0)
            
            # Adjust for confidence
            return base_sentiment * regime.confidence
        except:
            return 0.0
    
    def _find_macro_support(
        self,
        df: pd.DataFrame,
        regime: MarketRegime
    ) -> Optional[float]:
        """Find macro-based support level."""
        try:
            # Use regime-specific calculation
            if regime.type == 'risk_off':
                return df['price'].rolling(100).min().iloc[-1]
            else:
                return df['price'].rolling(50).min().iloc[-1]
        except:
            return None
    
    def _find_macro_resistance(
        self,
        df: pd.DataFrame,
        regime: MarketRegime
    ) -> Optional[float]:
        """Find macro-based resistance level."""
        try:
            # Use regime-specific calculation
            if regime.type == 'risk_off':
                return df['price'].rolling(100).max().iloc[-1]
            else:
                return df['price'].rolling(50).max().iloc[-1]
        except:
            return None
    
    def _find_regime_pivot(
        self,
        df: pd.DataFrame,
        regime: MarketRegime
    ) -> Optional[float]:
        """Find regime pivot level."""
        try:
            # Calculate based on regime type
            if regime.type == 'transition':
                return df['price'].rolling(20).mean().iloc[-1]
            else:
                return None
        except:
            return None
    
    def _find_event_level(self, df: pd.DataFrame) -> Optional[float]:
        """Find significant event price level."""
        try:
            # Use volume profile
            volume_profile = df.groupby('price')['volume'].sum()
            return volume_profile.idxmax()
        except:
            return None
    
    def _detect_market_stress(self, df: pd.DataFrame) -> bool:
        """Detect market stress conditions."""
        try:
            # Calculate stress indicators
            volatility = df['price'].pct_change().std() * np.sqrt(252)
            volume_surge = df['volume'].iloc[-1] > df['volume'].mean() * 2
            price_gap = abs(df['price'].diff().iloc[-1]) > df['price'].std() * 2
            
            return volatility > 0.3 or volume_surge or price_gap
        except:
            return False 