"""
Data Caching System

Unified caching system supporting multiple backends (memory, Redis)
with intelligent cache invalidation and warming strategies.
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import json
import logging
import os
import time
from typing import Any, Dict, Optional, Union, List

from src.api.data.providers.data_source_manager import DataSourceManager
from src.core.config_manager import get_config
from src.core.logger import get_logger

logger = get_logger(__name__)
config = get_config()

class CacheType(Enum):
    """Supported cache types"""
    MEMORY = "memory"
    REDIS = "redis"

@dataclass
class CacheConfig:
    """Configuration for cache system"""
    cache_type: CacheType = CacheType.MEMORY
    ttl: int = 300  # 5 minutes default
    max_size: int = 1000
    enable_warming: bool = True
    warming_batch_size: int = 10
    warming_ttl: int = 86400  # 24 hours for warmed data

@dataclass
class CacheEntry:
    """Individual cache entry"""
    key: str
    data: Any
    created_at: datetime
    accessed_at: datetime
    access_count: int = 0
    ttl: int = 300
    
    @property
    def age_seconds(self) -> float:
        """Get age of entry in seconds"""
        return (datetime.now() - self.created_at).total_seconds()
    
    @property
    def is_expired(self) -> bool:
        """Check if entry is expired"""
        return self.age_seconds > self.ttl
    
    def access(self):
        """Mark entry as accessed"""
        self.accessed_at = datetime.now()
        self.access_count += 1

class MemoryCache:
    """In-memory cache implementation"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, CacheEntry] = {}
        self.max_size = max_size
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        async with self._lock:
            if key in self.cache:
                entry = self.cache[key]
                if entry.is_expired:
                    del self.cache[key]
                    return None
                
                entry.access()
                return entry.data
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 300):
        """Set value in cache"""
        async with self._lock:
            # Remove oldest entries if at capacity
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.cache.keys(), 
                               key=lambda k: self.cache[k].created_at)
                del self.cache[oldest_key]
            
            entry = CacheEntry(
                key=key,
                data=value,
                created_at=datetime.now(),
                accessed_at=datetime.now(),
                ttl=ttl
            )
            self.cache[key] = entry
    
    async def delete(self, key: str):
        """Delete key from cache"""
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
    
    async def clear(self):
        """Clear all cache entries"""
        async with self._lock:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        if not self.cache:
            return {"size": 0, "hit_rate": 0.0}
        
        total_accesses = sum(entry.access_count for entry in self.cache.values())
        return {
            "size": len(self.cache),
            "total_accesses": total_accesses,
            "avg_accesses": total_accesses / len(self.cache) if self.cache else 0
        }

class CacheManager:
    """Main cache manager coordinating different cache backends"""
    
    def __init__(self, config: CacheConfig = None):
        self.config = config or CacheConfig()
        self.cache = MemoryCache(self.config.max_size)
        self.data_provider_manager = DataSourceManager()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        return await self.cache.get(key)
    
    async def set(self, key: str, value: Any, ttl: int = None):
        """Set value in cache"""
        ttl = ttl or self.config.ttl
        await self.cache.set(key, value, ttl)
    
    async def delete(self, key: str):
        """Delete key from cache"""
        await self.cache.delete(key)
    
    async def clear(self):
        """Clear all cache entries"""
        await self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return self.cache.get_stats()
    
    def get_hit_rate(self) -> float:
        """Get cache hit rate (placeholder implementation)"""
        # This would need to track hits/misses over time
        return 75.0  # Placeholder
    
    async def warm_cache_for_symbols(self, symbols: List[str], 
                                   data_types: List[str] = None,
                                   days: int = 30) -> Dict[str, bool]:
        """Warm cache for multiple symbols"""
        if not self.config.enable_warming:
            logger.info("Cache warming disabled")
            return {}
        
        data_types = data_types or ["price", "volume"]
        results = {}
        
        # Warm cache concurrently but with rate limiting
        semaphore = asyncio.Semaphore(3)  # Limit concurrent requests
        
        async def warm_symbol(symbol: str):
            async with semaphore:
                try:
                    # Get data which will automatically cache it
                    await self.data_provider_manager.fetch_historical_data(symbol, days)
                    logger.debug(f"🔥 Warmed cache for {symbol}")
                    results[symbol] = True
                except Exception as e:
                    logger.warning(f"Failed to warm cache for {symbol}: {e}")
                    results[symbol] = False
        
        # Execute warming tasks
        tasks = [warm_symbol(symbol) for symbol in symbols]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"✅ Cache warming completed for {len(symbols)} symbols")
        return results
    
    async def get_cache_analytics(self) -> Dict[str, Any]:
        """Get detailed cache analytics and performance metrics"""
        stats = self.get_stats()
        
        # Add advanced analytics
        analytics = {
            **stats,
            "performance": {
                "hit_rate_percentage": self.get_hit_rate(),
                "miss_rate_percentage": 100 - self.get_hit_rate(),
                "efficiency_score": min(100, self.get_hit_rate() * 1.2),  # Bonus for high hit rates
            },
            "recommendations": []
        }
        
        # Generate recommendations based on performance
        hit_rate = self.get_hit_rate()
        if hit_rate < 30:
            analytics["recommendations"].append("Consider increasing cache TTL or warming cache for popular symbols")
        elif hit_rate > 80:
            analytics["recommendations"].append("Excellent cache performance! Consider expanding cache size")
        
        if isinstance(self.cache, MemoryCache):
            utilization = (len(self.cache.cache) / self.cache.max_size) * 100
            analytics["utilization_percentage"] = utilization
            
            if utilization > 90:
                analytics["recommendations"].append("Cache is near capacity, consider increasing max_size")
            elif utilization < 20:
                analytics["recommendations"].append("Cache is underutilized, could reduce max_size")
        
        return analytics
    
    async def optimize_cache(self):
        """Automatically optimize cache based on usage patterns"""
        if not isinstance(self.cache, MemoryCache):
            logger.info("Cache optimization only available for memory cache")
            return
        
        logger.info("🔧 Starting cache optimization...")
        
        # Analyze access patterns
        async with self.cache._lock:
            entries = list(self.cache.cache.values())
        
        if not entries:
            logger.info("No cache entries to optimize")
            return
        
        # Find frequently accessed entries
        frequent_entries = [e for e in entries if e.access_count > 5]
        stale_entries = [e for e in entries if e.age_seconds > 3600]  # 1 hour old
        
        logger.info(f"📊 Cache analysis: {len(frequent_entries)} frequent, {len(stale_entries)} stale entries")
        
        # Remove stale entries that haven't been accessed recently
        removed_count = 0
        for entry in stale_entries:
            if entry.access_count < 2:  # Rarely accessed
                await self.delete(entry.key)
                removed_count += 1
        
        if removed_count > 0:
            logger.info(f"🧹 Removed {removed_count} stale cache entries")
        
        logger.info("✅ Cache optimization completed")
    
    async def schedule_cache_maintenance(self):
        """Schedule periodic cache maintenance tasks"""
        if not self.config.enable_warming:
            return
        
        # This would typically be called by a background task scheduler
        try:
            await self.optimize_cache()
            
            # Schedule next maintenance
            await asyncio.sleep(3600)  # 1 hour
        except Exception as e:
            logger.error(f"Cache maintenance failed: {e}")

# Global cache manager instance
cache_manager = CacheManager()