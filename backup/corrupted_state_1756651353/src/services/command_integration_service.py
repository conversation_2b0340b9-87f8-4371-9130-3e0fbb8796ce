"""
Command Integration Service

Connects the timeframe service system with Discord commands to provide
real-time market insights, ORB detection, and manipulation alerts.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

from .timeframe_service import TimeframeEngine
from .alert_service import HeartbeatMonitor
from .market_stream import MarketStream
from .config import TIMEFRAME_CONFIG, ALERT_THRESHOLDS

logger = logging.getLogger(__name__)


class CommandIntegrationService:
    """
    Service that integrates timeframe analysis with Discord commands.
    Provides real-time market insights and alerts.
    """
    
    def __init__(self):
        self.timeframe_engine = TimeframeEngine(
            base_resolution=TIMEFRAME_CONFIG["base_resolution"],
            secondary_tf=TIMEFRAME_CONFIG["secondary_tf"]
        )
        self.alert_monitor = HeartbeatMonitor()
        self.market_stream = MarketStream()
        
        # Connect components
        self.market_stream.set_timeframe_engine(self.timeframe_engine)
        self.market_stream.set_alert_monitor(self.alert_monitor)
        
        # Cache for recent analysis results
        self.analysis_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        logger.info("CommandIntegrationService initialized successfully")
    
    async def get_real_time_analysis(self, symbol: str, user_id: str = None) -> Dict[str, Any]:
        """
        Get real-time analysis for a symbol using timeframe service data.
        
        Args:
            symbol: Stock symbol to analyze
            user_id: Optional user ID for personalized insights
            
        Returns:
            Dictionary containing real-time analysis data
        """
        try:
            # Check cache first
            cache_key = f"{symbol}_{user_id or 'anonymous'}"
            if cache_key in self.analysis_cache:
                cached = self.analysis_cache[cache_key]
                if datetime.now() - cached['timestamp'] < timedelta(seconds=self.cache_ttl):
                    logger.debug(f"Returning cached analysis for {symbol}")
                    return cached['data']
            
            # Get real-time timeframe data
            timeframe_data = await self._get_timeframe_data(symbol)
            
            # Get current alerts and patterns
            alerts = await self._get_current_alerts(symbol)
            
            # Generate comprehensive analysis
            analysis = await self._generate_comprehensive_analysis(
                symbol, timeframe_data, alerts, user_id
            )
            
            # Cache the result
            self.analysis_cache[cache_key] = {
                'data': analysis,
                'timestamp': datetime.now()
            }
            
            logger.info(f"Generated real-time analysis for {symbol}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error getting real-time analysis for {symbol}: {e}", exc_info=True)
            return self._get_fallback_analysis(symbol)
    
    async def get_orb_analysis(self, symbol: str) -> Dict[str, Any]:
        """
        Get Opening Range Breakout (ORB) analysis for a symbol.
        
        Args:
            symbol: Stock symbol to analyze
            
        Returns:
            Dictionary containing ORB analysis
        """
        try:
            # Get current market data
            current_data = await self._get_current_market_data(symbol)
            
            # Check for ORB patterns
            orb_analysis = await self._analyze_orb_patterns(symbol, current_data)
            
            return orb_analysis
            
        except Exception as e:
            logger.error(f"Error getting ORB analysis for {symbol}: {e}", exc_info=True)
            return {"error": "Unable to analyze ORB patterns", "symbol": symbol}
    
    async def get_manipulation_alerts(self, symbol: str = None) -> List[Dict[str, Any]]:
        """
        Get current manipulation pattern alerts.
        
        Args:
            symbol: Optional specific symbol to check
            
        Returns:
            List of manipulation alerts
        """
        try:
            if symbol:
                # Check specific symbol
                alerts = await self._check_symbol_manipulation(symbol)
                return [alerts] if alerts else []
            else:
                # Check all monitored symbols
                all_alerts = []
                monitored_symbols = await self._get_monitored_symbols()
                
                for sym in monitored_symbols:
                    alert = await self._check_symbol_manipulation(sym)
                    if alert:
                        all_alerts.append(alert)
                
                return all_alerts
                
        except Exception as e:
            logger.error(f"Error getting manipulation alerts: {e}", exc_info=True)
            return []
    
    async def get_global_market_context(self) -> Dict[str, Any]:
        """
        Get global market context for multi-session analysis.
        
        Returns:
            Dictionary containing global market context
        """
        try:
            # Get market-wide data
            market_data = await self._get_market_wide_data()
            
            # Analyze global patterns
            global_analysis = await self._analyze_global_patterns(market_data)
            
            # Predict NYSE behavior
            nyse_prediction = await self._predict_nyse_behavior(global_analysis)
            
            return {
                "global_context": global_analysis,
                "nyse_prediction": nyse_prediction,
                "timestamp": datetime.now().isoformat(),
                "confidence": global_analysis.get("confidence", 0.0)
            }
            
        except Exception as e:
            logger.error(f"Error getting global market context: {e}", exc_info=True)
            return {"error": "Unable to analyze global market context"}
    
    async def _get_timeframe_data(self, symbol: str) -> Dict[str, Any]:
        """Get timeframe data for a symbol."""
        try:
            # Get all available timeframes
            timeframes = {}
            for tf in ["1m", "5m", "15m", "1h", "4h"]:
                data = await self._get_stored_candles(symbol, tf)
                if data:
                    timeframes[tf] = data
            
            return timeframes
            
        except Exception as e:
            logger.error(f"Error getting timeframe data for {symbol}: {e}")
            return {}
    
    async def _get_current_alerts(self, symbol: str) -> Dict[str, Any]:
        """Get current alerts for a symbol."""
        try:
            return self.alert_monitor.check_ticker(symbol, self.timeframe_engine)
        except Exception as e:
            logger.error(f"Error getting alerts for {symbol}: {e}")
            return {}
    
    async def _generate_comprehensive_analysis(
        self, 
        symbol: str, 
        timeframe_data: Dict[str, Any], 
        alerts: Dict[str, Any],
        user_id: str = None
    ) -> Dict[str, Any]:
        """Generate comprehensive analysis combining all data sources."""
        try:
            analysis = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "timeframe_analysis": {},
                "alerts": alerts,
                "orb_analysis": {},
                "manipulation_patterns": [],
                "risk_assessment": {},
                "trade_thesis": {},
                "user_context": {}
            }
            
            # Analyze each timeframe
            for tf, data in timeframe_data.items():
                if data:
                    tf_analysis = await self._analyze_timeframe(symbol, tf, data)
                    analysis["timeframe_analysis"][tf] = tf_analysis
            
            # Add ORB analysis
            orb_data = await self.get_orb_analysis(symbol)
            analysis["orb_analysis"] = orb_data
            
            # Add manipulation patterns
            manipulation = await self._check_symbol_manipulation(symbol)
            if manipulation:
                analysis["manipulation_patterns"].append(manipulation)
            
            # Generate risk assessment
            analysis["risk_assessment"] = await self._generate_risk_assessment(
                symbol, timeframe_data, alerts
            )
            
            # Generate trade thesis
            analysis["trade_thesis"] = await self._generate_trade_thesis(
                symbol, timeframe_data, alerts, orb_data
            )
            
            # Add user-specific context if available
            if user_id:
                analysis["user_context"] = await self._get_user_context(user_id, symbol)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error generating comprehensive analysis: {e}", exc_info=True)
            return {"error": "Analysis generation failed", "symbol": symbol}
    
    async def _analyze_timeframe(self, symbol: str, timeframe: str, data: List) -> Dict[str, Any]:
        """Analyze data for a specific timeframe."""
        try:
            if not data:
                return {"error": "No data available"}
            
            # Get latest candle
            latest = data[-1] if data else None
            if not latest:
                return {"error": "No latest data"}
            
            # Basic analysis
            analysis = {
                "timeframe": timeframe,
                "latest_candle": {
                    "timestamp": latest.timestamp.isoformat() if hasattr(latest, 'timestamp') else str(latest),
                    "open": getattr(latest, 'open', 0),
                    "high": getattr(latest, 'high', 0),
                    "low": getattr(latest, 'low', 0),
                    "close": getattr(latest, 'close', 0),
                    "volume": getattr(latest, 'volume', 0)
                },
                "trend": "unknown",
                "volume_analysis": "normal",
                "volatility": "normal"
            }
            
            # Determine trend
            if len(data) >= 2:
                prev = data[-2]
                if hasattr(latest, 'close') and hasattr(prev, 'close'):
                    if latest.close > prev.close:
                        analysis["trend"] = "bullish"
                    elif latest.close < prev.close:
                        analysis["trend"] = "bearish"
                    else:
                        analysis["trend"] = "sideways"
            
            # Volume analysis
            if hasattr(latest, 'volume') and latest.volume:
                avg_volume = sum(getattr(c, 'volume', 0) for c in data[-10:]) / 10
                if latest.volume > avg_volume * 2:
                    analysis["volume_analysis"] = "high"
                elif latest.volume < avg_volume * 0.5:
                    analysis["volume_analysis"] = "low"
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing timeframe {timeframe} for {symbol}: {e}")
            return {"error": f"Timeframe analysis failed: {e}"}
    
    async def _analyze_orb_patterns(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze ORB patterns for a symbol."""
        try:
            # This would integrate with the ORB detector from the timeframe service
            # For now, return a placeholder analysis
            return {
                "symbol": symbol,
                "orb_status": "analyzing",
                "opening_range": "9:30-10:00 AM ET",
                "breakout_levels": {
                    "upper": "Not yet established",
                    "lower": "Not yet established"
                },
                "current_status": "Within opening range",
                "confidence": 0.7
            }
            
        except Exception as e:
            logger.error(f"Error analyzing ORB patterns for {symbol}: {e}")
            return {"error": "ORB analysis failed", "symbol": symbol}
    
    async def _check_symbol_manipulation(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Check for manipulation patterns in a symbol."""
        try:
            # Get recent data for manipulation detection
            recent_data = await self._get_stored_candles(symbol, "1m", limit=100)
            
            if not recent_data:
                return None
            
            # Check for common manipulation patterns
            patterns = []
            
            # Volume manipulation
            volume_pattern = await self._detect_volume_manipulation(recent_data)
            if volume_pattern:
                patterns.append(volume_pattern)
            
            # Price manipulation
            price_pattern = await self._detect_price_manipulation(recent_data)
            if price_pattern:
                patterns.append(price_pattern)
            
            # Stop hunting
            stop_hunting = await self._detect_stop_hunting(recent_data)
            if stop_hunting:
                patterns.append(stop_hunting)
            
            if patterns:
                return {
                    "symbol": symbol,
                    "timestamp": datetime.now().isoformat(),
                    "patterns": patterns,
                    "confidence": sum(p.get("confidence", 0) for p in patterns) / len(patterns)
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking manipulation for {symbol}: {e}")
            return None
    
    async def _detect_volume_manipulation(self, data: List) -> Optional[Dict[str, Any]]:
        """Detect volume-based manipulation patterns."""
        try:
            if len(data) < 20:
                return None
            
            # Check for unusual volume spikes
            volumes = [getattr(c, 'volume', 0) for c in data[-20:]]
            avg_volume = sum(volumes) / len(volumes)
            
            # Look for volume spikes followed by price reversal
            for i in range(len(data) - 5):
                if volumes[i] > avg_volume * 3:  # 3x average volume
                    # Check if price reversed after volume spike
                    if i + 2 < len(data):
                        price_before = getattr(data[i], 'close', 0)
                        price_after = getattr(data[i + 2], 'close', 0)
                        
                        if abs(price_after - price_before) / price_before > 0.02:  # 2% move
                            return {
                                "type": "volume_manipulation",
                                "confidence": 0.8,
                                "description": "High volume spike followed by price reversal",
                                "timestamp": getattr(data[i], 'timestamp', datetime.now()).isoformat()
                            }
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting volume manipulation: {e}")
            return None
    
    async def _detect_price_manipulation(self, data: List) -> Optional[Dict[str, Any]]:
        """Detect price-based manipulation patterns."""
        try:
            if len(data) < 30:
                return None
            
            # Check for price wicks (potential stop hunting)
            wicks = []
            for candle in data[-30:]:
                if hasattr(candle, 'high') and hasattr(candle, 'low') and hasattr(candle, 'close'):
                    body_size = abs(candle.close - candle.open)
                    wick_size = candle.high - candle.low
                    
                    if wick_size > body_size * 3:  # Long wick
                        wicks.append({
                            "timestamp": getattr(candle, 'timestamp', datetime.now()),
                            "wick_ratio": wick_size / body_size if body_size > 0 else 0
                        })
            
            if len(wicks) > 5:  # Multiple long wicks
                return {
                    "type": "price_manipulation",
                    "confidence": 0.7,
                    "description": "Multiple long wicks suggesting stop hunting",
                    "wick_count": len(wicks),
                    "timestamp": datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting price manipulation: {e}")
            return None
    
    async def _detect_stop_hunting(self, data: List) -> Optional[Dict[str, Any]]:
        """Detect stop hunting patterns."""
        try:
            if len(data) < 50:
                return None
            
            # Look for price moves that quickly reverse
            reversals = 0
            for i in range(len(data) - 10):
                if i + 5 < len(data):
                    # Check for quick move followed by reversal
                    move_start = getattr(data[i], 'close', 0)
                    move_peak = getattr(data[i + 2], 'close', 0)
                    move_end = getattr(data[i + 5], 'close', 0)
                    
                    # Significant move (>1%) that reverses
                    if abs(move_peak - move_start) / move_start > 0.01:
                        if abs(move_end - move_start) / move_start < 0.002:  # Reversed
                            reversals += 1
            
            if reversals > 3:  # Multiple reversals
                return {
                    "type": "stop_hunting",
                    "confidence": 0.8,
                    "description": "Multiple price reversals suggesting stop hunting",
                    "reversal_count": reversals,
                    "timestamp": datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting stop hunting: {e}")
            return None
    
    async def _generate_risk_assessment(
        self, 
        symbol: str, 
        timeframe_data: Dict[str, Any], 
        alerts: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate risk assessment for a symbol."""
        try:
            risk_score = 0.5  # Base risk score
            
            # Adjust based on alerts
            if alerts.get("volume_alert"):
                risk_score += 0.2
            if alerts.get("volatility_alert"):
                risk_score += 0.3
            
            # Adjust based on timeframe alignment
            if alerts.get("tf_alignment"):
                risk_score -= 0.1  # Better alignment reduces risk
            
            # Clamp risk score
            risk_score = max(0.1, min(1.0, risk_score))
            
            return {
                "overall_risk": risk_score,
                "risk_factors": [
                    "Market volatility",
                    "Volume patterns",
                    "Timeframe alignment"
                ],
                "recommendation": "Moderate" if risk_score < 0.7 else "High",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating risk assessment: {e}")
            return {"error": "Risk assessment failed"}
    
    async def _generate_trade_thesis(
        self, 
        symbol: str, 
        timeframe_data: Dict[str, Any], 
        alerts: Dict[str, Any],
        orb_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate trade thesis for a symbol."""
        try:
            # Analyze timeframe alignment
            tf_alignment = alerts.get("tf_alignment", {})
            
            # Determine overall bias
            bias = "neutral"
            if tf_alignment.get("confidence", 0) > 0.7:
                if tf_alignment.get("trend") == "bullish":
                    bias = "bullish"
                elif tf_alignment.get("trend") == "bearish":
                    bias = "bearish"
            
            # Generate entry/exit levels
            entry_levels = await self._generate_entry_levels(symbol, timeframe_data)
            exit_levels = await self._generate_exit_levels(symbol, timeframe_data, bias)
            
            return {
                "bias": bias,
                "confidence": tf_alignment.get("confidence", 0.5),
                "entry_levels": entry_levels,
                "exit_levels": exit_levels,
                "timeframe": "intraday",
                "risk_reward": "2:1",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating trade thesis: {e}")
            return {"error": "Trade thesis generation failed"}
    
    async def _generate_entry_levels(self, symbol: str, timeframe_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate entry levels for a symbol."""
        try:
            # This would use support/resistance analysis
            # For now, return placeholder levels
            return {
                "support": "Dynamic calculation needed",
                "resistance": "Dynamic calculation needed",
                "breakout": "Dynamic calculation needed"
            }
            
        except Exception as e:
            logger.error(f"Error generating entry levels: {e}")
            return {"error": "Entry level generation failed"}
    
    async def _generate_exit_levels(self, symbol: str, timeframe_data: Dict[str, Any], bias: str) -> Dict[str, Any]:
        """Generate exit levels for a symbol."""
        try:
            # This would use technical analysis
            # For now, return placeholder levels
            return {
                "take_profit": "Dynamic calculation needed",
                "stop_loss": "Dynamic calculation needed",
                "trailing_stop": "Dynamic calculation needed"
            }
            
        except Exception as e:
            logger.error(f"Error generating exit levels: {e}")
            return {"error": "Exit level generation failed"}
    
    async def _get_user_context(self, user_id: str, symbol: str) -> Dict[str, Any]:
        """Get user-specific context for analysis."""
        try:
            # This would integrate with user preferences and watchlists
            # For now, return basic context
            return {
                "user_id": user_id,
                "symbol": symbol,
                "watchlist_status": "unknown",
                "preferences": "default"
            }
            
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return {"error": "User context retrieval failed"}
    
    async def _get_fallback_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get fallback analysis when real-time data is unavailable."""
        return {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "status": "fallback",
            "message": "Real-time analysis unavailable, using cached data",
            "timeframe_analysis": {},
            "alerts": {},
            "orb_analysis": {},
            "manipulation_patterns": [],
            "risk_assessment": {"overall_risk": 0.5, "recommendation": "Unknown"},
            "trade_thesis": {"bias": "neutral", "confidence": 0.0}
        }
    
    async def _get_stored_candles(self, symbol: str, timeframe: str, limit: int = 100) -> List:
        """Get stored candles for a symbol and timeframe."""
        try:
            # This would integrate with the actual storage system
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Error getting stored candles: {e}")
            return []
    
    async def _get_current_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get current market data for a symbol."""
        try:
            # This would integrate with market data providers
            # For now, return placeholder data
            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "status": "placeholder"
            }
            
        except Exception as e:
            logger.error(f"Error getting current market data: {e}")
            return {"error": "Market data retrieval failed"}
    
    async def _get_monitored_symbols(self) -> List[str]:
        """Get list of currently monitored symbols."""
        try:
            # This would integrate with the watchlist system
            # For now, return common symbols
            return ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
            
        except Exception as e:
            logger.error(f"Error getting monitored symbols: {e}")
            return []
    
    async def _get_market_wide_data(self) -> Dict[str, Any]:
        """Get market-wide data for global analysis."""
        try:
            # This would integrate with market data providers
            # For now, return placeholder data
            return {
                "timestamp": datetime.now().isoformat(),
                "status": "placeholder"
            }
            
        except Exception as e:
            logger.error(f"Error getting market-wide data: {e}")
            return {"error": "Market-wide data retrieval failed"}
    
    async def _analyze_global_patterns(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze global market patterns."""
        try:
            # This would perform global market analysis
            # For now, return placeholder analysis
            return {
                "global_trend": "neutral",
                "sector_rotation": "unknown",
                "market_sentiment": "neutral",
                "confidence": 0.5,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing global patterns: {e}")
            return {"error": "Global pattern analysis failed"}
    
    async def _predict_nyse_behavior(self, global_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Predict NYSE behavior based on global analysis."""
        try:
            # This would use the global context analyzer
            # For now, return placeholder prediction
            return {
                "prediction": "neutral",
                "confidence": 0.5,
                "factors": ["Global market conditions", "Sector rotation", "Market sentiment"],
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting NYSE behavior: {e}")
            return {"error": "NYSE behavior prediction failed"}


# Global instance for easy access
command_integration_service = CommandIntegrationService() 