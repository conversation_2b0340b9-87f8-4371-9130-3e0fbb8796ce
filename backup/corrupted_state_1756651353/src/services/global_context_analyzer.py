"""
Global Market Context Analyzer

Analyzes global market sessions to determine likely NYSE behavior,
including accumulation/distribution patterns and manipulation detection.
"""

import logging
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class MarketSession(Enum):
    ASIAN = "asian"
    EUROPEAN = "european"
    NYSE = "nyse"
    PRE_MARKET = "pre_market"
    AFTER_HOURS = "after_hours"

class MarketManipulation(Enum):
    ACCUMULATION = "accumulation"
    DISTRIBUTION = "distribution"
    LIQUIDITY_TRAP = "liquidity_trap"
    STOP_HUNTING = "stop_hunting"
    FAKE_BREAKOUT = "fake_breakout"

@dataclass
class GlobalContext:
    """Global market context for a ticker."""
    ticker: str
    date: datetime
    asian_session: Dict[str, float]
    european_session: Dict[str, float]
    pre_market: Dict[str, float]
    nyse_session: Dict[str, float]
    after_hours: Dict[str, float]
    
    # Analysis results
    global_trend: str
    manipulation_detected: List[MarketManipulation]
    nyse_probability: float
    risk_level: str

@dataclass
class ManipulationPattern:
    """Detected manipulation pattern."""
    pattern_type: MarketManipulation
    confidence: float
    evidence: List[str]
    time_range: Tuple[datetime, datetime]
    price_levels: Dict[str, float]

class GlobalContextAnalyzer:
    """
    Analyzes global market context to predict NYSE behavior.
    """
    
    def __init__(self):
        # Market session times (EST)
        self.session_times = {
            MarketSession.ASIAN: {
                "start": time(14, 0),  # 2:00 PM EST
                "end": time(6, 0)      # 6:00 AM EST
            },
            MarketSession.EUROPEAN: {
                "start": time(2, 0),   # 2:00 AM EST
                "end": time(11, 0)     # 11:00 AM EST
            },
            MarketSession.PRE_MARKET: {
                "start": time(4, 0),   # 4:00 AM EST
                "end": time(9, 30)     # 9:30 AM EST
            },
            MarketSession.NYSE: {
                "start": time(9, 30),  # 9:30 AM EST
                "end": time(16, 0)     # 4:00 PM EST
            },
            MarketSession.AFTER_HOURS: {
                "start": time(16, 0),  # 4:00 PM EST
                "end": time(20, 0)     # 8:00 PM EST
            }
        }
        
        # Manipulation detection thresholds
        self.manipulation_thresholds = {
            "volume_spike": 3.0,        # 3x average volume
            "price_reversal": 0.02,     # 2% reversal
            "liquidity_trap": 0.005,    # 0.5% trap
            "stop_hunting": 0.01        # 1% stop hunt
        }
        
        logger.info("Global Context Analyzer initialized")
    
    def analyze_global_context(self, ticker: str, all_session_data: Dict) -> GlobalContext:
        """
        Analyze global market context for a ticker.
        
        Args:
            ticker: Stock symbol
            all_session_data: Data from all market sessions
            
        Returns:
            GlobalContext with analysis results
        """
        try:
            logger.info(f"Analyzing global context for {ticker}")
            
            # Analyze each session
            asian_analysis = self._analyze_session(all_session_data.get("asian", []), MarketSession.ASIAN)
            european_analysis = self._analyze_session(all_session_data.get("european", []), MarketSession.EUROPEAN)
            pre_market_analysis = self._analyze_session(all_session_data.get("pre_market", []), MarketSession.PRE_MARKET)
            nyse_analysis = self._analyze_session(all_session_data.get("nyse", []), MarketSession.NYSE)
            after_hours_analysis = self._analyze_session(all_session_data.get("after_hours", []), MarketSession.AFTER_HOURS)
            
            # Detect manipulation patterns
            manipulation_patterns = self._detect_manipulation_patterns(
                ticker, all_session_data
            )
            
            # Calculate NYSE probability
            nyse_probability = self._calculate_nyse_probability(
                asian_analysis, european_analysis, pre_market_analysis
            )
            
            # Determine global trend
            global_trend = self._determine_global_trend(
                asian_analysis, european_analysis, pre_market_analysis
            )
            
            # Assess risk level
            risk_level = self._assess_risk_level(manipulation_patterns, nyse_probability)
            
            context = GlobalContext(
                ticker=ticker,
                date=datetime.now().date(),
                asian_session=asian_analysis,
                european_session=european_analysis,
                pre_market=pre_market_analysis,
                nyse_session=nyse_analysis,
                after_hours=after_hours_analysis,
                global_trend=global_trend,
                manipulation_detected=[p.pattern_type for p in manipulation_patterns],
                nyse_probability=nyse_probability,
                risk_level=risk_level
            )
            
            logger.info(f"Global context analysis complete for {ticker}: {global_trend}, "
                       f"Risk: {risk_level}, NYSE Probability: {nyse_probability:.1%}")
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing global context for {ticker}: {e}", exc_info=True)
            return None
    
    def _analyze_session(self, session_data: List, session_type: MarketSession) -> Dict[str, float]:
        """Analyze a specific market session."""
        if not session_data:
            return {"trend": "no_data", "strength": 0.0, "volume_ratio": 0.0}
        
        try:
            # Calculate basic metrics
            prices = [c.close for c in session_data]
            volumes = [c.volume for c in session_data if hasattr(c, 'volume')]
            
            # Trend calculation
            if len(prices) >= 2:
                trend = "bullish" if prices[-1] > prices[0] else "bearish"
                strength = abs(prices[-1] - prices[0]) / prices[0]
            else:
                trend = "neutral"
                strength = 0.0
            
            # Volume analysis
            if volumes:
                avg_volume = sum(volumes) / len(volumes)
                volume_ratio = volumes[-1] / avg_volume if avg_volume > 0 else 1.0
            else:
                volume_ratio = 1.0
            
            return {
                "trend": trend,
                "strength": strength,
                "volume_ratio": volume_ratio,
                "session_type": session_type.value
            }
            
        except Exception as e:
            logger.error(f"Error analyzing {session_type.value} session: {e}")
            return {"trend": "error", "strength": 0.0, "volume_ratio": 0.0}
    
    def _detect_manipulation_patterns(self, ticker: str, all_session_data: Dict) -> List[ManipulationPattern]:
        """Detect manipulation patterns across all sessions."""
        patterns = []
        
        try:
            # Check for liquidity traps (common in pre-market)
            pre_market_data = all_session_data.get("pre_market", [])
            if pre_market_data:
                liquidity_trap = self._detect_liquidity_trap(ticker, pre_market_data)
                if liquidity_trap:
                    patterns.append(liquidity_trap)
            
            # Check for stop hunting
            for session_name, session_data in all_session_data.items():
                if session_data:
                    stop_hunting = self._detect_stop_hunting(ticker, session_data, session_name)
                    if stop_hunting:
                        patterns.append(stop_hunting)
            
            # Check for fake breakouts
            fake_breakout = self._detect_fake_breakout(ticker, all_session_data)
            if fake_breakout:
                patterns.append(fake_breakout)
            
            # Check for accumulation/distribution
            accumulation = self._detect_accumulation_distribution(ticker, all_session_data)
            if accumulation:
                patterns.append(accumulation)
            
            logger.info(f"Detected {len(patterns)} manipulation patterns for {ticker}")
            
        except Exception as e:
            logger.error(f"Error detecting manipulation patterns: {e}")
        
        return patterns
    
    def _detect_liquidity_trap(self, ticker: str, session_data: List) -> Optional[ManipulationPattern]:
        """Detect liquidity trap patterns."""
        try:
            if len(session_data) < 5:
                return None
            
            # Look for price spikes followed by quick reversals
            for i in range(2, len(session_data)):
                current = session_data[i]
                prev = session_data[i-1]
                prev2 = session_data[i-2]
                
                # Check for spike and reversal
                spike_up = (current.high - prev.high) / prev.high
                reversal_down = (current.close - current.high) / current.high
                
                if (spike_up > self.manipulation_thresholds["liquidity_trap"] and 
                    reversal_down > self.manipulation_thresholds["liquidity_trap"]):
                    
                    return ManipulationPattern(
                        pattern_type=MarketManipulation.LIQUIDITY_TRAP,
                        confidence=0.8,
                        evidence=[
                            f"Price spike: {spike_up:.2%}",
                            f"Quick reversal: {reversal_down:.2%}",
                            "Liquidity trap detected"
                        ],
                        time_range=(prev2.timestamp, current.timestamp),
                        price_levels={
                            "spike_high": current.high,
                            "reversal_close": current.close,
                            "trap_level": prev.high
                        }
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting liquidity trap: {e}")
            return None
    
    def _detect_stop_hunting(self, ticker: str, session_data: List, session_name: str) -> Optional[ManipulationPattern]:
        """Detect stop hunting patterns."""
        try:
            if len(session_data) < 3:
                return None
            
            # Look for price moves below obvious support levels
            for i in range(1, len(session_data)):
                current = session_data[i]
                prev = session_data[i-1]
                
                # Check for stop hunt below support
                if (prev.close > prev.low and 
                    current.low < prev.low and 
                    current.close > prev.low):
                    
                    stop_hunt_distance = (prev.low - current.low) / prev.low
                    
                    if stop_hunt_distance > self.manipulation_thresholds["stop_hunting"]:
                        return ManipulationPattern(
                            pattern_type=MarketManipulation.STOP_HUNTING,
                            confidence=0.7,
                            evidence=[
                                f"Stop hunt below support: {stop_hunt_distance:.2%}",
                                f"Quick recovery above support",
                                f"Session: {session_name}"
                            ],
                            time_range=(prev.timestamp, current.timestamp),
                            price_levels={
                                "support_level": prev.low,
                                "stop_hunt_low": current.low,
                                "recovery_close": current.close
                            }
                        )
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting stop hunting: {e}")
            return None
    
    def _detect_fake_breakout(self, ticker: str, all_session_data: Dict) -> Optional[ManipulationPattern]:
        """Detect fake breakout patterns."""
        try:
            # Look for breakouts that fail within short time
            for session_name, session_data in all_session_data.items():
                if not session_data or len(session_data) < 10:
                    continue
                
                # Check for breakout and failure pattern
                for i in range(5, len(session_data)):
                    # Look for breakout attempt
                    breakout_candle = session_data[i]
                    prev_candles = session_data[i-5:i]
                    
                    # Check if this candle broke above recent highs
                    recent_high = max(c.high for c in prev_candles)
                    breakout_strength = (breakout_candle.high - recent_high) / recent_high
                    
                    if breakout_strength > 0.005:  # 0.5% breakout
                        # Check if it failed within next few candles
                        next_candles = session_data[i+1:i+6] if i+6 <= len(session_data) else session_data[i+1:]
                        
                        if next_candles:
                            failure_candle = next_candles[-1]
                            failure_strength = (recent_high - failure_candle.close) / recent_high
                            
                            if failure_strength > 0.01:  # 1% failure
                                return ManipulationPattern(
                                    pattern_type=MarketManipulation.FAKE_BREAKOUT,
                                    confidence=0.8,
                                    evidence=[
                                        f"Breakout attempt: {breakout_strength:.2%}",
                                        f"Quick failure: {failure_strength:.2%}",
                                        f"Session: {session_name}"
                                    ],
                                    time_range=(breakout_candle.timestamp, failure_candle.timestamp),
                                    price_levels={
                                        "breakout_high": breakout_candle.high,
                                        "resistance_level": recent_high,
                                        "failure_close": failure_candle.close
                                    }
                                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting fake breakout: {e}")
            return None
    
    def _detect_accumulation_distribution(self, ticker: str, all_session_data: Dict) -> Optional[ManipulationPattern]:
        """Detect accumulation/distribution patterns."""
        try:
            # Look for volume patterns that suggest accumulation/distribution
            for session_name, session_data in all_session_data.items():
                if not session_data or len(session_data) < 10:
                    continue
                
                # Calculate volume-weighted price changes
                volume_changes = []
                price_changes = []
                
                for i in range(1, len(session_data)):
                    current = session_data[i]
                    prev = session_data[i-1]
                    
                    if hasattr(current, 'volume') and hasattr(prev, 'volume'):
                        volume_change = (current.volume - prev.volume) / prev.volume if prev.volume > 0 else 0
                        price_change = (current.close - prev.close) / prev.close if prev.close > 0 else 0
                        
                        volume_changes.append(volume_change)
                        price_changes.append(price_change)
                
                if len(volume_changes) >= 5:
                    # Check for accumulation (high volume on down days)
                    down_days_volume = [v for p, v in zip(price_changes, volume_changes) if p < 0]
                    up_days_volume = [v for p, v in zip(price_changes, volume_changes) if p > 0]
                    
                    if down_days_volume and up_days_volume:
                        avg_down_volume = sum(down_days_volume) / len(down_days_volume)
                        avg_up_volume = sum(up_days_volume) / len(up_days_volume)
                        
                        # Accumulation: high volume on down days
                        if avg_down_volume > avg_up_volume * 1.5:
                            return ManipulationPattern(
                                pattern_type=MarketManipulation.ACCUMULATION,
                                confidence=0.7,
                                evidence=[
                                    f"High volume on down days: {avg_down_volume:.2f}x",
                                    f"Low volume on up days: {avg_up_volume:.2f}x",
                                    f"Session: {session_name}"
                                ],
                                time_range=(session_data[0].timestamp, session_data[-1].timestamp),
                                price_levels={
                                    "avg_down_volume": avg_down_volume,
                                    "avg_up_volume": avg_up_volume,
                                    "volume_ratio": avg_down_volume / avg_up_volume
                                }
                            )
                        
                        # Distribution: high volume on up days
                        elif avg_up_volume > avg_down_volume * 1.5:
                            return ManipulationPattern(
                                pattern_type=MarketManipulation.DISTRIBUTION,
                                confidence=0.7,
                                evidence=[
                                    f"High volume on up days: {avg_up_volume:.2f}x",
                                    f"Low volume on down days: {avg_down_volume:.2f}x",
                                    f"Session: {session_name}"
                                ],
                                time_range=(session_data[0].timestamp, session_data[-1].timestamp),
                                price_levels={
                                    "avg_up_volume": avg_up_volume,
                                    "avg_down_volume": avg_down_volume,
                                    "volume_ratio": avg_up_volume / avg_down_volume
                                }
                            )
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting accumulation/distribution: {e}")
            return None
    
    def _calculate_nyse_probability(self, asian: Dict, european: Dict, pre_market: Dict) -> float:
        """Calculate probability of NYSE following global trend."""
        try:
            base_probability = 0.5  # 50% base probability
            
            # Asian session influence (30% weight)
            if asian.get("trend") != "no_data":
                asian_trend = 1.0 if asian["trend"] == "bullish" else 0.0
                asian_strength = min(asian.get("strength", 0.0) * 10, 1.0)  # Normalize strength
                asian_influence = asian_trend * asian_strength * 0.3
            else:
                asian_influence = 0.0
            
            # European session influence (40% weight)
            if european.get("trend") != "no_data":
                european_trend = 1.0 if european["trend"] == "bullish" else 0.0
                european_strength = min(european.get("strength", 0.0) * 10, 1.0)
                european_influence = european_trend * european_strength * 0.4
            else:
                european_influence = 0.0
            
            # Pre-market influence (30% weight)
            if pre_market.get("trend") != "no_data":
                pre_market_trend = 1.0 if pre_market["trend"] == "bullish" else 0.0
                pre_market_strength = min(pre_market.get("strength", 0.0) * 10, 1.0)
                pre_market_influence = pre_market_trend * pre_market_strength * 0.3
            else:
                pre_market_influence = 0.0
            
            # Calculate final probability
            final_probability = base_probability + asian_influence + european_influence + pre_market_influence
            
            # Ensure probability is between 0 and 1
            return max(0.0, min(1.0, final_probability))
            
        except Exception as e:
            logger.error(f"Error calculating NYSE probability: {e}")
            return 0.5
    
    def _determine_global_trend(self, asian: Dict, european: Dict, pre_market: Dict) -> str:
        """Determine overall global trend."""
        try:
            trends = []
            weights = []
            
            # Collect trends with weights
            if asian.get("trend") != "no_data":
                trends.append(asian["trend"])
                weights.append(0.3)  # Asian weight
            
            if european.get("trend") != "no_data":
                trends.append(european["trend"])
                weights.append(0.4)  # European weight
            
            if pre_market.get("trend") != "no_data":
                trends.append(pre_market["trend"])
                weights.append(0.3)  # Pre-market weight
            
            if not trends:
                return "neutral"
            
            # Calculate weighted trend
            bullish_score = sum(w for t, w in zip(trends, weights) if t == "bullish")
            bearish_score = sum(w for t, w in zip(trends, weights) if t == "bearish")
            
            if bullish_score > bearish_score + 0.1:
                return "bullish"
            elif bearish_score > bullish_score + 0.1:
                return "bearish"
            else:
                return "neutral"
                
        except Exception as e:
            logger.error(f"Error determining global trend: {e}")
            return "neutral"
    
    def _assess_risk_level(self, manipulation_patterns: List[ManipulationPattern], nyse_probability: float) -> str:
        """Assess overall risk level."""
        try:
            risk_score = 0.0
            
            # Manipulation pattern risk
            for pattern in manipulation_patterns:
                if pattern.pattern_type in [MarketManipulation.LIQUIDITY_TRAP, MarketManipulation.STOP_HUNTING]:
                    risk_score += 0.3
                elif pattern.pattern_type == MarketManipulation.FAKE_BREAKOUT:
                    risk_score += 0.4
                elif pattern.pattern_type in [MarketManipulation.ACCUMULATION, MarketManipulation.DISTRIBUTION]:
                    risk_score += 0.2
            
            # NYSE probability risk
            if nyse_probability < 0.3 or nyse_probability > 0.7:
                risk_score += 0.2  # High uncertainty
            
            # Determine risk level
            if risk_score >= 0.7:
                return "HIGH"
            elif risk_score >= 0.4:
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            logger.error(f"Error assessing risk level: {e}")
            return "UNKNOWN"
    
    def get_manipulation_summary(self, ticker: str, patterns: List[ManipulationPattern]) -> str:
        """Generate human-readable manipulation summary."""
        if not patterns:
            return f"✅ No manipulation patterns detected for {ticker}"
        
        summary_parts = [f"⚠️ **Manipulation Alert for {ticker}**"]
        
        for pattern in patterns:
            summary_parts.append(f"\n🔍 **{pattern.pattern_type.value.replace('_', ' ').title()}**")
            summary_parts.append(f"  • Confidence: {pattern.confidence:.1%}")
            summary_parts.append(f"  • Time: {pattern.time_range[0].strftime('%H:%M')} - {pattern.time_range[1].strftime('%H:%M')}")
            
            for evidence in pattern.evidence:
                summary_parts.append(f"  • {evidence}")
        
        return "\n".join(summary_parts) 