"""
Opening Range Breakout (ORB) Detector

Detects and tracks opening range highs/lows and breakout patterns
across multiple timeframes for enhanced trading signals.
"""

import logging
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ORBLevels:
    """Opening Range Breakout levels for a ticker."""
    ticker: str
    date: datetime
    opening_range_high: float
    opening_range_low: float
    opening_range_mid: float
    breakout_high: Optional[float] = None
    breakout_low: Optional[float] = None
    breakout_time: Optional[datetime] = None
    breakout_volume: Optional[int] = None
    confirmation_timeframes: List[str] = None
    strength_score: float = 0.0

@dataclass
class BreakoutSignal:
    """Breakout signal with confirmation data."""
    ticker: str
    timestamp: datetime
    signal_type: str  # "HIGH_BREAKOUT" or "LOW_BREAKOUT"
    price: float
    volume: int
    timeframe_confirmations: List[str]
    confidence_score: float
    orb_levels: ORBLevels

class ORBDetector:
    """
    Detects Opening Range Breakouts using multi-timeframe analysis.
    """
    
    def __init__(self):
        self.orb_levels: Dict[str, ORBLevels] = {}
        self.breakout_signals: List[BreakoutSignal] = []
        
        # Market hours (EST)
        self.market_open = time(9, 30)  # 9:30 AM EST
        self.orb_end = time(10, 0)      # 10:00 AM EST
        
        # Breakout confirmation thresholds
        self.volume_threshold = 1.5      # 1.5x average volume
        self.price_threshold = 0.001     # 0.1% above/below ORB level
        
        logger.info("ORB Detector initialized")
    
    def process_opening_range(self, ticker: str, candles: List, current_time: datetime) -> Optional[ORBLevels]:
        """
        Process opening range data and establish ORB levels.
        
        Args:
            ticker: Stock symbol
            candles: List of 1-minute candles
            current_time: Current market time
            
        Returns:
            ORBLevels if opening range is complete, None otherwise
        """
        try:
            # Check if we're still in opening range
            if current_time.time() < self.orb_end:
                return None
            
            # Filter candles for opening range (9:30-10:00)
            opening_candles = []
            for candle in candles:
                candle_time = candle.timestamp.time()
                if self.market_open <= candle_time <= self.orb_end:
                    opening_candles.append(candle)
            
            if len(opening_candles) < 5:  # Need at least 5 candles
                logger.warning(f"Insufficient opening range data for {ticker}")
                return None
            
            # Calculate ORB levels
            high = max(c.high for c in opening_candles)
            low = min(c.low for c in opening_candles)
            mid = (high + low) / 2
            
            # Create ORB levels
            orb_levels = ORBLevels(
                ticker=ticker,
                date=current_time.date(),
                opening_range_high=high,
                opening_range_low=low,
                opening_range_mid=mid,
                confirmation_timeframes=[]
            )
            
            # Store ORB levels
            key = f"{ticker}_{current_time.date()}"
            self.orb_levels[key] = orb_levels
            
            logger.info(f"ORB levels established for {ticker}: High=${high:.2f}, Low=${low:.2f}")
            return orb_levels
            
        except Exception as e:
            logger.error(f"Error processing opening range for {ticker}: {e}", exc_info=True)
            return None
    
    def check_breakouts(self, ticker: str, current_candle, timeframes_data: Dict) -> List[BreakoutSignal]:
        """
        Check for breakout signals across multiple timeframes.
        
        Args:
            ticker: Stock symbol
            current_candle: Current 1-minute candle
            timeframes_data: Data from all timeframes
            
        Returns:
            List of breakout signals
        """
        signals = []
        
        try:
            # Get today's ORB levels
            today = current_candle.timestamp.date()
            orb_key = f"{ticker}_{today}"
            
            if orb_key not in self.orb_levels:
                logger.debug(f"No ORB levels for {ticker} on {today}")
                return signals
            
            orb_levels = self.orb_levels[orb_key]
            current_price = current_candle.close
            current_volume = current_candle.volume
            
            # Check for high breakout
            if current_price > orb_levels.opening_range_high:
                signal = self._create_breakout_signal(
                    ticker, current_candle, "HIGH_BREAKOUT", 
                    orb_levels, timeframes_data
                )
                if signal:
                    signals.append(signal)
                    orb_levels.breakout_high = current_price
                    orb_levels.breakout_time = current_candle.timestamp
                    orb_levels.breakout_volume = current_volume
            
            # Check for low breakout
            elif current_price < orb_levels.opening_range_low:
                signal = self._create_breakout_signal(
                    ticker, current_candle, "LOW_BREAKOUT", 
                    orb_levels, timeframes_data
                )
                if signal:
                    signals.append(signal)
                    orb_levels.breakout_low = current_price
                    orb_levels.breakout_time = current_candle.timestamp
                    orb_levels.breakout_volume = current_volume
            
            # Update ORB levels if needed
            if signals:
                self._update_orb_levels(orb_levels, current_candle, timeframes_data)
            
        except Exception as e:
            logger.error(f"Error checking breakouts for {ticker}: {e}", exc_info=True)
        
        return signals
    
    def _create_breakout_signal(self, ticker: str, candle, signal_type: str, 
                               orb_levels: ORBLevels, timeframes_data: Dict) -> Optional[BreakoutSignal]:
        """Create a breakout signal with confirmation data."""
        try:
            # Check timeframe confirmations
            confirmations = self._check_timeframe_confirmations(
                ticker, signal_type, orb_levels, timeframes_data
            )
            
            # Calculate confidence score
            confidence = self._calculate_confidence_score(confirmations, candle, orb_levels)
            
            # Only generate signal if confidence is high enough
            if confidence < 0.6:  # 60% minimum confidence
                return None
            
            signal = BreakoutSignal(
                ticker=ticker,
                timestamp=candle.timestamp,
                signal_type=signal_type,
                price=candle.close,
                volume=candle.volume,
                timeframe_confirmations=confirmations,
                confidence_score=confidence,
                orb_levels=orb_levels
            )
            
            # Store signal
            self.breakout_signals.append(signal)
            
            logger.info(f"Breakout signal generated: {ticker} {signal_type} @ ${candle.close:.2f} "
                       f"(Confidence: {confidence:.1%})")
            
            return signal
            
        except Exception as e:
            logger.error(f"Error creating breakout signal: {e}")
            return None
    
    def _check_timeframe_confirmations(self, ticker: str, signal_type: str, 
                                     orb_levels: ORBLevels, timeframes_data: Dict) -> List[str]:
        """Check which timeframes confirm the breakout."""
        confirmations = []
        
        try:
            # Check each timeframe for confirmation
            for tf, data in timeframes_data.items():
                if not data or len(data) < 2:
                    continue
                
                # Get last two candles for trend confirmation
                last_candle = data[-1]
                prev_candle = data[-2]
                
                if signal_type == "HIGH_BREAKOUT":
                    # Check if higher timeframe shows bullish momentum
                    if (last_candle.close > prev_candle.close and 
                        last_candle.close > orb_levels.opening_range_high):
                        confirmations.append(tf)
                
                elif signal_type == "LOW_BREAKOUT":
                    # Check if higher timeframe shows bearish momentum
                    if (last_candle.close < prev_candle.close and 
                        last_candle.close < orb_levels.opening_range_low):
                        confirmations.append(tf)
            
            logger.debug(f"Timeframe confirmations for {ticker} {signal_type}: {confirmations}")
            
        except Exception as e:
            logger.error(f"Error checking timeframe confirmations: {e}")
        
        return confirmations
    
    def _calculate_confidence_score(self, confirmations: List[str], candle, 
                                  orb_levels: ORBLevels) -> float:
        """Calculate confidence score for breakout signal."""
        try:
            base_score = 0.5  # Base confidence
            
            # Timeframe confirmation bonus
            tf_bonus = len(confirmations) * 0.1  # 10% per confirming timeframe
            base_score += min(tf_bonus, 0.3)  # Max 30% bonus
            
            # Volume confirmation
            if hasattr(candle, 'volume') and hasattr(candle, 'volume_avg'):
                if candle.volume > candle.volume_avg * self.volume_threshold:
                    base_score += 0.2  # 20% volume bonus
            
            # Price momentum
            if hasattr(candle, 'price_change_pct'):
                momentum_bonus = min(abs(candle.price_change_pct) * 0.1, 0.2)
                base_score += momentum_bonus
            
            # Ensure score is between 0 and 1
            return max(0.0, min(1.0, base_score))
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.5
    
    def _update_orb_levels(self, orb_levels: ORBLevels, current_candle, timeframes_data: Dict):
        """Update ORB levels with new information."""
        try:
            # Update confirmation timeframes
            if orb_levels.confirmation_timeframes is None:
                orb_levels.confirmation_timeframes = []
            
            # Add new confirmations
            for tf, data in timeframes_data.items():
                if tf not in orb_levels.confirmation_timeframes and data:
                    orb_levels.confirmation_timeframes.append(tf)
            
            # Calculate strength score
            orb_levels.strength_score = len(orb_levels.confirmation_timeframes) * 0.2
            
            logger.debug(f"Updated ORB levels for {orb_levels.ticker}: "
                        f"Strength={orb_levels.strength_score:.1f}")
            
        except Exception as e:
            logger.error(f"Error updating ORB levels: {e}")
    
    def get_orb_levels(self, ticker: str, date: datetime = None) -> Optional[ORBLevels]:
        """Get ORB levels for a specific ticker and date."""
        if date is None:
            date = datetime.now().date()
        
        key = f"{ticker}_{date}"
        return self.orb_levels.get(key)
    
    def get_breakout_signals(self, ticker: str = None, date: datetime = None) -> List[BreakoutSignal]:
        """Get breakout signals, optionally filtered by ticker and date."""
        signals = self.breakout_signals
        
        if ticker:
            signals = [s for s in signals if s.ticker == ticker]
        
        if date:
            signals = [s for s in signals if s.timestamp.date() == date.date()]
        
        return signals
    
    def get_orb_summary(self, ticker: str, date: datetime = None) -> Dict:
        """Get comprehensive ORB summary for a ticker."""
        if date is None:
            date = datetime.now().date()
        
        orb_levels = self.get_orb_levels(ticker, date)
        if not orb_levels:
            return {"error": "No ORB levels found"}
        
        signals = self.get_breakout_signals(ticker, date)
        
        return {
            "ticker": ticker,
            "date": date.isoformat(),
            "orb_levels": {
                "high": orb_levels.opening_range_high,
                "low": orb_levels.opening_range_low,
                "mid": orb_levels.opening_range_mid
            },
            "breakouts": {
                "high_breakout": {
                    "price": orb_levels.breakout_high,
                    "time": orb_levels.breakout_time.isoformat() if orb_levels.breakout_time else None,
                    "volume": orb_levels.breakout_volume
                },
                "low_breakout": {
                    "price": orb_levels.breakout_low,
                    "time": orb_levels.breakout_time.isoformat() if orb_levels.breakout_time else None,
                    "volume": orb_levels.breakout_volume
                }
            },
            "confirmations": orb_levels.confirmation_timeframes,
            "strength_score": orb_levels.strength_score,
            "signals_count": len(signals)
        } 