"""
Timeframe Engine Service

Handles real-time aggregation of 1m data into higher timeframes
and maintains multi-timeframe state for all tickers.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from services.orb_detector import ORBDetector

logger = logging.getLogger(__name__)

@dataclass
class Candle:
    """Standardized candle data structure."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    ticker: str
    timeframe: str

class TimeframeEngine:
    """
    Core engine for building higher timeframes from 1m data.
    """
    
    def __init__(self, base_resolution: str = "1m", secondary_tf: str = "5m"):
        self.base_resolution = base_resolution
        self.secondary_tf = secondary_tf
        self.derived_tfs = ["15m", "1h", "4h"]
        
        # Timeframe multipliers (in minutes)
        self.tf_multipliers = {
            "5m": 5,
            "15m": 15,
            "1h": 60,
            "4h": 240
        }
        
        # Initialize ORB detector
        self.orb_detector = ORBDetector()
        
        logger.info(f"TimeframeEngine initialized: {base_resolution} -> {secondary_tf} + {self.derived_tfs}")
    
    def process_tick(self, ticker: str, new_candle: Dict[str, Any]) -> None:
        """
        Process incoming 1m candle and update all timeframes.
        
        Args:
            ticker: Stock symbol (e.g., 'AAPL')
            new_candle: Raw candle data from TradingView
        """
        try:
            # Validate and standardize candle data
            candle = self._standardize_candle(new_candle, ticker)
            
            # Store raw 1m data
            self._store_candle(ticker, "1m", candle)
            
            # Update all higher timeframes
            self._update_secondary_timeframes(ticker)
            
            # Check for heartbeat alerts
            self._check_heartbeat_alerts(ticker, candle)
            
            # Check for ORB breakouts
            self._check_orb_breakouts(ticker, candle)
            
            logger.debug(f"Processed {ticker} 1m candle at {candle.timestamp}")
            
        except Exception as e:
            logger.error(f"Error processing tick for {ticker}: {e}", exc_info=True)
    
    def _standardize_candle(self, raw_candle: Dict[str, Any], ticker: str) -> Candle:
        """Convert raw candle data to standardized format."""
        return Candle(
            timestamp=datetime.fromisoformat(raw_candle.get('timestamp', datetime.now().isoformat())),
            open=float(raw_candle.get('open', 0)),
            high=float(raw_candle.get('high', 0)),
            low=float(raw_candle.get('low', 0)),
            close=float(raw_candle.get('close', 0)),
            volume=int(raw_candle.get('volume', 0)),
            ticker=ticker,
            timeframe="1m"
        )
    
    def _store_candle(self, ticker: str, timeframe: str, candle: Candle) -> None:
        """Store candle data (placeholder for Redis integration)."""
        # TODO: Replace with actual Redis storage
        key = f"candles:{ticker}:{timeframe}"
        logger.debug(f"Storing {key}: {candle}")
        
        # For now, use test storage for testing
        self._store_test_candle(ticker, timeframe, candle)
        
        # Redis.current().lpush(key, json.dumps(candle.__dict__))
    
    def _update_secondary_timeframes(self, ticker: str) -> None:
        """Build higher timeframes from 1m data."""
        try:
            # Get stored 1m candles
            raw_1m = self._get_stored_candles(ticker, "1m")
            
            if not raw_1m:
                logger.warning(f"No 1m data available for {ticker}")
                return
            
            # Update each timeframe
            for tf in [self.secondary_tf] + self.derived_tfs:
                agg_candles = self._resample_candles(raw_1m, self.tf_multipliers[tf])
                if agg_candles:
                    self._store_candle(ticker, tf, agg_candles[-1])
                    logger.debug(f"Updated {ticker} {tf}: {len(agg_candles)} candles")
                    
        except Exception as e:
            logger.error(f"Error updating timeframes for {ticker}: {e}", exc_info=True)
    
    def _resample_candles(self, candles: List[Candle], target_minutes: int) -> List[Candle]:
        """
        Resample 1m candles to target timeframe.
        
        Args:
            candles: List of 1m candles
            target_minutes: Target timeframe in minutes
            
        Returns:
            List of aggregated candles
        """
        if not candles:
            return []
        
        # Sort by timestamp
        sorted_candles = sorted(candles, key=lambda x: x.timestamp)
        
        aggregated = []
        current_group = []
        current_timestamp = None
        
        for candle in sorted_candles:
            # Determine if this candle starts a new group
            if current_timestamp is None:
                current_timestamp = candle.timestamp
                current_group = [candle]
            else:
                # Check if we've reached the target timeframe
                time_diff = (candle.timestamp - current_timestamp).total_seconds() / 60
                
                if time_diff >= target_minutes:
                    # Create aggregated candle from current group
                    if current_group:
                        agg_candle = self._aggregate_candle_group(current_group, target_minutes)
                        aggregated.append(agg_candle)
                    
                    # Start new group
                    current_timestamp = candle.timestamp
                    current_group = [candle]
                else:
                    current_group.append(candle)
        
        # Don't forget the last group
        if current_group:
            agg_candle = self._aggregate_candle_group(current_group, target_minutes)
            aggregated.append(agg_candle)
        
        return aggregated
    
    def _aggregate_candle_group(self, candles: List[Candle], timeframe_minutes: int) -> Candle:
        """Aggregate a group of candles into a single higher timeframe candle."""
        if not candles:
            raise ValueError("Cannot aggregate empty candle group")
        
        # Use first candle's ticker and timeframe info
        base_candle = candles[0]
        
        return Candle(
            timestamp=base_candle.timestamp,
            open=candles[0].open,
            high=max(c.high for c in candles),
            low=min(c.low for c in candles),
            close=candles[-1].close,
            volume=sum(c.volume for c in candles),
            ticker=base_candle.ticker,
            timeframe=f"{timeframe_minutes}m" if timeframe_minutes < 60 else f"{timeframe_minutes//60}h"
        )
    
    def _get_stored_candles(self, ticker: str, timeframe: str) -> List[Candle]:
        """Get stored candles for a ticker/timeframe (placeholder for Redis)."""
        # TODO: Replace with actual Redis retrieval
        # For now, use test storage for testing
        return self._get_test_candles(ticker, timeframe)
    
    def _store_test_candle(self, ticker: str, timeframe: str, candle: Candle) -> None:
        """Store candle data for testing (in-memory storage)."""
        if not hasattr(self, '_test_storage'):
            self._test_storage = {}
        
        key = f"{ticker}:{timeframe}"
        if key not in self._test_storage:
            self._test_storage[key] = []
        
        self._test_storage[key].append(candle)
    
    def _get_test_candles(self, ticker: str, timeframe: str) -> List[Candle]:
        """Get stored candles for testing (in-memory storage)."""
        if not hasattr(self, '_test_storage'):
            return []
        
        key = f"{ticker}:{timeframe}"
        return self._test_storage.get(key, [])
    
    def _check_heartbeat_alerts(self, ticker: str, candle: Candle) -> None:
        """Check for immediate heartbeat alerts on 1m data."""
        try:
            # Get recent candles for comparison
            recent_candles = self._get_stored_candles(ticker, "1m")
            
            if len(recent_candles) < 5:  # Need at least 5 candles for analysis
                return
            
            # Calculate basic metrics
            avg_volume = sum(c.volume for c in recent_candles[-5:]) / 5
            avg_range = sum(abs(c.high - c.low) for c in recent_candles[-5:]) / 5
            
            # Check for alerts
            alerts = []
            
            # Volume spike
            if candle.volume > 3 * avg_volume:
                alerts.append({
                    "type": "VOLUME_SPIKE",
                    "ticker": ticker,
                    "timestamp": candle.timestamp,
                    "volume": candle.volume,
                    "avg_volume": avg_volume,
                    "multiplier": candle.volume / avg_volume
                })
            
            # Price range spike
            current_range = abs(candle.high - candle.low)
            if current_range > 2 * avg_range:
                alerts.append({
                    "type": "VOLATILITY_SPIKE",
                    "ticker": ticker,
                    "timestamp": candle.timestamp,
                    "range": current_range,
                    "avg_range": avg_range,
                    "multiplier": current_range / avg_range
                })
            
            # Log alerts
            for alert in alerts:
                logger.info(f"HEARTBEAT ALERT: {alert}")
                # TODO: Send to alert service
                
        except Exception as e:
            logger.error(f"Error checking heartbeat alerts for {ticker}: {e}", exc_info=True)
    
    def _check_orb_breakouts(self, ticker: str, candle: Candle) -> None:
        """Check for Opening Range Breakout patterns."""
        try:
            # Get all timeframe data for confirmation
            all_timeframes = self.get_all_timeframes(ticker)
            
            # Check for ORB breakouts
            breakout_signals = self.orb_detector.check_breakouts(
                ticker, candle, all_timeframes
            )
            
            # Process any breakout signals
            for signal in breakout_signals:
                logger.info(f"ORB BREAKOUT DETECTED: {ticker} {signal.signal_type} "
                           f"@ ${signal.price:.2f} (Confidence: {signal.confidence_score:.1%})")
                
                # TODO: Send to alert service or notification system
                
        except Exception as e:
            logger.error(f"Error checking ORB breakouts for {ticker}: {e}", exc_info=True)
    
    def get_orb_summary(self, ticker: str, date: datetime = None) -> Dict:
        """Get ORB summary for a ticker."""
        return self.orb_detector.get_orb_summary(ticker, date)
    
    def get_orb_levels(self, ticker: str, date: datetime = None):
        """Get ORB levels for a ticker."""
        return self.orb_detector.get_orb_levels(ticker, date)
    
    def get_timeframe_data(self, ticker: str, timeframe: str) -> Optional[List[Candle]]:
        """Get current timeframe data for a ticker."""
        try:
            return self._get_stored_candles(ticker, timeframe)
        except Exception as e:
            logger.error(f"Error getting {timeframe} data for {ticker}: {e}")
            return None
    
    def get_all_timeframes(self, ticker: str) -> Dict[str, List[Candle]]:
        """Get all timeframe data for a ticker."""
        timeframes = ["1m", self.secondary_tf] + self.derived_tfs
        
        return {
            tf: self.get_timeframe_data(ticker, tf) or []
            for tf in timeframes
        } 