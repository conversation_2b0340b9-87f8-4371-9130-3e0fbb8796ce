
"""
Unified Data Access Layer

Provides a single interface for accessing both SQLAlchemy and Supabase databases
with correlation ID logging, connection pooling, and automatic failover.
"""

from abc import ABC, abstractmethod
import asyncio
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, Union, AsyncGenerator

import backoff
from connection import engine
from query_wrapper import execute_query_with_correlation
from query_wrapper import execute_query_with_correlation
from query_wrapper import execute_query_with_correlation
from query_wrapper import execute_query_with_correlation

from src.core.exceptions import DatabaseError, ConfigurationError
from src.core.logger import get_logger
        
        where_clause = " AND ".join(f"{k} = :{k}" for k in filters.keys())
        query = f"DELETE FROM {table} WHERE {where_clause}"
        
        result = await execute_query_with_correlation(
            query=query,
            params=filters,
            correlation_id=correlation_id,
            query_type="delete"
        )
        
        return getattr(result, 'rowcount', 0) > 0
    
    async def execute_raw(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> Any:
        """Execute raw SQL query."""
        await self._ensure_initialized()