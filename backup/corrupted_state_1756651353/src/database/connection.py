import asyncio
import os
from typing import Dict, Optional

from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

from src.core.logger import get_logger

logger = get_logger(__name__)

# Global database manager instance
db_manager = None


class TransactionContext:
    """Transaction context manager for database operations."""
    
    def __init__(self, conn):
        self._conn = conn
        self._tx = None

    async def __aenter__(self):
        # begin the transaction in a background thread
        self._tx = await asyncio.to_thread(self._conn.begin)
        return self._tx

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Commit if no exception, otherwise rollback
        if exc_type is None:
            try:
                await asyncio.to_thread(self._tx.commit)
            except Exception:
                await asyncio.to_thread(self._tx.rollback)
        else:
            await asyncio.to_thread(self._tx.rollback)


class ConnectionWrapper:
    """Wrapper for database connections to provide async interface."""
    
    def __init__(self, conn):
        self._conn = conn
    
    async def transaction(self):
        """Get a transaction context."""
        return TransactionContext(self._conn)
        
    @property
    def rowcount(self):
        return getattr(self._conn, 'rowcount', 0)
        
    def commit(self):
        """Commit the transaction"""
        return self._conn.commit()
        
    def rollback(self):
        """Rollback the transaction"""
        return self._conn.rollback()


def get_database_connection():
    """Get a database connection."""
    try:
        # Use a simple SQLite database for now
        db_url = 'sqlite:///trading_bot.db'
        
        # Create engine
        engine = create_engine(db_url, poolclass=QueuePool)
        
        # Create synchronous connection and wrap it for async use
        conn = engine.connect()
        return ConnectionWrapper(conn)
        
    except Exception as e:
        logger.error(f"Failed to create database connection: {e}")
        return None


# Initialize global database manager
try:
    db_manager = get_database_connection()
    if db_manager:
        logger.info("Database connection initialized successfully")
    else:
        logger.warning("Database connection failed, running without database")
except Exception as e:
    logger.warning(f"Failed to initialize database connection: {e}")
    db_manager = None