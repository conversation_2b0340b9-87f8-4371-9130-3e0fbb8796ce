#!/usr/bin/env python3
"""
ORB (Opening Range Breakout) Detection Demonstration

This script shows how the system detects and tracks ORB levels
and breakout patterns across multiple timeframes.
"""

import sys
import os
import logging
from datetime import datetime, time, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.timeframe_service import TimeframeEngine
from services.orb_detector import ORBDetector

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_market_session_data():
    """Create realistic market session data for ORB demonstration."""
    # Market opens at 9:30 AM EST
    market_open = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    
    # Create 1-minute candles for the entire session
    candles = []
    
    # Opening range (9:30-10:00) - 30 minutes
    base_price = 175.0
    for i in range(30):
        candle_time = market_open + timedelta(minutes=i)
        
        # Simulate opening range with some volatility
        if i < 15:  # First 15 minutes
            price_change = (i % 3 - 1) * 0.2  # Oscillating pattern
        else:  # Second 15 minutes
            price_change = (i % 2) * 0.1  # Slight uptrend
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price + price_change,
            'high': base_price + price_change + 0.3,
            'low': base_price + price_change - 0.2,
            'close': base_price + price_change + 0.1,
            'volume': 500000 + (i * 1000),
            'ticker': 'AAPL',
            'timeframe': '1m'
        })()
        candles.append(candle)
    
    # Post-opening range (10:00+) - simulate breakout
    for i in range(30, 60):  # 10:00 AM to 11:00 AM
        candle_time = market_open + timedelta(minutes=i)
        
        # Simulate breakout above ORB high
        if i == 30:  # First minute after ORB
            price_change = 0.8  # Break above ORB high
            volume = 800000  # High volume breakout
        else:
            price_change = 0.1 + (i % 5) * 0.05  # Continued uptrend
            volume = 600000 + (i % 10) * 1000
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': base_price + price_change - 0.1,
            'high': base_price + price_change + 0.2,
            'low': base_price + price_change - 0.1,
            'close': base_price + price_change,
            'volume': volume,
            'ticker': 'AAPL',
            'timeframe': '1m'
        })()
        candles.append(candle)
    
    return candles

def demonstrate_orb_detection():
    """Demonstrate ORB detection capabilities."""
    print("🎯 **ORB Detection Demonstration**")
    print("=" * 50)
    
    # Create timeframe engine
    engine = TimeframeEngine(base_resolution="1m", secondary_tf="5m")
    
    # Create market session data
    session_candles = create_market_session_data()
    print(f"📊 Created {len(session_candles)} minutes of market session data")
    
    # Process opening range (9:30-10:00)
    print("\n🕐 **Processing Opening Range (9:30-10:00 AM)**")
    opening_range_candles = session_candles[:30]
    
    for candle in opening_range_candles:
        # Store in timeframe engine
        engine._store_test_candle("AAPL", "1m", candle)
        
        # Check if opening range is complete
        if candle.timestamp.time() >= time(10, 0):
            orb_levels = engine.orb_detector.process_opening_range(
                "AAPL", opening_range_candles, candle.timestamp
            )
            if orb_levels:
                print(f"✅ ORB Levels Established:")
                print(f"  • High: ${orb_levels.opening_range_high:.2f}")
                print(f"  • Low: ${orb_levels.opening_range_low:.2f}")
                print(f"  • Mid: ${orb_levels.opening_range_mid:.2f}")
                break
    
    # Process post-opening range and detect breakouts
    print("\n🚀 **Processing Post-ORB Data (10:00 AM+)**")
    post_orb_candles = session_candles[30:]
    
    for i, candle in enumerate(post_orb_candles):
        # Store in timeframe engine
        engine._store_test_candle("AAPL", "1m", candle)
        
        # Update timeframes
        engine._update_secondary_timeframes("AAPL")
        
        # Check for breakouts
        all_timeframes = engine.get_all_timeframes("AAPL")
        breakout_signals = engine.orb_detector.check_breakouts(
            "AAPL", candle, all_timeframes
        )
        
        # Report any breakouts
        for signal in breakout_signals:
            print(f"🎯 BREAKOUT DETECTED at {candle.timestamp.strftime('%H:%M')}:")
            print(f"  • Type: {signal.signal_type}")
            print(f"  • Price: ${signal.price:.2f}")
            print(f"  • Confidence: {signal.confidence_score:.1%}")
            print(f"  • Timeframe Confirmations: {signal.timeframe_confirmations}")
            
            # Get ORB summary
            summary = engine.get_orb_summary("AAPL")
            if "error" not in summary:
                print(f"  • ORB High: ${summary['orb_levels']['high']:.2f}")
                print(f"  • ORB Low: ${summary['orb_levels']['low']:.2f}")
                print(f"  • Breakout Price: ${summary['breakouts']['high_breakout']['price']:.2f}")
                print(f"  • Breakout Time: {summary['breakouts']['high_breakout']['time']}")
                print(f"  • Strength Score: {summary['strength_score']:.1f}")
            
            return  # Found breakout, stop processing
    
    print("ℹ️  No breakouts detected in this session")

def demonstrate_orb_analysis():
    """Demonstrate ORB analysis capabilities."""
    print("\n📊 **ORB Analysis Demonstration**")
    print("=" * 50)
    
    # Create engine and process some data
    engine = TimeframeEngine()
    
    # Create sample data with known ORB levels
    base_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    base_price = 100.0
    
    # Opening range candles
    for i in range(30):
        candle_time = base_time + timedelta(minutes=i)
        price = base_price + (i % 3 - 1) * 0.1
        
        candle = type('MockCandle', (), {
            'timestamp': candle_time,
            'open': price,
            'high': price + 0.2,
            'low': price - 0.1,
            'close': price + 0.05,
            'volume': 500000,
            'ticker': 'TSLA',
            'timeframe': '1m'
        })()
        
        engine._store_test_candle("TSLA", "1m", candle)
    
    # Establish ORB levels
    opening_candles = [engine._get_test_candles("TSLA", "1m")[i] for i in range(30)]
    orb_levels = engine.orb_detector.process_opening_range(
        "TSLA", opening_candles, base_time + timedelta(minutes=30)
    )
    
    if orb_levels:
        print(f"✅ ORB Levels for TSLA:")
        print(f"  • High: ${orb_levels.opening_range_high:.2f}")
        print(f"  • Low: ${orb_levels.opening_range_low:.2f}")
        print(f"  • Range: ${orb_levels.opening_range_high - orb_levels.opening_range_low:.2f}")
        
        # Simulate breakout
        breakout_candle = type('MockCandle', (), {
            'timestamp': base_time + timedelta(minutes=31),
            'open': orb_levels.opening_range_high + 0.1,
            'high': orb_levels.opening_range_high + 0.3,
            'low': orb_levels.opening_range_high + 0.05,
            'close': orb_levels.opening_range_high + 0.2,
            'volume': 800000,
            'ticker': 'TSLA',
            'timeframe': '1m'
        })()
        
        # Check for breakout
        all_timeframes = engine.get_all_timeframes("TSLA")
        signals = engine.orb_detector.check_breakouts(
            "TSLA", breakout_candle, all_timeframes
        )
        
        if signals:
            print(f"\n🎯 Breakout Signal Generated:")
            signal = signals[0]
            print(f"  • Type: {signal.signal_type}")
            print(f"  • Price: ${signal.price:.2f}")
            print(f"  • Confidence: {signal.confidence_score:.1%}")
            print(f"  • Confirmations: {signal.timeframe_confirmations}")
        
        # Get final summary
        summary = engine.get_orb_summary("TSLA")
        print(f"\n📋 ORB Summary:")
        
        # Safely handle None values
        high_breakout = summary['breakouts']['high_breakout']
        if high_breakout['price'] is not None:
            print(f"  • Breakout Price: ${high_breakout['price']:.2f}")
        else:
            print(f"  • Breakout Price: No breakout yet")
            
        if high_breakout['time'] is not None:
            print(f"  • Breakout Time: {high_breakout['time']}")
        else:
            print(f"  • Breakout Time: No breakout yet")
            
        print(f"  • Strength Score: {summary['strength_score']:.1f}")
        print(f"  • Signals Count: {summary['signals_count']}")

def main():
    """Run the ORB detection demonstration."""
    print("🚀 **ORB Detection System Demonstration**")
    print("=" * 60)
    print("This demonstration shows how the system:")
    print("• Establishes Opening Range levels (9:30-10:00 AM)")
    print("• Detects breakouts above/below ORB levels")
    print("• Confirms breakouts across multiple timeframes")
    print("• Provides confidence scoring and analysis")
    print("=" * 60)
    
    try:
        # Demonstrate ORB detection
        demonstrate_orb_detection()
        
        # Demonstrate ORB analysis
        demonstrate_orb_analysis()
        
        print("\n🎉 **ORB Detection Demonstration Complete!**")
        print("The system successfully detects and tracks ORB patterns.")
        
    except Exception as e:
        print(f"\n❌ **Demonstration Failed:** {e}")
        logger.error(f"Demonstration error: {e}", exc_info=True)

if __name__ == "__main__":
    main() 