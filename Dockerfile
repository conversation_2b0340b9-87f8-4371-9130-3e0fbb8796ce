# Multi-stage build for security
FROM python:3.11-slim-bullseye AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip \
    && pip install --user -r requirements.txt

# Production stage
FROM python:3.11-slim-bullseye

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN useradd -m -u 1000 appuser && \
    mkdir -p /app && \
    chown -R appuser:appuser /app

# Set working directory
WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY --chown=appuser:appuser . .

# Create secrets directory with proper permissions
RUN mkdir -p /app/secrets && \
    chown -R appuser:appuser /app/secrets && \
    chmod 700 /app/secrets

# Add entrypoint script
COPY --chown=appuser:appuser docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Switch to non-root user
USER appuser

# Set PATH for user-installed packages
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Health check disabled - bot functionality verified through logs
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#     CMD echo "Bot is running" || exit 1

# Default command
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["python", "-m", "src.bot.client"]
