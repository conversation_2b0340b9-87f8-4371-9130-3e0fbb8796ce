#!/usr/bin/env python3
"""
Simple test to verify the order of operations in Docker.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

async def trace_ask_execution():
    """Trace the execution flow of the /ask command."""
    print("🔍 Tracing /ask command execution flow...")
    
    try:
        # Step 1: Import and create components
        print("\n1️⃣ Importing components...")
        from src.bot.pipeline.commands.ask.stages.ai_chat_processor import FlexibleAIChatProcessor
        from src.bot.pipeline.commands.ask.config import AskPipelineConfig
        
        # Step 2: Create processor
        print("2️⃣ Creating AI processor...")
        config = AskPipelineConfig()
        processor = FlexibleAIChatProcessor(config.to_dict())
        
        # Check which processor it's using
        if hasattr(processor, 'use_enhanced') and processor.use_enhanced:
            print("✅ Using enhanced AI processor (with tool execution)")
        else:
            print("⚠️ Using basic AI processor (no tool execution)")
        
        # Step 3: Process a query
        print("3️⃣ Processing test query...")
        test_query = "Analyze AAPL technical indicators"
        print(f"Query: {test_query}")
        
        result = await processor.process(test_query)
        
        # Step 4: Analyze result
        print("4️⃣ Analyzing result...")
        print(f"Result type: {type(result)}")
        print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            if 'response' in result:
                response = result['response']
                print(f"Response length: {len(response)} characters")
                print(f"Response preview: {response[:150]}...")
                
                # Check for signs of real analysis vs fake responses
                analysis_indicators = [
                    'price:', 'rsi:', 'volume:', 'technical', 'analysis',
                    'bullish', 'bearish', 'support', 'resistance', 'macd'
                ]
                
                found_indicators = [ind for ind in analysis_indicators if ind.lower() in response.lower()]
                if found_indicators:
                    print(f"✅ Found analysis indicators: {found_indicators}")
                else:
                    print("⚠️ No specific analysis indicators found")
            
            if 'tools_used' in result:
                tools = result['tools_used']
                print(f"✅ Tools used: {tools}")
            else:
                print("⚠️ No tools_used information")
            
            if 'intent' in result:
                intent = result['intent']
                print(f"✅ Detected intent: {intent}")
            else:
                print("⚠️ No intent information")
        
        return True
        
    except Exception as e:
        print(f"❌ Execution trace failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_processor_availability():
    """Test if the enhanced processor is available."""
    print("\n🔧 Testing enhanced processor availability...")
    
    try:
        # Try to import the enhanced processor directly
        from src.bot.pipeline.commands.ask.stages.enhanced_ai_chat_processor import enhanced_ai_processor
        print("✅ Enhanced AI processor is available")
        
        # Check if it has the expected methods
        if hasattr(enhanced_ai_processor, 'process_expert_conversation'):
            print("✅ Enhanced processor has process_expert_conversation method")
        else:
            print("❌ Enhanced processor missing process_expert_conversation method")
        
        # Try to import tool registry and executor
        from src.bot.pipeline.commands.ask.stages.ai_tool_registry import tool_registry
        from src.bot.pipeline.commands.ask.stages.ai_tool_executor import tool_executor
        print("✅ Tool registry and executor are available")
        
        # Check available tools
        available_tools = list(tool_registry.tools.keys())
        print(f"✅ Available tools: {available_tools[:5]}..." if len(available_tools) > 5 else f"✅ Available tools: {available_tools}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Enhanced processor not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing enhanced processor: {e}")
        return False

async def test_full_pipeline_flow():
    """Test the full pipeline flow."""
    print("\n🚀 Testing full pipeline flow...")
    
    try:
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Execute with a trading-specific query
        query = "What's the current trend for AAPL?"
        print(f"Executing pipeline with query: {query}")
        
        result = await execute_ask_pipeline(
            query=query,
            user_id="test_user",
            guild_id="test_guild"
        )
        
        print(f"Pipeline result type: {type(result)}")
        
        # Check the result structure
        if hasattr(result, 'processing_results'):
            processing_results = result.processing_results
            print(f"Processing results keys: {list(processing_results.keys())}")
            
            if 'response' in processing_results:
                response = processing_results['response']
                print(f"Pipeline response preview: {response[:100]}...")
                
                # Check if response indicates tool usage
                if any(word in response.lower() for word in ['price', 'analysis', 'technical', 'market']):
                    print("✅ Response contains trading analysis content")
                else:
                    print("⚠️ Response may not contain real trading analysis")
            else:
                print("⚠️ No response in processing results")
        
        if hasattr(result, 'status'):
            print(f"Pipeline status: {result.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Full pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all order tests."""
    print("🚀 Testing /ask command order of operations in Docker...\n")
    
    test1_passed = await trace_ask_execution()
    test2_passed = await test_enhanced_processor_availability()
    test3_passed = await test_full_pipeline_flow()
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 Order of operations tests passed!")
        print("\n📋 Execution Flow Verified:")
        print("1. ✅ AI processor created")
        print("2. ✅ Enhanced processor available")
        print("3. ✅ Tools registry accessible")
        print("4. ✅ Pipeline executes successfully")
        print("5. ✅ Results contain trading analysis")
        print("\n🔧 Your /ask command should now work with proper tool execution!")
    else:
        print("\n❌ Some order of operations tests failed.")
        print("Check the errors above to see what needs to be fixed.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
