# Error Handling Guide - Task 4.3

## Overview

This guide documents the standardized error handling patterns implemented across the trading bot codebase. It ensures consistent error responses, proper logging, and maintainable error handling code.

## Error Handling Principles

### 1. Use Specific Exception Types
- **Avoid**: `except Exception:` (too generic)
- **Prefer**: `except ValueError:`, `except TimeoutError:`, etc.
- **Use**: Custom exception classes for domain-specific errors

### 2. Standardized Error Responses
- All errors return consistent response structure
- Include correlation IDs for debugging
- Provide user-friendly messages
- Log detailed error information

### 3. Proper Error Logging
- Log errors with context (function, correlation_id, error_type)
- Include relevant arguments and state
- Use appropriate log levels

## Error Response Structure

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "code": "VALIDATION_ERROR",
    "message": "Invalid symbol: AAPL123",
    "timestamp": "2025-08-30T12:00:00Z",
    "correlation_id": "req_12345",
    "context": {
      "field": "symbol",
      "value": "AAPL123",
      "constraint": "Alphanumeric only"
    }
  }
}
```

### API Error Response
```json
{
  "success": false,
  "status_code": 400,
  "error": {
    "type": "ValidationError",
    "code": "VALIDATION_ERROR",
    "message": "Invalid symbol: AAPL123",
    "timestamp": "2025-08-30T12:00:00Z",
    "correlation_id": "req_12345",
    "http_status": 400,
    "context": {
      "field": "symbol",
      "value": "AAPL123",
      "constraint": "Alphanumeric only"
    }
  }
}
```

### Discord Error Response
```json
{
  "success": false,
  "platform": "discord",
  "error": {
    "type": "ValidationError",
    "code": "VALIDATION_ERROR",
    "message": "Invalid symbol: AAPL123",
    "timestamp": "2025-08-30T12:00:00Z",
    "correlation_id": "req_12345",
    "user_visible": true,
    "context": {
      "field": "symbol",
      "value": "AAPL123",
      "constraint": "Alphanumeric only"
    }
  }
}
```

## Using Error Handling Decorators

### Basic Error Handling
```python
from src.shared.error_handling.decorators import handle_errors

@handle_errors()
def risky_function():
    # Function that might raise exceptions
    pass
```

### Specific Error Types
```python
@handle_errors(error_types=(ValueError, TypeError))
def validation_function():
    # Only catch specific error types
    pass
```

### Custom Default Response
```python
@handle_errors(default_response={"error": "Custom error message"})
def function_with_custom_response():
    pass
```

### Validation Error Handling
```python
from src.shared.error_handling.decorators import handle_validation_errors

def validate_symbol(value):
    if not value.isalpha():
        raise ValueError("Symbol must be alphabetic")

@handle_validation_errors(field_validators={"symbol": validate_symbol})
def process_symbol(symbol: str):
    # Function will return standardized validation error response
    pass
```

### Timeout Error Handling
```python
from src.shared.error_handling.decorators import handle_timeout_errors

@handle_timeout_errors(timeout_seconds=30.0)
def long_running_function():
    # Function will return timeout error response if it takes too long
    pass
```

### Retry Logic
```python
from src.shared.error_handling.decorators import retry_on_error

@retry_on_error(max_attempts=3, delay_seconds=1.0, backoff_factor=2.0)
def unreliable_function():
    # Function will retry up to 3 times with exponential backoff
    pass
```

### Error Logging
```python
from src.shared.error_handling.decorators import log_errors

@log_errors(log_level="ERROR", include_args=True)
def function_with_logging():
    # All errors will be logged with function arguments
    pass
```

## Manual Error Handling

### Creating Error Responses
```python
from src.shared.error_handling.response_templates import (
    create_error_response,
    create_api_error_response,
    create_discord_error_response
)

# Standard error response
error_response = create_error_response(
    error=ValueError("Invalid input"),
    error_code="VALIDATION_ERROR",
    user_message="Please provide valid input",
    correlation_id="req_12345"
)

# API error response
api_error = create_api_error_response(
    error=ValueError("Invalid input"),
    status_code=400,
    error_code="VALIDATION_ERROR",
    user_message="Please provide valid input",
    correlation_id="req_12345"
)

# Discord error response
discord_error = create_discord_error_response(
    error=ValueError("Invalid input"),
    error_code="VALIDATION_ERROR",
    user_message="Please provide valid input",
    correlation_id="req_12345"
)
```

### Using Error Handler
```python
from src.shared.error_handling.response_templates import ErrorHandler

try:
    result = risky_operation()
except Exception as e:
    error_response = ErrorHandler.handle_generic_error(
        error=e,
        operation="risky_operation",
        correlation_id="req_12345"
    )
    return error_response
```

## Custom Exception Classes

### Creating Domain-Specific Exceptions
```python
from src.core.exceptions import TradingBotBaseException, ErrorCategory, ErrorSeverity

class SymbolValidationError(TradingBotBaseException):
    """Raised when symbol validation fails."""
    
    def __init__(self, symbol: str, reason: str, **kwargs):
        super().__init__(
            message=f"Symbol '{symbol}' validation failed: {reason}",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            context={'symbol': symbol, 'reason': reason},
            **kwargs
        )

class MarketDataError(TradingBotBaseException):
    """Raised when market data operations fail."""
    
    def __init__(self, operation: str, symbol: str, **kwargs):
        super().__init__(
            message=f"Market data operation '{operation}' failed for {symbol}",
            category=ErrorCategory.DATA_ACCESS,
            severity=ErrorSeverity.HIGH,
            context={'operation': operation, 'symbol': symbol},
            **kwargs
        )
```

### Using Custom Exceptions
```python
def validate_symbol(symbol: str):
    if not symbol.isalpha():
        raise SymbolValidationError(
            symbol=symbol,
            reason="Symbol must contain only letters"
        )
    
    if len(symbol) > 5:
        raise SymbolValidationError(
            symbol=symbol,
            reason="Symbol must be 5 characters or less"
        )

def fetch_market_data(symbol: str):
    try:
        # Attempt to fetch data
        data = api_client.get_data(symbol)
        if not data:
            raise MarketDataError(
                operation="fetch_data",
                symbol=symbol
            )
        return data
    except Exception as e:
        # Re-raise as domain-specific exception
        raise MarketDataError(
            operation="fetch_data",
            symbol=symbol,
            original_error=e
        )
```

## Best Practices

### 1. Error Context
- Always include relevant context in error responses
- Use correlation IDs for request tracking
- Provide actionable error messages

### 2. Logging
- Log errors at appropriate levels
- Include correlation IDs in log entries
- Don't log sensitive information

### 3. User Experience
- Provide user-friendly error messages
- Include suggestions for resolution
- Don't expose internal system details

### 4. Performance
- Use decorators for common error handling patterns
- Avoid deep exception hierarchies
- Cache error responses when appropriate

### 5. Testing
- Test error handling paths
- Verify error response formats
- Test retry logic and timeouts

## Migration Guide

### From Generic Exception Handling
```python
# Before
try:
    result = operation()
except Exception as e:
    return {"error": str(e)}

# After
from src.shared.error_handling.decorators import handle_errors

@handle_errors()
def operation():
    # Function implementation
    pass
```

### From Manual Error Responses
```python
# Before
try:
    result = operation()
except ValueError as e:
    return {"error": "Validation failed", "details": str(e)}

# After
from src.shared.error_handling.response_templates import create_error_response

try:
    result = operation()
except ValueError as e:
    return create_error_response(
        error=e,
        error_code="VALIDATION_ERROR",
        user_message="Validation failed"
    )
```

## Error Codes Reference

### Common Error Codes
- `VALIDATION_ERROR`: Input validation failures
- `TIMEOUT_ERROR`: Operation timeouts
- `OPERATION_FAILED`: General operation failures
- `DATA_ACCESS_ERROR`: Data retrieval/access issues
- `CONFIGURATION_ERROR`: Configuration problems
- `AUTHENTICATION_ERROR`: Authentication/authorization failures
- `RATE_LIMIT_ERROR`: API rate limit exceeded
- `NETWORK_ERROR`: Network connectivity issues

### Error Categories
- `VALIDATION`: Input validation and data format issues
- `BUSINESS_LOGIC`: Business rule violations
- `DATA_ACCESS`: Database and external API access issues
- `CONFIGURATION`: Configuration and environment problems
- `PIPELINE`: Pipeline execution and stage failures
- `EXTERNAL_SERVICE`: Third-party service integration issues

### Error Severity Levels
- `LOW`: Non-critical issues, warnings
- `MEDIUM`: Standard errors, user action required
- `HIGH`: Important failures, system impact
- `CRITICAL`: System-breaking errors, immediate attention required

## Conclusion

This standardized error handling approach provides:
- **Consistency**: All errors follow the same response format
- **Maintainability**: Centralized error handling logic
- **Debugging**: Correlation IDs and structured logging
- **User Experience**: Clear, actionable error messages
- **Flexibility**: Decorators and manual handling options

Follow these patterns to ensure your code integrates seamlessly with the existing error handling infrastructure. 