# AI Trading Bot - Testing Guide

## 🧪 Test Suite Overview

### Prerequisites
- Python 3.11+
- Pytest
- Supabase credentials
- Discord Bot Token

### Running Tests

#### Basic Test Run
```bash
pytest
```

#### Specific Test Types
```bash
# Run only integration tests
pytest -m integration

# Run only Supabase tests
pytest -m supabase

# Run tests with coverage report
pytest --cov=src
```

### Test Configuration

#### Environment Variables
Create a `.env` file with the following:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key
- `DISCORD_BOT_TOKEN`: Your Discord bot token

### Test Categories
- **Unit Tests**: Isolated component testing
- **Integration Tests**: Service and database interactions
- **Supabase Tests**: Database-specific tests

### Troubleshooting
- Ensure all dependencies are installed
- Check environment variable configuration
- Verify network connectivity

### Best Practices
- Keep tests independent
- Use fixtures for common test data
- Mock external services when possible
