#!/usr/bin/env python3
"""
Test script for the new /ask command functionality
Tests AI chat processor and command handling
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from bot.pipeline.commands.ask.stages.ai_chat_processor import FlexibleAIChatProcessor as AIChatProcessor
from bot.pipeline.commands.ask.stages.models import AIAskResult


async def test_ai_processor():
    """Test the AI chat processor functionality"""
    print("🧪 Testing AI Chat Processor...")
    
    try:
        # Initialize processor
        config = {
            "openai_api_key": os.getenv("OPENAI_API_KEY", "test_key"),
            "model": "gpt-4o-mini",
            "temperature": 0.7
        }
        
        processor = AIChatProcessor(config)
        print("✅ AI Chat Processor initialized successfully")
        
        # Test queries
        test_queries = [
            "What's the weather like?",
            "How are you doing?",
            "What's the price of $AAPL?",
            "Can you explain RSI to me?",
            "What's your favorite color?",
            "How do I start options trading?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing query: {query}")
            
            try:
                result = await processor.process(query)
                
                print(f"✅ Response received:")
                print(f"   Intent: {result.get('intent', 'N/A')}")
                print(f"   Symbols: {result.get('symbols', [])}")
                print(f"   Tools: {result.get('tools_used', [])}")
                print(f"   Data available: {result.get('data_available', False)}")
                print(f"   Response: {result.get('response', 'N/A')[:100]}...")
                
            except Exception as e:
                print(f"❌ Query failed: {e}")
        
        print("\n🎉 AI Chat Processor test completed!")
        
    except Exception as e:
        print(f"❌ AI Chat Processor test failed: {e}")
        import traceback
        traceback.print_exc()


async def test_models():
    """Test the Pydantic models"""
    print("\n🧪 Testing Pydantic Models...")
    
    try:
        # Test valid AI result
        valid_data = {
            "intent": "price_check",
            "symbols": ["AAPL", "MSFT"],
            "tools_required": ["price_fetch"],
            "needs_data": True,
            "response": "Here's the current price data for the requested symbols."
        }
        
        ai_result = AIAskResult(**valid_data)
        print(f"✅ Valid AI result created: {ai_result.intent}")
        
        # Test symbol validation
        test_symbols = ["$AAPL", "$MSFT", "INVALID", "$123", "$TOOLONGNAME"]
        for symbol in test_symbols:
            try:
                result = AIAskResult(
                    intent="test",
                    symbols=[symbol],
                    tools_required=[],
                    needs_data=False,
                    response="test"
                )
                print(f"✅ Symbol '{symbol}' processed: {result.symbols}")
            except Exception as e:
                print(f"❌ Symbol '{symbol}' failed: {e}")
        
        # Test intent validation
        test_intents = ["price_check", "technical_analysis", "invalid_intent", "educational"]
        for intent in test_intents:
            try:
                result = AIAskResult(
                    intent=intent,
                    symbols=[],
                    tools_required=[],
                    needs_data=False,
                    response="test"
                )
                print(f"✅ Intent '{intent}' processed: {result.intent}")
            except Exception as e:
                print(f"❌ Intent '{intent}' failed: {e}")
        
        print("🎉 Pydantic Models test completed!")
        
    except Exception as e:
        print(f"❌ Pydantic Models test failed: {e}")
        import traceback
        traceback.print_exc()


async def test_fallback_analysis():
    """Test fallback analysis when AI is unavailable"""
    print("\n🧪 Testing Fallback Analysis...")
    
    try:
        # Create processor without OpenAI client
        config = {}
        processor = AIChatProcessor(config)
        
        # Test queries that should trigger fallback
        test_queries = [
            "What's the price of $AAPL?",
            "Can you show me technical indicators for $TSLA?",
            "How do I calculate RSI?",
            "What's the weather like?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing fallback for: {query}")
            
            try:
                result = await processor.process(query)
                
                print(f"✅ Fallback response:")
                print(f"   Intent: {result.get('intent', 'N/A')}")
                print(f"   Symbols: {result.get('symbols', [])}")
                print(f"   Tools: {result.get('tools_used', [])}")
                print(f"   Response: {result.get('response', 'N/A')[:100]}...")
                
            except Exception as e:
                print(f"❌ Fallback failed: {e}")
        
        print("🎉 Fallback Analysis test completed!")
        
    except Exception as e:
        print(f"❌ Fallback Analysis test failed: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Run all tests"""
    print("🚀 Starting /ask Command Tests...\n")
    
    # Test AI processor
    await test_ai_processor()
    
    # Test models
    await test_models()
    
    # Test fallback analysis
    await test_fallback_analysis()
    
    print("\n🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main()) 