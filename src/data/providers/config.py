"""
Data Providers Configuration Module

Provides data provider configuration by importing from the centralized config manager.
All environment variable access is centralized in src.core.config_manager.
"""

from typing import Dict, Any, List

from src.core.config_manager import get_config


def get_data_providers_config() -> Dict[str, Any]:
    """
    Get data providers configuration from centralized config manager
    
    Returns:
        Dictionary containing data providers configuration
    """
    config = get_config()
    return config.get_section('data_providers')


def get_provider_config(provider_name: str) -> Dict[str, Any]:
    """
    Get configuration for a specific data provider
    
    Args:
        provider_name: Name of the provider (yahoo_finance, polygon, finnhub, alpha_vantage)
        
    Returns:
        Dictionary containing provider-specific configuration
    """
    config = get_config()
    
    # Default provider configurations
    provider_defaults = {
        "yahoo_finance": {
            "enabled": True,
            "rate_limit": 5,
            "timeout": 10.0
        },
        "polygon": {
            "enabled": True,
            "rate_limit": 5,
            "timeout": 8.0,
            "api_key": ""
        },
        "finnhub": {
            "enabled": True,
            "rate_limit": 30,
            "timeout": 5.0,
            "api_key": ""
        },
        "alpha_vantage": {
            "enabled": True,
            "rate_limit": 5,
            "timeout": 10.0,
            "api_key": ""
        }
    }
    
    # Get provider config from centralized config or use defaults
    provider_config = config.get_section('data_providers').get(provider_name, {})
    defaults = provider_defaults.get(provider_name, {})
    
    # Merge with defaults
    result = defaults.copy()
    result.update(provider_config)
    
    return result


def get_enabled_providers() -> List[str]:
    """Get list of enabled provider names"""
    providers = ["yahoo_finance", "polygon", "finnhub", "alpha_vantage"]
    enabled = []
    
    for provider in providers:
        provider_config = get_provider_config(provider)
        if provider_config.get("enabled", True):
            enabled.append(provider)
    
    return enabled


def is_provider_enabled(provider_name: str) -> bool:
    """Check if a specific provider is enabled"""
    provider_config = get_provider_config(provider_name)
    return provider_config.get("enabled", True)


def get_config_summary() -> Dict[str, Any]:
    """Get data providers configuration summary"""
    enabled_providers = get_enabled_providers()
    
    rate_limits = {}
    timeouts = {}
    
    for provider in ["yahoo_finance", "polygon", "finnhub", "alpha_vantage"]:
        config = get_provider_config(provider)
        rate_limits[provider] = config.get("rate_limit", 5)
        timeouts[provider] = config.get("timeout", 10.0)
    
    return {
        "enabled_providers": enabled_providers,
        "total_providers": len(enabled_providers),
        "rate_limits": rate_limits,
        "timeouts": timeouts
    }