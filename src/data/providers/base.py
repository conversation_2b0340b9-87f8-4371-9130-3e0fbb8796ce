"""
Base Data Provider Interface

Defines the standard interface and common functionality for all data providers.
This consolidates the scattered provider interfaces into a single, authoritative definition.
"""

from abc import ABC, abstractmethod
import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
from typing import Dict, Any, List, Optional, Union

from src.core.config_manager import get_config
from src.core.logger import get_logger

logger = get_logger(__name__)
config = get_config()


class ProviderStatus(Enum):
    """Data provider status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNAVAILABLE = "unavailable"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"


@dataclass
class DataProviderConfig:
    """Configuration for a data provider"""
    name: str
    enabled: bool = True
    priority: int = 100  # Lower number = higher priority
    rate_limit: int = 60  # Requests per minute
    timeout: float = 10.0  # Seconds
    retry_attempts: int = 3
    retry_delay: float = 1.0  # Seconds
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    additional_config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketDataRequest:
    """Request for market data"""
    symbol: str
    data_types: List[str] = field(default_factory=lambda: ["price", "volume"])
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    interval: str = "1d"  # 1m, 5m, 15m, 1h, 1d, etc.
    include_extended_hours: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketDataResponse:
    """Response containing market data"""
    symbol: str
    data: Dict[str, Any]
    provider: str
    timestamp: datetime = field(default_factory=datetime.now)
    quality_score: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_valid(self) -> bool:
        """Check if response contains valid data"""
        return bool(self.data and self.quality_score > 0.5)


class RateLimiter:
    """Rate limiter for API providers"""
    
    def __init__(self, requests_per_minute: int):
        self.requests_per_minute = requests_per_minute
        self.requests = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire rate limit permission"""
        async with self.lock:
            now = time.time()
            # Remove requests older than 1 minute
            self.requests = [req_time for req_time in self.requests if now - req_time < 60]
            
            if len(self.requests) >= self.requests_per_minute:
                # Calculate wait time
                oldest_request = min(self.requests)
                wait_time = 60 - (now - oldest_request)
                if wait_time > 0:
                    logger.warning(f"Rate limit reached, waiting {wait_time:.2f}s")
                    await asyncio.sleep(wait_time)
            
            self.requests.append(now)


class BaseDataProvider(ABC):
    """
    Abstract base class for all market data providers
    
    This is the single, authoritative interface that all data providers must implement.
    It consolidates functionality from the scattered provider implementations.
    """
    
    def __init__(self, config: DataProviderConfig):
        self.config = config
        self.name = config.name
        self.status = ProviderStatus.HEALTHY
        self.last_error: Optional[Exception] = None
        self.last_request_time: Optional[datetime] = None
        self.request_count = 0
        self.error_count = 0
        self.rate_limiter = RateLimiter(config.rate_limit)
        
        # Initialize provider-specific configuration
        self._initialize()
    
    def _initialize(self):
        """Initialize provider-specific configuration"""
        pass
    
    async def get_market_data(self, request: MarketDataRequest) -> MarketDataResponse:
        """
        Get market data for the specified request
        
        Args:
            request: Market data request
            
        Returns:
            MarketDataResponse: Response containing market data
            
        Raises:
            DataProviderError: If data retrieval fails
            DataProviderTimeoutError: If request times out
            DataProviderRateLimitError: If rate limit is exceeded
        """
        # Rate limiting
        await self.rate_limiter.acquire()
        
        # Update request tracking
        self.last_request_time = datetime.now()
        self.request_count += 1
        
        logger.debug(f"🔍 {self.name}: Fetching data for {request.symbol}")
        
        try:
            # Execute with timeout and retries
            response = await self._execute_with_retries(request)
            
            # Validate response
            if not response.is_valid:
                raise DataQualityError(
                    f"Invalid data received for {request.symbol}",
                    quality_score=response.quality_score
                )
            
            # Update status
            self.status = ProviderStatus.HEALTHY
            self.last_error = None
            
            logger.debug(f"✅ {self.name}: Successfully fetched data for {request.symbol}")
            return response
            
        except asyncio.TimeoutError:
            error = DataProviderTimeoutError(self.name, self.config.timeout)
            self._handle_error(error)
            raise error
            
        except Exception as e:
            if "rate limit" in str(e).lower():
                error = DataProviderRateLimitError(self.name)
                self.status = ProviderStatus.RATE_LIMITED
            else:
                error = DataProviderError(f"Failed to fetch data: {str(e)}", self.name, request.symbol)
                self.status = ProviderStatus.ERROR
            
            self._handle_error(error)
            raise error
    
    async def _execute_with_retries(self, request: MarketDataRequest) -> MarketDataResponse:
        """Execute request with retry logic"""
        last_error = None
        
        for attempt in range(1, self.config.retry_attempts + 1):
            try:
                # Execute with timeout
                response = await asyncio.wait_for(
                    self._fetch_data(request),
                    timeout=self.config.timeout
                )
                return response
                
            except Exception as e:
                last_error = e
                if attempt < self.config.retry_attempts:
                    wait_time = self.config.retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                    logger.warning(f"{self.name}: Attempt {attempt} failed, retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"{self.name}: All {self.config.retry_attempts} attempts failed")
        
        raise last_error
    
    def _handle_error(self, error: Exception):
        """Handle provider error"""
        self.last_error = error
        self.error_count += 1
        logger.error(f"❌ {self.name}: {error}")
    
    @abstractmethod
    async def _fetch_data(self, request: MarketDataRequest) -> MarketDataResponse:
        """
        Fetch data from the provider - must be implemented by subclasses
        
        Args:
            request: Market data request
            
        Returns:
            MarketDataResponse: Raw response from provider
        """
        pass
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get provider health status"""
        return {
            "name": self.name,
            "status": self.status.value,
            "enabled": self.config.enabled,
            "priority": self.config.priority,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "last_request": self.last_request_time.isoformat() if self.last_request_time else None,
            "last_error": str(self.last_error) if self.last_error else None,
            "rate_limit": self.config.rate_limit,
            "timeout": self.config.timeout
        }
    
    def reset_error_count(self):
        """Reset error tracking"""
        self.error_count = 0
        self.last_error = None
        if self.status == ProviderStatus.ERROR:
            self.status = ProviderStatus.HEALTHY
    
    @staticmethod
    def normalize_symbol(symbol: str) -> str:
        """Normalize symbol format"""
        if not symbol:
            return ""
        
        # Remove common prefixes and clean
        symbol = symbol.strip().upper()
        if symbol.startswith('$'):
            symbol = symbol[1:]
        
        # Handle exchange suffixes (e.g., AAPL.US -> AAPL)
        if '.' in symbol:
            symbol = symbol.split('.')[0]
        
        return symbol
    
    def __str__(self) -> str:
        return f"{self.name} (Status: {self.status.value}, Priority: {self.config.priority})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}', status='{self.status.value}')>"