"""
Shared rate limiter utilities.
Consolidates duplicate rate limiting logic across bot modules.
"""

import time
from typing import Dict, List


class RateLimiter:
    """Simple rate limiter for user requests"""
    
    def __init__(self, max_requests: int = 50, time_window: int = 3600):
        self.max_requests = max_requests
        self.time_window = time_window
        self.user_requests: Dict[str, List[float]] = {}
    
    def can_make_request(self, user_id: str) -> bool:
        """Check if user can make a request"""
        now = time.time()
        
        if user_id not in self.user_requests:
            return True
        
        # Clean old requests
        self.user_requests[user_id] = [
            req_time for req_time in self.user_requests[user_id]
            if now - req_time < self.time_window
        ]
        
        return len(self.user_requests[user_id]) < self.max_requests
    
    def record_user_query(self, user_id: str):
        """Record a user query"""
        now = time.time()
        
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        
        self.user_requests[user_id].append(now)
    
    def get_remaining_time(self, user_id: str) -> float:
        """Get remaining time until user can make another request"""
        if user_id not in self.user_requests:
            return 0.0
        
        now = time.time()
        oldest_request = min(self.user_requests[user_id])
        return max(0.0, self.time_window - (now - oldest_request))
    
    def get_user_stats(self, user_id: str) -> Dict[str, any]:
        """Get rate limiting statistics for a user"""
        if user_id not in self.user_requests:
            return {
                'requests_made': 0,
                'requests_remaining': self.max_requests,
                'time_until_reset': 0.0
            }
        
        now = time.time()
        current_requests = len(self.user_requests[user_id])
        requests_remaining = max(0, self.max_requests - current_requests)
        
        if current_requests > 0:
            oldest_request = min(self.user_requests[user_id])
            time_until_reset = max(0.0, self.time_window - (now - oldest_request))
        else:
            time_until_reset = 0.0
        
        return {
            'requests_made': current_requests,
            'requests_remaining': requests_remaining,
            'time_until_reset': time_until_reset
        }
    
    def reset_user(self, user_id: str):
        """Reset rate limiting for a specific user"""
        if user_id in self.user_requests:
            del self.user_requests[user_id]
    
    def clear_all(self):
        """Clear all rate limiting data"""
        self.user_requests.clear()

# Alias for backward compatibility
SimpleRateLimiter = RateLimiter 