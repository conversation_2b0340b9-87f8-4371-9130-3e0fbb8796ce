"""
Shared caching utilities for data providers.
Consolidates duplicate caching logic across provider implementations.
"""

import time
from typing import Any, Dict, Optional, Tuple


class CacheManager:
    """Simple cache manager for data providers."""
    
    def __init__(self, cache_expiry: int = 300):
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self.cache_expiry = cache_expiry
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached data if not expired"""
        if key in self._cache:
            data, timestamp = self._cache[key]
            if time.time() - timestamp < self.cache_expiry:
                return data
            else:
                # Remove expired cache entry
                del self._cache[key]
        return None
    
    def set(self, key: str, data: Any) -> None:
        """Cache data with current timestamp"""
        self._cache[key] = (data, time.time())
    
    def clear(self) -> None:
        """Clear all cached data"""
        self._cache.clear()
    
    def remove(self, key: str) -> None:
        """Remove a specific cache entry"""
        if key in self._cache:
            del self._cache[key]
    
    def size(self) -> int:
        """Get current cache size"""
        return len(self._cache)
    
    def cleanup_expired(self) -> int:
        """Remove all expired entries and return count removed"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self._cache.items()
            if current_time - timestamp >= self.cache_expiry
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        return len(expired_keys)

# Convenience functions for backward compatibility
def cache_get(cache: Dict[str, Tuple[Any, float]], key: str, cache_expiry: int) -> Optional[Any]:
    """Get cached data if not expired (legacy function)"""
    if key in cache:
        data, timestamp = cache[key]
        if time.time() - timestamp < cache_expiry:
            return data
        else:
            # Remove expired cache entry
            del cache[key]
    return None

def cache_set(cache: Dict[str, Tuple[Any, float]], key: str, data: Any) -> None:
    """Cache data with current timestamp (legacy function)"""
    cache[key] = (data, time.time()) 