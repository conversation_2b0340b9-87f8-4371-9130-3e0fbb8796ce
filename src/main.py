#!/usr/bin/env python3
"""
Main entry point for the trading automation system.
"""

import asyncio
import logging
import os
from datetime import datetime

# Import our new services
from src.services.timeframe_service import TimeframeEngine
from src.services.alert_service import HeartbeatMonitor
from src.services.market_stream import MarketStream
from src.services.config import TIMEFRAME_CONFIG, ALERT_THRESHOLDS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingSystem:
    """Main trading system orchestrator."""
    
    def __init__(self):
        self.timeframe_engine = None
        self.alert_monitor = None
        self.market_stream = None
        self.is_running = False
        
    async def initialize(self):
        """Initialize all system components."""
        try:
            logger.info("Initializing Trading System...")
            
            # Initialize timeframe engine
            self.timeframe_engine = TimeframeEngine(
                base_resolution=TIMEFRAME_CONFIG["base_resolution"],
                secondary_tf=TIMEFRAME_CONFIG["secondary_tf"]
            )
            logger.info("✅ Timeframe Engine initialized")
            
            # Initialize alert monitor
            self.alert_monitor = HeartbeatMonitor()
            logger.info("✅ Alert Monitor initialized")
            
            # Initialize market stream
            self.market_stream = MarketStream()
            self.market_stream.set_timeframe_engine(self.timeframe_engine)
            self.market_stream.set_alert_monitor(self.alert_monitor)
            logger.info("✅ Market Stream initialized")
            
            # Subscribe to market data events
            self.market_stream.subscribe('market_data_processed', self._on_market_data)
            self.market_stream.subscribe('stream_started', self._on_stream_started)
            self.market_stream.subscribe('stream_stopped', self._on_stream_stopped)
            
            logger.info("🎯 Trading System initialization complete!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Trading System: {e}", exc_info=True)
            return False
    
    async def start(self):
        """Start the trading system."""
        if not self.timeframe_engine or not self.alert_monitor or not self.market_stream:
            logger.error("❌ System not initialized. Call initialize() first.")
            return False
        
        try:
            logger.info("🚀 Starting Trading System...")
            
            # Start market stream monitoring
            self.market_stream.start_monitoring()
            
            # Start background tasks
            asyncio.create_task(self._heartbeat_monitor_loop())
            asyncio.create_task(self._system_health_check())
            
            self.is_running = True
            logger.info("✅ Trading System started successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Trading System: {e}", exc_info=True)
            return False
    
    async def stop(self):
        """Stop the trading system."""
        try:
            logger.info("🛑 Stopping Trading System...")
            
            self.is_running = False
            
            if self.market_stream:
                self.market_stream.stop_monitoring()
            
            logger.info("✅ Trading System stopped successfully!")
            
        except Exception as e:
            logger.error(f"❌ Error stopping Trading System: {e}", exc_info=True)
    
    async def _on_market_data(self, data):
        """Handle processed market data."""
        try:
            logger.debug(f"📊 Market data processed: {data.ticker} {data.timeframe}")
            
            # Additional processing can be added here
            # For example, storing to database, triggering alerts, etc.
            
        except Exception as e:
            logger.error(f"Error handling market data: {e}", exc_info=True)
    
    async def _on_stream_started(self, data):
        """Handle stream start event."""
        logger.info("🌊 Market stream started")
    
    async def _on_stream_stopped(self, data):
        """Handle stream stop event."""
        logger.info("🌊 Market stream stopped")
    
    async def _heartbeat_monitor_loop(self):
        """Background loop for monitoring system health."""
        while self.is_running:
            try:
                # Get system status
                stream_status = self.market_stream.get_status()
                
                # Log status every 5 minutes
                if datetime.now().minute % 5 == 0:
                    logger.info(f"📊 System Status: {stream_status}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in heartbeat monitor: {e}", exc_info=True)
                await asyncio.sleep(60)
    
    async def _system_health_check(self):
        """Periodic system health check."""
        while self.is_running:
            try:
                # Check if all components are healthy
                if not self.timeframe_engine or not self.alert_monitor or not self.market_stream:
                    logger.error("❌ System components missing - attempting restart...")
                    await self.initialize()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in health check: {e}", exc_info=True)
                await asyncio.sleep(300)
    
    def get_status(self):
        """Get current system status."""
        return {
            'is_running': self.is_running,
            'components': {
                'timeframe_engine': self.timeframe_engine is not None,
                'alert_monitor': self.alert_monitor is not None,
                'market_stream': self.market_stream is not None
            },
            'market_stream_status': self.market_stream.get_status() if self.market_stream else None,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main application entry point."""
    trading_system = TradingSystem()
    
    try:
        # Initialize system
        if not await trading_system.initialize():
            logger.error("❌ Failed to initialize system")
            return
        
        # Start system
        if not await trading_system.start():
            logger.error("❌ Failed to start system")
            return
        
        # Keep system running
        logger.info("🔄 Trading System is running. Press Ctrl+C to stop.")
        
        # Simulate some market data for testing
        await asyncio.sleep(2)  # Wait for system to start
        
        # Test with simulated data
        logger.info("🧪 Testing with simulated market data...")
        await trading_system.market_stream.simulate_market_data("AAPL", "1m")
        await trading_system.market_stream.simulate_market_data("TSLA", "1m")
        await trading_system.market_stream.simulate_market_data("NVDA", "1m")
        
        # Keep running
        while trading_system.is_running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 Received interrupt signal")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}", exc_info=True)
    finally:
        await trading_system.stop()
        logger.info("👋 Trading System shutdown complete")

if __name__ == "__main__":
    asyncio.run(main()) 