from datetime import datetime, timezone

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean
from sqlalchemy.orm import Mapped, mapped_column

from src.core.utils import generate_unique_id
from src.database.connection import Base

class MarketData(Base):
    """
    SQLAlchemy model for storing market data.
    
    Tracks historical and current market information for various financial instruments.
    """
    __tablename__ = "market_data"

    # Primary key
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=generate_unique_id
    )
    
    # Instrument details
    symbol: Mapped[str] = mapped_column(String(10), index=True)
    exchange: Mapped[str] = mapped_column(String(50), nullable=True)
    
    # Price information
    open_price: Mapped[float] = mapped_column(Float, nullable=True)
    close_price: Mapped[float] = mapped_column(Float, nullable=True)
    high_price: Mapped[float] = mapped_column(Float, nullable=True)
    low_price: Mapped[float] = mapped_column(Float, nullable=True)
    
    # Volume and trading metrics
    volume: Mapped[float] = mapped_column(Float, nullable=True)
    market_cap: Mapped[float] = mapped_column(Float, nullable=True)
    
    # Temporal information
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    
    # Additional metadata
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    source: Mapped[str] = mapped_column(String(50), nullable=True)
    
    def __repr__(self):
        return f"<MarketData(symbol={self.symbol}, timestamp={self.timestamp})>"

class SymbolMetadata(Base):
    """
    Additional metadata for financial instruments.
    """
    __tablename__ = "symbol_metadata"
    
    symbol: Mapped[str] = mapped_column(String(10), primary_key=True)
    name: Mapped[str] = mapped_column(String(200), nullable=True)
    sector: Mapped[str] = mapped_column(String(100), nullable=True)
    industry: Mapped[str] = mapped_column(String(100), nullable=True)
    country: Mapped[str] = mapped_column(String(100), nullable=True)
    
    def __repr__(self):
        return f"<SymbolMetadata(symbol={self.symbol}, name={self.name})>" 