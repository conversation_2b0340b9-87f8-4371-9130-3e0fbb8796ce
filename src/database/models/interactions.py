from datetime import datetime, timezone

from sqlalchemy import Column, Integer, String, DateTime, JSON, Text, Float
from sqlalchemy.orm import Mapped, mapped_column

from src.core.utils import generate_unique_id
from src.database.connection import Base

class DiscordInteraction(Base):
    """
    SQLAlchemy model for tracking Discord bot interactions.
    
    Stores details of user interactions with the bot for analytics and logging.
    """
    __tablename__ = "discord_interactions"

    # Primary key
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=generate_unique_id
    )
    
    # User and channel identifiers
    user_id: Mapped[str] = mapped_column(String(50), index=True)
    channel_id: Mapped[str] = mapped_column(String(50), index=True)
    
    # Interaction details
    command: Mapped[str] = mapped_column(String(100))
    symbol: Mapped[str] = mapped_column(String(10), nullable=True, index=True)
    
    # Interaction content
    message_content: Mapped[str] = mapped_column(Text)
    response_content: Mapped[str] = mapped_column(Text, nullable=True)
    
    # Additional metadata
    metadata: Mapped[dict] = mapped_column(JSON, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    
    def __repr__(self):
        return f"<DiscordInteraction(id={self.id}, user_id={self.user_id}, command={self.command})>"

class MarketAnalysisCache(Base):
    """
    SQLAlchemy model for caching market analysis results.
    
    Stores recent market analysis to reduce redundant API calls and 
    provide quick access to recent analysis.
    """
    __tablename__ = "market_analysis_cache"

    # Primary key
    symbol: Mapped[str] = mapped_column(String(10), primary_key=True)
    
    # Analysis results
    price: Mapped[float] = mapped_column(Float)
    recommendation: Mapped[str] = mapped_column(String(10))
    confidence: Mapped[int] = mapped_column(Integer)
    summary: Mapped[str] = mapped_column(Text)
    change_percent: Mapped[float] = mapped_column(Float)
    
    # Metadata
    analyzed_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc)
    )
    
    def __repr__(self):
        return f"<MarketAnalysisCache(symbol={self.symbol}, recommendation={self.recommendation})>" 