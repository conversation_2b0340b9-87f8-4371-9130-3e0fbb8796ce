import os
from typing import Optional, Dict, Any

from pydantic import BaseModel, validator, Field
from supabase import create_client, Client

from src.core.logger import get_logger
from src.core.secrets import SecretsManager

logger = get_logger(__name__)

class SupabaseConfig(BaseModel):
    """
    Secure Supabase configuration model with validation.
    """
    url: str = Field(..., min_length=10, description="Supabase project URL")
    key: str = Field(..., min_length=20, description="Supabase API key")
    
    @validator('url')
    def validate_url(cls, url):
        """
        Validate Supabase URL format.
        """
        if not url.startswith(('https://', 'http://')):
            raise ValueError("Supabase URL must start with https:// or http://")
        return url

class SupabaseClientManager:
    """
    Secure Supabase client manager with advanced features.
    
    Features:
    - Secure configuration management
    - Connection pooling
    - Error handling
    - Logging
    - Secrets management
    """
    
    _instance = None
    
    def __new__(cls):
        """
        Singleton pattern implementation.
        """
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """
        Initialize Supabase client with secure configuration.
        """
        if not hasattr(self, '_initialized'):
            self._secrets_manager = SecretsManager()
            self._client = self._create_supabase_client()
            self._initialized = True
    
    def _create_supabase_client(self) -> Optional[Client]:
        """
        Create a secure Supabase client.
        
        Returns:
            Supabase Client or None if configuration is invalid
        """
        try:
            # Attempt to load Supabase credentials from secrets
            secrets = self._secrets_manager.load_secrets()
            
            # If no secrets found, try environment variables
            url = secrets.get('SUPABASE_URL') or os.getenv('SUPABASE_URL')
            key = secrets.get('SUPABASE_KEY') or os.getenv('SUPABASE_KEY')
            
            if not url or not key:
                logger.warning("Supabase credentials not found. Client not initialized.")
                return None
            
            # Validate configuration
            config = SupabaseConfig(url=url, key=key)
            
            # Create and return Supabase client
            client = create_client(config.url, config.key)
            logger.info("Supabase client initialized successfully")
            return client
        
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            return None
    
    def get_client(self) -> Optional[Client]:
        """
        Get the Supabase client instance.
        
        Returns:
            Supabase Client or None
        """
        return self._client
    
    def query_data(self, table: str, query: Dict[str, Any], correlation_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Securely query data from a Supabase table.
        
        Args:
            table (str): Target table name
            query (dict): Query parameters
            correlation_id (str): Correlation ID for request tracing
        
        Returns:
            Query results or empty dict
        """
        if not self._client:
            logger.error("Supabase client not initialized")
            return {}
        
        try:
            # Perform query with logging
            result = self._client.table(table).select("*").match(query).execute()
            
            logger.info(
                f"Supabase query executed successfully",
                extra={
                    "table": table,
                    "query_params": query,
                    "result_count": len(result.data),
                    "correlation_id": correlation_id
                }
            )
            
            return result.data
        
        except Exception as e:
            logger.error(
                f"Supabase query failed",
                extra={
                    "table": table,
                    "query_params": query,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            return {}
    
    def insert_data(self, table: str, data: Dict[str, Any], correlation_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Securely insert data into a Supabase table.
        
        Args:
            table (str): Target table name
            data (dict): Data to insert
            correlation_id (str): Correlation ID for request tracing
        
        Returns:
            Inserted data or None
        """
        if not self._client:
            logger.error("Supabase client not initialized")
            return None
        
        try:
            result = self._client.table(table).insert(data).execute()
            
            logger.info(
                f"Data inserted successfully into {table}",
                extra={
                    "inserted_data": data,
                    "correlation_id": correlation_id
                }
            )
            
            return result.data
        
        except Exception as e:
            logger.error(
                f"Failed to insert data into {table}",
                extra={
                    "data": data,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            return None
    
    def update_data(
        self, 
        table: str, 
        match_criteria: Dict[str, Any], 
        update_data: Dict[str, Any],
        correlation_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Securely update data in a Supabase table.
        
        Args:
            table (str): Target table name
            match_criteria (dict): Criteria to match records
            update_data (dict): Data to update
            correlation_id (str): Correlation ID for request tracing
        
        Returns:
            Updated data or None
        """
        if not self._client:
            logger.error("Supabase client not initialized")
            return None
        
        try:
            result = (
                self._client.table(table)
                .update(update_data)
                .match(match_criteria)
                .execute()
            )
            
            logger.info(
                f"Data updated successfully in {table}",
                extra={
                    "match_criteria": match_criteria,
                    "updated_data": update_data,
                    "correlation_id": correlation_id
                }
            )
            
            return result.data
        
        except Exception as e:
            logger.error(
                f"Failed to update data in {table}",
                extra={
                    "match_criteria": match_criteria,
                    "update_data": update_data,
                    "error": str(e),
                    "correlation_id": correlation_id
                }
            )
            return None

# Singleton instance for easy import and use
supabase_client = SupabaseClientManager()

def test_supabase_connection(correlation_id: Optional[str] = None):
    """
    Test Supabase connection and basic functionality.
    
    Args:
        correlation_id (str): Correlation ID for request tracing
    """
    client = supabase_client.get_client()
    
    if not client:
        logger.error(
            "Supabase connection test failed: No client available",
            extra={"correlation_id": correlation_id}
        )
        return False
    
    try:
        # Perform a simple test query
        result = client.table('test_table').select("*").limit(1).execute()
        logger.info(
            "Supabase connection test successful",
            extra={"correlation_id": correlation_id}
        )
        return True
    except Exception as e:
        logger.error(
            f"Supabase connection test failed: {e}",
            extra={"correlation_id": correlation_id}
        )
        return False 