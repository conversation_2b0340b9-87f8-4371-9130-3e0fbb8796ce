"""
Database Configuration Module

Provides database-specific configuration by importing from the centralized config manager.
All environment variable access is centralized in src.core.config_manager.
"""

from typing import Dict, Any

from src.core.config_manager import get_config


def get_database_config() -> Dict[str, Any]:
    """
    Get database configuration from centralized config manager
    
    Returns:
        Dictionary containing database configuration
    """
    config = get_config()
    return config.get_section('database')


def get_database_url() -> str:
    """Get database URL from centralized config"""
    config = get_config()
    return config.get('database', 'url', 'sqlite:///./local_dev.db')


def use_supabase() -> bool:
    """Check if Supabase should be used"""
    config = get_config()
    return config.get('database', 'use_supabase', False)


def get_pool_config() -> Dict[str, int]:
    """Get database pool configuration"""
    config = get_config()
    return {
        'pool_size': config.get('database', 'pool_size', 5),
        'max_overflow': config.get('database', 'max_overflow', 10)
    }


def get_config_summary() -> Dict[str, Any]:
    """Get database configuration summary"""
    config = get_config()
    db_config = config.get_section('database')
    
    return {
        "use_supabase": db_config.get('use_supabase', False),
        "database_url": db_config.get('url', 'sqlite:///./local_dev.db'),
        "pool_size": db_config.get('pool_size', 5),
        "max_overflow": db_config.get('max_overflow', 10),
        "echo": db_config.get('echo', False)
    }