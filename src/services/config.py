"""
Configuration for timeframe and alert services.
"""

# Timeframe Configuration
TIMEFRAME_CONFIG = {
    "base_resolution": "1m",
    "secondary_tf": "5m",  # Options: "3m", "5m", "15m"
    "derived_timeframes": ["15m", "1h", "4h"],
    
    # Timeframe multipliers (in minutes)
    "multipliers": {
        "3m": 3,
        "5m": 5,
        "15m": 15,
        "1h": 60,
        "4h": 240
    }
}

# Alert Thresholds
ALERT_THRESHOLDS = {
    "volume_spike": 3.0,      # Volume > 3x average
    "volatility_spike": 2.5,   # Range > 2.5x average
    "price_surge": 2.0,       # Price change > 2x average
    "tf_misalignment": 0.8,   # 80% confidence threshold
    "momentum_divergence": 0.5 # Significant momentum difference
}

# Redis Configuration (placeholder for future implementation)
REDIS_CONFIG = {
    "host": "localhost",
    "port": 6379,
    "db": 0,
    "key_prefix": "tradingbot:",
    
    # Key patterns for different data types
    "keys": {
        "candles": "{ticker}:{timeframe}",
        "alerts": "alerts:{ticker}",
        "status": "status:{ticker}"
    }
}

# Market Data Configuration
MARKET_DATA_CONFIG = {
    "max_candles_per_ticker": 1000,  # Maximum candles to store per ticker
    "cleanup_interval": 3600,        # Cleanup old data every hour
    "heartbeat_interval": 60,         # Check heartbeat every minute
    "alert_retention": 86400         # Keep alerts for 24 hours
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "logs/timeframe_service.log"
} 