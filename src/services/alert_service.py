"""
Heartbeat Alert Service

Monitors real-time market data for immediate alerts and timeframe alignment.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class Alert:
    """Standardized alert structure."""
    id: str
    ticker: str
    alert_type: str
    severity: str
    message: str
    timestamp: datetime
    data: Dict[str, Any]
    timeframe: str

class HeartbeatMonitor:
    """
    Real-time market monitor for heartbeat alerts and timeframe analysis.
    """
    
    def __init__(self):
        self.alert_thresholds = {
            "volume_spike": 3.0,      # Volume > 3x average
            "volatility_spike": 2.5,   # Range > 2.5x average
            "price_surge": 2.0,       # Price change > 2x average
            "tf_misalignment": 0.8    # 80% confidence threshold
        }
        
        logger.info("HeartbeatMonitor initialized with alert thresholds")
    
    def check_ticker(self, ticker: str, timeframe_engine) -> Dict[str, Any]:
        """
        Comprehensive ticker check across all timeframes.
        
        Args:
            ticker: Stock symbol to check
            timeframe_engine: TimeframeEngine instance for data access
            
        Returns:
            Dictionary containing all alert conditions
        """
        try:
            # Get all timeframe data
            all_timeframes = timeframe_engine.get_all_timeframes(ticker)
            
            if not all_timeframes:
                logger.warning(f"No timeframe data available for {ticker}")
                return {"error": "No data available"}
            
            # Check each type of alert
            alerts = {
                "volume_alert": self._check_volume_alerts(ticker, all_timeframes),
                "volatility_alert": self._check_volatility_alerts(ticker, all_timeframes),
                "tf_alignment": self._check_timeframe_alignment(ticker, all_timeframes),
                "momentum_shift": self._check_momentum_shift(ticker, all_timeframes),
                "timestamp": datetime.now().isoformat()
            }
            
            logger.debug(f"Completed ticker check for {ticker}: {len([a for a in alerts.values() if a])} alerts")
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking ticker {ticker}: {e}", exc_info=True)
            return {"error": str(e)}
    
    def _check_volume_alerts(self, ticker: str, timeframes: Dict[str, List]) -> Dict[str, Any]:
        """Check for volume-based alerts across timeframes."""
        alerts = {}
        
        try:
            # Check 1m volume spike
            if "1m" in timeframes and timeframes["1m"]:
                latest_1m = timeframes["1m"][-1]
                recent_1m = timeframes["1m"][-5:] if len(timeframes["1m"]) >= 5 else timeframes["1m"]
                
                if len(recent_1m) >= 3:
                    avg_volume = sum(c.volume for c in recent_1m) / len(recent_1m)
                    volume_ratio = latest_1m.volume / avg_volume if avg_volume > 0 else 0
                    
                    if volume_ratio > self.alert_thresholds["volume_spike"]:
                        alerts["1m_volume_spike"] = {
                            "current_volume": latest_1m.volume,
                            "avg_volume": avg_volume,
                            "ratio": volume_ratio,
                            "threshold": self.alert_thresholds["volume_spike"]
                        }
            
            # Check secondary timeframe volume
            secondary_tf = "5m"  # This should come from timeframe engine config
            if secondary_tf in timeframes and timeframes[secondary_tf]:
                latest_secondary = timeframes[secondary_tf][-1]
                recent_secondary = timeframes[secondary_tf][-3:] if len(timeframes[secondary_tf]) >= 3 else timeframes[secondary_tf]
                
                if len(recent_secondary) >= 2:
                    avg_volume = sum(c.volume for c in recent_secondary) / len(recent_secondary)
                    volume_ratio = latest_secondary.volume / avg_volume if avg_volume > 0 else 0
                    
                    if volume_ratio > self.alert_thresholds["volume_spike"] * 0.8:  # Slightly lower threshold for higher TF
                        alerts[f"{secondary_tf}_volume_spike"] = {
                            "current_volume": latest_secondary.volume,
                            "avg_volume": avg_volume,
                            "ratio": volume_ratio,
                            "threshold": self.alert_thresholds["volume_spike"] * 0.8
                        }
                        
        except Exception as e:
            logger.error(f"Error checking volume alerts for {ticker}: {e}")
            alerts["error"] = str(e)
        
        return alerts
    
    def _check_volatility_alerts(self, ticker: str, timeframes: Dict[str, List]) -> Dict[str, Any]:
        """Check for volatility-based alerts across timeframes."""
        alerts = {}
        
        try:
            # Check 1m volatility spike
            if "1m" in timeframes and timeframes["1m"]:
                latest_1m = timeframes["1m"][-1]
                recent_1m = timeframes["1m"][-5:] if len(timeframes["1m"]) >= 5 else timeframes["1m"]
                
                if len(recent_1m) >= 3:
                    current_range = abs(latest_1m.high - latest_1m.low)
                    avg_range = sum(abs(c.high - c.low) for c in recent_1m) / len(recent_1m)
                    range_ratio = current_range / avg_range if avg_range > 0 else 0
                    
                    if range_ratio > self.alert_thresholds["volatility_spike"]:
                        alerts["1m_volatility_spike"] = {
                            "current_range": current_range,
                            "avg_range": avg_range,
                            "ratio": range_ratio,
                            "threshold": self.alert_thresholds["volatility_spike"]
                        }
            
            # Check price surge (percentage change)
            if "1m" in timeframes and timeframes["1m"]:
                latest_1m = timeframes["1m"][-1]
                if hasattr(latest_1m, 'open') and hasattr(latest_1m, 'close'):
                    price_change_pct = abs((latest_1m.close - latest_1m.open) / latest_1m.open * 100)
                    
                    # Get average price change from recent candles
                    recent_1m = timeframes["1m"][-5:] if len(timeframes["1m"]) >= 5 else timeframes["1m"]
                    if len(recent_1m) >= 3:
                        avg_change_pct = sum(abs((c.close - c.open) / c.open * 100) for c in recent_1m if c.open > 0) / len(recent_1m)
                        change_ratio = price_change_pct / avg_change_pct if avg_change_pct > 0 else 0
                        
                        if change_ratio > self.alert_thresholds["price_surge"]:
                            alerts["1m_price_surge"] = {
                                "current_change_pct": price_change_pct,
                                "avg_change_pct": avg_change_pct,
                                "ratio": change_ratio,
                                "threshold": self.alert_thresholds["price_surge"]
                            }
                            
        except Exception as e:
            logger.error(f"Error checking volatility alerts for {ticker}: {e}")
            alerts["error"] = str(e)
        
        return alerts
    
    def _check_timeframe_alignment(self, ticker: str, timeframes: Dict[str, List]) -> Dict[str, Any]:
        """Check if different timeframes are aligned in trend direction."""
        alignment_data = {}
        
        try:
            # Define timeframes to check (from lowest to highest)
            check_tfs = ["1m", "5m", "15m", "1h", "4h"]
            available_tfs = [tf for tf in check_tfs if tf in timeframes and timeframes[tf]]
            
            if len(available_tfs) < 2:
                return {"error": "Insufficient timeframe data"}
            
            # Calculate trend for each timeframe
            trends = {}
            for tf in available_tfs:
                trend = self._calculate_trend(timeframes[tf])
                trends[tf] = trend
                alignment_data[f"{tf}_trend"] = trend
            
            # Check alignment
            unique_trends = set(trends.values())
            if len(unique_trends) == 1:
                alignment_data["status"] = "ALIGNED"
                alignment_data["trend"] = list(unique_trends)[0]
                alignment_data["confidence"] = 1.0
            else:
                alignment_data["status"] = "MISALIGNED"
                alignment_data["trend"] = "MIXED"
                alignment_data["confidence"] = 1.0 / len(unique_trends)
                
                # Identify which timeframes disagree
                trend_counts = {}
                for trend in trends.values():
                    trend_counts[trend] = trend_counts.get(trend, 0) + 1
                
                alignment_data["trend_distribution"] = trend_counts
                alignment_data["majority_trend"] = max(trend_counts.items(), key=lambda x: x[1])[0]
            
        except Exception as e:
            logger.error(f"Error checking timeframe alignment for {ticker}: {e}")
            alignment_data["error"] = str(e)
        
        return alignment_data
    
    def _check_momentum_shift(self, ticker: str, timeframes: Dict[str, List]) -> Dict[str, Any]:
        """Check for momentum shifts that might indicate trend changes."""
        momentum_data = {}
        
        try:
            # Check 1m momentum vs 5m momentum
            if "1m" in timeframes and "5m" in timeframes:
                if timeframes["1m"] and timeframes["5m"]:
                    latest_1m = timeframes["1m"][-1]
                    latest_5m = timeframes["5m"][-1]
                    
                    # Calculate momentum indicators
                    momentum_1m = self._calculate_momentum(timeframes["1m"][-3:]) if len(timeframes["1m"]) >= 3 else 0
                    momentum_5m = self._calculate_momentum(timeframes["5m"][-3:]) if len(timeframes["5m"]) >= 3 else 0
                    
                    momentum_data["1m_momentum"] = momentum_1m
                    momentum_data["5m_momentum"] = momentum_5m
                    
                    # Check for momentum divergence
                    if abs(momentum_1m - momentum_5m) > 0.5:  # Significant divergence
                        momentum_data["divergence"] = {
                            "type": "MOMENTUM_DIVERGENCE",
                            "1m": momentum_1m,
                            "5m": momentum_5m,
                            "difference": abs(momentum_1m - momentum_5m)
                        }
            
            # Check for reversal patterns
            if "1m" in timeframes and len(timeframes["1m"]) >= 5:
                recent_1m = timeframes["1m"][-5:]
                reversal_pattern = self._detect_reversal_pattern(recent_1m)
                if reversal_pattern:
                    momentum_data["reversal_pattern"] = reversal_pattern
                    
        except Exception as e:
            logger.error(f"Error checking momentum shift for {ticker}: {e}")
            momentum_data["error"] = str(e)
        
        return momentum_data
    
    def _calculate_trend(self, candles: List) -> str:
        """Calculate trend direction from a list of candles."""
        if len(candles) < 2:
            return "NEUTRAL"
        
        try:
            # Simple trend calculation based on price movement
            first_close = candles[0].close
            last_close = candles[-1].close
            
            # Calculate average price change
            price_changes = []
            for i in range(1, len(candles)):
                change = (candles[i].close - candles[i-1].close) / candles[i-1].close
                price_changes.append(change)
            
            avg_change = sum(price_changes) / len(price_changes) if price_changes else 0
            
            # Determine trend
            if avg_change > 0.001:  # 0.1% threshold
                return "BULLISH"
            elif avg_change < -0.001:
                return "BEARISH"
            else:
                return "NEUTRAL"
                
        except Exception as e:
            logger.error(f"Error calculating trend: {e}")
            return "NEUTRAL"
    
    def _calculate_momentum(self, candles: List) -> float:
        """Calculate momentum indicator from candles."""
        if len(candles) < 2:
            return 0.0
        
        try:
            # Simple momentum: average price change over time
            momentum_values = []
            for i in range(1, len(candles)):
                if candles[i-1].close > 0:
                    momentum = (candles[i].close - candles[i-1].close) / candles[i-1].close
                    momentum_values.append(momentum)
            
            return sum(momentum_values) / len(momentum_values) if momentum_values else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating momentum: {e}")
            return 0.0
    
    def _detect_reversal_pattern(self, candles: List) -> Optional[Dict[str, Any]]:
        """Detect potential reversal patterns in recent candles."""
        if len(candles) < 3:
            return None
        
        try:
            # Check for doji pattern (opening and closing prices are very close)
            latest = candles[-1]
            doji_threshold = abs(latest.high - latest.low) * 0.1  # 10% of range
            
            if abs(latest.close - latest.open) <= doji_threshold:
                # Check if this follows a strong move
                if len(candles) >= 4:
                    prev_candles = candles[-4:-1]
                    strong_move = any(abs(c.close - c.open) > abs(c.high - c.low) * 0.6 for c in prev_candles)
                    
                    if strong_move:
                        return {
                            "pattern": "DOJI_REVERSAL",
                            "confidence": 0.7,
                            "description": "Doji pattern after strong move - potential reversal"
                        }
            
            # Check for hammer/shooting star
            body = abs(latest.close - latest.open)
            upper_shadow = latest.high - max(latest.open, latest.close)
            lower_shadow = min(latest.open, latest.close) - latest.low
            
            if body < (upper_shadow + lower_shadow) * 0.3:  # Small body relative to shadows
                if lower_shadow > body * 2 and upper_shadow < body * 0.5:
                    return {
                        "pattern": "HAMMER",
                        "confidence": 0.6,
                        "description": "Hammer pattern - potential bullish reversal"
                    }
                elif upper_shadow > body * 2 and lower_shadow < body * 0.5:
                    return {
                        "pattern": "SHOOTING_STAR",
                        "confidence": 0.6,
                        "description": "Shooting star pattern - potential bearish reversal"
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting reversal pattern: {e}")
            return None
    
    def generate_alert_summary(self, ticker: str, alert_data: Dict[str, Any]) -> str:
        """Generate a human-readable summary of alerts."""
        if "error" in alert_data:
            return f"❌ Error checking {ticker}: {alert_data['error']}"
        
        summary_parts = [f"📊 **{ticker} Alert Summary**"]
        
        # Volume alerts
        if alert_data.get("volume_alert"):
            summary_parts.append("🔔 **Volume Alerts:**")
            for alert_type, details in alert_data["volume_alert"].items():
                if isinstance(details, dict) and "ratio" in details:
                    summary_parts.append(f"  • {alert_type}: {details['ratio']:.1f}x average")
        
        # Volatility alerts
        if alert_data.get("volatility_alert"):
            summary_parts.append("📈 **Volatility Alerts:**")
            for alert_type, details in alert_data["volatility_alert"].items():
                if isinstance(details, dict) and "ratio" in details:
                    summary_parts.append(f"  • {alert_type}: {details['ratio']:.1f}x average")
        
        # Timeframe alignment
        if alert_data.get("tf_alignment"):
            tf_data = alert_data["tf_alignment"]
            if "status" in tf_data:
                status_emoji = "✅" if tf_data["status"] == "ALIGNED" else "⚠️"
                summary_parts.append(f"{status_emoji} **Timeframe Alignment:** {tf_data['status']}")
                if "trend" in tf_data:
                    summary_parts.append(f"  • Trend: {tf_data['trend']}")
        
        # Momentum shifts
        if alert_data.get("momentum_shift"):
            momentum_data = alert_data["momentum_shift"]
            if "divergence" in momentum_data:
                summary_parts.append("🔄 **Momentum Divergence Detected**")
                div = momentum_data["divergence"]
                summary_parts.append(f"  • 1m: {div['1m']:.3f}, 5m: {div['5m']:.3f}")
        
        return "\n".join(summary_parts) 