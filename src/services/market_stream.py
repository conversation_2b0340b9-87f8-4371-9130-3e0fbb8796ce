"""
Market Stream Connector

Receives real-time market data from TradingView webhooks
and feeds it to the timeframe engine for processing.
"""

import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Standardized market data structure."""
    ticker: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    timeframe: str
    source: str = "tradingview"

class MarketStream:
    """
    Handles incoming market data streams and routes to processing engines.
    """
    
    def __init__(self):
        self.subscribers: Dict[str, Callable] = {}
        self.timeframe_engine = None
        self.alert_monitor = None
        self.is_running = False
        
        logger.info("MarketStream initialized")
    
    def set_timeframe_engine(self, engine):
        """Set the timeframe engine for data processing."""
        self.timeframe_engine = engine
        logger.info("Timeframe engine connected to market stream")
    
    def set_alert_monitor(self, monitor):
        """Set the alert monitor for heartbeat alerts."""
        self.alert_monitor = monitor
        logger.info("Alert monitor connected to market stream")
    
    def subscribe(self, event_type: str, callback: Callable):
        """Subscribe to market data events."""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)
        logger.info(f"Subscribed to {event_type} events")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """Unsubscribe from market data events."""
        if event_type in self.subscribers:
            try:
                self.subscribers[event_type].remove(callback)
                logger.info(f"Unsubscribed from {event_type} events")
            except ValueError:
                logger.warning(f"Callback not found in {event_type} subscribers")
    
    async def emit(self, event_type: str, data: Any):
        """Emit an event to all subscribers."""
        if event_type in self.subscribers:
            for callback in self.subscribers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"Error in {event_type} callback: {e}", exc_info=True)
    
    def process_tradingview_webhook(self, webhook_data: Dict[str, Any]) -> bool:
        """
        Process incoming TradingView webhook data.
        
        Args:
            webhook_data: Raw webhook payload from TradingView
            
        Returns:
            True if processed successfully, False otherwise
        """
        try:
            logger.info(f"Processing TradingView webhook: {webhook_data.get('symbol', 'UNKNOWN')}")
            
            # Validate webhook data
            if not self._validate_webhook_data(webhook_data):
                logger.warning("Invalid webhook data received")
                return False
            
            # Extract and standardize market data
            market_data = self._extract_market_data(webhook_data)
            if not market_data:
                logger.warning("Could not extract market data from webhook")
                return False
            
            # Process through timeframe engine
            if self.timeframe_engine:
                self.timeframe_engine.process_tick(market_data.ticker, {
                    'timestamp': market_data.timestamp.isoformat(),
                    'open': market_data.open,
                    'high': market_data.high,
                    'low': market_data.low,
                    'close': market_data.close,
                    'volume': market_data.volume
                })
                
                # Emit processed data event
                asyncio.create_task(self.emit('market_data_processed', market_data))
                
                logger.debug(f"Processed {market_data.ticker} {market_data.timeframe} data")
                return True
            else:
                logger.error("No timeframe engine connected")
                return False
                
        except Exception as e:
            logger.error(f"Error processing TradingView webhook: {e}", exc_info=True)
            return False
    
    def _validate_webhook_data(self, data: Dict[str, Any]) -> bool:
        """Validate incoming webhook data structure."""
        required_fields = ['symbol', 'open', 'high', 'low', 'close', 'volume']
        
        for field in required_fields:
            if field not in data:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate data types and ranges
        try:
            symbol = str(data['symbol'])
            if not symbol or len(symbol) > 10:
                return False
            
            # Check price fields are numeric and positive
            for price_field in ['open', 'high', 'low', 'close']:
                price = float(data[price_field])
                if price < 0:
                    logger.warning(f"Negative price in {price_field}: {price}")
                    return False
            
            # Check volume is positive integer
            volume = int(data['volume'])
            if volume < 0:
                logger.warning(f"Negative volume: {volume}")
                return False
            
            # Validate price logic
            high = float(data['high'])
            low = float(data['low'])
            open_price = float(data['open'])
            close_price = float(data['close'])
            
            if low > high:
                logger.warning("Low price greater than high price")
                return False
            
            if open_price < low or open_price > high:
                logger.warning("Open price outside high-low range")
                return False
            
            if close_price < low or close_price > high:
                logger.warning("Close price outside high-low range")
                return False
            
            return True
            
        except (ValueError, TypeError) as e:
            logger.warning(f"Data type validation failed: {e}")
            return False
    
    def _extract_market_data(self, webhook_data: Dict[str, Any]) -> Optional[MarketData]:
        """Extract and standardize market data from webhook."""
        try:
            # Parse timestamp
            timestamp_str = webhook_data.get('timestamp')
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                except ValueError:
                    timestamp = datetime.now()
            else:
                timestamp = datetime.now()
            
            # Determine timeframe from webhook data
            timeframe = webhook_data.get('timeframe', '1m')
            
            # Create standardized market data
            market_data = MarketData(
                ticker=str(webhook_data['symbol']).upper(),
                timestamp=timestamp,
                open=float(webhook_data['open']),
                high=float(webhook_data['high']),
                low=float(webhook_data['low']),
                close=float(webhook_data['close']),
                volume=int(webhook_data['volume']),
                timeframe=timeframe,
                source='tradingview'
            )
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error extracting market data: {e}", exc_info=True)
            return None
    
    def start_monitoring(self):
        """Start the market stream monitoring."""
        if self.is_running:
            logger.warning("Market stream already running")
            return
        
        self.is_running = True
        logger.info("Market stream monitoring started")
        
        # Emit startup event
        asyncio.create_task(self.emit('stream_started', {'timestamp': datetime.now().isoformat()}))
    
    def stop_monitoring(self):
        """Stop the market stream monitoring."""
        if not self.is_running:
            logger.warning("Market stream not running")
            return
        
        self.is_running = False
        logger.info("Market stream monitoring stopped")
        
        # Emit shutdown event
        asyncio.create_task(self.emit('stream_stopped', {'timestamp': datetime.now().isoformat()}))
    
    def get_status(self) -> Dict[str, Any]:
        """Get current market stream status."""
        return {
            'is_running': self.is_running,
            'subscribers': {event: len(callbacks) for event, callbacks in self.subscribers.items()},
            'timeframe_engine_connected': self.timeframe_engine is not None,
            'alert_monitor_connected': self.alert_monitor is not None,
            'timestamp': datetime.now().isoformat()
        }
    
    async def simulate_market_data(self, ticker: str, timeframe: str = "1m"):
        """
        Simulate market data for testing purposes.
        
        Args:
            ticker: Stock symbol to simulate
            timeframe: Timeframe for the simulated data
        """
        if not self.is_running:
            logger.warning("Cannot simulate data - stream not running")
            return
        
        import random
        
        # Generate realistic mock data
        base_price = 100.0 + random.uniform(-20, 20)
        price_change = random.uniform(-2, 2)
        
        mock_data = {
            'symbol': ticker,
            'timestamp': datetime.now().isoformat(),
            'open': base_price,
            'high': base_price + abs(price_change) + random.uniform(0, 1),
            'low': base_price - abs(price_change) - random.uniform(0, 1),
            'close': base_price + price_change,
            'volume': random.randint(100000, 1000000),
            'timeframe': timeframe
        }
        
        logger.info(f"Simulating {ticker} {timeframe} data")
        self.process_tradingview_webhook(mock_data) 