from datetime import datetime, timedelta
from typing import Dict, Any, List

from src.core.feedback_mechanism import response_feedback_collector
from src.core.monitoring import response_metrics_tracker

class AnalyticsService:
    """
    Service layer for processing and analyzing response generation metrics and feedback
    """
    
    @staticmethod
    def generate_performance_report(days: int = 30) -> Dict[str, Any]:
        """
        Generate a comprehensive performance report
        
        Args:
            days (int, optional): Number of days to analyze. Defaults to 30.
        
        Returns:
            dict: Comprehensive performance report
        """
        # Get metrics report
        metrics = response_metrics_tracker.get_metrics_report()
        
        # Get feedback report
        feedback = response_feedback_collector.get_detailed_analysis(days)
        
        # Combine metrics and feedback
        performance_report = {
            "metrics": metrics,
            "feedback": feedback,
            "period": {
                "start_date": (datetime.now() - timedelta(days=days)).isoformat(),
                "end_date": datetime.now().isoformat(),
                "days": days
            }
        }
        
        return performance_report
    
    @staticmethod
    def identify_improvement_areas() -> List[str]:
        """
        Identify key areas for system improvement based on metrics and feedback
        
        Returns:
            list: Recommended improvement areas
        """
        # Get feedback report
        feedback_report = response_feedback_collector.get_feedback_report()
        
        improvement_areas = []
        
        # Check response type performance
        for response_type, type_data in feedback_report.get('feedback_by_type', {}).items():
            if type_data.get('average_rating', 0) < 3.5:
                improvement_areas.append(f"Improve {response_type} response quality")
        
        # Check sentiment analysis
        sentiment_data = feedback_report.get('sentiment_analysis', {})
        if sentiment_data.get('average_sentiment_score', 0) < 0:
            improvement_areas.append("Address overall negative sentiment")
        
        # Check confidence metrics
        confidence_data = feedback_report.get('confidence_metrics', {})
        if confidence_data.get('average_confidence', 0) < 0.7:
            improvement_areas.append("Increase response confidence")
        
        # Check response length
        length_metrics = feedback_report.get('response_length_metrics', {})
        if length_metrics.get('average_length', 0) < 50 or length_metrics.get('average_length', 0) > 500:
            improvement_areas.append("Optimize response length")
        
        return improvement_areas
    
    @staticmethod
    def export_analytics_report(format: str = 'json') -> Any:
        """
        Export analytics report in specified format
        
        Args:
            format (str, optional): Export format. Defaults to 'json'.
        
        Returns:
            Exported analytics report
        """
        performance_report = AnalyticsService.generate_performance_report()
        
        if format == 'json':
            return performance_report
        elif format == 'csv':
            # Implement CSV export logic
            raise NotImplementedError("CSV export not yet implemented")
        elif format == 'xlsx':
            # Implement Excel export logic
            raise NotImplementedError("Excel export not yet implemented")
        else:
            raise ValueError(f"Unsupported export format: {format}")

# Global analytics service instance
analytics_service = AnalyticsService() 