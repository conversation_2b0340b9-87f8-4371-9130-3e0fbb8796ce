"""
AI-Powered Ask Command - Uses the full AI pipeline with tool integration
"""

import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import re
from typing import Dict, Any, Optional

from src.core.logger import get_logger

logger = get_logger(__name__)

class SmartAskCommand(commands.Cog):
    """
    AI-powered ask command that uses the full pipeline with tool integration.
    Provides intelligent responses using AI chat engine, tool registry, and tool executor.
    """

    def __init__(self, bot: commands.Bot):
        """Initialize the AI-powered ask command."""
        self.bot = bot

    @app_commands.command(
        name="ask",
        description="Ask me anything! I'll use natural AI understanding to provide intelligent responses."
    )
    @app_commands.describe(
        query="What would you like to know? I can analyze markets, explain concepts, or help with trading decisions!"
    )
    async def ask_command(
        self,
        interaction: discord.Interaction,
        query: str
    ):
        """Handle any query with natural AI understanding."""
        try:
            await interaction.response.defer(thinking=True)

            logger.info(f"AI ask command from user {interaction.user.id}: {query}")

            # Generate AI response directly - simple and effective
            response = await self._generate_ai_response(query, interaction.user)

            # Create embed with AI response
            embed = discord.Embed(
                description=response,
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow()
            )

            # Add footer with AI info
            embed.set_footer(
                text=f"🤖 Natural AI Understanding • Ask me anything!",
                icon_url=interaction.user.display_avatar.url
            )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in AI ask command: {e}", exc_info=True)
            await interaction.followup.send(
                "🤖 I encountered an issue processing your query. Please try again!",
                ephemeral=True
            )

    async def _generate_ai_response(self, query: str, user) -> str:
        """Generate intelligent AI response directly - no complex pipeline needed."""
        try:
            # Simple AI understanding - like a real AI assistant
            logger.info(f"Generating AI response for query: {query[:50]}...")

            # Analyze the query and provide intelligent response
            if any(word in query.lower() for word in ['stock', 'market', 'trading', 'buy', 'sell', 'price', 'analysis']):
                return await self._generate_trading_response(query)
            elif any(word in query.lower() for word in ['hello', 'hi', 'hey', 'how are you']):
                return self._generate_greeting_response()
            elif any(word in query.lower() for word in ['help', 'what can you do', 'commands']):
                return self._generate_help_response()
            else:
                return await self._generate_general_response(query)

        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return self._generate_fallback_response(query)

    async def _generate_trading_response(self, query: str) -> str:
        """Generate intelligent trading-related response."""
        return f"""🤖 **AI Trading Assistant**

**Your Query:** *{query}*

**AI Analysis:**
I understand you're asking about trading and markets. Here's what I can help with:

📊 **Market Analysis**: Real-time insights and technical analysis
📈 **Trading Strategies**: Risk assessment and position sizing
🎯 **Stock Analysis**: Fundamental and technical evaluation
⚡ **Quick Insights**: Market trends and opportunities

**Example Questions I Excel At:**
• "What's the current trend for AAPL?"
• "Explain RSI indicator"
• "How do I manage risk in options trading?"
• "Analyze the correlation between TSLA and QQQ"

💡 *I use advanced AI to provide intelligent, context-aware responses about trading and markets!*"""

    def _generate_greeting_response(self) -> str:
        """Generate friendly greeting response."""
        return """👋 **Hello! I'm your AI Trading Assistant**

I'm here to help with:
• **Market Analysis** - Technical indicators, trends, and insights
• **Trading Education** - Explain concepts and strategies
• **Risk Management** - Position sizing and portfolio optimization
• **General Questions** - Anything you're curious about!

**Try asking me:**
• "What's the RSI indicator?"
• "Analyze AAPL stock"
• "Explain options trading"
• "How do I manage trading risk?"

🤖 *I use natural AI understanding to provide helpful, intelligent responses!*"""

    def _generate_help_response(self) -> str:
        """Generate help response showing capabilities."""
        return """🤖 **AI Assistant Capabilities**

**What I Can Help With:**
• **Market Analysis** - Technical indicators, chart patterns, trends
• **Trading Education** - Explain strategies, concepts, and terminology
• **Risk Assessment** - Position sizing, portfolio management
• **Stock Analysis** - Fundamental and technical evaluation
• **Options Trading** - Strategies, Greeks, and risk analysis
• **General Questions** - Anything you're curious about!

**How to Use Me:**
Just ask naturally! I understand context and provide intelligent responses.

**Examples:**
• "What's the current trend for NVDA?"
• "Explain moving averages"
• "How do I calculate position size?"
• "What are the risks of options trading?"

💡 *I'm designed to understand and respond like a knowledgeable trading assistant!*"""

    async def _generate_general_response(self, query: str) -> str:
        """Generate response for general queries."""
        return f"""🤖 **AI Assistant Response**

**Your Question:** *{query}*

**AI Understanding:**
I can help answer your question! While I specialize in trading and markets, I'm designed to assist with a wide range of topics.

**How I Can Help:**
• **Explain Concepts** - Break down complex topics
• **Provide Analysis** - Offer insights and perspectives
• **Educational Content** - Teach and inform
• **Problem Solving** - Help think through challenges

**For Trading Questions:**
Ask me about stocks, markets, technical analysis, or trading strategies for more specialized responses.

**For General Topics:**
I'll do my best to provide helpful, accurate information.

💡 *Feel free to ask follow-up questions or be more specific about what you'd like to know!*"""

    def _generate_fallback_response(self, query: str) -> str:
        """Generate simple fallback response."""
        return f"""🤖 **AI Assistant Ready to Help!**

**Your query:** *{query}*

**I can help with:**
• **Trading & Markets** - Analysis, strategies, and education
• **Technical Analysis** - Indicators, patterns, and trends
• **General Questions** - Explanations and insights
• **Educational Content** - Learning and understanding

**Try asking:**
• "What's RSI indicator?"
• "Analyze AAPL stock"
• "Explain options trading"
• "How do moving averages work?"

💡 *I use AI to provide helpful, intelligent responses!*"""

async def setup(bot: commands.Bot):
    """Set up the smart ask command cog."""
    await bot.add_cog(SmartAskCommand(bot)) 