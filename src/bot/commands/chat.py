"""
Modern Chat Command - Clean, Simple, Effective
Built from scratch with best practices and no over-engineering
"""

import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import time
from typing import Dict, Optional
from datetime import datetime, timedelta

from src.core.logger import get_logger
from src.database.supabase_client import SupabaseClientManager
from src.shared.utils.rate_limiter import RateLimiter

logger = get_logger(__name__)

# Try to import technical analysis tools
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logger.warning("Pandas not available - technical analysis will be limited")

# Try to import Supabase for TradingView alerts
try:
    from supabase import Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    logger.warning("Supabase not available - TradingView alerts will be limited")


class ChatCommand(commands.Cog):
    """
    Modern chat command with natural conversation capabilities.
    Clean, simple, and effective - no over-engineering.
    """
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot

        # Use centralized services instead of local implementations
        try:
            from src.core.conversation_memory_service import ConversationMemoryService
            self.memory_service = ConversationMemoryService()
            logger.info("✅ ConversationMemoryService integrated")
        except ImportError:
            logger.warning("⚠️ ConversationMemoryService not available, using fallback")
            self.memory_service = None

        try:
            from src.bot.pipeline.commands.ask.stages.query_analyzer import AIQueryAnalyzer
            self.query_analyzer = AIQueryAnalyzer()
            logger.info("✅ AIQueryAnalyzer integrated")
        except ImportError:
            logger.warning("⚠️ AIQueryAnalyzer not available, using fallback")
            self.query_analyzer = None

        try:
            from src.bot.pipeline.commands.ask.stages.symbol_validator import SymbolValidator
            self.symbol_validator = SymbolValidator()
            logger.info("✅ SymbolValidator integrated")
        except ImportError:
            logger.warning("⚠️ SymbolValidator not available, using fallback")
            self.symbol_validator = None

        # Initialize TradingView alert system and Supabase
        self.supabase_manager = SupabaseClientManager()
        self.supabase_client = None
        self._initialize_supabase()

        # Note: Rate limiting is now handled by the global bot client RateLimiter
        # No local rate limiting implementation needed
    
    @app_commands.command(
        name="chat",
        description="Have a natural conversation with AI - ask anything!"
    )
    @app_commands.describe(
        message="What would you like to chat about? I can discuss trading, markets, or anything else!"
    )
    async def chat_command(
        self, 
        interaction: discord.Interaction, 
        message: str
    ):
        """Handle natural conversation with AI."""
        try:
            user_id = interaction.user.id

            # Defer response for processing
            await interaction.response.defer(thinking=True)

            logger.info(f"Chat from user {user_id}: {message[:50]}...")

            # Use centralized ConversationMemoryService to record the user message
            if self.memory_service:
                try:
                    self.memory_service.add_message(user_id, "user", message)
                except Exception as e:
                    logger.warning(f"Failed to record user message in memory service: {e}")

            # Generate AI response
            response = await self._generate_chat_response(user_id, message)

            # Use centralized ConversationMemoryService to record the assistant response
            if self.memory_service:
                try:
                    self.memory_service.add_message(user_id, "assistant", response)
                except Exception as e:
                    logger.warning(f"Failed to record assistant response in memory service: {e}")
            
            # Create response embed
            embed = self._create_chat_embed(message, response, interaction.user)
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in chat command: {e}", exc_info=True)
            try:
                await interaction.followup.send(
                    "🤖 Sorry, I encountered an issue. Please try again!",
                    ephemeral=True
                )
            except:
                # If followup fails, try response
                await interaction.response.send_message(
                    "🤖 Sorry, I encountered an issue. Please try again!",
                    ephemeral=True
                )
    
    async def _generate_chat_response(self, user_id: int, message: str) -> str:
        """Generate intelligent chat response with context awareness."""
        try:
            # Get conversation context from the centralized ConversationMemoryService
            context = ""
            if self.memory_service:
                try:
                    context = self.memory_service.get_context(user_id)
                except Exception as e:
                    logger.warning(f"Failed to get conversation context: {e}")

            # Use centralized AIQueryAnalyzer for intent detection
            if self.query_analyzer:
                try:
                    analysis = await self.query_analyzer.analyze_query(message, {"user_id": user_id})
                    intent = analysis.intent.value if hasattr(analysis.intent, 'value') else str(analysis.intent)

                    # Route based on AI-determined intent
                    if intent in ['greeting', 'social']:
                        return self._generate_greeting_response(context)
                    elif intent in ['stock_analysis', 'technical_analysis', 'trading']:
                        return await self._generate_trading_response(message, context)
                    elif intent in ['personal', 'about_ai']:
                        return self._generate_personal_response(message, context)
                    elif intent in ['help', 'support']:
                        return self._generate_help_response()
                    else:
                        return self._generate_general_response(message, context)
                except Exception as e:
                    logger.error(f"AIQueryAnalyzer failed: {e}")
                    return self._generate_fallback_response()
            else:
                # AIQueryAnalyzer not available - provide general response
                logger.warning("AIQueryAnalyzer not available - providing general response")
                return self._generate_general_response(message, context)
                
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            return self._generate_fallback_response()
    

    
    def _generate_greeting_response(self, context: str) -> str:
        """Generate friendly greeting response."""
        if context:
            return "👋 Hey there! Good to chat with you again! What's on your mind today?"
        else:
            return "👋 Hello! I'm excited to chat with you! I can discuss trading, markets, or anything else you're curious about. What would you like to talk about?"
    
    async def _generate_trading_response(self, message: str, context: str) -> str:
        """Generate intelligent trading-related response with real market data."""
        try:
            # Use centralized SymbolValidator for symbol extraction
            symbol = None
            if self.symbol_validator:
                try:
                    suggestions = self.symbol_validator.suggest_symbols(message)
                    if suggestions and len(suggestions) > 0:
                        # Get the highest confidence symbol
                        best_suggestion = max(suggestions, key=lambda s: s.confidence)
                        if best_suggestion.confidence > 0.7:  # High confidence threshold
                            symbol = best_suggestion.text
                except Exception as e:
                    logger.warning(f"SymbolValidator failed, falling back to local extraction: {e}")

            # Fallback to local symbol extraction if SymbolValidator is not available
            if not symbol:
                symbol = self._extract_symbol_from_message(message)

            if symbol:
                # Get TradingView alerts for the symbol
                alert_data = await self._get_tradingview_alerts(symbol)
                if alert_data:
                    return await self._generate_tradingview_analysis(symbol, alert_data, message)

            # General trading response with market context
            return await self._generate_general_trading_response(message, context)

        except Exception as e:
            logger.error(f"Error generating trading response: {e}")
            return self._generate_fallback_trading_response(message)

    def _generate_fallback_response(self) -> str:
        """Generate a fallback response when AI services are unavailable"""
        return (
            "I'm experiencing some technical difficulties with my AI services right now. "
            "Please try again in a moment, or use the `/ask` command for more detailed analysis."
        )
    
    def _generate_personal_response(self, message: str, context: str) -> str:
        """Generate response about the AI itself."""
        return f"""🤖 **About Me**

Thanks for asking! I'm your AI trading assistant, and I love having conversations about markets and more.

**What I Am:**
• An AI designed to be helpful, knowledgeable, and conversational
• Specialized in trading and financial markets
• Built to have natural, engaging discussions

**What I Enjoy:**
• Analyzing market trends and patterns
• Helping people understand trading concepts
• Having thoughtful conversations about investing
• Learning from each interaction

**My Approach:**
I try to be direct, helpful, and genuinely useful. I'm not just giving template responses - I'm here for real conversations!

💬 *What would you like to know more about? I'm happy to chat!*"""
    
    def _generate_help_response(self) -> str:
        """Generate helpful guidance response."""
        return """🆘 **I'm Here to Help!**

**How I Can Assist:**
• **Natural Conversation** - Just chat with me like you would a friend
• **Trading Discussions** - Markets, strategies, analysis
• **Learning Support** - Explain concepts, answer questions
• **Problem Solving** - Work through challenges together

**Chat Examples:**
• "What do you think about the current market?"
• "Can you explain how options work?"
• "I'm new to trading, where should I start?"
• "What's your opinion on this stock?"

**Tips for Better Chats:**
• Be specific about what you want to discuss
• Ask follow-up questions
• Share your thoughts - I love interactive conversations!

💬 *I'm designed for natural conversation, so just talk to me normally!*"""
    
    def _generate_general_response(self, message: str, context: str) -> str:
        """Generate response for general topics."""
        return f"""💭 **Let's Chat!**

**Your Message:** *{message}*

I'm happy to discuss this with you! While I specialize in trading and markets, I enjoy conversations on many topics.

**My Thoughts:**
I find your question interesting. Here's how I'd approach it:

• I try to give thoughtful, helpful responses
• I can share insights and different perspectives  
• I'm always happy to explore ideas together

**Want to Dive Deeper?**
Feel free to:
• Ask follow-up questions
• Share your own thoughts
• Explore related topics
• Switch to trading topics anytime

💬 *I'm here for genuine conversation - what else would you like to discuss?*"""
    
    def _generate_fallback_response(self) -> str:
        """Generate fallback response for errors."""
        return """🤖 **Oops!**

I encountered a small hiccup, but I'm still here to chat!

**Let's Try Again:**
• Ask me about trading or markets
• Start a new conversation topic
• Tell me what you're thinking about

💬 *I'm ready to chat whenever you are!*"""

    def _initialize_supabase(self):
        """Initialize Supabase client for TradingView alerts."""
        try:
            if SUPABASE_AVAILABLE:
                self.supabase_client = self.supabase_manager.get_client()
                if self.supabase_client:
                    logger.info("Supabase client initialized for TradingView alerts")
                else:
                    logger.warning("Failed to initialize Supabase client")
            else:
                logger.warning("Supabase not available")
        except Exception as e:
            logger.error(f"Error initializing Supabase: {e}")

    def _extract_symbol_from_message(self, message: str) -> Optional[str]:
        """
        Fallback symbol extraction method.

        Note: This method is now used as a fallback when the centralized SymbolValidator
        is not available. The primary symbol extraction should use SymbolValidator
        which includes database validation and suggestion capabilities.
        """
        import re
        # Look for stock symbols (1-5 uppercase letters)
        symbols = re.findall(r'\b[A-Z]{1,5}\b', message.upper())

        # Common stock symbols to prioritize
        known_symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX', 'SPY', 'QQQ']

        for symbol in symbols:
            if symbol in known_symbols or len(symbol) <= 5:
                return symbol

        return symbols[0] if symbols else None

    async def _get_tradingview_alerts(self, symbol: str) -> Optional[dict]:
        """Get TradingView alerts for a symbol from Supabase."""
        try:
            if not self.supabase_client:
                logger.warning("Supabase client not available")
                return None

            # Query recent TradingView alerts for the symbol
            response = self.supabase_client.table('webhook_alerts').select('*').eq('symbol', symbol.upper()).order('timestamp', desc=True).limit(10).execute()

            if not response.data:
                logger.info(f"No TradingView alerts found for {symbol}")
                return None

            alerts = response.data
            latest_alert = alerts[0]

            # Calculate alert statistics
            total_alerts = len(alerts)
            signal_types = {}
            recent_signals = []

            for alert in alerts:
                signal = alert.get('signal', 'unknown')
                signal_types[signal] = signal_types.get(signal, 0) + 1

                # Format recent signals
                if len(recent_signals) < 5:
                    recent_signals.append({
                        'signal': signal,
                        'timestamp': alert.get('timestamp'),
                        'entry_price': alert.get('entry_price'),
                        'timeframe': alert.get('timeframe'),
                        'created_at': alert.get('created_at')
                    })

            return {
                'symbol': symbol.upper(),
                'latest_alert': latest_alert,
                'total_alerts': total_alerts,
                'signal_distribution': signal_types,
                'recent_signals': recent_signals,
                'last_signal': latest_alert.get('signal'),
                'last_entry_price': latest_alert.get('entry_price'),
                'last_timeframe': latest_alert.get('timeframe'),
                'last_timestamp': latest_alert.get('timestamp'),
                'tp1_price': latest_alert.get('tp1_price'),
                'tp2_price': latest_alert.get('tp2_price'),
                'tp3_price': latest_alert.get('tp3_price'),
                'sl_price': latest_alert.get('sl_price')
            }

        except Exception as e:
            logger.error(f"Error fetching TradingView alerts for {symbol}: {e}")

        return None

    async def _get_alert_statistics(self) -> Optional[dict]:
        """Get general TradingView alert statistics."""
        try:
            if not self.supabase_client:
                return None

            # Get recent alerts (last 24 hours)
            from datetime import datetime, timedelta
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_timestamp = int(yesterday.timestamp())

            response = self.supabase_client.table('webhook_alerts').select('symbol, signal, timestamp').gte('timestamp', yesterday_timestamp).order('timestamp', desc=True).execute()

            if not response.data:
                return None

            alerts = response.data

            # Calculate statistics
            total_alerts = len(alerts)
            symbols = {}
            signals = {}

            for alert in alerts:
                symbol = alert.get('symbol', 'Unknown')
                signal = alert.get('signal', 'Unknown')

                symbols[symbol] = symbols.get(symbol, 0) + 1
                signals[signal] = signals.get(signal, 0) + 1

            # Get top symbols and signals
            top_symbols = sorted(symbols.items(), key=lambda x: x[1], reverse=True)[:5]
            top_signals = sorted(signals.items(), key=lambda x: x[1], reverse=True)[:5]

            return {
                'total_alerts_24h': total_alerts,
                'top_symbols': top_symbols,
                'top_signals': top_signals,
                'unique_symbols': len(symbols),
                'unique_signals': len(signals)
            }

        except Exception as e:
            logger.error(f"Error fetching alert statistics: {e}")
            return None

    async def _generate_tradingview_analysis(self, symbol: str, alert_data: dict, original_message: str) -> str:
        """Generate analysis for a symbol using TradingView alert data."""
        latest_alert = alert_data.get('latest_alert', {})
        total_alerts = alert_data.get('total_alerts', 0)
        signal_distribution = alert_data.get('signal_distribution', {})
        recent_signals = alert_data.get('recent_signals', [])

        # Extract key data from latest alert
        last_signal = latest_alert.get('signal', 'N/A')
        entry_price = latest_alert.get('entry_price')
        timeframe = latest_alert.get('timeframe', 'N/A')
        tp1_price = latest_alert.get('tp1_price')
        tp2_price = latest_alert.get('tp2_price')
        tp3_price = latest_alert.get('tp3_price')
        sl_price = latest_alert.get('sl_price')

        # Determine signal sentiment
        signal_emoji = "🟢" if "buy" in last_signal.lower() else "🔴" if "sell" in last_signal.lower() else "🟡"

        # Format timestamp
        timestamp = latest_alert.get('timestamp')
        if timestamp:
            from datetime import datetime
            alert_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        else:
            alert_time = "Unknown"

        analysis_text = f"""📊 **TradingView Analysis: ${symbol}**

**Your Question:** *{original_message}*

**Latest TradingView Alert:**
• **Signal**: {last_signal} {signal_emoji}
• **Entry Price**: ${entry_price or 'N/A'}
• **Timeframe**: {timeframe}
• **Alert Time**: {alert_time}

**Price Targets & Risk:**"""

        if tp1_price or tp2_price or tp3_price or sl_price:
            analysis_text += f"""
• **Take Profit 1**: ${tp1_price or 'N/A'}
• **Take Profit 2**: ${tp2_price or 'N/A'}
• **Take Profit 3**: ${tp3_price or 'N/A'}
• **Stop Loss**: ${sl_price or 'N/A'}"""
        else:
            analysis_text += "\n• No specific price targets in latest alert"

        # Signal distribution analysis
        if signal_distribution:
            top_signals = sorted(signal_distribution.items(), key=lambda x: x[1], reverse=True)[:3]
            signal_summary = ", ".join([f"{signal}: {count}" for signal, count in top_signals])
            analysis_text += f"""

**Alert History (Last 10 alerts):**
• **Total Alerts**: {total_alerts}
• **Signal Distribution**: {signal_summary}"""

        # Recent signals trend
        if recent_signals:
            analysis_text += f"""

**Recent Signal Trend:**"""
            for i, signal in enumerate(recent_signals[:3]):
                signal_name = signal.get('signal', 'Unknown')
                signal_timeframe = signal.get('timeframe', 'N/A')
                analysis_text += f"""
• **{i+1}.** {signal_name} ({signal_timeframe})"""

        analysis_text += f"""

**TradingView Intelligence:**
Based on {total_alerts} TradingView alerts, ${symbol} shows active trading signals. The latest {last_signal} signal suggests current market sentiment.

**What I can analyze further:**
• Alert frequency and timing patterns
• Signal accuracy and follow-through
• Price target achievement rates
• Risk-reward ratios from alerts
• Correlation with market movements

**Ask me specific questions like:**
• "How accurate are {symbol} TradingView alerts?"
• "What's the trend in {symbol} signals?"
• "Should I follow the latest {symbol} alert?"

💡 *This analysis uses real TradingView alerts stored in our Supabase database!*"""

        return analysis_text

    async def _generate_symbol_analysis(self, symbol: str, market_data: dict, original_message: str) -> str:
        """Generate analysis for a specific symbol with real data and technical analysis."""
        price = market_data.get('price', 'N/A')
        change = market_data.get('change', 0)
        change_percent = market_data.get('change_percent', 0)
        volume = market_data.get('volume', 'N/A')
        high = market_data.get('high', 0)
        low = market_data.get('low', 0)
        open_price = market_data.get('open', 0)

        # Determine trend
        trend_emoji = "📈" if change > 0 else "📉" if change < 0 else "➡️"
        trend_text = "bullish" if change > 0 else "bearish" if change < 0 else "neutral"

        # Calculate basic technical indicators if possible
        technical_analysis = await self._get_basic_technical_analysis(symbol, price, high, low, open_price)

        analysis_text = f"""📊 **Real-Time Analysis: ${symbol}**

**Your Question:** *{original_message}*

**Current Market Data:**
• **Price**: ${price} {trend_emoji}
• **Change**: {change:+.2f} ({change_percent:+.2f}%)
• **Volume**: {volume:,} shares
• **High/Low**: ${high} / ${low}
• **Trend**: {trend_text.title()}"""

        if technical_analysis:
            analysis_text += f"""

**Technical Indicators:**
{technical_analysis}"""

        analysis_text += f"""

**AI Insights:**
Based on current market data, ${symbol} is showing {trend_text} momentum.

**Advanced Analysis Available:**
• Technical indicators (RSI, MACD, Moving Averages)
• Support and resistance levels
• Volume analysis and trends
• Risk assessment and position sizing
• Correlation with market indices

**Ask me specific questions like:**
• "What's the RSI for {symbol}?"
• "Should I buy {symbol} now?"
• "What's the risk level for {symbol}?"
• "Analyze {symbol} technical indicators"

💡 *This analysis uses real-time market data from Finnhub API!*"""

        return analysis_text

    async def _get_basic_technical_analysis(self, symbol: str, price: float, high: float, low: float, open_price: float) -> str:
        """Get basic technical analysis for a symbol."""
        try:
            if not PANDAS_AVAILABLE or not all([price, high, low, open_price]):
                return ""

            # Calculate basic indicators
            indicators = []

            # Price position within daily range
            if high > low:
                range_position = ((price - low) / (high - low)) * 100
                if range_position > 80:
                    indicators.append("• **Range Position**: Near daily high (bullish)")
                elif range_position < 20:
                    indicators.append("• **Range Position**: Near daily low (bearish)")
                else:
                    indicators.append(f"• **Range Position**: {range_position:.1f}% of daily range")

            # Gap analysis
            if open_price and price:
                gap_percent = ((open_price - price) / price) * 100
                if abs(gap_percent) > 2:
                    gap_direction = "up" if gap_percent > 0 else "down"
                    indicators.append(f"• **Gap**: {abs(gap_percent):.1f}% gap {gap_direction}")

            # Volatility assessment
            if high > low and price > 0:
                daily_volatility = ((high - low) / price) * 100
                if daily_volatility > 5:
                    indicators.append("• **Volatility**: High (>5%)")
                elif daily_volatility > 2:
                    indicators.append("• **Volatility**: Moderate (2-5%)")
                else:
                    indicators.append("• **Volatility**: Low (<2%)")

            return "\n".join(indicators) if indicators else ""

        except Exception as e:
            logger.error(f"Error calculating basic technical analysis for {symbol}: {e}")
            return ""

    async def _generate_general_trading_response(self, message: str, context: str) -> str:
        """Generate general trading response with TradingView alert context."""

        # Get alert statistics to provide context
        alert_stats = await self._get_alert_statistics()

        base_response = f"""📈 **TradingView Intelligence Hub**

**Your Question:** *{message}*

**AI Analysis:**
I understand you're asking about trading and markets. Here's what I can help with using our TradingView alert system:

📊 **TradingView Alert Analysis**: Real alerts from your TradingView strategies
📈 **Signal Intelligence**: Entry points, price targets, and stop losses
🎯 **Alert History**: Track signal accuracy and performance
⚡ **Risk Management**: Analyze stop loss and take profit levels
🔍 **Pattern Recognition**: Identify trends in your alert signals"""

        # Add alert statistics if available
        if alert_stats:
            total_alerts = alert_stats.get('total_alerts_24h', 0)
            top_symbols = alert_stats.get('top_symbols', [])
            top_signals = alert_stats.get('top_signals', [])

            base_response += f"""

**Recent Alert Activity (Last 24 Hours):**
• **Total Alerts**: {total_alerts}
• **Active Symbols**: {alert_stats.get('unique_symbols', 0)}"""

            if top_symbols:
                top_3_symbols = ", ".join([f"{symbol} ({count})" for symbol, count in top_symbols[:3]])
                base_response += f"""
• **Most Active**: {top_3_symbols}"""

            if top_signals:
                top_3_signals = ", ".join([f"{signal} ({count})" for signal, count in top_signals[:3]])
                base_response += f"""
• **Top Signals**: {top_3_signals}"""

        base_response += f"""

**Example Questions I Excel At:**
• "What's the latest TradingView alert for AAPL?"
• "Show me TSLA's recent trading signals"
• "How accurate are my NVDA alerts?"
• "What's the trend in SPY signals this week?"

**Available Alert Data:**
• Entry prices and signal types
• Take profit levels (TP1, TP2, TP3)
• Stop loss recommendations
• Timeframe analysis
• Signal frequency and patterns

💡 *I use real TradingView alerts stored in our Supabase database to provide intelligent trading insights!*"""

        return base_response

    def _generate_fallback_trading_response(self, message: str) -> str:
        """Generate fallback trading response."""
        return f"""📈 **Trading Assistant**

**Your Question:** *{message}*

I'm here to help with trading and market analysis! While I encountered a small issue accessing real-time data, I can still assist with:

📊 **Market Analysis** - Technical indicators and trends
📈 **Trading Strategies** - Risk management and position sizing
🎯 **Educational Content** - Explain concepts and strategies
⚡ **General Insights** - Market understanding and analysis

**Try asking:**
• "Explain RSI indicator"
• "How do moving averages work?"
• "What's a good risk management strategy?"

💡 *I'll work to get you the best analysis possible!*"""

    def _create_chat_embed(self, user_message: str, ai_response: str, user) -> discord.Embed:
        """Create a clean chat embed."""
        embed = discord.Embed(
            description=ai_response,
            color=discord.Color.green(),
            timestamp=datetime.now()
        )
        
        embed.set_author(
            name=f"💬 Chat with {user.display_name}",
            icon_url=user.display_avatar.url
        )
        
        embed.set_footer(
            text="🤖 Natural AI Chat • Continue the conversation anytime!"
        )
        
        return embed


async def setup(bot: commands.Bot):
    """Set up the chat command."""
    await bot.add_cog(ChatCommand(bot))
    logger.info("Chat command setup complete")
