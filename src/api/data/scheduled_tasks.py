"""
Scheduled tasks for market data operations.

This module handles periodic tasks like cache warming, data cleanup, and monitoring.
"""

import asyncio
from datetime import datetime, time, timedelta
import logging
from typing import Optional, Dict, Any

import pytz

from src.api.data.cache import MarketDataCache, warm_top_symbols_cache
from src.core.logger import get_logger

logger = get_logger(__name__)

class ScheduledTaskManager:
    """Manages scheduled tasks for market data operations."""
    
    def __init__(self):
        self.cache = MarketDataCache()
        self.is_running = False
        self.tasks = {}
        
    async def start(self):
        """Start the scheduled task manager."""
        if self.is_running:
            logger.warning("Task manager is already running")
            return
            
        self.is_running = True
        logger.info("Starting scheduled task manager")
        
        # Start the main scheduling loop
        asyncio.create_task(self._scheduling_loop())
        
    async def stop(self):
        """Stop the scheduled task manager."""
        self.is_running = False
        logger.info("Stopping scheduled task manager")
        
        # Cancel all running tasks
        for task in self.tasks.values():
            if not task.done():
                task.cancel()
                
    async def _scheduling_loop(self):
        """Main scheduling loop that runs continuously."""
        while self.is_running:
            try:
                current_time = datetime.now(pytz.timezone('US/Eastern'))
                
                # Check if it's time to run scheduled tasks
                await self._check_and_run_tasks(current_time)
                
                # Wait 1 minute before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in scheduling loop: {e}")
                await asyncio.sleep(60)
                
    async def _check_and_run_tasks(self, current_time: datetime):
        """Check if it's time to run any scheduled tasks."""
        try:
            # Cache warming task - run 30 minutes before market open
            if self._should_run_cache_warming(current_time):
                await self._run_cache_warming()
                
            # Data cleanup task - run daily at 2 AM ET
            if self._should_run_data_cleanup(current_time):
                await self._run_data_cleanup()
                
        except Exception as e:
            logger.error(f"Error checking scheduled tasks: {e}")
            
    def _should_run_cache_warming(self, current_time: datetime) -> bool:
        """Check if cache warming should run now."""
        # Market opens at 9:30 AM ET, run warming at 9:00 AM ET
        market_open = current_time.replace(
            hour=9, minute=30, second=0, microsecond=0
        )
        warming_time = market_open - timedelta(minutes=30)
        
        # Check if we're within 5 minutes of the warming time
        time_diff = abs((current_time - warming_time).total_seconds())
        return time_diff < 300  # 5 minutes
        
    def _should_run_data_cleanup(self, current_time: datetime) -> bool:
        """Check if data cleanup should run now."""
        # Run cleanup at 2:00 AM ET
        cleanup_time = current_time.replace(
            hour=2, minute=0, second=0, microsecond=0
        )
        
        # Check if we're within 5 minutes of cleanup time
        time_diff = abs((current_time - cleanup_time).total_seconds())
        return time_diff < 300  # 5 minutes
        
    async def _run_cache_warming(self):
        """Run the cache warming task."""
        task_name = "cache_warming"
        
        # Check if task is already running
        if task_name in self.tasks and not self.tasks[task_name].done():
            logger.info("Cache warming task already running, skipping")
            return
            
        logger.info("Starting scheduled cache warming task")
        
        try:
            # Create and store the task
            task = asyncio.create_task(self._execute_cache_warming())
            self.tasks[task_name] = task
            
            # Wait for completion
            await task
            
        except Exception as e:
            logger.error(f"Cache warming task failed: {e}")
        finally:
            # Clean up completed task
            if task_name in self.tasks:
                del self.tasks[task_name]
                
    async def _execute_cache_warming(self):
        """Execute the cache warming task with monitoring."""
        start_time = datetime.now()
        
        try:
            # Get cache stats before warming
            stats_before = await self.cache.get_cache_stats()
            logger.info(f"Cache stats before warming: {stats_before}")
            
            # Execute cache warming
            results = await warm_top_symbols_cache(self.cache)
            
            # Get cache stats after warming
            stats_after = await self.cache.get_cache_stats()
            logger.info(f"Cache stats after warming: {stats_after}")
            
            # Calculate success metrics
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(
                f"Cache warming completed successfully in {duration:.2f}s: "
                f"{success_count}/{total_count} symbols ({success_rate:.1f}% success rate)"
            )
            
            # Log detailed results for monitoring
            failed_symbols = [symbol for symbol, success in results.items() if not success]
            if failed_symbols:
                logger.warning(f"Failed to warm cache for symbols: {failed_symbols}")
                
            # Emit metrics (can be integrated with Prometheus/Grafana)
            await self._emit_cache_warming_metrics(results, duration, success_rate)
            
        except Exception as e:
            logger.error(f"Cache warming execution failed: {e}")
            raise
            
    async def _run_data_cleanup(self):
        """Run the data cleanup task."""
        task_name = "data_cleanup"
        
        # Check if task is already running
        if task_name in self.tasks and not self.tasks[task_name].done():
            logger.info("Data cleanup task already running, skipping")
            return
            
        logger.info("Starting scheduled data cleanup task")
        
        try:
            # Create and store the task
            task = asyncio.create_task(self._execute_data_cleanup())
            self.tasks[task_name] = task
            
            # Wait for completion
            await task
            
        except Exception as e:
            logger.error(f"Data cleanup task failed: {e}")
        finally:
            # Clean up completed task
            if task_name in self.tasks:
                del self.tasks[task_name]
                
    async def _execute_data_cleanup(self):
        """Execute the data cleanup task."""
        start_time = datetime.now()
        
        try:
            # Clean up old cache entries
            # This could include removing expired data, optimizing memory usage, etc.
            logger.info("Data cleanup completed successfully")
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Data cleanup completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Data cleanup execution failed: {e}")
            raise
            
    async def _emit_cache_warming_metrics(
        self, 
        results: Dict[str, bool], 
        duration: float, 
        success_rate: float
    ):
        """Emit metrics for monitoring and alerting."""
        try:
            # This would integrate with your metrics system (Prometheus, etc.)
            metrics = {
                'cache_warming_duration_seconds': duration,
                'cache_warming_success_rate_percent': success_rate,
                'cache_warming_symbols_total': len(results),
                'cache_warming_symbols_successful': sum(1 for success in results.values() if success),
                'cache_warming_symbols_failed': sum(1 for success in results.values() if not success),
                'cache_warming_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Cache warming metrics: {metrics}")
            
            # TODO: Integrate with Prometheus/Grafana
            # await prometheus_client.gauge('cache_warming_success_rate').set(success_rate)
            # await prometheus_client.histogram('cache_warming_duration').observe(duration)
            
        except Exception as e:
            logger.warning(f"Failed to emit cache warming metrics: {e}")
            
    async def run_cache_warming_now(self) -> Dict[str, bool]:
        """Manually trigger cache warming (for testing or on-demand use)."""
        logger.info("Manual cache warming triggered")
        return await warm_top_symbols_cache(self.cache)
        
    async def get_task_status(self) -> Dict[str, Any]:
        """Get status of all scheduled tasks."""
        status = {
            'is_running': self.is_running,
            'active_tasks': {},
            'cache_stats': await self.cache.get_cache_stats()
        }
        
        for task_name, task in self.tasks.items():
            status['active_tasks'][task_name] = {
                'running': not task.done(),
                'cancelled': task.cancelled(),
                'exception': str(task.exception()) if task.exception() else None
            }
            
        return status


# Global instance for easy access
task_manager = ScheduledTaskManager()


async def start_scheduled_tasks():
    """Start the scheduled task manager (called during app startup)."""
    await task_manager.start()
    

async def stop_scheduled_tasks():
    """Stop the scheduled task manager (called during app shutdown)."""
    await task_manager.stop()
    

# Convenience functions for external use
async def warm_cache_now() -> Dict[str, bool]:
    """Trigger cache warming immediately."""
    return await task_manager.run_cache_warming_now()
    

async def get_task_status() -> Dict[str, Any]:
    """Get current task status."""
    return await task_manager.get_task_status() 