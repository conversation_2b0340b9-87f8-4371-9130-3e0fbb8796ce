"""
Market Data API endpoints for fetching stock data.
"""
from datetime import datetime, timedelta
import json
from typing import Optional, Dict, Any

from data.yfinance_provider import Y<PERSON>inanceProvider
from fastapi import APIRouter, HTTPException, Query
import pandas as pd


router = APIRouter(
    prefix="/api/market",
    tags=["market"],
    responses={404: {"description": "Not found"}},
)

# Initialize the data provider
provider = YFinanceProvider()

@router.get("/price/{symbol}")
async def get_current_price(symbol: str):
    """
    Get the current market price for a given symbol.
    
    Args:
        symbol: Stock symbol (e.g., AAPL, MSFT)
        
    Returns:
        dict: Current price information
    """
    try:
        price = provider.get_current_price(symbol)
        if price == 0.0:
            raise HTTPException(status_code=404, detail=f"Could not fetch price for {symbol}")
            
        return {
            "symbol": symbol.upper(),
            "price": price,
            "timestamp": datetime.utcnow().isoformat(),
            "source": "yfinance"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/historical/{symbol}")
async def get_historical(
    symbol: str,
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format (defaults to today)"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)")
):
    """
    Get historical price data for a symbol.
    
    Args:
        symbol: Stock symbol
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format (optional)
        interval: Data interval (1d, 1wk, 1mo)
        
    Returns:
        dict: Historical price data
    """
    try:
        # Convert dates to datetime objects for validation
        try:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d") if end_date else datetime.utcnow()
            
            if start > end:
                raise ValueError("Start date cannot be after end date")
                
            # Limit date range to prevent excessive data loading
            if (end - start).days > 365 * 5:  # 5 years max
                raise HTTPException(status_code=400, detail="Date range too large. Maximum 5 years of data allowed.")
                
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
        
        # Fetch the data
        data = provider.get_historical_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
        
        if data.empty:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        # Convert DataFrame to JSON-serializable format
        # Reset index to include Date as a column
        data = data.reset_index()
        
        # Convert datetime to string
        if 'Date' in data.columns:
            data['Date'] = data['Date'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
        return {
            "symbol": symbol.upper(),
            "data": data.to_dict(orient="records"),
            "start_date": start_date,
            "end_date": end_date if end_date else datetime.utcnow().strftime("%Y-%m-%d"),
            "interval": interval,
            "source": "yfinance"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching historical data: {str(e)}")

@router.get("/intraday/{symbol}")
async def get_intraday(
    symbol: str,
    interval: str = Query("5m", description="Data interval (1m, 5m, 15m, 30m, 60m, 90m, 1h)"),
    days: int = Query(1, description="Number of days of data to fetch (max 7)")
):
    """
    Get intraday price data for a symbol.
    
    Args:
        symbol: Stock symbol
        interval: Data interval (1m, 5m, 15m, 30m, 60m, 90m, 1h)
        days: Number of days of data to fetch (max 7)
        
    Returns:
        dict: Intraday price data
    """
    try:
        # Validate days parameter
        if days < 1 or days > 7:
            raise HTTPException(status_code=400, detail="Days must be between 1 and 7")
            
        # Calculate start date
        end_date = datetime.utcnow()
        start_date = (end_date - timedelta(days=days)).strftime("%Y-%m-%d")
        
        # Fetch the data
        data = provider.get_stock_data(
            symbol=symbol,
            period=f"{days}d",
            interval=interval
        )
        
        if data.empty:
            raise HTTPException(status_code=404, detail=f"No intraday data found for {symbol}")
        
        # Convert DataFrame to JSON-serializable format
        data = data.reset_index()
        
        # Convert datetime to string
        if 'Datetime' in data.columns:
            data['Datetime'] = data['Datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        return {
            "symbol": symbol.upper(),
            "data": data.to_dict(orient="records"),
            "interval": interval,
            "days": days,
            "source": "yfinance"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching intraday data: {str(e)}")
