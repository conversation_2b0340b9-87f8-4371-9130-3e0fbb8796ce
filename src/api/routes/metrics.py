from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException

from src.api.schemas.metrics_schema import MetricsReport
from src.core.monitoring import response_metrics_tracker

metrics_router = APIRouter(prefix="/metrics", tags=["Metrics"])

@metrics_router.get("/overview", response_model=MetricsReport)
async def get_metrics_overview():
    """
    Get a comprehensive overview of response generation metrics
    
    Returns:
        MetricsReport: Detailed metrics report
    """
    try:
        metrics = response_metrics_tracker.get_metrics_report()
        return MetricsReport(**metrics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving metrics: {str(e)}")

@metrics_router.get("/daily")
async def get_daily_metrics():
    """
    Get detailed daily metrics
    
    Returns:
        dict: Daily metrics breakdown
    """
    metrics = response_metrics_tracker.get_metrics_report()
    return metrics.get('daily_metrics', {})

@metrics_router.get("/response-types")
async def get_response_type_distribution():
    """
    Get distribution of response types
    
    Returns:
        dict: Response type distribution
    """
    metrics = response_metrics_tracker.get_metrics_report()
    return metrics.get('response_type_distribution', {}) 