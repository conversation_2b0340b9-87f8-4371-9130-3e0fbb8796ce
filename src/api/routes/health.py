"""
Health check API routes.
Provides system health monitoring endpoints.
"""

import psutil
from datetime import datetime
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from src.core.logger import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns:
        Dict containing system health status
    """
    try:
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Determine overall status
        status = "healthy"
        issues = []
        
        if cpu_percent > 90:
            status = "degraded"
            issues.append("High CPU usage")
            
        if memory.percent > 90:
            status = "degraded" 
            issues.append("High memory usage")
            
        if disk.percent > 90:
            status = "degraded"
            issues.append("High disk usage")
        
        return {
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "issues": issues,
            "metrics": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "disk_free_gb": round(disk.free / (1024**3), 2)
            },
            "services": {
                "api": "running",
                "database": "unknown",  # TODO: Add DB health check
                "cache": "unknown"      # TODO: Add Redis health check
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

@router.get("/health/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    Detailed health check with more comprehensive metrics.
    
    Returns:
        Dict containing detailed system health information
    """
    try:
        basic_health = await health_check()
        
        # Add more detailed metrics
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        
        # Network statistics
        network = psutil.net_io_counters()
        
        # Process count
        process_count = len(psutil.pids())
        
        basic_health.update({
            "uptime_seconds": uptime.total_seconds(),
            "boot_time": boot_time.isoformat(),
            "process_count": process_count,
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }
        })
        
        return basic_health
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(status_code=500, detail="Detailed health check failed")
