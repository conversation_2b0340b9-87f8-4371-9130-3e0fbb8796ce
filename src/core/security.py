"""
Unified security configuration and utilities.
All security settings and configurations should be managed here.
"""

import os
from typing import Optional
from pydantic import BaseModel, Field

class SecurityConfig(BaseModel):
    """
    Unified security configuration.
    All security settings should be defined here.
    """
    
    # JWT Configuration
    jwt_secret_key: str = Field(
        default_factory=lambda: os.getenv("JWT_SECRET_KEY"),
        description="JWT secret key for token signing"
    )
    jwt_algorithm: str = Field(
        default="HS256",
        description="Algorithm used for JWT signing"
    )
    jwt_token_expire_minutes: int = Field(
        default=30,
        description="JWT token expiration time in minutes"
    )
    
    # API Security
    api_key_header: str = Field(
        default="X-API-Key",
        description="Header name for API key authentication"
    )
    api_key_prefix: str = Field(
        default="Bearer",
        description="Prefix for API key authentication"
    )
    
    # Rate Limiting
    rate_limit_enabled: bool = Field(
        default=True,
        description="Enable/disable rate limiting"
    )
    rate_limit_requests: int = Field(
        default=100,
        description="Number of requests allowed per window"
    )
    rate_limit_window_seconds: int = Field(
        default=60,
        description="Time window for rate limiting in seconds"
    )
    
    # Password Policy
    min_password_length: int = Field(
        default=12,
        description="Minimum password length"
    )
    require_special_chars: bool = Field(
        default=True,
        description="Require special characters in passwords"
    )
    require_numbers: bool = Field(
        default=True,
        description="Require numbers in passwords"
    )
    require_uppercase: bool = Field(
        default=True,
        description="Require uppercase letters in passwords"
    )
    require_lowercase: bool = Field(
        default=True,
        description="Require lowercase letters in passwords"
    )
    
    # Session Security
    session_expire_minutes: int = Field(
        default=60,
        description="Session expiration time in minutes"
    )
    max_failed_logins: int = Field(
        default=5,
        description="Maximum failed login attempts before lockout"
    )
    lockout_duration_minutes: int = Field(
        default=15,
        description="Account lockout duration in minutes"
    )
    
    # CORS Settings
    cors_enabled: bool = Field(
        default=True,
        description="Enable/disable CORS"
    )
    allowed_origins: list[str] = Field(
        default_factory=lambda: os.getenv("ALLOWED_ORIGINS", "*").split(","),
        description="Allowed CORS origins"
    )
    allowed_methods: list[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="Allowed HTTP methods"
    )
    
    # SSL/TLS Configuration
    ssl_enabled: bool = Field(
        default=True,
        description="Enable/disable SSL/TLS"
    )
    ssl_cert_path: Optional[str] = Field(
        default=None,
        description="Path to SSL certificate"
    )
    ssl_key_path: Optional[str] = Field(
        default=None,
        description="Path to SSL private key"
    )
    
    class Config:
        """Pydantic model configuration."""
        env_prefix = "SECURITY_"
        case_sensitive = False

    @property
    def password_requirements(self) -> dict:
        """Get password requirements as a dictionary."""
        return {
            "min_length": self.min_password_length,
            "special_chars": self.require_special_chars,
            "numbers": self.require_numbers,
            "uppercase": self.require_uppercase,
            "lowercase": self.require_lowercase
        }
    
    def validate_password(self, password: str) -> tuple[bool, str]:
        """
        Validate a password against security requirements.
    
    Args:
            password: Password to validate
    
    Returns:
            (bool, str): (is_valid, error_message)
        """
        if len(password) < self.min_password_length:
            return False, f"Password must be at least {self.min_password_length} characters"
            
        if self.require_special_chars and not any(c for c in password if not c.isalnum()):
            return False, "Password must contain special characters"
            
        if self.require_numbers and not any(c.isdigit() for c in password):
            return False, "Password must contain numbers"
            
        if self.require_uppercase and not any(c.isupper() for c in password):
            return False, "Password must contain uppercase letters"
            
        if self.require_lowercase and not any(c.islower() for c in password):
            return False, "Password must contain lowercase letters"
            
        return True, "Password meets all requirements"

# Global security configuration instance
security_config = SecurityConfig()
