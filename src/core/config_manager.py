"""
Configuration Manager

Backward compatibility layer - imports from the new unified config system.
This ensures existing imports don't break while we migrate to the new system.
"""

import os
from typing import Any, Dict, Optional

from .config import (
    AppConfig,
    ConfigManager as NewConfigManager,
    ConfigurationError,
    BaseConfig,
    DatabaseConfig,
    RedisConfig,
    APIConfig,
    DataProviderConfig,
    TechnicalAnalysisConfig,
    TradingStrategyConfig,
    AIConfig
)

# Legacy configuration classes for backward compatibility
class TradingBotConfig:
    """Legacy trading bot config - wraps new config system."""
    
    def __init__(self):
        self._config_manager = NewConfigManager()
    
    @property
    def database(self):
        return self._config_manager.config.database
    
    @property
    def redis(self):
        return self._config_manager.config.redis
    
    @property
    def api(self):
        return self._config_manager.config.api
    
    @property
    def data_providers(self):
        return self._config_manager.config.data_providers
    
    @property
    def technical_analysis(self):
        return self._config_manager.config.technical_analysis
    
    @property
    def trading_strategy(self):
        return self._config_manager.config.trading_strategy
    
    @property
    def ai(self):
        return self._config_manager.config.ai

# Global instances for backward compatibility
_config_manager = NewConfigManager()
config = _config_manager.config

def get_config() -> AppConfig:
    """Get configuration - backward compatibility function."""
    return _config_manager.config

# Additional exports for compatibility
__all__ = [
    'config',
    'get_config', 
    'ConfigurationError',
    'TradingBotConfig',
    'BaseConfig',
    'DatabaseConfig',
    'RedisConfig', 
    'APIConfig',
    'DataProviderConfig',
    'TechnicalAnalysisConfig',
    'TradingStrategyConfig',
    'AIConfig'
] 