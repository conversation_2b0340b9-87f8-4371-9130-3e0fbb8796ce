"""
Unified logging system for the entire application.
Provides consistent logging across all components.
"""

import json
import logging
import os
import sys
from datetime import datetime
from functools import wraps
from pathlib import Path
from typing import Any, Callable, Optional, Union

# Constants
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'name': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'filename': record.filename,
            'lineno': record.lineno
        }
        
        # Add extra fields if they exist
        if hasattr(record, 'extra'):
            log_entry.update(record.extra)
            
        return json.dumps(log_entry)

class UnifiedLogger:
    """
    Unified logging system that handles all logging needs.
    Supports both file and console logging, with JSON formatting option.
    """
    
    def __init__(
        self,
        name: str,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        use_json: bool = False,
        console_output: bool = True
    ):
        """
        Initialize logger with specified configuration.
        
        Args:
            name: Logger name
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: Optional log file path
            use_json: Whether to use JSON formatting
            console_output: Whether to output to console
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Prevent duplicate handlers
        self.logger.handlers = []
        
        # Create formatters
        if use_json:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(DEFAULT_LOG_FORMAT)
        
        # Add console handler if requested
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # Add file handler if log file specified
        if log_file:
            # Ensure log directory exists
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log debug message."""
        self.logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log info message."""
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log warning message."""
        self.logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log error message."""
        self.logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log critical message."""
        self.logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log exception with traceback."""
        self.logger.exception(msg, *args, **kwargs)

class AILogger(UnifiedLogger):
    """Specialized logger for AI interactions."""
    
    def __init__(
        self,
        name: str = "ai_logger",
        log_file: Optional[str] = "logs/ai_interactions.log"
    ):
        """Initialize AI logger with specific configuration."""
        super().__init__(
            name=name,
            log_level="INFO",
            log_file=log_file,
            use_json=True
        )
    
    def log_ai_request(
        self,
        model: str,
        prompt: str,
        parameters: dict
    ) -> None:
        """Log AI request details."""
        self.logger.info("AI Request", extra={
            "event_type": "ai_request",
            "model": model,
            "prompt_length": len(prompt),
            "parameters": parameters,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def log_ai_response(
        self,
        model: str,
        response: str,
        duration_ms: float,
        token_count: int
    ) -> None:
        """Log AI response details."""
        self.logger.info("AI Response", extra={
            "event_type": "ai_response",
            "model": model,
            "response_length": len(response),
            "duration_ms": duration_ms,
            "token_count": token_count,
            "timestamp": datetime.utcnow().isoformat()
        })

class PipelineLogger(UnifiedLogger):
    """Specialized logger for pipeline operations."""
    
    def __init__(
        self,
        name: str = "pipeline_logger",
        log_file: Optional[str] = "logs/pipeline.log"
    ):
        """Initialize pipeline logger with specific configuration."""
        super().__init__(
            name=name,
            log_level="INFO",
            log_file=log_file,
            use_json=True
        )
    
    def log_stage_start(
        self,
        pipeline_id: str,
        stage_name: str
    ) -> None:
        """Log pipeline stage start."""
        self.logger.info("Stage Start", extra={
            "event_type": "stage_start",
            "pipeline_id": pipeline_id,
            "stage_name": stage_name,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def log_stage_end(
        self,
        pipeline_id: str,
        stage_name: str,
        duration_ms: float,
        status: str
    ) -> None:
        """Log pipeline stage completion."""
        self.logger.info("Stage End", extra={
            "event_type": "stage_end",
            "pipeline_id": pipeline_id,
            "stage_name": stage_name,
            "duration_ms": duration_ms,
            "status": status,
            "timestamp": datetime.utcnow().isoformat()
        })

def get_logger(
    name: str,
    log_type: str = "default",
    **kwargs: Any
) -> Union[UnifiedLogger, AILogger, PipelineLogger]:
    """
    Get appropriate logger instance based on type.
    
    Args:
        name: Logger name
        log_type: Type of logger (default, ai, pipeline)
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured logger instance
    """
    if log_type == "ai":
        return AILogger(name, **kwargs)
    elif log_type == "pipeline":
        return PipelineLogger(name, **kwargs)
    else:
        return UnifiedLogger(name, **kwargs)

def log_execution(logger: Optional[UnifiedLogger] = None) -> Callable:
    """
    Decorator to log function execution.
    
    Args:
        logger: Logger instance to use
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            start_time = datetime.utcnow()
            try:
                result = func(*args, **kwargs)
                duration = (datetime.utcnow() - start_time).total_seconds() * 1000
                logger.info(
                    f"Function {func.__name__} completed",
                    extra={
                        "function": func.__name__,
                        "duration_ms": duration,
                        "status": "success"
                    }
                )
                return result
            except Exception as e:
                duration = (datetime.utcnow() - start_time).total_seconds() * 1000
                logger.error(
                    f"Function {func.__name__} failed: {str(e)}",
                    extra={
                        "function": func.__name__,
                        "duration_ms": duration,
                        "status": "error",
                        "error": str(e)
                    }
                )
                raise
        return wrapper
    return decorator 