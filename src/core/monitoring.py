"""
Comprehensive System Monitoring and Performance Tracking

Provides advanced monitoring capabilities including:
- System resource tracking
- Performance metrics
- Health checks
- Metrics collection
"""

from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import functools
import json
import logging
import os
from pathlib import Path
import socket
import time
from typing import Dict, Any, Optional, Callable, List

from .config_manager import config
from .logger import get_logger
import platform
import psutil


logger = get_logger(__name__)

@dataclass
class SystemMetrics:
    """Comprehensive system metrics collection"""
    timestamp: datetime = field(default_factory=datetime.now)
    cpu_percent: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_io: Dict[str, float] = field(default_factory=dict)
    process_count: int = 0


class SystemMonitor:
    """
    Advanced system monitoring with comprehensive metrics collection
    """
    
    @classmethod
    def health_check(cls) -> Dict[str, Any]:
        """
        Perform comprehensive system health check
        
        Returns:
            Dictionary with system health metrics
        """
        try:
            # Check if monitoring is enabled via configuration
            if not config.get('app', 'monitoring_enabled', True):
                logger.warning("System monitoring is disabled")
                return {
                    'status': 'disabled',
                    'message': 'Monitoring is currently disabled'
                }
            
            # Collect system metrics
            metrics = {
                'cpu_metrics': cls._get_cpu_metrics(),
                'memory_metrics': cls._get_memory_metrics(),
                'disk_metrics': cls._get_disk_metrics(),
                'network_metrics': cls._get_network_metrics(),
                'process_metrics': cls._get_process_metrics()
            }
            
            # Add overall system status
            metrics['status'] = cls._evaluate_system_health(metrics)
            
            return metrics
        
        except Exception as e:
            logger.error(f"System health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def _get_cpu_metrics() -> Dict[str, Any]:
        """Get CPU usage metrics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            return {
                'cpu_percent': cpu_percent,
                'cpu_count': psutil.cpu_count(),
                'cpu_frequency': psutil.cpu_freq().current
            }
        except Exception as e:
            logger.warning(f"CPU metrics collection failed: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def _get_memory_metrics() -> Dict[str, Any]:
        """Get memory usage metrics"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'memory_percent': memory.percent
            }
        except Exception as e:
            logger.warning(f"Memory metrics collection failed: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def _get_disk_metrics() -> Dict[str, Any]:
        """Get disk usage metrics"""
        try:
            disk = psutil.disk_usage('/')
            return {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'disk_percent': disk.percent
            }
        except Exception as e:
            logger.warning(f"Disk metrics collection failed: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def _get_network_metrics() -> Dict[str, Any]:
        """Get network I/O metrics"""
        try:
            net_io = psutil.net_io_counters()
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
        except Exception as e:
            logger.warning(f"Network metrics collection failed: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def _get_process_metrics() -> Dict[str, Any]:
        """Get process-related metrics"""
        try:
            return {
                'total_processes': len(psutil.pids()),
                'current_process': {
                    'pid': os.getpid(),
                    'memory_info': dict(psutil.Process().memory_info()._asdict())
                }
            }
        except Exception as e:
            logger.warning(f"Process metrics collection failed: {e}")
            return {'error': str(e)}
    
    @classmethod
    def _evaluate_system_health(cls, metrics: Dict[str, Any]) -> str:
        """
        Evaluate overall system health based on collected metrics
        
        Args:
            metrics: Dictionary of system metrics
        
        Returns:
            Health status string
        """
        try:
            # Get thresholds from configuration
            cpu_threshold = config.get('app', 'max_cpu_threshold', 80.0)
            memory_threshold = config.get('app', 'max_memory_threshold', 90.0)
            disk_threshold = config.get('app', 'max_disk_threshold', 90.0)
            
            # Check individual metrics
            cpu_healthy = metrics['cpu_metrics'].get('cpu_percent', 0) < cpu_threshold
            memory_healthy = metrics['memory_metrics'].get('memory_percent', 0) < memory_threshold
            disk_healthy = metrics['disk_metrics'].get('disk_percent', 0) < disk_threshold
            
            # Determine overall status
            if cpu_healthy and memory_healthy and disk_healthy:
                return 'healthy'
            elif not (cpu_healthy and memory_healthy and disk_healthy):
                return 'critical'
            else:
                return 'degraded'
        
        except Exception as e:
            logger.error(f"System health evaluation failed: {e}")
            return 'error'


class PerformanceTracker:
    """
    Performance tracking and metrics collection
    """
    
    def __init__(self):
        """
        Initialize performance tracker with configuration
        """
        # Check if performance tracking is enabled
        self.enabled = getattr(config, 'debug', True)
        
        if not self.enabled:
            logger.warning("Performance tracking is disabled")
    
    def track_performance(self, operation: Optional[Callable] = None, *, enabled: Optional[bool] = None):
        """
        Performance tracking decorator or method wrapper.
        
        Can be used as:
        1. A method wrapper: tracker.track_performance(func)
        2. A decorator: @tracker.track_performance()
        3. A decorator with options: @tracker.track_performance(enabled=True)
        
        Args:
            operation: Function or method to track
            enabled: Optional override for tracking enablement
        
        Returns:
            Wrapped function with performance tracking
        """
        def decorator(func):
            # Support async and sync functions
            if hasattr(func, "__call__") and (getattr(func, "__code__", None) and func.__code__.co_flags & 0x80):
                # Coroutine function (async)
                @functools.wraps(func)
                async def async_wrapper(*args, **kwargs):
                    tracking_enabled = self.enabled if enabled is None else enabled
                    if not tracking_enabled:
                        return await func(*args, **kwargs)
                    start_time = time.time()
                    try:
                        result = await func(*args, **kwargs)
                        end_time = time.time()
                        logger.info(
                            f"Performance tracking: {func.__name__} took {end_time - start_time:.4f} seconds"
                        )
                        return result
                    except Exception as e:
                        end_time = time.time()
                        logger.error(
                            f"Performance tracking failed for {func.__name__} after {end_time - start_time:.4f}s: {e}"
                        )
                        raise
                return async_wrapper
            else:
                # Regular sync function
                @functools.wraps(func)
                def sync_wrapper(*args, **kwargs):
                    tracking_enabled = self.enabled if enabled is None else enabled
                    if not tracking_enabled:
                        return func(*args, **kwargs)
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        end_time = time.time()
                        logger.info(
                            f"Performance tracking: {func.__name__} took {end_time - start_time:.4f} seconds"
                        )
                        return result
                    except Exception as e:
                        end_time = time.time()
                        logger.error(
                            f"Performance tracking failed for {func.__name__} after {end_time - start_time:.4f}s: {e}"
                        )
                        raise
                return sync_wrapper
        
        # Allow using as a simple method wrapper or a decorator
        if operation is None:
            return decorator
        else:
            return decorator(operation)


class ResponseMetricsTracker:
    """
    Track and analyze response generation metrics with Singleton pattern
    """
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, metrics_file: str = 'data/response_metrics.json'):
        """
        Initialize metrics tracker with persistent storage
        
        Args:
            metrics_file (str): Path to metrics storage file
        """
        self.metrics_file = Path(metrics_file)
        self.metrics = self._load_metrics()
    
    def _load_metrics(self) -> Dict[str, Any]:
        """
        Load existing metrics from file, creating directory if needed
        
        Returns:
            Dict of metrics with default structure
        """
        if not self.metrics_file.exists():
            # Ensure directory exists
            self.metrics_file.parent.mkdir(exist_ok=True)
            
            return {
                'total_responses': 0,
                'response_types': defaultdict(int),
                'error_rates': defaultdict(int),
                'response_times': [],
                'daily_metrics': {},
                'timestamps': []
            }
        
        try:
            with open(self.metrics_file, 'r') as f:
                loaded_metrics = json.load(f)
                # Convert response_types back to defaultdict
                loaded_metrics['response_types'] = defaultdict(int, loaded_metrics.get('response_types', {}))
                return loaded_metrics
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error loading metrics: {e}")
            return {
                'total_responses': 0,
                'response_types': defaultdict(int),
                'error_rates': defaultdict(int),
                'response_times': [],
                'daily_metrics': {},
                'timestamps': []
            }
    
    def track_response(
        self, 
        response: Dict[str, Any], 
        processing_time: float, 
        error: Optional[Exception] = None
    ):
        """
        Track metrics for a generated response with enhanced tracking
        
        Args:
            response (dict): Generated response
            processing_time (float): Time taken to generate response
            error (Exception, optional): Any error that occurred
        """
        # Increment total responses
        self.metrics['total_responses'] += 1
        
        # Track response types
        response_type = response.get('response_type', 'unknown')
        self.metrics['response_types'][response_type] += 1
        
        # Track processing times
        self.metrics['response_times'].append(processing_time)
        
        # Track errors if any
        if error:
            error_type = type(error).__name__
            self.metrics['error_rates'][error_type] += 1
        
        # Track timestamps
        self.metrics['timestamps'].append(datetime.now().isoformat())
        
        # Track daily metrics
        today = datetime.now().strftime('%Y-%m-%d')
        if today not in self.metrics['daily_metrics']:
            self.metrics['daily_metrics'][today] = {
                'total_responses': 0,
                'response_types': defaultdict(int),
                'avg_processing_time': 0,
                'errors': defaultdict(int)
            }
        
        daily_metrics = self.metrics['daily_metrics'][today]
        daily_metrics['total_responses'] += 1
        daily_metrics['response_types'][response_type] += 1
        
        # Update average processing time
        daily_metrics['avg_processing_time'] = (
            (daily_metrics['avg_processing_time'] * (daily_metrics['total_responses'] - 1) + processing_time) / 
            daily_metrics['total_responses']
        )
        
        # Track daily errors
        if error:
            daily_metrics['errors'][type(error).__name__] += 1
        
        # Save metrics
        self._save_metrics()
    
    def _save_metrics(self):
        """Save metrics to file with error handling"""
        try:
            # Convert defaultdict to regular dict for JSON serialization
            serializable_metrics = {
                key: dict(value) if isinstance(value, defaultdict) else value
                for key, value in self.metrics.items()
            }
            
            with open(self.metrics_file, 'w') as f:
                json.dump(serializable_metrics, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save metrics: {e}")
    
    def get_metrics_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive metrics report
        
        Returns:
            dict: Detailed metrics report
        """
        # Build a schema-compatible, JSON-serializable report
        avg_time = (
            sum(self.metrics['response_times']) / len(self.metrics['response_times'])
            if self.metrics['response_times'] else 0
        )

        # Deep-convert daily metrics inner defaultdicts to dicts
        daily_serialized: Dict[str, Any] = {}
        for day, data in self.metrics['daily_metrics'].items():
            daily_serialized[day] = {
                'total_responses': data.get('total_responses', 0),
                'response_types': dict(data.get('response_types', {})),
                'avg_processing_time': data.get('avg_processing_time', 0),
                # Keep errors if present but ensure serializable
                'errors': dict(data.get('errors', {})) if isinstance(data.get('errors', {}), dict) else {}
            }

        # Only include fields expected by the API schema (extra keys are unnecessary)
        report = {
            'total_responses': self.metrics['total_responses'],
            'response_type_distribution': dict(self.metrics['response_types']),
            'avg_processing_time': avg_time,
            'daily_metrics': daily_serialized,
        }

        return report
    
    def reset_metrics(self):
        """Reset all collected metrics"""
        self.metrics = {
            'total_responses': 0,
            'response_types': defaultdict(int),
            'error_rates': defaultdict(int),
            'response_times': [],
            'daily_metrics': {},
            'timestamps': []
        }
        self._save_metrics()

# Global metrics tracker
response_metrics_tracker = ResponseMetricsTracker()
