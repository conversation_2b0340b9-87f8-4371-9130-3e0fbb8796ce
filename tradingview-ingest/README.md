# TradingView Ingestion Service

A high-performance, real-time market data ingestion and analysis system that receives webhooks from TradingView and processes them for automated trading strategies.

## 🚀 Features

- **Real-time Webhook Processing**: Handles TradingView webhooks with sub-second latency
- **Data Validation**: Comprehensive data quality checks and validation
- **Batch Processing**: Efficient database operations with configurable batch sizes
- **Technical Analysis**: Built-in technical indicators and signal generation
- **Alert System**: Configurable alerts via webhooks and Discord
- **Monitoring**: Prometheus metrics and structured logging
- **Scalable Architecture**: Async/await design with connection pooling

## 🏗️ Architecture

```
TradingView → Webhook → Ingestion Service → Database + Redis
                    ↓
              Real-time Analysis → Alert Engine
```

### Components

- **Webhook Receiver**: Validates and processes incoming webhooks
- **Data Parser**: Converts webhook data to structured objects
- **Storage Manager**: Handles database and Redis operations
- **Market Analyzer**: Generates trading signals from market data
- **Alert Engine**: Sends notifications for important events

## 🛠️ Installation

### Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose
- Python 3.11+
- PostgreSQL 15+
- Redis 7+

### Quick Start

1. **<PERSON><PERSON> and navigate to the directory:**
   ```bash
   cd tradingview-ingest
   ```

2. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

3. **Update configuration in `.env`:**
   ```bash
   # Set your webhook secret
   WEBHOOK_SECRET=your_super_secret_key_here
   
   # Configure alert endpoints
   DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-url
   ```

4. **Start the services:**
   ```bash
   docker-compose up -d
   ```

5. **Verify the service is running:**
   ```bash
   curl http://localhost:8001/health
   ```

## 📊 API Endpoints

### Health Check
```
GET /health
```

### Webhook Endpoint
```
POST /webhook/tradingview
```

### Metrics
```
GET /metrics
```

### Documentation
```
GET /docs
```

## 🌐 Internet Access Setup (ngrok)

### 1. Configure ngrok

1. **Get ngrok Auth Token**: Sign up at [ngrok.com](https://ngrok.com) and get your auth token
2. **Set Environment Variables**:
   ```bash
   export NGROK_AUTH_TOKEN="your_ngrok_auth_token_here"
   export WEBHOOK_SECRET="your_super_secret_webhook_key_here"
   ```
3. **Start ngrok Tunnel**:
   ```bash
   ./start_ngrok.sh
   ```
4. **Copy the ngrok URL**: It will look like `https://abc123.ngrok.io`

### 2. Create TradingView Alert

1. Go to TradingView and create a new alert
2. Set the alert condition (e.g., "Price crosses above SMA")
3. Set the alert action to "Webhook URL"
4. Enter your webhook URL: `https://abc123.ngrok.io/webhook/tradingview`

### 2. Webhook Payload Format

The service expects webhooks in this format:

```json
{
  "symbol": "AAPL",
  "event_type": "price_update",
  "timestamp": 1640995200,
  "price": 150.25,
  "volume": 1000000,
  "exchange": "NASDAQ"
}
```

### 3. Supported Event Types

- `price_update`: Market price data
- `indicator_update`: Technical indicator values
- `signal_generated`: Trading signals
- `alert_triggered`: Custom alerts

## 🗄️ Database Schema

The service automatically creates these tables:

- **market_data**: Price and volume data
- **technical_indicators**: Indicator values
- **trading_signals**: Generated trading signals
- **webhook_events**: Webhook processing history

## 📈 Monitoring

### Prometheus Metrics

- Webhook processing time and count
- Data parsing performance
- Storage operation metrics
- Analysis processing time
- Alert delivery status

### Logging

Structured JSON logging with:
- Request/response details
- Error tracking
- Performance metrics
- Data quality indicators

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `WEBHOOK_SECRET` | Secret for webhook validation | Required |
| `BATCH_SIZE` | Database batch size | 100 |
| `PROCESSING_DELAY` | Delay between batches (seconds) | 0.1 |
| `RATE_LIMIT_PER_MINUTE` | Webhook rate limit | 1000 |
| `ENABLE_ALERTS` | Enable alert system | true |

### Performance Tuning

- **Batch Size**: Increase for higher throughput, decrease for lower latency
- **Processing Delay**: Adjust based on your data volume
- **Database Connections**: Modify pool size in `docker-compose.yml`

## 🔒 Security

- **Webhook Validation**: HMAC signature verification
- **Rate Limiting**: Per-IP request limiting
- **Input Validation**: Comprehensive data validation
- **SQL Injection Protection**: Parameterized queries
- **Payload Size Limits**: Configurable maximum payload size

## 🧪 Testing

### Test Webhook

```bash
curl -X POST http://localhost:8001/webhook/tradingview \
  -H "Content-Type: application/json" \
  -H "X-TradingView-Signature: your_signature_here" \
  -d '{
    "symbol": "TEST",
    "event_type": "price_update",
    "timestamp": 1640995200,
    "price": 100.00,
    "exchange": "TEST"
  }'
```

### Load Testing

```bash
# Install Apache Bench
apt-get install apache2-utils

# Test with 1000 requests
ab -n 1000 -c 10 -p test_payload.json \
   -T application/json \
   http://localhost:8001/webhook/tradingview
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify connection string in `.env`
   - Check Docker network configuration

2. **Webhook Validation Failed**
   - Verify `WEBHOOK_SECRET` is set correctly
   - Check signature header format
   - Ensure payload matches expected schema

3. **High Memory Usage**
   - Reduce batch size
   - Increase processing delay
   - Monitor Redis memory usage

### Logs

```bash
# View service logs
docker-compose logs tradingview-ingest

# Follow logs in real-time
docker-compose logs -f tradingview-ingest

# View database logs
docker-compose logs tradingview-db
```

## 🔄 Integration with Trading Bot

This service can be integrated with your existing trading bot:

1. **Database Integration**: Connect to the PostgreSQL database
2. **Redis Cache**: Use Redis for real-time data access
3. **Webhook Alerts**: Receive real-time signals
4. **API Integration**: Use the REST API endpoints

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details 