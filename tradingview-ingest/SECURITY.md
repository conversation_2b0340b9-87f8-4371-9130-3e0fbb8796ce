# 🔒 Security Documentation

## Overview

This TradingView ingestion service implements comprehensive security measures to ensure only legitimate TradingView webhooks can access the system. All other traffic is blocked and monitored.

## 🛡️ Security Features

### 1. **Request Validation**
- **Method Restriction**: Only POST requests allowed
- **Content Type**: Must be `application/json`
- **Payload Size**: Maximum 1MB (configurable)
- **Path Restriction**: Only `/webhook/tradingview` endpoint accessible

### 2. **Rate Limiting**
- **Per-IP Limits**: 100 requests per minute per IP address
- **Automatic Blocking**: IPs exceeding limits are temporarily blocked
- **Suspicious Activity Detection**: IPs with suspicious behavior are blocked

### 3. **IP Address Security**
- **Dynamic Blocking**: IPs with suspicious activity are automatically blocked
- **Suspicious Activity Tracking**: Monitors and records suspicious behavior
- **Configurable Whitelist**: Can restrict to specific IP ranges if needed

### 4. **User-Agent Filtering**
- **Bot Detection**: Blocks common bot/script User-Agents
- **TradingView Compatibility**: Allows TradingView webhooks (may not have specific User-Agent)
- **Blocked Patterns**: `bot`, `crawler`, `spider`, `scraper`, `curl`, `wget`, etc.

### 5. **Webhook Signature Validation**
- **HMAC-SHA256**: Validates webhook signatures using secret key
- **Configurable**: Can be enabled/disabled via configuration
- **Timestamp Validation**: Rejects webhooks older than 5 minutes

### 6. **ngrok Security**
- **TLS Only**: Forces HTTPS connections
- **Host Header Validation**: Prevents host header attacks
- **Region Restriction**: Configurable to specific regions
- **Inspection Disabled**: Prevents ngrok inspection interface access

## 🚫 Blocked Traffic

### **Automatically Blocked:**
- Non-POST requests
- Non-JSON content types
- Payloads larger than 1MB
- Requests to unauthorized endpoints
- Known bot/script User-Agents
- IPs with suspicious activity patterns
- Rate limit violations

### **Suspicious Activity:**
- Multiple failed validation attempts
- Unusual request patterns
- Excessive request frequency
- Malformed payloads

## 🔐 Configuration

### **Security Settings in `config/tradingview_config.py`:**
```python
# Security Configuration
rate_limit_per_minute: int = 100  # Requests per minute per IP
max_payload_size: int = 1024 * 1024  # 1MB max payload
webhook_ip_whitelist: list = []  # IP restrictions (empty = none)
require_signature: bool = True  # Require webhook signature
max_webhook_age_seconds: int = 300  # 5 minutes max age
```

### **Environment Variables:**
```bash
# Required for security
WEBHOOK_SECRET=your_super_secret_key_here
NGROK_AUTH_TOKEN=your_ngrok_token_here

# Optional security settings
ENVIRONMENT=production  # Enables stricter logging
LOG_LEVEL=WARNING  # Reduces sensitive information in logs
```

## 🚨 Incident Response

### **Automatic Responses:**
1. **Rate Limit Exceeded**: Returns 429 status, logs IP
2. **Invalid Request**: Returns 400 status, logs details
3. **Suspicious Activity**: Records IP, may block after 5 attempts
4. **Blocked IP**: Returns 403 status, logs access attempt

### **Manual Actions:**
1. **Review Logs**: Check `docker-compose logs tradingview-ingest`
2. **Monitor Metrics**: Use `/metrics` endpoint for security stats
3. **IP Management**: View blocked IPs in security middleware
4. **Configuration Updates**: Modify security settings as needed

## 📊 Security Monitoring

### **Available Metrics:**
- Blocked IP count
- Suspicious activity count
- Rate limit violations
- Request validation failures
- Security middleware statistics

### **Logging:**
- All security events are logged with structured logging
- IP addresses and request details are recorded
- Suspicious activity is flagged and tracked
- Failed validations are logged with context

## 🔧 Security Hardening

### **Production Recommendations:**
1. **Set `ENVIRONMENT=production`** for stricter logging
2. **Use strong `WEBHOOK_SECRET`** (32+ characters)
3. **Monitor logs regularly** for suspicious activity
4. **Set up alerting** for security events
5. **Regular security reviews** of configuration

### **Network Security:**
1. **Firewall Rules**: Only allow ngrok traffic
2. **VPN Access**: Consider VPN for admin access
3. **IP Whitelisting**: Restrict to known TradingView IPs if possible
4. **SSL/TLS**: ngrok provides automatic HTTPS

## 🚀 Getting Started Securely

### **1. Set Environment Variables:**
```bash
export WEBHOOK_SECRET="your_very_long_random_secret_here"
export NGROK_AUTH_TOKEN="your_ngrok_auth_token"
```

### **2. Start Services:**
```bash
# Start the secure service
docker-compose up -d

# Start ngrok tunnel
./start_ngrok.sh
```

### **3. Verify Security:**
```bash
# Test health endpoint (should work)
curl http://localhost:8001/health

# Test unauthorized endpoint (should be blocked)
curl http://localhost:8001/unauthorized

# Test webhook with invalid method (should be blocked)
curl -X GET http://localhost:8001/webhook/tradingview
```

### **4. Monitor Security:**
```bash
# Check security metrics
curl http://localhost:8001/metrics

# View security logs
docker-compose logs tradingview-ingest | grep -i security
```

## 🆘 Security Support

### **If You Suspect a Breach:**
1. **Immediate**: Stop ngrok tunnel
2. **Investigate**: Review logs and metrics
3. **Block**: Add suspicious IPs to firewall
4. **Rotate**: Change webhook secret
5. **Monitor**: Watch for continued suspicious activity

### **Security Contacts:**
- Review logs: `docker-compose logs tradingview-ingest`
- Check metrics: `curl http://localhost:8001/metrics`
- Security config: `config/tradingview_config.py`

---

**Remember**: Security is an ongoing process. Regularly review logs, update configurations, and monitor for new threats. 