"""
Storage Manager for TradingView Data
Handles database and Redis operations for storing market data
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, Union, List
import structlog
from prometheus_client import Counter, Histogram, Gauge
import asyncpg
import redis.asyncio as redis
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from .data_parser import MarketData, TechnicalIndicator, TradingSignal
from config.tradingview_config import config

logger = structlog.get_logger()

class StorageManager:
    """Manages storage operations for market data"""
    
    def __init__(self):
        self.storage_counter = Counter(
            'tradingview_storage_operations_total',
            'Total number of storage operations',
            ['operation_type', 'data_type', 'status']
        )
        self.storage_time = Histogram(
            'tradingview_storage_operation_seconds',
            'Time spent on storage operations',
            ['operation_type', 'data_type']
        )
        self.storage_queue_size = Gauge(
            'tradingview_storage_queue_size',
            'Number of items in storage queue'
        )
        
        # Database connection
        self.db_pool: Optional[asyncpg.Pool] = None
        self.redis_client: Optional[redis.Redis] = None
        
        # Batch processing
        self.batch_queue: List[Union[MarketData, TechnicalIndicator, TradingSignal]] = []
        self.batch_size = config.batch_size
        self.processing_delay = config.processing_delay
        
        # Background task
        self.batch_processor_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """Initialize database and Redis connections"""
        try:
            # Initialize PostgreSQL connection pool with optimized settings
            self.db_pool = await asyncpg.create_pool(
                config.database_url,
                min_size=10,        # Higher minimum for consistent performance
                max_size=50,        # Increase if handling high throughput
                max_queries=50000,  # Prevent connection overuse
                max_inactive_connection_lifetime=300,  # 5 minutes
                command_timeout=30  # Prevent hanging queries
            )
            logger.info("PostgreSQL connection pool initialized")
            
            # Initialize Redis connection
            self.redis_client = redis.from_url(config.redis_url)
            await self.redis_client.ping()
            logger.info("Redis connection initialized")
            
            # Start batch processor
            self.batch_processor_task = asyncio.create_task(self._batch_processor())
            logger.info("Batch processor started")
            
        except Exception as e:
            logger.error("Failed to initialize storage manager", error=str(e))
            raise
    
    async def check_database_health(self) -> bool:
        """Check if database connections are healthy"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def check_redis_health(self) -> bool:
        """Check if Redis connection is healthy"""
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    async def ensure_connections(self):
        """Ensure all connections are healthy, reconnect if needed"""
        if not await self.check_database_health():
            logger.info("Reconnecting to database...")
            await self.initialize()
        
        if not await self.check_redis_health():
            logger.info("Reconnecting to Redis...")
            await self.initialize()
    
    async def cleanup(self):
        """Cleanup connections and tasks"""
        try:
            # Stop batch processor
            if self.batch_processor_task:
                self.batch_processor_task.cancel()
                try:
                    await self.batch_processor_task
                except asyncio.CancelledError:
                    pass
            
            # Close database connections
            if self.db_pool:
                await self.db_pool.close()
            
            # Close Redis connection
            if self.redis_client:
                await self.redis_client.close()
                
            logger.info("Storage manager cleanup completed")
            
        except Exception as e:
            logger.error("Error during storage manager cleanup", error=str(e))
    
    async def store_market_data(self, data: Union[MarketData, TechnicalIndicator, TradingSignal]):
        """
        Store market data (adds to batch queue for efficient processing)
        
        Args:
            data: Market data to store
        """
        try:
            # For now, only store in Redis and skip PostgreSQL until tables are created
            # TODO: Create proper tables for market_data and technical_indicators when needed
            
            # Store in Redis for immediate access
            await self._store_in_redis(data)
            
            # Record metrics
            self.storage_counter.labels(
                operation_type="queue_add",
                data_type=getattr(data, 'data_type', 'unknown'),
                status="success"
            ).inc()
            
            logger.info("Data stored in Redis (PostgreSQL tables not yet created)",
                        symbol=getattr(data, 'symbol', 'unknown'),
                        data_type=getattr(data, 'data_type', 'unknown'))
            
        except Exception as e:
            self.storage_counter.labels(
                operation_type="queue_add",
                data_type=getattr(data, 'data_type', 'unknown'),
                status="error"
            ).inc()
            
            logger.error("Failed to store data", error=str(e))
            raise
    
    async def _store_in_redis(self, data: Union[MarketData, TechnicalIndicator, TradingSignal]):
        """Store data in Redis for immediate access"""
        try:
            if isinstance(data, MarketData):
                key = f"market_data:{data.symbol}"
                value = {
                    "symbol": data.symbol,
                    "exchange": data.exchange,
                    "price": data.price,
                    "volume": data.volume,
                    "timestamp": data.timestamp,
                    "data_source": data.data_source
                }
                # Set with 1 hour expiration
                await self.redis_client.setex(key, 3600, json.dumps(value))
                
            elif isinstance(data, TechnicalIndicator):
                key = f"indicator:{data.symbol}:{data.indicator_name}"
                value = {
                    "symbol": data.symbol,
                    "indicator_name": data.indicator_name,
                    "value": data.value,
                    "timestamp": data.timestamp,
                    "parameters": data.parameters
                }
                await self.redis_client.setex(key, 3600, json.dumps(value))
                
            elif isinstance(data, TradingSignal):
                key = f"signal:{data.symbol}:{data.timestamp}"
                value = {
                    "symbol": data.symbol,
                    "signal_type": data.signal_type,
                    "confidence": data.confidence,
                    "price": data.price,
                    "timestamp": data.timestamp,
                    "indicators_used": data.indicators_used,
                    "reasoning": data.reasoning
                }
                await self.redis_client.setex(key, 3600, json.dumps(value))
                
        except Exception as e:
            logger.error("Failed to store data in Redis", error=str(e))
    
    async def _batch_processor(self):
        """Background task to process batched data"""
        while True:
            try:
                if len(self.batch_queue) >= self.batch_size:
                    # Process batch
                    batch = self.batch_queue[:self.batch_size]
                    self.batch_queue = self.batch_queue[self.batch_size:]
                    self.storage_queue_size.set(len(self.batch_queue))
                    
                    await self._process_batch(batch)
                    
                elif len(self.batch_queue) > 0:
                    # Process remaining items after delay
                    await asyncio.sleep(self.processing_delay)
                    batch = self.batch_queue.copy()
                    self.batch_queue.clear()
                    self.storage_queue_size.set(0)
                    
                    await self._process_batch(batch)
                
                else:
                    # No data to process, wait
                    await asyncio.sleep(self.processing_delay)
                    
            except asyncio.CancelledError:
                logger.info("Batch processor cancelled")
                break
            except Exception as e:
                logger.error("Error in batch processor", error=str(e))
                await asyncio.sleep(1)  # Wait before retrying
    
    async def _process_batch(self, batch: List[Union[MarketData, TechnicalIndicator, TradingSignal]]):
        """Process a batch of data items"""
        start_time = time.time()
        
        try:
            # Group by data type for efficient processing
            market_data_batch = []
            indicator_batch = []
            signal_batch = []
            
            for item in batch:
                if isinstance(item, MarketData):
                    market_data_batch.append(item)
                elif isinstance(item, TechnicalIndicator):
                    indicator_batch.append(item)
                elif isinstance(item, TradingSignal):
                    signal_batch.append(item)
            
            # Process each batch type
            tasks = []
            if market_data_batch:
                tasks.append(self._store_market_data_batch(market_data_batch))
            if indicator_batch:
                tasks.append(self._store_indicator_batch(indicator_batch))
            if signal_batch:
                tasks.append(self._store_signal_batch(signal_batch))
            
            if tasks:
                await asyncio.gather(*tasks)
            
            processing_time = time.time() - start_time
            logger.info("Batch processed successfully",
                       batch_size=len(batch),
                       processing_time_ms=round(processing_time * 1000, 2))
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error("Batch processing failed",
                        batch_size=len(batch),
                        processing_time_ms=round(processing_time * 1000, 2),
                        error=str(e))
            
            # Re-queue failed items
            self.batch_queue.extend(batch)
            self.storage_queue_size.set(len(self.batch_queue))
    
    async def _store_market_data_batch(self, batch: List[MarketData]):
        """Store batch of market data in PostgreSQL"""
        if not batch:
            return
        
        start_time = time.time()
        
        try:
            # Prepare batch insert
            values = []
            for data in batch:
                values.append((
                    data.symbol,
                    data.exchange,
                    data.price,
                    data.volume,
                    data.open_price,
                    data.high_price,
                    data.low_price,
                    data.close_price,
                    data.timestamp,
                    data.data_source
                ))
            
            # Execute batch insert
            async with self.db_pool.acquire() as conn:
                await conn.executemany("""
                    INSERT INTO market_data 
                    (symbol, exchange, price, volume, open_price, high_price, low_price, close_price, timestamp, data_source)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, to_timestamp($9), $10)
                """, values)
            
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="market_data"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="market_data",
                status="success"
            ).inc()
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="market_data"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="market_data",
                status="error"
            ).inc()
            
            logger.error("Failed to store market data batch", error=str(e))
            raise
    
    async def _store_indicator_batch(self, batch: List[TechnicalIndicator]):
        """Store batch of technical indicators in PostgreSQL"""
        if not batch:
            return
        
        start_time = time.time()
        
        try:
            values = []
            for data in batch:
                values.append((
                    data.symbol,
                    data.indicator_name,
                    data.value,
                    data.timestamp,
                    json.dumps(data.parameters) if data.parameters else None
                ))
            
            async with self.db_pool.acquire() as conn:
                await conn.executemany("""
                    INSERT INTO technical_indicators 
                    (symbol, indicator_name, value, timestamp, parameters)
                    VALUES ($1, $2, $3, to_timestamp($4), $5)
                """, values)
            
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="technical_indicator"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="technical_indicator",
                status="success"
            ).inc()
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="technical_indicator"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="technical_indicator",
                status="error"
            ).inc()
            
            logger.error("Failed to store indicator batch", error=str(e))
            raise
    
    async def _store_signal_batch(self, batch: List[TradingSignal]):
        """Store batch of trading signals in PostgreSQL"""
        if not batch:
            return
        
        start_time = time.time()
        
        try:
            values = []
            for data in batch:
                values.append((
                    data.symbol,
                    data.signal_type,
                    data.confidence,
                    data.price,
                    data.timestamp,
                    json.dumps(data.indicators_used) if data.indicators_used else None,
                    data.reasoning
                ))
            
            async with self.db_pool.acquire() as conn:
                await conn.executemany("""
                    INSERT INTO trading_signals 
                    (symbol, signal_type, confidence, price, timestamp, indicators_used, reasoning, created_at)
                    VALUES ($1, $2, $3, $4, to_timestamp($5), $6, $7, CURRENT_TIMESTAMP)
                """, values)
            
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="trading_signal"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="trading_signal",
                status="success"
            ).inc()
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.storage_time.labels(
                operation_type="batch_insert",
                data_type="trading_signal"
            ).observe(processing_time)
            
            self.storage_counter.labels(
                operation_type="batch_insert",
                data_type="trading_signal",
                status="error"
            ).inc()
            
            logger.error("Failed to store signal batch", error=str(e))
            raise
    
    async def store_webhook_alert(self, webhook_data: Dict[str, Any]):
        """Store webhook alert data in the webhook_alerts table"""
        try:
            # Log the raw webhook data for debugging
            logger.info("Processing webhook data", 
                       webhook_data=webhook_data,
                       has_raw_text='raw_text' in webhook_data,
                       has_symbol='symbol' in webhook_data,
                       data_type=type(webhook_data))
            
            # Handle different data formats intelligently
            ticker = None
            signal_type = None
            timestamp = None
            timeframe = None
            entry_price = None
            tp1_price = None
            tp2_price = None
            tp3_price = None
            sl_price = None
            raw_text = None
            
            # Format 1: Pipe-separated text (legacy format)
            if 'raw_text' in webhook_data and '|' in webhook_data['raw_text']:
                logger.info("Processing pipe-separated format")
                parts = webhook_data['raw_text'].split('|')
                if len(parts) >= 9:
                    ticker = parts[0].strip()
                    signal_type = parts[1].strip()
                    timestamp = int(parts[2].strip())
                    timeframe = parts[3].strip()
                    entry_price = float(parts[4].strip())
                    tp1_price = float(parts[5].strip())
                    tp2_price = float(parts[6].strip())
                    tp3_price = float(parts[7].strip())
                    sl_price = float(parts[8].strip())
                    raw_text = webhook_data['raw_text']
                    logger.info("Successfully parsed pipe-separated format", ticker=ticker, signal_type=signal_type)
                else:
                    logger.warning(f"Invalid pipe-separated format: {webhook_data['raw_text']}")
                    return None
                    
            # Format 2: Direct field mapping (JSON array format from main.py)
            elif all(key in webhook_data for key in ['symbol', 'signal', 'timestamp', 'timeframe', 'entry_price', 'tp1_price', 'tp2_price', 'tp3_price', 'sl_price']):
                logger.info("Processing direct field format")
                ticker = webhook_data['symbol']
                signal_type = webhook_data['signal']
                timestamp = webhook_data['timestamp']
                timeframe = webhook_data['timeframe']
                entry_price = webhook_data['entry_price']
                tp1_price = webhook_data['tp1_price']
                tp2_price = webhook_data['tp2_price']
                tp3_price = webhook_data['tp3_price']
                sl_price = webhook_data['sl_price']
                raw_text = str(webhook_data)  # Convert the dict to string for storage
                logger.info("Successfully parsed direct field format", ticker=ticker, signal_type=signal_type)
                
            # Format 3: Raw text that might be JSON array
            elif 'raw_text' in webhook_data and webhook_data['raw_text'].startswith('[') and webhook_data['raw_text'].endswith(']'):
                logger.info("Processing raw JSON array text")
                try:
                    import ast
                    # Safely evaluate the string representation of the array
                    array_data = ast.literal_eval(webhook_data['raw_text'])
                    if isinstance(array_data, list) and len(array_data) >= 9:
                        ticker = str(array_data[0])
                        signal_type = str(array_data[1])
                        timestamp = int(array_data[2])
                        timeframe = str(array_data[3])
                        entry_price = float(array_data[4])
                        tp1_price = float(array_data[5])
                        tp2_price = float(array_data[6])
                        tp3_price = float(array_data[7])
                        sl_price = float(array_data[8])
                        raw_text = webhook_data['raw_text']
                        logger.info("Successfully parsed raw JSON array text", ticker=ticker, signal_type=signal_type)
                    else:
                        logger.warning(f"Invalid array format in raw_text: {webhook_data['raw_text']}")
                        return None
                except Exception as e:
                    logger.error(f"Failed to parse raw JSON array text: {e}", raw_text=webhook_data['raw_text'])
                    return None
            else:
                logger.warning("Unrecognized webhook data format", 
                             webhook_data=webhook_data,
                             available_keys=list(webhook_data.keys()))
                return None
            
            # Determine alert type from signal type
            if 'ENTRY' in signal_type:
                alert_type = 'ENTRY'
            elif 'TP' in signal_type:
                alert_type = 'TP_HIT'
            elif 'SL' in signal_type:
                alert_type = 'SL_HIT'
            else:
                alert_type = 'UNKNOWN'
            
            # Insert into webhook_alerts table
            query = """
                INSERT INTO webhook_alerts (
                    alert_type, signal, symbol, timestamp, timeframe,
                    entry_price, tp1_price, tp2_price, tp3_price, sl_price,
                    raw_text, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)
                RETURNING id
            """
            
            values = (
                alert_type,
                signal_type,
                ticker,
                timestamp,
                timeframe,
                entry_price,
                tp1_price,
                tp2_price,
                tp3_price,
                sl_price,
                raw_text
            )
            
            async with self.db_pool.acquire() as conn:
                result = await conn.fetchval(query, *values)
                logger.info(f"Stored webhook alert with ID: {result}", 
                           ticker=ticker, signal_type=signal_type, alert_type=alert_type)
                
                # Also store in Redis for caching with ticker as key for easy filtering
                redis_key = f"alert:{ticker}:{timestamp}"
                await self.redis_client.setex(
                    redis_key,
                    3600,  # 1 hour TTL
                    json.dumps({
                        'id': result,
                        'ticker': ticker,
                        'signal_type': signal_type,
                        'timestamp': timestamp,
                        'timeframe': timeframe,
                        'entry_price': entry_price,
                        'tp1_price': tp1_price,
                        'tp2_price': tp2_price,
                        'tp3_price': tp3_price,
                        'sl_price': sl_price,
                        'alert_type': alert_type
                    })
                )
                
                # Store ticker index for easy filtering
                await self.redis_client.sadd(f"tickers:{timeframe}", ticker)
                await self.redis_client.zadd(f"alerts_by_time:{ticker}", {str(result): timestamp})
                
                # Ensure ticker exists in tickers table (auto-create if new)
                await self.ensure_ticker_exists(ticker)
                
                return result
                
        except Exception as e:
            logger.error(f"Failed to store webhook alert: {e}", 
                        webhook_data=webhook_data,
                        error_details=str(e))
            raise
                
        except Exception as e:
            logger.error(f"Failed to store webhook alert: {e}")
            raise
    
    async def store_webhook_error(self, error_message: str, webhook_data: Dict[str, Any]):
        """Store webhook error event"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO webhook_events 
                    (event_type, payload, status, error_message)
                    VALUES ($1, $2, $3, $4)
                """, "error", json.dumps(webhook_data), "error", error_message)
                
        except Exception as e:
            logger.error("Failed to store webhook error", error=str(e))
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get storage manager metrics"""
        return {
            "queue_size": len(self.batch_queue),
            "total_operations": 0,  # Prometheus handles this automatically
            "storage_time_avg": 0.0  # Prometheus handles this automatically
        } 

    async def get_alerts_by_ticker(self, ticker: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alerts for a specific ticker, ordered by time (newest first)"""
        try:
            query = """
                SELECT * FROM webhook_alerts 
                WHERE symbol = $1 
                ORDER BY timestamp DESC, created_at DESC 
                LIMIT $2
            """
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, ticker, limit)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get alerts for ticker {ticker}: {e}")
            raise

    async def get_alerts_by_timeframe(self, timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alerts for a specific timeframe, ordered by time (newest first)"""
        try:
            query = """
                SELECT * FROM webhook_alerts 
                WHERE timeframe = $1 
                ORDER BY timestamp DESC, created_at DESC 
                LIMIT $2
            """
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, timeframe, limit)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get alerts for timeframe {timeframe}: {e}")
            raise

    async def get_alerts_by_signal_type(self, signal_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get alerts for a specific signal type, ordered by time (newest first)"""
        try:
            query = """
                SELECT * FROM webhook_alerts 
                WHERE signal = $1 
                ORDER BY timestamp DESC, created_at DESC 
                LIMIT $2
            """
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, signal_type, limit)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get alerts for signal type {signal_type}: {e}")
            raise

    async def get_recent_alerts(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent alerts from the last N hours, ordered by time (newest first)"""
        try:
            query = """
                SELECT * FROM webhook_alerts 
                WHERE created_at >= NOW() - INTERVAL '1 hour' * $1
                ORDER BY timestamp DESC, created_at DESC 
                LIMIT $2
            """
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, hours, limit)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get recent alerts: {e}")
            raise

    async def get_ticker_summary(self, ticker: str) -> Dict[str, Any]:
        """Get a summary of all alerts for a specific ticker"""
        try:
            query = """
                SELECT 
                    symbol,
                    COUNT(*) as total_alerts,
                    COUNT(CASE WHEN alert_type = 'ENTRY' THEN 1 END) as entry_alerts,
                    COUNT(CASE WHEN alert_type = 'TP_HIT' THEN 1 END) as tp_alerts,
                    COUNT(CASE WHEN alert_type = 'SL_HIT' THEN 1 END) as sl_alerts,
                    MIN(timestamp) as first_alert,
                    MAX(timestamp) as last_alert,
                    array_agg(DISTINCT timeframe) as timeframes
                FROM webhook_alerts 
                WHERE symbol = $1 
                GROUP BY symbol
            """
            
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(query, ticker)
                if row:
                    return dict(row)
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get ticker summary for {ticker}: {e}")
            raise 

    # ============================================================================
    # TICKER MANAGEMENT METHODS
    # ============================================================================
    
    async def ensure_ticker_exists(self, ticker: str) -> bool:
        """
        Ensure a ticker exists in the tickers table.
        If it doesn't exist, create it. If it exists, update last_seen.
        Returns True if ticker was created, False if it already existed.
        """
        try:
            async with self.db_pool.acquire() as conn:
                # Check if ticker exists
                existing = await conn.fetchrow(
                    "SELECT symbol, alert_count FROM tickers WHERE symbol = $1",
                    ticker
                )
                
                if existing:
                    # Update last_seen and increment alert_count
                    await conn.execute(
                        """
                        UPDATE tickers 
                        SET last_seen = CURRENT_TIMESTAMP, 
                            alert_count = alert_count + 1,
                            is_active = TRUE
                        WHERE symbol = $1
                        """,
                        ticker
                    )
                    logger.info(f"Updated existing ticker: {ticker} (total alerts: {existing['alert_count'] + 1})")
                    return False  # Already existed
                else:
                    # Create new ticker
                    await conn.execute(
                        """
                        INSERT INTO tickers (symbol, first_seen, last_seen, alert_count, is_active)
                        VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, TRUE)
                        """,
                        ticker
                    )
                    logger.info(f"Created new ticker: {ticker}")
                    return True  # Was created
                    
        except Exception as e:
            logger.error(f"Failed to ensure ticker exists for {ticker}: {e}")
            raise
    
    async def get_all_tickers(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all tickers, optionally filtered by active status"""
        try:
            query = """
                SELECT symbol, first_seen, last_seen, alert_count, is_active
                FROM tickers
                {}
                ORDER BY last_seen DESC
            """.format("WHERE is_active = TRUE" if active_only else "")
            
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get tickers: {e}")
            raise
    
    async def deactivate_ticker(self, ticker: str) -> bool:
        """Deactivate a ticker (mark as inactive)"""
        try:
            async with self.db_pool.acquire() as conn:
                result = await conn.execute(
                    "UPDATE tickers SET is_active = FALSE WHERE symbol = $1",
                    ticker
                )
                if result == "UPDATE 1":
                    logger.info(f"Deactivated ticker: {ticker}")
                    return True
                else:
                    logger.warning(f"Ticker not found for deactivation: {ticker}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to deactivate ticker {ticker}: {e}")
            raise
    
    async def reactivate_ticker(self, ticker: str) -> bool:
        """Reactivate a ticker (mark as active)"""
        try:
            async with self.db_pool.acquire() as conn:
                result = await conn.execute(
                    "UPDATE tickers SET is_active = TRUE WHERE symbol = $1",
                    ticker
                )
                if result == "UPDATE 1":
                    logger.info(f"Reactivated ticker: {ticker}")
                    return True
                else:
                    logger.warning(f"Ticker not found for reactivation: {ticker}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to reactivate ticker {ticker}: {e}")
            raise
    
    async def save_raw_webhook(self, webhook_id: str, webhook_data: Dict[str, Any]) -> bool:
        """Save raw webhook data for research purposes with retry logic"""
        return await self._execute_with_retry(
            self._save_raw_webhook_impl, webhook_id, webhook_data
        )
    
    async def _save_raw_webhook_impl(self, webhook_id: str, webhook_data: Dict[str, Any]) -> bool:
        """Implementation of raw webhook storage"""
        try:
            if not self.db_pool:
                logger.warning("Database not connected, skipping storage")
                return False
                
            # Use the existing webhooks table
            query = """
                INSERT INTO webhooks (
                    webhook_id, raw_data, client_ip, timestamp, status
                ) VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (webhook_id) DO UPDATE SET
                    raw_data = EXCLUDED.raw_data,
                    client_ip = EXCLUDED.client_ip,
                    timestamp = EXCLUDED.timestamp,
                    status = EXCLUDED.status
            """
            
            await self.db_pool.execute(
                query,
                webhook_id,
                json.dumps(webhook_data),
                webhook_data.get("client_ip", "unknown"),
                webhook_data.get("timestamp", time.time()),
                "received"
            )
            
            logger.info("Raw webhook saved", webhook_id=webhook_id)
            return True
            
        except Exception as e:
            logger.error(f"Failed to save raw webhook {webhook_id}: {str(e)}")
            return False
    
    async def store_webhook_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Store parsed webhook alert data with retry logic"""
        return await self._execute_with_retry(
            self._store_webhook_alert_impl, alert_data
        )
    
    async def _store_webhook_alert_impl(self, alert_data: Dict[str, Any]) -> bool:
        """Implementation of webhook alert storage"""
        try:
            if not self.db_pool:
                logger.warning("Database not connected, skipping storage")
                return False
            
            # Extract the actual data from the webhook structure
            # Test webhooks have: {"payload": {...}, "raw_text": "..."}
            actual_data = alert_data.get("payload", alert_data)
            
            # Use the existing webhook_alerts table with correct column names
            query = """
                INSERT INTO webhook_alerts (
                    alert_type, signal, symbol, timestamp, timeframe,
                    entry_price, tp1_price, tp2_price, tp3_price, sl_price, raw_text
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            """
            
            # Extract values with sensible defaults
            symbol = actual_data.get("symbol", "unknown")
            signal_type = actual_data.get("signal_type", "alert")
            timestamp = int(actual_data.get("timestamp", time.time()))
            price = actual_data.get("price")
            
            await self.db_pool.execute(
                query,
                signal_type,                    # alert_type
                signal_type,                    # signal  
                symbol,                         # symbol
                timestamp,                      # timestamp
                "1h",                          # timeframe (default)
                price,                          # entry_price (use price if available)
                None,                           # tp1_price (not provided)
                None,                           # tp2_price (not provided)
                None,                           # tp3_price (not provided)
                None,                           # sl_price (not provided)
                json.dumps(alert_data)          # Store full data as raw_text
            )
            
            logger.info("Webhook alert stored successfully", 
                       symbol=symbol,
                       alert_type=signal_type,
                       timestamp=timestamp)
            return True
            
        except Exception as e:
            logger.error("Failed to store webhook alert", 
                        error=str(e),
                        alert_data_keys=list(alert_data.keys()) if isinstance(alert_data, dict) else "not_dict",
                        symbol=alert_data.get("symbol", "unknown") if isinstance(alert_data, dict) else "unknown")
            return False
    
    async def save_parsed_data(self, webhook_id: str, parsed_data: Dict[str, Any]) -> bool:
        """Save parsed webhook data to database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO parsed_webhooks (webhook_id, parsed_data, timestamp)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (webhook_id) DO UPDATE SET
                        parsed_data = EXCLUDED.parsed_data,
                        timestamp = EXCLUDED.timestamp
                """, 
                webhook_id, 
                json.dumps(parsed_data), 
                time.time()
                )
                
                logger.info(f"Parsed webhook data saved: {webhook_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save parsed data for webhook {webhook_id}: {e}")
            return False
    
    async def get_ticker_stats(self) -> Dict[str, Any]:
        """Get overall ticker statistics"""
        try:
            async with self.db_pool.acquire() as conn:
                stats = await conn.fetchrow("""
                    SELECT 
                        COUNT(*) as total_tickers,
                        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_tickers,
                        COUNT(CASE WHEN is_active = FALSE THEN 1 END) as inactive_tickers,
                        SUM(alert_count) as total_alerts,
                        AVG(alert_count) as avg_alerts_per_ticker,
                        MAX(last_seen) as most_recent_activity
                    FROM tickers
                """)
                
                return dict(stats) if stats else {}
                
        except Exception as e:
            logger.error(f"Failed to get ticker stats: {e}")
            raise 

    async def get_health_status(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        return {
            "database": await self.check_database_health(),
            "redis": await self.check_redis_health(),
            "queue_size": len(self.batch_queue),
            "queue_health": len(self.batch_queue) < 1000,  # Simple threshold
            "processor_running": self.batch_processor_task and not self.batch_processor_task.done(),
            "last_processed": getattr(self, 'last_batch_processed_time', None)
        } 

    async def _execute_with_retry(self, func, *args, max_retries: int = 3, **kwargs):
        """Execute function with exponential backoff retry"""
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Operation failed after {max_retries} attempts", error=str(e))
                    raise
                
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(f"Operation attempt {attempt + 1} failed, retrying in {wait_time}s", error=str(e))
                await asyncio.sleep(wait_time)
                
                # Try to reconnect if it's a connection issue
                if attempt == 1:  # On second attempt
                    await self.ensure_connections() 