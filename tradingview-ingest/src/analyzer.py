"""
Market Analyzer for TradingView Data
Processes incoming market data and generates trading signals
"""

import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
import structlog
from prometheus_client import Counter, Histogram, Gauge

from .data_parser import MarketData, TechnicalIndicator, TradingSignal
from config.tradingview_config import config

logger = structlog.get_logger()

@dataclass
class AnalysisResult:
    """Result of market data analysis"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 to 1.0
    price: Optional[float] = None
    timestamp: float = None
    indicators_used: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None
    should_alert: bool = False
    data_source: str = "tradingview"
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class MarketAnalyzer:
    """Analyzes market data and generates trading signals"""
    
    def __init__(self):
        self.analysis_counter = Counter(
            'tradingview_analysis_total',
            'Total number of analysis operations',
            ['symbol', 'signal_type', 'status']
        )
        self.analysis_time = Histogram(
            'tradingview_analysis_seconds',
            'Time spent on analysis',
            ['symbol', 'signal_type']
        )
        self.active_symbols = Gauge(
            'tradingview_active_symbols',
            'Number of active symbols being analyzed'
        )
        
        # Data storage for analysis
        self.market_data_cache: Dict[str, List[MarketData]] = {}
        self.indicator_cache: Dict[str, List[TechnicalIndicator]] = {}
        self.signal_history: Dict[str, List[TradingSignal]] = {}
        
        # Analysis parameters
        self.min_data_points = 10
        self.confidence_threshold = 0.7
        self.alert_threshold = 0.8
        
        # Track active symbols
        self.active_symbols_set = set()
    
    async def initialize(self):
        """Initialize the analyzer"""
        try:
            logger.info("Market analyzer initialized")
        except Exception as e:
            logger.error("Failed to initialize market analyzer", error=str(e))
            raise
    
    async def cleanup(self):
        """Cleanup analyzer resources"""
        try:
            # Clear caches
            self.market_data_cache.clear()
            self.indicator_cache.clear()
            self.signal_history.clear()
            self.active_symbols_set.clear()
            
            logger.info("Market analyzer cleanup completed")
        except Exception as e:
            logger.error("Error during market analyzer cleanup", error=str(e))
    
    async def analyze_data(self, data: Union[MarketData, TechnicalIndicator, TradingSignal]) -> Optional[AnalysisResult]:
        """
        Analyze incoming data and generate trading signals
        
        Args:
            data: Market data, technical indicator, or trading signal
            
        Returns:
            Analysis result with trading signal, or None if no signal generated
        """
        start_time = time.time()
        
        try:
            if isinstance(data, MarketData):
                result = await self._analyze_market_data(data)
            elif isinstance(data, TechnicalIndicator):
                result = await self._analyze_technical_indicator(data)
            elif isinstance(data, TradingSignal):
                result = await self._analyze_trading_signal(data)
            else:
                logger.warning("Unknown data type for analysis", data_type=type(data))
                return None
            
            if result:
                # Record metrics
                processing_time = time.time() - start_time
                self.analysis_time.labels(
                    symbol=result.symbol,
                    signal_type=result.signal_type
                ).observe(processing_time)
                
                self.analysis_counter.labels(
                    symbol=result.symbol,
                    signal_type=result.signal_type,
                    status="success"
                ).inc()
                
                logger.info("Analysis completed successfully",
                           symbol=result.symbol,
                           signal=result.signal_type,
                           confidence=result.confidence,
                           processing_time_ms=round(processing_time * 1000, 2))
            
            return result
            
        except Exception as e:
            symbol = data.symbol if hasattr(data, 'symbol') else 'unknown'
            signal_type = 'unknown'
            
            self.analysis_counter.labels(
                symbol=symbol,
                signal_type=signal_type,
                status="error"
            ).inc()
            
            logger.error("Analysis failed", error=str(e))
            return None
    
    async def _analyze_market_data(self, data: MarketData) -> Optional[AnalysisResult]:
        """Analyze market data for trading opportunities"""
        try:
            # Add to cache
            if data.symbol not in self.market_data_cache:
                self.market_data_cache[data.symbol] = []
            self.market_data_cache[data.symbol].append(data)
            
            # Keep only recent data (last 100 points)
            if len(self.market_data_cache[data.symbol]) > 100:
                self.market_data_cache[data.symbol] = self.market_data_cache[data.symbol][-100:]
            
            # Update active symbols
            self.active_symbols_set.add(data.symbol)
            self.active_symbols.set(len(self.active_symbols_set))
            
            # Check if we have enough data for analysis
            if len(self.market_data_cache[data.symbol]) < self.min_data_points:
                return None
            
            # Perform technical analysis
            analysis = await self._perform_technical_analysis(data.symbol)
            
            if analysis:
                # Check if this is a significant signal
                if analysis.confidence >= self.confidence_threshold:
                    analysis.should_alert = analysis.confidence >= self.alert_threshold
                    return analysis
            
            return None
            
        except Exception as e:
            logger.error("Failed to analyze market data", symbol=data.symbol, error=str(e))
            return None
    
    async def _analyze_technical_indicator(self, data: TechnicalIndicator) -> Optional[AnalysisResult]:
        """Analyze technical indicator data"""
        try:
            # Add to cache
            if data.symbol not in self.indicator_cache:
                self.indicator_cache[data.symbol] = []
            self.indicator_cache[data.symbol].append(data)
            
            # Keep only recent indicators (last 50 points)
            if len(self.indicator_cache[data.symbol]) > 50:
                self.indicator_cache[data.symbol] = self.indicator_cache[data.symbol][-50:]
            
            # Check for extreme indicator values
            if data.indicator_name == "RSI":
                if data.value > 80:
                    return AnalysisResult(
                        symbol=data.symbol,
                        signal_type="sell",
                        confidence=0.8,
                        price=None,
                        timestamp=data.timestamp,
                        indicators_used={"RSI": data.value},
                        reasoning=f"RSI overbought: {data.value:.2f}",
                        should_alert=True
                    )
                elif data.value < 20:
                    return AnalysisResult(
                        symbol=data.symbol,
                        signal_type="buy",
                        confidence=0.8,
                        price=None,
                        timestamp=data.timestamp,
                        indicators_used={"RSI": data.value},
                        reasoning=f"RSI oversold: {data.value:.2f}",
                        should_alert=True
                    )
            
            elif data.indicator_name == "MACD":
                # MACD analysis would go here
                pass
            
            return None
            
        except Exception as e:
            logger.error("Failed to analyze technical indicator", symbol=data.symbol, error=str(e))
            return None
    
    async def _analyze_trading_signal(self, data: TradingSignal) -> Optional[AnalysisResult]:
        """Analyze incoming trading signal from TradingView"""
        try:
            # Store signal in history
            if data.symbol not in self.signal_history:
                self.signal_history[data.symbol] = []
            self.signal_history[data.symbol].append(data)
            
            # Keep only recent signals (last 20)
            if len(self.signal_history[data.symbol]) > 20:
                self.signal_history[data.symbol] = self.signal_history[data.symbol][-20:]
            
            # Check if this is a high-confidence signal
            if data.confidence >= self.confidence_threshold:
                return AnalysisResult(
                    symbol=data.symbol,
                    signal_type=data.signal_type,
                    confidence=data.confidence,
                    price=data.price,
                    timestamp=data.timestamp,
                    indicators_used=data.indicators_used,
                    reasoning=data.reasoning,
                    should_alert=data.confidence >= self.alert_threshold
                )
            
            return None
            
        except Exception as e:
            logger.error("Failed to analyze trading signal", symbol=data.symbol, error=str(e))
            return None
    
    async def _perform_technical_analysis(self, symbol: str) -> Optional[AnalysisResult]:
        """Perform technical analysis on cached market data"""
        try:
            if symbol not in self.market_data_cache:
                return None
            
            market_data = self.market_data_cache[symbol]
            if len(market_data) < self.min_data_points:
                return None
            
            # Get recent price data
            recent_prices = [data.price for data in market_data[-20:]]
            current_price = recent_prices[-1]
            
            # Calculate simple moving averages
            sma_5 = sum(recent_prices[-5:]) / 5
            sma_20 = sum(recent_prices[-20:]) / 20
            
            # Generate signals based on moving average crossover
            if sma_5 > sma_20 and recent_prices[-2] <= sma_20:
                # Golden cross (5-day MA crosses above 20-day MA)
                return AnalysisResult(
                    symbol=symbol,
                    signal_type="buy",
                    confidence=0.75,
                    price=current_price,
                    timestamp=time.time(),
                    indicators_used={"SMA_5": sma_5, "SMA_20": sma_20},
                    reasoning="Golden cross: 5-day MA crossed above 20-day MA",
                    should_alert=True
                )
            
            elif sma_5 < sma_20 and recent_prices[-2] >= sma_20:
                # Death cross (5-day MA crosses below 20-day MA)
                return AnalysisResult(
                    symbol=symbol,
                    signal_type="sell",
                    confidence=0.75,
                    price=current_price,
                    timestamp=time.time(),
                    indicators_used={"SMA_5": sma_5, "SMA_20": sma_20},
                    reasoning="Death cross: 5-day MA crossed below 20-day MA",
                    should_alert=True
                )
            
            # Check for price momentum
            price_change = (current_price - recent_prices[0]) / recent_prices[0]
            if abs(price_change) > 0.05:  # 5% change
                signal_type = "buy" if price_change > 0 else "sell"
                confidence = min(0.6 + abs(price_change) * 2, 0.9)
                
                return AnalysisResult(
                    symbol=symbol,
                    signal_type=signal_type,
                    confidence=confidence,
                    price=current_price,
                    timestamp=time.time(),
                    indicators_used={"price_change_pct": price_change * 100},
                    reasoning=f"Price momentum: {price_change * 100:.1f}% change",
                    should_alert=confidence >= self.alert_threshold
                )
            
            return None
            
        except Exception as e:
            logger.error("Failed to perform technical analysis", symbol=symbol, error=str(e))
            return None
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get analyzer metrics"""
        return {
            "active_symbols": len(self.active_symbols_set),
            "total_analysis": 0,  # Prometheus handles this automatically
            "analysis_time_avg": 0.0,  # Prometheus handles this automatically
            "cached_market_data": sum(len(data) for data in self.market_data_cache.values()),
            "cached_indicators": sum(len(data) for data in self.indicator_cache.values()),
            "cached_signals": sum(len(data) for data in self.signal_history.values())
        } 