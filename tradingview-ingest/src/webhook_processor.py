"""
Webhook Processor for TradingView Alerts
Processes queued webhooks and converts them into actionable trading signals
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
import structlog
from prometheus_client import Counter, Histogram, Gauge

from .data_parser import DataParser
from .storage_manager import StorageManager
from .alert_engine import Alert<PERSON>ngine
from .text_parser import PineScriptAlertParser
from .models import Webhook<PERSON>lert, WebhookData
from .automated_analyzer import AITradingAnalyzer

logger = structlog.get_logger()

# Prometheus metrics
webhook_processor_total = Counter('webhook_processor_total', 'Total webhooks processed by processor')
webhook_processor_duration = Histogram('webhook_processor_duration_seconds', 'Webhook processor duration')
webhook_processor_errors = Counter('webhook_processor_errors_total', 'Total webhook processor errors')
webhook_format_detection = Counter('webhook_format_detection_total', 'Webhook format detection', ['format_type'])

class WebhookProcessor:
    """Processes TradingView webhooks and converts them to trading signals"""
    
    def __init__(self):
        """Initialize the webhook processor"""
        self.redis_client = None
        self.storage_manager = StorageManager()
        self.alert_engine = AlertEngine()
        self.ai_trading_analyzer = None
        self.data_parser = DataParser()
        self.text_parser = PineScriptAlertParser()
        self.is_running = False
        self.initialized = False
        self.processing_task = None
        self.last_webhook_time = None
        self.error_count = 0
        self.last_error_time = None
        
        # Note: Metrics are defined at module level to avoid duplication
        
    async def initialize(self):
        """Initialize all components"""
        try:
            # Initialize storage manager
            await self.storage_manager.initialize()
            
            # Get Discord webhook URL from environment
            import os
            discord_webhook_url = os.getenv("DISCORD_WEBHOOK_URL")
            
            # Initialize AI trading analyzer with Discord integration
            self.ai_trading_analyzer = AITradingAnalyzer(
                self.storage_manager.db_pool, 
                discord_webhook_url
            )
            
            self.initialized = True
            logger.info("Webhook processor initialized successfully", 
                       has_discord=bool(discord_webhook_url))
        except Exception as e:
            logger.error("Failed to initialize webhook processor", error=str(e))
            raise
        
    async def start_processing(self, redis_client, queue_name: str = "webhook_queue"):
        """Start processing webhooks from Redis queue"""
        if not self.initialized:
            await self.initialize()
        
        # Store the Redis client
        self.redis_client = redis_client
        self.is_running = True
        logger.info("Starting webhook processor", queue_name=queue_name)
        
        # Start AI trading analyzer in background
        if self.ai_trading_analyzer:
            asyncio.create_task(self.ai_trading_analyzer.start(interval_minutes=5))
            logger.info("AI Trading Analyzer started (5-minute intervals)")
        
        while self.is_running:
            try:
                # Check Redis connection health
                if not self.redis_client:
                    logger.warning("Redis client not available, waiting...")
                    await asyncio.sleep(2)
                    continue
                
                try:
                    await self.redis_client.ping()
                except Exception:
                    logger.warning("Redis connection lost, attempting to reconnect...")
                    await asyncio.sleep(2)
                    continue
                
                # Wait for webhook data
                result = await self.redis_client.blpop(queue_name, timeout=1)
                
                if result:
                    queue_name, webhook_data_raw = result
                    await self.process_webhook(webhook_data_raw)
                    
            except asyncio.CancelledError:
                logger.info("Webhook processor cancelled")
                break
            except Exception as e:
                logger.error("Error in webhook processing loop", error=str(e))
                webhook_processor_errors.inc()
                await asyncio.sleep(1)  # Brief pause on error
                
        logger.info("Webhook processor stopped")
    
    async def process_webhook_with_retry(self, webhook_data_raw: str, max_retries: int = 3):
        """Process webhook with exponential backoff retry"""
        for attempt in range(max_retries):
            try:
                await self.process_webhook(webhook_data_raw)
                return True
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Webhook failed after {max_retries} attempts", error=str(e))
                    webhook_processor_errors.inc()
                    # Store failed webhook for manual review
                    await self._store_failed_webhook(webhook_data_raw, str(e))
                    raise
                
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(f"Webhook attempt {attempt + 1} failed, retrying in {wait_time}s", error=str(e))
                await asyncio.sleep(wait_time)
    
    async def process_webhook(self, webhook_data_raw: str):
        """Process a single webhook"""
        start_time = time.time()
        
        try:
            # Parse webhook data
            webhook_data = json.loads(webhook_data_raw)
            webhook_id = webhook_data.get("webhook_id", "unknown")
            
            logger.info("Processing webhook", webhook_id=webhook_id)
            
            # Extract payload
            payload = webhook_data.get("payload", {})
            raw_text = webhook_data.get("raw_text", "")
            
            # Parse the webhook data with format detection
            parsed_data = await self._parse_webhook_payload(payload, raw_text)
            
            if parsed_data:
                # Store the parsed data
                await self._store_webhook_data(webhook_id, webhook_data, parsed_data)
                
                # Send alerts if configured
                await self._send_alerts(parsed_data)
                
                # Record success metrics
                webhook_processor_total.inc()
                processing_time = time.time() - start_time
                webhook_processor_duration.observe(processing_time)
                
                logger.info("Webhook processed successfully", 
                           webhook_id=webhook_id,
                           symbol=getattr(parsed_data, 'symbol', 'unknown'),
                           processing_time_ms=round(processing_time * 1000, 2))
            else:
                logger.warning("Failed to parse webhook data", webhook_id=webhook_id)
                webhook_processor_errors.inc()
                
        except Exception as e:
            logger.error("Failed to process webhook", error=str(e))
            webhook_processor_errors.inc()
    
    @staticmethod
    def detect_format(webhook_data: Dict[str, Any]) -> str:
        """Auto-detect webhook format"""
        if 'raw_text' in webhook_data and '|' in webhook_data['raw_text']:
            return 'pipe_separated'
        elif all(key in webhook_data for key in ['symbol', 'signal', 'timestamp']):
            return 'direct_fields'
        elif 'raw_text' in webhook_data and webhook_data['raw_text'].startswith('['):
            return 'json_array'
        elif 'symbol' in webhook_data and 'event_type' in webhook_data:
            return 'structured'
        return 'unknown'
    
    async def _parse_webhook_payload(self, payload: Any, raw_text: str) -> Optional[Any]:
        """Parse webhook payload into structured data with format detection"""
        try:
            # Detect format and record metrics
            format_type = self.detect_format({"raw_text": raw_text, **payload} if isinstance(payload, dict) else {})
            webhook_format_detection.labels(format_type=format_type).inc()
            
            # Try to parse as structured data first
            if isinstance(payload, dict) and payload:
                # Use the data parser for structured data
                return self.data_parser.parse_webhook_data(payload)
            
            # Fallback to text parsing for raw text alerts
            elif raw_text and isinstance(raw_text, str):
                # Try to parse as Pine Script alert format
                return self.text_parser.parse_alert(raw_text)
            
            else:
                logger.warning("Unable to parse webhook payload", 
                             payload_type=type(payload).__name__,
                             has_raw_text=bool(raw_text),
                             format_type=format_type)
                return None
                
        except Exception as e:
            logger.error("Failed to parse webhook payload", error=str(e))
            return None
    
    async def _store_webhook_data(self, webhook_id: str, webhook_data: Dict[str, Any], parsed_data: Any):
        """Store webhook data in database"""
        try:
            # Store raw webhook data
            await self.storage_manager.save_raw_webhook(webhook_id, webhook_data)
            
            # Store parsed data if it has a to_dict method
            if hasattr(parsed_data, 'to_dict'):
                parsed_dict = parsed_data.to_dict()
                success = await self.storage_manager.store_webhook_alert(parsed_dict)
                if not success:
                    logger.error("Failed to store webhook alert", webhook_id=webhook_id)
                    await self._store_failed_webhook(webhook_data, "Storage failed")
            else:
                # Store as-is if it's already a dict
                success = await self.storage_manager.store_webhook_alert(parsed_data)
                if not success:
                    logger.error("Failed to store webhook alert", webhook_id=webhook_id)
                    await self._store_failed_webhook(webhook_data, "Storage failed")
                
        except Exception as e:
            logger.error("Failed to store webhook data", 
                        error=str(e), 
                        webhook_id=webhook_id,
                        webhook_data_type=type(webhook_data).__name__,
                        parsed_data_type=type(parsed_data).__name__)
            await self._store_failed_webhook(webhook_data, f"Storage error: {str(e)}")
    
    async def _send_alerts(self, parsed_data: Any):
        """Send alerts based on parsed webhook data"""
        try:
            # Convert to dict if needed
            if hasattr(parsed_data, 'to_dict'):
                data_dict = parsed_data.to_dict()
            else:
                data_dict = parsed_data
            
            # Safely get symbol - handle both object and dict formats
            symbol = None
            if hasattr(parsed_data, 'symbol'):
                symbol = parsed_data.symbol
            elif isinstance(data_dict, dict):
                symbol = data_dict.get("symbol", "unknown")
            else:
                symbol = "unknown"
            
            # Import AnalysisResult here to avoid circular imports
            from .analyzer import AnalysisResult
            
            # Create proper AnalysisResult object for alert engine
            analysis_result = AnalysisResult(
                symbol=symbol,
                signal_type="alert",
                confidence=0.5,
                timestamp=time.time(),
                data_source="tradingview",
                should_alert=True
            )
            
            # Send alert
            await self.alert_engine.send_alert(analysis_result)
            
        except Exception as e:
            logger.error("Failed to send alert", error=str(e))
    
    async def _store_failed_webhook(self, webhook_data_raw: str, error: str):
        """Store failed webhook for manual review"""
        try:
            # Store in a failed webhooks table or log for review
            logger.error("Failed webhook stored for review", 
                        webhook_data=webhook_data_raw[:200] if isinstance(webhook_data_raw, str) else str(webhook_data_raw)[:200],
                        error=error,
                        timestamp=time.time())
            
            # TODO: Implement failed webhook storage table
            # For now, just log it for manual review
            
        except Exception as e:
            logger.error("Failed to store failed webhook", error=str(e))
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status of the webhook processor"""
        try:
            return {
                "status": "healthy" if self.is_running else "stopped",
                "is_running": self.is_running,
                "redis_connected": self.redis_client is not None,
                "storage_manager_healthy": await self.storage_manager.get_health_status() if self.storage_manager else False,
                "alert_engine_healthy": self.alert_engine is not None,
                "ai_analyzer_healthy": self.ai_trading_analyzer is not None if hasattr(self, 'ai_trading_analyzer') else False,
                "last_webhook_processed": getattr(self, 'last_webhook_time', None),
                "processing_queue_size": await self.redis_client.llen("webhook_queue") if self.redis_client else None,
                "errors_in_last_hour": getattr(self, 'error_count', 0)
            }
        except Exception as e:
            logger.error("Failed to get health status", error=str(e))
            return {
                "status": "error",
                "error": str(e)
            }
    
    def stop(self):
        """Stop the webhook processor"""
        self.is_running = False
        logger.info("Webhook processor stop requested")

# Global webhook processor instance
webhook_processor = WebhookProcessor() 