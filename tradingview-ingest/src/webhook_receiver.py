"""
High-Volume Webhook Receiver for TradingView
Handles multiple simultaneous webhooks efficiently using Redis queues
"""

import asyncio
import json
import time
import hashlib
import hmac
import os
from typing import Dict, Any, Optional
from fastapi import FastAPI, Request, HTTPException, status, BackgroundTasks
from fastapi.responses import JSONResponse
import redis.asyncio as redis
import structlog
from prometheus_client import Counter, Histogram, Gauge

from .security import security_middleware

# Initialize FastAPI app
app = FastAPI(title="TradingView Webhook Receiver", version="1.0.0")

# Prometheus metrics
webhook_requests_total = Counter('webhook_requests_total', 'Total webhook requests received')
webhook_processing_duration = Histogram('webhook_processing_duration_seconds', 'Webhook processing duration')
webhook_queue_size = Gauge('webhook_queue_size', 'Current webhook queue size')
webhook_errors_total = Counter('webhook_errors_total', 'Total webhook processing errors')

# Redis connection
redis_client: Optional[redis.Redis] = None
logger = structlog.get_logger()

# Configuration
WEBHOOK_QUEUE = "webhook_queue"
MAX_CONCURRENT_WEBHOOKS = 50
WEBHOOK_TIMEOUT = 30

@app.on_event("startup")
async def startup_event():
    """Initialize Redis connection on startup"""
    global redis_client
    redis_url = os.getenv("REDIS_URL", "redis://:tradingview_pass@redis:6379/0")
    redis_client = redis.from_url(redis_url, decode_responses=True)
    
    # Test Redis connection
    try:
        await redis_client.ping()
        logger.info("Redis connection established")
        
        # Start webhook processor in background
        from src.webhook_processor import webhook_processor
        asyncio.create_task(webhook_processor.start_processing(redis_client))
        logger.info("Webhook processor started")
        
    except Exception as e:
        logger.error("Failed to connect to Redis", error=str(e))
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Close Redis connection on shutdown"""
    if redis_client:
        await redis_client.close()
        logger.info("Redis connection closed")

@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint"""
    try:
        # Check Redis connection
        redis_healthy = False
        if redis_client:
            try:
                await redis_client.ping()
                redis_healthy = True
            except Exception:
                pass
        
        # Check webhook processor status
        processor_healthy = False
        try:
            from src.webhook_processor import webhook_processor
            processor_healthy = webhook_processor.initialized and webhook_processor.is_running
        except Exception:
            pass
        
        # Check storage manager health
        storage_healthy = False
        try:
            from src.storage_manager import StorageManager
            storage = StorageManager()
            storage_health = await storage.get_health_status()
            storage_healthy = storage_health.get("database", False) and storage_health.get("redis", False)
        except Exception:
            pass
        
        # Overall health
        overall_healthy = redis_healthy and processor_healthy and storage_healthy
        
        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": time.time(),
            "components": {
                "redis": redis_healthy,
                "webhook_processor": processor_healthy,
                "storage_manager": storage_healthy
            },
            "overall": overall_healthy
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return {
            "status": "error",
            "timestamp": time.time(),
            "error": str(e)
        }

@app.get("/metrics")
async def get_metrics():
    """Get Prometheus metrics"""
    try:
        queue_size = await redis_client.llen(WEBHOOK_QUEUE) if redis_client else 0
        webhook_queue_size.set(queue_size)
        
        return {
            "webhook_requests_total": webhook_requests_total._value.sum(),
            "webhook_errors_total": webhook_errors_total._value.sum(),
            "webhook_queue_size": queue_size,
            "security_stats": security_middleware.get_security_stats()
        }
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        return {"error": str(e)}

@app.post("/webhook/tradingview")
async def tradingview_webhook(request: Request, background_tasks: BackgroundTasks):
    """
    High-volume webhook endpoint for TradingView data
    Queues webhooks for asynchronous processing
    """
    start_time = time.time()
    client_ip = request.client.host
    
    try:
        # Apply security middleware first
        await security_middleware.validate_request(request)
        
        # Increment request counter
        webhook_requests_total.inc()
        
        # Get webhook payload
        payload = await request.json()
        
        # Validate webhook signature if required
        if not await _validate_webhook_signature(request, payload):
            webhook_errors_total.inc()
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid webhook signature"
            )
        
        # Add metadata to payload
        webhook_data = {
            "payload": payload,
            "timestamp": time.time(),
            "client_ip": client_ip,
            "headers": dict(request.headers),
            "webhook_id": _generate_webhook_id(payload)
        }
        
        # Queue webhook for processing
        await _queue_webhook(webhook_data)
        
        # Add background task for immediate processing if queue is small
        background_tasks.add_task(_process_webhook_if_needed)
        
        processing_time = time.time() - start_time
        webhook_processing_duration.observe(processing_time)
        
        logger.info("Webhook queued successfully", 
                   webhook_id=webhook_data["webhook_id"],
                   processing_time=processing_time,
                   client_ip=client_ip)
        
        return {
            "status": "queued",
            "webhook_id": webhook_data["webhook_id"],
            "message": "Webhook received and queued for processing",
            "processing_time": processing_time
        }
        
    except HTTPException:
        raise
    except json.JSONDecodeError as e:
        webhook_errors_total.inc()
        logger.error("Invalid JSON payload", error=str(e), client_ip=client_ip)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON payload"
        )
    except Exception as e:
        webhook_errors_total.inc()
        logger.error("Webhook processing error", error=str(e), client_ip=client_ip)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

async def _validate_webhook_signature(request: Request, payload: Dict[str, Any]) -> bool:
    """Validate webhook signature using HMAC"""
    try:
        # Check if signature validation is required
        require_signature = os.getenv("REQUIRE_WEBHOOK_SIGNATURE")
        
        if not require_signature or require_signature.lower() != "true":
            logger.info("Signature validation disabled - allowing webhook through")
            return True
        
        # Get signature from headers
        signature = request.headers.get("X-TradingView-Signature")
        if not signature:
            logger.warning("Missing webhook signature")
            return False
        
        # Get webhook secret from environment
        webhook_secret = os.getenv("WEBHOOK_SECRET")
        if not webhook_secret:
            logger.error("WEBHOOK_SECRET environment variable not set.")
            return False
        
        # Create expected signature
        payload_str = json.dumps(payload, separators=(',', ':'))
        expected_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            payload_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures
        return hmac.compare_digest(signature, expected_signature)
        
    except Exception as e:
        logger.error("Signature validation error", error=str(e))
        return False

async def _queue_webhook(webhook_data: Dict[str, Any]) -> bool:
    """Queue webhook in Redis for processing"""
    try:
        if not redis_client:
            raise Exception("Redis client not available")
        
        # Add to queue
        await redis_client.lpush(WEBHOOK_QUEUE, json.dumps(webhook_data))
        
        # Set expiration (24 hours)
        await redis_client.expire(WEBHOOK_QUEUE, 86400)
        
        logger.info("Webhook queued",
                   webhook_id=webhook_data["webhook_id"],
                   queue_size=await redis_client.llen(WEBHOOK_QUEUE))
        
        return True
        
    except Exception as e:
        logger.error("Failed to queue webhook", error=str(e))
        return False

async def _process_webhook_if_needed():
    """Process webhook immediately if queue is small"""
    try:
        if not redis_client:
            return
        
        queue_size = await redis_client.llen(WEBHOOK_QUEUE)
        
        # If queue is small, process immediately
        if queue_size < 5:
            logger.info("Queue is small, processing immediately", queue_size=queue_size)
            # This would trigger immediate processing
            # For now, just log it
            
    except Exception as e:
        logger.error("Error in immediate processing check", error=str(e))

def _generate_webhook_id(payload: Dict[str, Any]) -> str:
    """Generate unique webhook ID"""
    payload_str = json.dumps(payload, separators=(',', ':'))
    return hashlib.sha256(f"{payload_str}{time.time()}".encode()).hexdigest()[:16]

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions"""
    logger.warning("HTTP exception", 
                   status_code=exc.status_code,
                   detail=exc.detail,
                   client_ip=request.client.host)
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error("Unhandled exception", 
                error=str(exc),
                client_ip=request.client.host)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"error": "Internal server error"}
    ) 