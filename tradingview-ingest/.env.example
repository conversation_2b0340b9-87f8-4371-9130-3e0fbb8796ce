# TradingView Ingestion Service Environment Variables

# Database Configuration
DATABASE_URL=postgresql://tradingview_user:tradingview_pass@localhost:5433/tradingview_data

# Redis Configuration
REDIS_URL=redis://localhost:6380/0

# Webhook Configuration
WEBHOOK_SECRET=your_super_secret_webhook_key_here

# ngrok Configuration
NGROK_AUTH_TOKEN=your_ngrok_auth_token_here
NGROK_REGION=us

# Server Configuration
HOST=0.0.0.0
PORT=8001
DEBUG=true
ENVIRONMENT=development

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Data Processing Configuration
BATCH_SIZE=100
PROCESSING_DELAY=0.1

# Security Configuration
RATE_LIMIT_PER_MINUTE=1000
MAX_PAYLOAD_SIZE=10485760

# Alert Configuration
ENABLE_ALERTS=true
ALERT_WEBHOOK_URL=https://your-webhook-endpoint.com/alerts
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-url

# TradingView Specific Configuration
SUPPORTED_EXCHANGES=["NASDAQ", "NYSE", "AMEX", "BATS", "ARCA"]
SUPPORTED_TIMEFRAMES=["1m", "5m", "15m", "30m", "1h", "4h", "1d"] 