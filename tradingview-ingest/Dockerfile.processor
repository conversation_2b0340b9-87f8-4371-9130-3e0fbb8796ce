FROM python:3.11-slim

# Security: Run as non-root user
RUN groupadd -r processor && useradd -r -g processor processor

# Install only essential packages
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy processor code
COPY src/ ./src/
COPY config/ ./config/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp && \
    chown -R processor:processor /app

# Security: Switch to non-root user
USER processor

# Create placeholder for processor service
RUN echo 'print("Processor service placeholder")' > processor.py

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "print('Processor healthy')" || exit 1

# Start processor placeholder
CMD ["python", "processor.py"] 