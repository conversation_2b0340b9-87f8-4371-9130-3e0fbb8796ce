FROM python:3.11-slim

# Security: Run as non-root user
RUN groupadd -r webhook && useradd -r -g webhook webhook

# Install only essential packages
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy only webhook receiver requirements
COPY requirements.webhook.txt .
RUN pip install --no-cache-dir -r requirements.webhook.txt

# Copy entire src directory for all dependencies
COPY src/ ./src/
COPY config/ ./config/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp && \
    chown -R webhook:webhook /app

# Security: Switch to non-root user
USER webhook

# Expose only webhook port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Start webhook receiver only
C<PERSON> ["python", "-m", "uvicorn", "src.webhook_receiver:app", "--host", "0.0.0.0", "--port", "8001"] 