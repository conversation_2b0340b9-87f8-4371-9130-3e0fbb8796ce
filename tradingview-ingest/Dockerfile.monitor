FROM python:3.11-slim

# Security: Run as non-root user
RUN groupadd -r monitor && useradd -r -g monitor monitor

# Install only essential packages
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy monitor code
COPY src/ ./src/
COPY config/ ./config/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp && \
    chown -R monitor:monitor /app

# Security: Switch to non-root user
USER monitor

# Create placeholder for monitor service
RUN echo 'print("Monitor service placeholder")' > monitor.py

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "print('Monitor healthy')" || exit 1

# Start monitor placeholder
CMD ["python", "monitor.py"] 