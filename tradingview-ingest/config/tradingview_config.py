"""
TradingView Ingestion Configuration
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings

class TradingViewConfig(BaseSettings):
    """Configuration for TradingView ingestion system"""
    
    # Database Configuration
    database_url: str = "postgresql://tradingview_user:tradingview_pass@localhost:5433/tradingview_data"
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    
    # Webhook Configuration
    webhook_secret: str = "your_webhook_secret_here"
    webhook_endpoint: str = "/webhook/tradingview"
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8001
    debug: bool = False
    
    # Logging Configuration
    log_level: str = "INFO"
    log_format: str = "json"
    
    # Data Processing Configuration
    batch_size: int = 100
    processing_delay: float = 0.1  # seconds
    
    # Security Configuration
    rate_limit_per_minute: int = 100  # Reduced for security
    max_payload_size: int = 1024 * 1024  # 1MB max for security
    webhook_ip_whitelist: list = []  # Empty means no IP restrictions
    require_signature: bool = True
    max_webhook_age_seconds: int = 300  # 5 minutes max age
    
    # TradingView Specific Configuration
    supported_exchanges: list = ["NASDAQ", "NYSE", "AMEX", "BATS", "ARCA"]
    supported_timeframes: list = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
    
    # Alert Configuration
    enable_alerts: bool = True
    alert_webhook_url: Optional[str] = None
    discord_webhook_url: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global configuration instance
config = TradingViewConfig()

# Environment-specific overrides
if os.getenv("ENVIRONMENT") == "production":
    config.debug = False
    config.log_level = "WARNING"
elif os.getenv("ENVIRONMENT") == "development":
    config.debug = True
    config.log_level = "DEBUG" 