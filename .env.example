# ======================================================================
# TRADING AUTOMATION - ENVIRONMENT CONFIGURATION TEMPLATE
# ======================================================================
# IMPORTANT: 
# 1. NEVER commit actual secrets to version control
# 2. Replace placeholders with your ACTUAL secrets
# 3. Use strong, unique values for each environment

# ======================================================================
# CORE APPLICATION CONFIGURATION
# ======================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# ======================================================================
# DATABASE CONFIGURATION
# ======================================================================
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/tradingbot
USE_SUPABASE=false
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_key_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ======================================================================
# SECURITY CONFIGURATION
# ======================================================================
SECRET_KEY=generate_a_long_random_secret_key_here
JWT_SECRET=your_jwt_secret_here

# ======================================================================
# MARKET DATA PROVIDER CONFIGURATION
# ======================================================================
POLYGON_API_KEY=your_polygon_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_API_SECRET=your_alpaca_api_secret

# ======================================================================
# DISCORD BOT CONFIGURATION
# ======================================================================
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_discord_guild_id
DISCORD_AI_CHANNEL_ID=your_discord_channel_id

# ======================================================================
# AI/LLM CONFIGURATION
# ======================================================================
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_ENABLED=false

# Add any other environment-specific configurations here
# Remember: NEVER commit actual secrets!

# Redis
REDIS_PASSWORD=your_secure_password_here
