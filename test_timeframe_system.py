#!/usr/bin/env python3
"""
Test script for the timeframe service system.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.timeframe_service import TimeframeEngine
from services.alert_service import HeartbeatMonitor
from services.market_stream import MarketStream

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_timeframe_engine():
    """Test the timeframe engine functionality."""
    print("🧪 Testing Timeframe Engine...")
    
    try:
        # Create engine
        engine = TimeframeEngine(base_resolution="1m", secondary_tf="5m")
        print("✅ Timeframe Engine created successfully")
        
        # Test candle aggregation
        test_candles = []
        base_time = datetime.now()
        
        # Create 10 test 1-minute candles
        for i in range(10):
            candle = type('MockCandle', (), {
                'timestamp': base_time + timedelta(minutes=i),
                'open': 100.0 + i * 0.1,
                'high': 100.0 + i * 0.1 + 0.5,
                'low': 100.0 + i * 0.1 - 0.3,
                'close': 100.0 + i * 0.1 + 0.2,
                'volume': 100000 + i * 1000,
                'ticker': 'TEST',
                'timeframe': '1m'
            })()
            test_candles.append(candle)
        
        print(f"✅ Created {len(test_candles)} test candles")
        
        # Test resampling to 5m
        resampled = engine._resample_candles(test_candles, 5)
        print(f"✅ Resampled to 5m: {len(resampled)} candles")
        
        # Test resampling to 15m
        resampled_15m = engine._resample_candles(test_candles, 15)
        print(f"✅ Resampled to 15m: {len(resampled_15m)} candles")
        
        return True
        
    except Exception as e:
        print(f"❌ Timeframe Engine test failed: {e}")
        return False

def test_alert_monitor():
    """Test the alert monitor functionality."""
    print("\n🧪 Testing Alert Monitor...")
    
    try:
        # Create monitor
        monitor = HeartbeatMonitor()
        print("✅ Alert Monitor created successfully")
        
        # Test trend calculation
        test_candles = []
        base_time = datetime.now()
        
        # Create test candles with bullish trend
        for i in range(5):
            candle = type('MockCandle', (), {
                'timestamp': base_time + timedelta(minutes=i),
                'open': 100.0 + i * 0.5,
                'high': 100.0 + i * 0.5 + 0.3,
                'low': 100.0 + i * 0.5 - 0.1,
                'close': 100.0 + i * 0.5 + 0.2,
                'volume': 100000,
                'ticker': 'TEST',
                'timeframe': '1m'
            })()
            test_candles.append(candle)
        
        # Test trend calculation
        trend = monitor._calculate_trend(test_candles)
        print(f"✅ Trend calculation: {trend}")
        
        # Test momentum calculation
        momentum = monitor._calculate_momentum(test_candles)
        print(f"✅ Momentum calculation: {momentum:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Alert Monitor test failed: {e}")
        return False

def test_market_stream():
    """Test the market stream functionality."""
    print("\n🧪 Testing Market Stream...")
    
    try:
        # Create stream
        stream = MarketStream()
        print("✅ Market Stream created successfully")
        
        # Test webhook processing
        test_webhook = {
            'symbol': 'AAPL',
            'timestamp': datetime.now().isoformat(),
            'open': 175.0,
            'high': 175.5,
            'low': 174.8,
            'close': 175.2,
            'volume': 500000,
            'timeframe': '1m'
        }
        
        # Test validation
        is_valid = stream._validate_webhook_data(test_webhook)
        print(f"✅ Webhook validation: {is_valid}")
        
        # Test data extraction
        market_data = stream._extract_market_data(test_webhook)
        if market_data:
            print(f"✅ Data extraction: {market_data.ticker} @ {market_data.close}")
        else:
            print("❌ Data extraction failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Market Stream test failed: {e}")
        return False

async def test_integration():
    """Test the integrated system."""
    print("\n🧪 Testing System Integration...")
    
    try:
        # Create components
        engine = TimeframeEngine()
        monitor = HeartbeatMonitor()
        stream = MarketStream()
        
        # Connect components
        stream.set_timeframe_engine(engine)
        stream.set_alert_monitor(monitor)
        
        print("✅ Components connected successfully")
        
        # Test simulated data
        await stream.simulate_market_data("AAPL", "1m")
        await stream.simulate_market_data("TSLA", "1m")
        
        print("✅ Simulated data processed successfully")
        
        # Get status
        status = stream.get_status()
        print(f"✅ Stream status: {status['is_running']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Timeframe Service System Tests...\n")
    
    # Run individual tests
    tests = [
        ("Timeframe Engine", test_timeframe_engine),
        ("Alert Monitor", test_alert_monitor),
        ("Market Stream", test_market_stream)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Run integration test
    try:
        integration_result = asyncio.run(test_integration())
        results.append(("System Integration", integration_result))
    except Exception as e:
        print(f"❌ Integration test crashed: {e}")
        results.append(("System Integration", False))
    
    # Print results
    print("\n📊 Test Results:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 